package config

import "gitlab.dailyyoga.com.cn/server/go-artifact/config"

type Conf struct {
	config.Base
	YogaRedis       config.Redis        `yaml:"redis"`
	YogaRedis01     config.Redis        `yaml:"yoga_redis_01"`
	YogaLockRedis   config.Redis        `yaml:"lock_redis"`
	DancefitRedis   config.Redis        `yaml:"dancefit_redis"`
	FitnessRedis    config.Redis        `yaml:"fitness_redis"`
	StretchRedis    config.Redis        `yaml:"stretch_redis"`
	ChildrenRedis   config.Redis        `yaml:"children_redis"`
	YogaMemcached   config.Memcached    `yaml:"memcached"`
	Php620Address   string              `yaml:"php_620_address"`
	PhpYogaAddress  string              `yaml:"php_yoga_address"`
	SensorsDataPath string              `yaml:"sensors_data_path"`
	DefaultBroker   string              `yaml:"default_broker"`
	DBUserCenter    config.Database     `yaml:"db_user_center"`
	DBStatRDS       config.Database     `yaml:"db_stat_rds"`
	DBStat          config.Database     `yaml:"db_stat"`
	DBDancefit      config.Database     `yaml:"db_dancefit"`
	DBFitness       config.Database     `yaml:"db_fitness"`
	DBStretch       config.Database     `yaml:"db_stretch"`
	DBChildren      config.Database     `yaml:"db_children"`
	AppleConfig     iOSPaymentConfig    `yaml:"apple_config"`
	Qiniu           QiniuCloud          `yaml:"qiniu"`
	HxConfig        HxConfig            `yaml:"hx_config"`
	AppleAdsConfig  AppleAdsConfig      `yaml:"apple_ads_config"`
	ConsumerGroups  []ConsumerGroupItem `yaml:"consumer_groups"`
	ShortURLConfig  ShortURLConf        `yaml:"short_url_config"`
	ALI             Ali                 `yaml:"ali"`
	Wechat          WeChat              `yaml:"wechat"`
	DBConfig        DBSConfig           `yaml:"database_config"`
	WebhookURL      string              `yaml:"webhook_url"`
}

type DBSConfig struct {
	DBUserCenter     DBConfig `yaml:"db_user_center"`
	DBStatRDS        DBConfig `yaml:"db_stat_rds"`
	DBStat           DBConfig `yaml:"db_stat"`
	DatabaseDancefit DBConfig `yaml:"db_dancefit"`
	DatabaseFitness  DBConfig `yaml:"db_fitness"`
	DatabaseStretch  DBConfig `yaml:"db_stretch"`
	DatabaseChildren DBConfig `yaml:"db_children"`
}

type DBConfig struct {
	Master DBConfigItem `yaml:"master"`
	Slaves DBConfigItem `yaml:"slaves"`
}

type DBConfigItem struct {
	MaxOpen int `yaml:"max_open"`
	MaxIdle int `yaml:"max_idle"`
}

type WeChat struct {
	AppID  string `yaml:"app_id"`
	MchID  string `yaml:"mch_id"`
	AppKey string `yaml:"app_key"`
}

type Ali struct {
	Pay struct {
		DailYoga DailYoga `yaml:"daily_yoga"`
	} `yaml:"pay"`
}

type DailYoga struct {
	AppID        string `yaml:"app_id"`
	PrivateKey   string `yaml:"private_key"`
	AliPublicKey string `yaml:"ali_public_key"`
}

type QiniuCloud struct {
	AccessKey string `yaml:"access_key"`
	SecretKey string `yaml:"secret_key"`
}

type HxConfig struct {
	ClientID     string `yaml:"client_id"`
	ClientSecret string `yaml:"client_secret"`
	OrgName      string `yaml:"org_name"`
	AppName      string `yaml:"app_name"`
	MaxUser      int64  `yaml:"max_user"`
	GrantType    string `yaml:"grant_type"`
	HxURL        string `yaml:"hx_url"`
}

type iOSPaymentConfig struct {
	SandboxDomain string `yaml:"sandbox_domain"`
	ProductDomain string `yaml:"product_domain"`
	URI           string `yaml:"uri"`
	Password      string `yaml:"password"`
	Retry         int64  `yaml:"retry"`
}

type ConsumerGroupItem struct {
	Name          string         `yaml:"name"`
	Broker        string         `yaml:"broker"`
	MaxRetryTimes int            `yaml:"max_retry_times"`
	InstanceNum   int            `yaml:"instance_num"`
	Topic         []string       `yaml:"topic"`
	ParamsConfig  []ParamsConfig `yaml:"params_config"`
}

type ParamsConfig struct {
	Key   string      `yaml:"key"`
	Value interface{} `yaml:"value"`
}

type AppleAdsConfig struct {
	ClientID string `yaml:"client_id"`
	TeamID   string `yaml:"team_id"`
	KeyID    string `yaml:"key_id"`
	Audience string `yaml:"audience"`
	Alg      string `yaml:"alg"`
	OrgName  string `yaml:"org_name"`
}

type ShortURLConf struct {
	URL string `yaml:"url"`
}

var gConf Conf

func Get() *Conf {
	return &gConf
}
