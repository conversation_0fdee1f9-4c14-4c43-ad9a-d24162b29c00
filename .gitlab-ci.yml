stages:
  - mchid-check
  - linter
  - compile
  - images

before_script:
  - git version
  - go version
  - go env -w GOPRIVATE=*.dailyyoga.com.cn
  - go env -w GO111MODULE=on
  - go env -w GOPROXY="https://goproxy.io,direct"
  - export CGO_ENABLED=1

golangci-lint:
  stage: linter
  tags:
    - server-runner
  script:
    - /usr/local/bin/golangci-lint run
  allow_failure: false
  only:
    refs:
      - master
      - release/dev
      - release/mirror

compile-app:
  stage: compile
  tags:
    - server-runner
  script:
    - GOOS=linux go build -tags dynamic -ldflags '-extldflags  -s -w' -o app  ./*.go
  allow_failure: false
  only:
    refs:
      - master
      - release/dev
      - release/mirror
  artifacts:
    name: "app"
    expire_in: 5 min
    paths:
      - app

images:
  stage: images
  tags:
    - server-runner
  script:
    - echo $CI_COMMIT_REF_NAME
    - /alidata/go/bin/yogactl push-acr --service=srv-task --refs=$CI_COMMIT_REF_NAME
  allow_failure: false
  only:
    refs:
      - master
      - release/dev
      - release/mirror
