package consumer

import (
	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/kafka/consumer"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/group"
	"gitlab.dailyyoga.com.cn/server/srv-task/group/cs"
	"gitlab.dailyyoga.com.cn/server/srv-task/group/df"
	"gitlab.dailyyoga.com.cn/server/srv-task/group/ft"
	"gitlab.dailyyoga.com.cn/server/srv-task/group/yq"
)

var groupEnum = struct {
	PlayTime   string
	Practice   string
	Async      string
	Equity     string
	Payment    string
	DfPractice string
	FtPractice string
	YQPractice string
	CsPractice string
	PayAmoChec string
}{
	PlayTime:   "play_time",
	Practice:   "practice",
	Async:      "async",
	Equity:     "equity",
	Payment:    "payment",
	DfPractice: "df_practice",
	FtPractice: "ft_practice",
	YQPractice: "yq_practice",
	CsPractice: "cs_practice",
	PayAmoChec: "payment_amount_check",
}

var topicEnum = struct {
	PlayTime   string
	Practice   string
	Async      string
	Equity     string
	Payment    string
	DfPractice string
	FtPractice string
	YQPractice string
	CsPractice string
	PayAmoChec string
}{
	PlayTime:   "play_time",
	Practice:   "practice_log",
	Async:      "async",
	Equity:     "equity",
	Payment:    "payment",
	DfPractice: "df_practice",
	FtPractice: "ft_practice",
	YQPractice: "yq_practice",
	CsPractice: "cs_practice",
	PayAmoChec: "payment_amount_check",
}

func Init(cfg *config.Conf) error {
	for _, item := range cfg.ConsumerGroups {
		if err := initConsumerItem(item); err != nil {
			return err
		}
	}
	return nil
}

// nolint
func initConsumerItem(item config.ConsumerGroupItem) error {
	currentEnv := config.Get().GetEnv()
	name := getRealConsumerGroupName(currentEnv, item.Name)
	if name == "" {
		return errors.Errorf("consumer %s: group name not found", item.Name)
	}
	topics := func() []string {
		topic := make([]string, 0)
		for _, v := range item.Topic {
			if realName := getRealTopicName(currentEnv, v); realName != "" {
				topic = append(topic, realName)
			}
		}
		return topic
	}
	broker := func() string {
		if item.Broker != "" {
			return item.Broker
		}
		return config.Get().DefaultBroker
	}
	retries := func() int {
		defaultRetryTimes := 5
		if item.MaxRetryTimes < 1 {
			return defaultRetryTimes
		}
		return item.MaxRetryTimes
	}
	instanceNum := func() int {
		if item.InstanceNum < 1 {
			return 1
		}
		return item.InstanceNum
	}
	consumerGroup := &consumer.GroupConfig{
		Name:          name,
		Broker:        broker(),
		MaxRetryTimes: retries(),
		InstanceNum:   instanceNum(),
		Topic:         topics(),
	}
	if len(consumerGroup.Topic) == 0 {
		return errors.Errorf("consumer %s: topics undefined.", item.Name)
	}
	runner := getRunner(item.Name)
	if runner == nil {
		return errors.Errorf("consumer %s: runner not found", item.Name)
	}
	confMap := make(map[string]interface{})
	if len(item.ParamsConfig) > 0 {
		for i := range item.ParamsConfig {
			confMap[item.ParamsConfig[i].Key] = item.ParamsConfig[i].Value
		}
	}
	if err := consumer.New(consumerGroup, runner, consumer.WithConfigMap(confMap)); err != nil {
		return err
	}
	return nil
}

func validateGroupName(name string) bool {
	names := []string{
		groupEnum.PlayTime, groupEnum.Practice,
		groupEnum.Async, groupEnum.Equity,
		groupEnum.Payment,
		groupEnum.DfPractice,
		groupEnum.FtPractice,
		groupEnum.YQPractice,
		groupEnum.CsPractice,
		groupEnum.PayAmoChec,
	}
	for _, v := range names {
		if v == name {
			return true
		}
	}
	return false
}

func validateTopicName(name string) bool {
	names := []string{
		topicEnum.PlayTime, topicEnum.Practice,
		topicEnum.Async, topicEnum.Equity,
		topicEnum.Payment,
		topicEnum.DfPractice,
		topicEnum.FtPractice,
		topicEnum.YQPractice,
		topicEnum.CsPractice,
		topicEnum.PayAmoChec,
	}
	for _, v := range names {
		if v == name {
			return true
		}
	}
	return false
}

func getRealConsumerGroupName(currentEnv env.Env, name string) string {
	if ok := validateGroupName(name); !ok {
		return ""
	}
	prefix := "cg_qa_"
	if currentEnv == env.Mirror {
		prefix = "cg_mirror_"
	}
	if currentEnv == env.Product {
		prefix = "cg_"
	}
	return prefix + name
}

func getRealTopicName(currentEnv env.Env, name string) string {
	if ok := validateTopicName(name); !ok {
		return ""
	}

	prefix := "topic_qa_"
	if currentEnv == env.Mirror {
		prefix = "topic_mirror_"
	}
	if currentEnv == env.Product {
		prefix = "topic_"
	}
	return prefix + name
}

func getRunner(name string) consumer.Runner {
	if ok := validateGroupName(name); !ok {
		return nil
	}
	maps := map[string]consumer.Runner{
		groupEnum.PlayTime:   new(group.PlayTimeProcessor),
		groupEnum.Practice:   new(group.PracticeProcessor),
		groupEnum.Async:      new(group.AsyncProcessor),
		groupEnum.Equity:     new(group.EquityProcessor),
		groupEnum.Payment:    new(group.PaymentProcessor),
		groupEnum.DfPractice: new(df.PracticeProcessor),
		groupEnum.FtPractice: new(ft.PracticeProcessor),
		groupEnum.YQPractice: new(yq.PracticeProcessor),
		groupEnum.CsPractice: new(cs.PracticeProcessor),
		groupEnum.PayAmoChec: new(group.PaymentAmountCheck),
	}
	if runner, ok := maps[name]; ok {
		return runner
	}
	return nil
}
