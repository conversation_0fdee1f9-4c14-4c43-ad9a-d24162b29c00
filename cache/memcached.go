package cache

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/memcache"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

var gMemcached memcache.Memcache

func initMemcached(cfg *config.Conf) error {
	mc := cfg.YogaMemcached
	if err := gMemcached.Init(mc.Host, mc.Name, mc.Password); err != nil {
		return err
	}
	logger.Infof("memcached 初始化成功，地址: %s", mc.Host)
	return nil
}

func GetMemcached() *memcache.Memcache {
	return &gMemcached
}
