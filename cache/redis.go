package cache

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/redisv2"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

var (
	yogaRedis   *redisv2.Client
	yogaRedis01 *redisv2.Client
	lockRedis   *redisv2.Client
	dfRedis     *redisv2.Client
	ftRedis     *redisv2.Client
	stRedis     *redisv2.Client
	csRedis     *redisv2.Client
)

func initRedis(rd *config.Conf) error {
	var err error
	yogaRedis, err = redisv2.NewClient(rd.YogaRedis.Address, rd.YogaRedis.Password)
	if err != nil {
		return err
	}
	yogaRedis01, err = redisv2.NewClient(rd.YogaRedis01.Address, rd.YogaRedis01.Password)
	if err != nil {
		return err
	}
	lockRedis, err = redisv2.NewClient(rd.YogaLockRedis.Address, rd.YogaLockRedis.Password)
	if err != nil {
		return err
	}
	dfRedis, err = redisv2.NewClient(rd.DancefitRedis.Address, rd.DancefitRedis.Password)
	if err != nil {
		return err
	}
	ftRedis, err = redisv2.NewClient(rd.FitnessRedis.Address, rd.FitnessRedis.Password)
	if err != nil {
		return err
	}
	stRedis, err = redisv2.NewClient(rd.StretchRedis.Address, rd.StretchRedis.Password)
	if err != nil {
		return err
	}
	csRedis, err = redisv2.NewClient(rd.ChildrenRedis.Address, rd.ChildrenRedis.Password)
	if err != nil {
		return err
	}
	return nil
}

func GetYogaRedis() *redisv2.Client {
	return yogaRedis
}

func GetYogaRedis01() *redisv2.Client {
	return yogaRedis01
}

func GetLockRedis() *redisv2.Client {
	return lockRedis
}

func GetDancefitRedis() *redisv2.Client {
	return dfRedis
}

func GetFitnessRedis() *redisv2.Client {
	return ftRedis
}

func GetStretchRedis() *redisv2.Client {
	return stRedis
}

func GetChildrenRedis() *redisv2.Client {
	return csRedis
}
