package main

import (
	"log"

	conf "gitlab.dailyyoga.com.cn/server/go-artifact/config"
	"gitlab.dailyyoga.com.cn/server/go-artifact/cron"
	"gitlab.dailyyoga.com.cn/server/go-artifact/microsvr"
	"gitlab.dailyyoga.com.cn/server/go-artifact/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/consumer"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
)

func main() {
	cfg := config.Get()

	resourceKeys := []string{
		"common/etcd", "common/redis", "common/memcached",
		"db/usercenter", "db/statrds", "db/stat", "db/dancefit", "db/fitness", "db/stretch", "db/children",
	}
	resources := []func(option *conf.Option) *conf.Option{
		conf.WithKeys(resourceKeys),
	}
	conf.Init(cfg, "srv-task", resources...)

	if err := microsvr.Init(cfg); err != nil {
		log.Fatal(err)
	}

	initFns := []func(cfg *config.Conf) error{
		cache.Init, databases.Init, grpc.Init, consumer.Init,
	}
	for _, fn := range initFns {
		if err := fn(cfg); err != nil {
			log.Fatal(err)
		}
	}

	// init cron
	cronServer := cron.NewCronServer()
	cronHandle(cronServer)
	cronServer.Start()

	// init sensors data
	if err := sensorsdata.Init(cfg.SensorsDataPath, false, cfg.GetEnv()); err != nil {
		log.Fatalf("sensors data init failed. %s", err)
	}

	// run service
	microsvr.Run()
}
