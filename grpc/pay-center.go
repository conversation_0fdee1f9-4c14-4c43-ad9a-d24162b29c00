package grpc

import (
	proto "gitlab.dailyyoga.com.cn/protogen/pay-center-go/paycenter"
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/grpc"
)

var payCenterClient proto.PayCenterClient

func GetPayCenterClient() proto.PayCenterClient {
	return payCenterClient
}

func initPayCenter(cfgEnv env.Env) error {
	liveServ := "pay-center"
	if cfgEnv == env.Mirror {
		liveServ = "pay-center-mirro"
	}
	conn, err := grpc.NewGRPCClient(liveServ)
	if err != nil {
		return err
	}
	payCenterClient = proto.NewPayCenterClient(conn)
	return nil
}
