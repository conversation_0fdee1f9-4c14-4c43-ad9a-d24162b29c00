package grpc

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

func Init(cfg *config.Conf) error {
	envior := cfg.GetEnv()

	initFns := []func(envior env.Env) error{
		initUserCenter, initModeration, initAccount, initPayCenter, initFitness,
		initDancefit, initYoGaCs, initStretch, initPayBridge,
	}
	for _, fn := range initFns {
		if err := fn(envior); err != nil {
			return err
		}
	}
	return nil
}
