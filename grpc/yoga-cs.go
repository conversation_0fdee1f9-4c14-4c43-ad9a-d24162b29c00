package grpc

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/grpc"

	proto "gitlab.dailyyoga.com.cn/protogen/yoga-cs-go/yoga-cs"
)

var yoGaCsClient proto.YoGaCSClient

func GetYoGaCsClientClient() proto.YoGaCSClient {
	return yoGaCsClient
}

func initYoGaCs(cfgEnv env.Env) error {
	serviceName := "yoga-cs"
	if cfgEnv == env.Mirror {
		serviceName = "yoga-cs-mirro"
	}

	cc, err := grpc.NewGRPCClient(serviceName)
	if err != nil {
		return err
	}
	yoGaCsClient = proto.NewYoGaCSClient(cc)
	return nil
}
