package grpc

import (
	proto "gitlab.dailyyoga.com.cn/protogen/fitness-go/fitness"
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/grpc"
)

var fitnessClient proto.FitnessClient

func GetFitnessClient() proto.FitnessClient {
	return fitnessClient
}

func initFitness(envior env.Env) error {
	serviceName := "fitness"
	if envior == env.Mirror {
		serviceName = "fitness-mirror"
	}
	cc, err := grpc.NewGRPCClient(serviceName)
	if err != nil {
		return err
	}
	fitnessClient = proto.NewFitnessClient(cc)
	return nil
}
