package grpc

import (
	proto "gitlab.dailyyoga.com.cn/protogen/dancefit-go/dancefit"
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/grpc"
)

var dancefitClient proto.DancefitClient

func GetDancefitClient() proto.DancefitClient {
	return dancefitClient
}

func initDancefit(envior env.Env) error {
	serviceName := "dancefit"
	if envior == env.Mirror {
		serviceName = "dancefit-mirror"
	}
	cc, err := grpc.NewGRPCClient(serviceName)
	if err != nil {
		return err
	}
	dancefitClient = proto.NewDancefitClient(cc)
	return nil
}
