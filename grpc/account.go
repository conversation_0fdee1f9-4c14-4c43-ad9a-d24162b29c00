package grpc

import (
	proto "gitlab.dailyyoga.com.cn/protogen/account-go"
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/grpc"
)

var accountClient proto.AccountClient

func GetAccountClient() proto.AccountClient {
	return accountClient
}

func initAccount(envior env.Env) error {
	serviceName := "account"
	if envior == env.Mirror {
		serviceName = "account-mirror"
	}
	cc, err := grpc.NewGRPCClient(serviceName)
	if err != nil {
		return err
	}
	accountClient = proto.NewAccountClient(cc)
	return nil
}
