package grpc

import (
	"gitlab.dailyyoga.com.cn/protogen/stretch-go/stretch"
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/grpc"
)

var stretchClient stretch.StretchClient

func GetStretchClient() stretch.StretchClient {
	return stretchClient
}

func initStretch(cfgEnv env.Env) error {
	serviceName := "stretch"
	if cfgEnv == env.Mirror {
		serviceName = "stretch-mirror"
	}
	cc, err := grpc.NewGRPCClient(serviceName)
	if err != nil {
		return err
	}
	stretchClient = stretch.NewStretchClient(cc)
	return nil
}
