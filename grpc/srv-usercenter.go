package grpc

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/grpc"

	"gitlab.dailyyoga.com.cn/protogen/srv-usercenter-go/proto"
)

var userCenterClient proto.UserCenterClient

func GetUserCenterClient() proto.UserCenterClient {
	return userCenterClient
}

func initUserCenter(cfgEnv env.Env) error {
	serviceName := "srv-usercenter"
	if cfgEnv == env.Mirror {
		serviceName = "srv-usercenter-mirro"
	}
	cc, err := grpc.NewGRPCClient(serviceName)
	if err != nil {
		return err
	}
	userCenterClient = proto.NewUserCenterClient(cc)
	return nil
}
