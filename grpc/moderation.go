package grpc

import (
	proto "gitlab.dailyyoga.com.cn/protogen/moderation-go"
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/grpc"
)

var moderationClient proto.ModerationClient

func GetModerationClient() proto.ModerationClient {
	return moderationClient
}

func initModeration(cfgEnv env.Env) error {
	serviceName := "moderation"
	if cfgEnv == env.Mirror {
		serviceName = "moderation-mirro"
	}

	cc, err := grpc.NewGRPCClient(serviceName)
	if err != nil {
		return err
	}
	moderationClient = proto.NewModerationClient(cc)
	return nil
}
