package grpc

import (
	"gitlab.dailyyoga.com.cn/protogen/pay-bridge-go/paybridge"
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/grpc"
)

var payBridgeClient paybridge.PayBridgeClient

func GetPayBridgeClient() paybridge.PayBridgeClient {
	return payBridgeClient
}

func initPayBridge(cfgEnv env.Env) error {
	serviceName := "pay-bridge"
	if cfgEnv == env.Mirror {
		serviceName = "pay-bridge-mirro"
	}

	conn, err := grpc.NewGRPCClient(serviceName)
	if err != nil {
		return err
	}
	payBridgeClient = paybridge.NewPayBridgeClient(conn)
	return nil
}
