package main

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/cron"
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	cronsrv "gitlab.dailyyoga.com.cn/server/srv-task/cron"
)

func cronHandle(s *cron.Server) {
	cfgEnv := config.Get().GetEnv()
	if cfgEnv == env.Mirror {
		// Mirror 规定不允许跑定时任务
		return
	}
	if cfgEnv != env.Mirror {
		s.Handle("prepare_practice_table", "@every 4h", cronsrv.PreparePracticeTable)
		s.<PERSON>le("prepare_usercenter_table", "@every 4h", cronsrv.PrepareUserCenterTable)
		s.<PERSON><PERSON>("prepare_statrds_table", "@every 4h", cronsrv.PrePareDBStatTable)
		s.<PERSON><PERSON>("collect_practice_data", "@every 10s", cronsrv.CollectPracticeData)
		s.<PERSON>le("delete_expire_label_data", "@every 10s", cronsrv.DeleteExpireLabelData)
		s.<PERSON>("calc_expire_lable_data", "0 2,4 * * *", cronsrv.CalcExpireUserLable)
		s.Handle("retry_apple_order", "@every 15s", cronsrv.RetryAppleOrder) // ios 订单创建重试
	}
}
