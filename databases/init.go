package databases

import (
	conf "gitlab.dailyyoga.com.cn/server/go-artifact/config"
	"gitlab.dailyyoga.com.cn/server/go-artifact/database"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

var (
	userCenterConnect *database.Connect
	statRDSConnect    *database.Connect
	statConnect       *database.Connect
	dancefitConnect   *database.Connect
	fitnessConnect    *database.Connect
	stretchConnect    *database.Connect
	childrenConnect   *database.Connect
)

func GetUserCenter() *database.Connect {
	return userCenterConnect
}

func GetStatRDS() *database.Connect {
	return statRDSConnect
}

func GetStat() *database.Connect {
	return statConnect
}

func GetDanceFit() *database.Connect {
	return dancefitConnect
}

func GetFitness() *database.Connect {
	return fitnessConnect
}

func GetStretch() *database.Connect {
	return stretchConnect
}

func GetChildren() *database.Connect {
	return childrenConnect
}

func Init(cfg *config.Conf) error {
	userCenterConnect = database.Init()
	statRDSConnect = database.Init()
	statConnect = database.Init()
	dancefitConnect = database.Init()
	fitnessConnect = database.Init()
	stretchConnect = database.Init()
	childrenConnect = database.Init()
	if err := initBase(userCenterConnect, &cfg.DBUserCenter, &cfg.DBConfig.DBUserCenter); err != nil {
		return err
	}
	if err := initBase(statRDSConnect, &cfg.DBStatRDS, &cfg.DBConfig.DBStatRDS); err != nil {
		return err
	}
	if err := initBase(statConnect, &cfg.DBStat, &cfg.DBConfig.DBStat); err != nil {
		return err
	}
	if err := initBase(dancefitConnect, &cfg.DBDancefit, &cfg.DBConfig.DatabaseDancefit); err != nil {
		return err
	}
	if err := initBase(fitnessConnect, &cfg.DBFitness, &cfg.DBConfig.DatabaseFitness); err != nil {
		return err
	}
	if err := initBase(stretchConnect, &cfg.DBStretch, &cfg.DBConfig.DatabaseStretch); err != nil {
		return err
	}
	if err := initBase(childrenConnect, &cfg.DBChildren, &cfg.DBConfig.DatabaseChildren); err != nil {
		return err
	}
	return nil
}

func initBase(db *database.Connect, cfg *conf.Database, bConfig *config.DBConfig) error {
	MaxOpen := database.WithMaxOpenConns(10)
	maxIdle := database.WithMaxIdleConns(10)
	if bConfig.Master.MaxOpen > 0 {
		MaxOpen = database.WithMaxOpenConns(bConfig.Master.MaxOpen)
	}
	if bConfig.Master.MaxIdle > 0 {
		maxIdle = database.WithMaxIdleConns(bConfig.Master.MaxIdle)
	}
	if err := db.AddConnect(cfg.Master.String(), database.Master, MaxOpen, maxIdle); err != nil {
		return err
	}
	logger.Infof("database %s 链接成功, db: %s, type: master", cfg.Master.Databases, cfg.Master.Address)
	MaxOpen = database.WithMaxOpenConns(10)
	maxIdle = database.WithMaxIdleConns(10)
	if bConfig.Slaves.MaxOpen > 0 {
		MaxOpen = database.WithMaxOpenConns(bConfig.Slaves.MaxOpen)
	}
	if bConfig.Slaves.MaxIdle > 0 {
		maxIdle = database.WithMaxIdleConns(bConfig.Slaves.MaxIdle)
	}
	// slaves
	for _, v := range cfg.Slaves {
		if err := db.AddConnect(v.String(), database.Slave, MaxOpen, maxIdle); err != nil {
			return err
		}
		logger.Infof("database %s 链接成功, type: slave", v.Address)
	}
	return nil
}
