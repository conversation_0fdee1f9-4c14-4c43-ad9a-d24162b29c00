package top

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/stat"
)

// Click
type Click struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 设备ID
	DeviceID string `xorm:"not null varchar(255) 'device_id'"`
	// 用户ID
	UID int64 `xorm:"not null bigint(30) 'uid'"`
	// 广告ID
	TopID int64 `xorm:"not null int(11) 'top_id'"`
	// 广告类型：1:普通广告,2:视频广告
	AdType int32 `xorm:"not null default '1' tinyint(3) 'ad_type'"`
	// 渠道号
	Channels int32 `xorm:"not null int(14) 'channels'"`
	// 设备类型 1234
	DeviceType int32 `xorm:"not null tinyint(1) 'device_type'"`
	// 版本号
	Version    int32 `xorm:"not null int(10) 'version'"`
	CreateTime int64 `xorm:"not null int(10) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(10) 'update_time'"`
}

// TableName 获取表名
func (c *Click) TableName() string {
	month := time.Unix(c.CreateTime, 0).Format("200601")
	return fmt.Sprintf("top_click_detail_%s", month)
}

func (c *Click) Save() error {
	_, err := stat.GetEngineMaster().Insert(c)
	return err
}
