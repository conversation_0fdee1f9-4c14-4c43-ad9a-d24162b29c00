package top

// ReportType 广告类型
type ReportType int32

// ReportTypeEnum 挑战赛任务类型
var ReportTypeEnum = struct {
	ReportShow  ReportType
	ReportClick ReportType
	ReportGet   ReportType
	ReportHref  ReportType
}{
	ReportShow:  1, // 广告展示量
	ReportClick: 2, // 广告点击量
	ReportGet:   3, // 广告拉取量
	ReportHref:  4, // 广告跳转展示
}

// TopTypeEnum 广告类型
var TopTypeEnum = struct {
	DefaultType ReportType
	VideoType   ReportType
}{
	DefaultType: 1, // 普通top类型广告
	VideoType:   2, // 视屏广告
}
