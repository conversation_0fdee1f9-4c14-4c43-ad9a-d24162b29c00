package st

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/stat"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type Table struct {
	ID int64 `xorm:"not null autoincr pk bigint(20) unsigned 'id'"`
	// 用户id
	UID int64 `xorm:"not null int(11) 'uid'"`
	// 计划id，可能为空
	ProgramID int64 `xorm:"not null int(11) 'program_id'"`
	// 课程id
	SessionID int64 `xorm:"not null int(11) 'session_id'"`
	// 动作id
	ActionID int64 `xorm:"not null int(11) 'action_id'"`
	// 播放时长
	PlayTime int64 `xorm:"not null int(11) 'play_time'"`
	// 卡路里
	Calories int32 `xorm:"not null int(11) 'calories'"`
	// 课程第几天
	SessionIndex int32 `xorm:"not null int(11) 'session_index'"`
	// 版本号
	Version string `xorm:"not null varchar(16) 'version'"`
	// 设备类型：1ap2ip3ipad4apad
	DeviceType int32                `xorm:"not null tinyint(1) 'device_type'"`
	CreateTime int64                `xorm:"not null int(10) 'create_time'"`
	UpdateTime int64                `xorm:"not null int(10) 'update_time'"`
	StTypeName library.StReportType `xorm:"-"`
}

func (t *Table) TableName() string {
	if t.UID < 1 {
		return ""
	}
	mod := t.UID % 128
	switch t.StTypeName {
	case library.StReportTypeEnum.DownloadFinished:
		return fmt.Sprintf("st_session_download_finished_%d", mod)
	case library.StReportTypeEnum.DownloadStart:
		return fmt.Sprintf("st_session_download_start_%d", mod)
	case library.StReportTypeEnum.InProgramPlayFinish:
		return fmt.Sprintf("st_session_in_program_play_finish_%d", mod)
	case library.StReportTypeEnum.InProgramPlayStart:
		return fmt.Sprintf("st_session_in_program_play_start_%d", mod)
	case library.StReportTypeEnum.JoinSession:
		return fmt.Sprintf("st_join_session_%d", mod)
	case library.StReportTypeEnum.ProgramFinished:
		return fmt.Sprintf("st_program_finished_%d", mod)
	case library.StReportTypeEnum.ProgramStart:
		return fmt.Sprintf("st_program_start_%d", mod)
	case library.StReportTypeEnum.PlayFinished:
		return fmt.Sprintf("st_session_play_finished_%d", mod)
	case library.StReportTypeEnum.PlayStart:
		return fmt.Sprintf("st_session_play_start_%d", mod)
	case library.StReportTypeEnum.SessionQuit:
		return fmt.Sprintf("st_quit_session_%d", mod)
	default:
		return ""
	}
}

func (t *Table) Save() error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime

	_, err := stat.GetEngineMaster().Insert(t)
	return err
}
