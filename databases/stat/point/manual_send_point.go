package point

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/stat"
)

type BannerClickDaily struct {
	ID int64 `xorm:"not null autoincr pk bigint(20) unsigned 'id'"`
	// 操作类型：1-加，2-减
	OperationType int32  `xorm:"tinyint(1) unsigned notnull 'operation_type'" json:"operation_type"`
	SendType      int32  `xorm:"tinyint(1) unsigned notnull 'send_type'" json:"send_type"`
	SendData      int64  `xorm:"int(11) unsigned notnull default 0 'send_data'" json:"send_data"`
	PushTitle     string `xorm:"varchar(255) default '' notnull 'push_title'" json:"push_title"`
	PushContent   string `xorm:"varchar(512) default '' notnull 'push_content'" json:"push_content"`
	ScoreContent  string `xorm:"varchar(255) default '' notnull 'score_content'" json:"score_content"`
	Score         int32  `xorm:" smallint(5) unsigned notnull 'score'" json:"score"`
	Reason        string `xorm:"varchar(512) default '' notnull 'reason'" json:"reason"` // 操作原因
	AdminID       int64  `xorm:"int(11) unsigned notnull default 0 'admin_id'" json:"admin_id"`
	CreateTime    int64  `xorm:"not null int(10) unsigned 'create_time'"` // 创建时间
	UpdateTime    int64  `xorm:"not null int(10) unsigned 'update_time'"` // 更新时间
}

// TableName 获取表名
func (BannerClickDaily) TableName() string {
	return "manual_send_point"
}

type _BannerClickDaily struct {
}

var TbBannerClickDaily _BannerClickDaily

// GetItem 手动操作瑜币
func (w *_BannerClickDaily) GetItem(id int64) *BannerClickDaily {
	var table BannerClickDaily
	ok, err := stat.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Errorf("获取手动操作瑜币失败, err: %s", err)
		return nil
	}
	if !ok {
		return nil
	}

	return &table
}
