package point

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/stat"
)

type ManualSendPointLog struct {
	ID                int64 `xorm:"not null autoincr pk bigint(20) unsigned 'id'"`
	ManualSendPointID int64 `xorm:"int(11) unsigned notnull 'manual_send_point_id'" json:"manual_send_point_id"`
	UID               int64 `xorm:"int(11) unsigned notnull 'uid'" json:"uid"`
	// 操作类型：1-加，2-减
	OperationType int32 `xorm:"tinyint(1) unsigned notnull 'operation_type'" json:"operation_type"`
	// 发送类型：1-输入，2-群组
	Score      int32 `xorm:" smallint(5) unsigned notnull 'score'" json:"score"`
	Status     int32 `xorm:"tinyint(1) unsigned notnull 'status'" json:"status"`
	CreateTime int64 `xorm:"not null int(10) unsigned 'create_time'"` // 创建时间
	UpdateTime int64 `xorm:"not null int(10) unsigned 'update_time'"` // 更新时间
}

// TableName 获取表名
func (c ManualSendPointLog) TableName() string {
	if c.UID < 1 {
		return ""
	}
	return fmt.Sprintf("manual_send_point_log_%d", c.ManualSendPointID%32)
}

// Save 保存数据
func (c ManualSendPointLog) Insert() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime
	_, err := stat.GetEngineMaster().Insert(c)
	return err
}
