package member

import (
	"strconv"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/stat"
)

type UserMemberTrade struct {
	ID         int    `json:"id" xorm:"'id' not null pk autoincr comment('自增ID') INT(11)"`
	UID        int    `json:"uid" xorm:"'uid' default 0 comment('用户id') unique(UNIQUE_ORDER_UID_ID_TYPE) INT(11)"`
	OrderID    string `json:"order_id" xorm:"'order_id' default '' VARCHAR(64)"`
	TradeTime  int    `json:"trade_time" xorm:"'trade_time' not null default 0 comment('订单创建时间') INT(11)"`
	CreateTime int    `json:"create_time" xorm:"'create_time' not null INT(10)"`
	UpdateTime int    `json:"update_time" xorm:"'update_time' not null INT(10)"`
}

func (e UserMemberTrade) TableName() string {
	return "user_member_trade_" + strconv.Itoa(e.UID%32)
}

type _userMemberTrade struct{}

var TbUserMemberTrade _userMemberTrade

// GetItemsByUID 根据 UID 获取多条
func (s *_userMemberTrade) GetItemsByUID(uid int64) []*UserMemberTrade {
	resp := make([]*UserMemberTrade, 0)
	if err := stat.GetEngine().Table(UserMemberTrade{UID: int(uid)}.TableName()).
		Where("uid = ?", uid).Find(&resp); err != nil {
		logger.Errorf("获取用户会员业务交易记录失败 %s", err)
		return nil
	}
	return resp
}

// GetLastItemByUID 根据 UID 获取最后一条消费记录
func (s *_userMemberTrade) GetLastItemByUID(uid int64) *UserMemberTrade {
	resp := new(UserMemberTrade)
	ok, err := stat.GetEngine().Table(UserMemberTrade{UID: int(uid)}.TableName()).
		Where("uid = ?", uid).OrderBy("id desc").Get(resp)
	if err != nil {
		logger.Errorf("获取用户会员业务交易最后一条消费记录失败 %s", err.Error())
		return nil
	}
	if !ok {
		return nil
	}
	return resp
}
