package member

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/stat"
)

// UserMemberTradeTotal 用户交易汇总
type UserMemberTradeTotal struct {
	ID             int `json:"id" xorm:"id not null pk autoincr comment('自增ID') INT(11)"`
	UID            int `json:"uid" xorm:"'uid' default 0 comment('用户id') index INT(11)"`
	TradeTimes     int `json:"trade_times" xorm:"'trade_times' not null comment('交易次数 每个订单号一次') INT(4)"`
	CreateTime     int `json:"create_time" xorm:"not null INT(10)"`
	UpdateTime     int `json:"update_time" xorm:"not null INT(10)"`
	FirstTradeTime int `json:"first_trade_time" xorm:"'first_trade_time' not null comment('第一次交易时间') INT(10)"`
}

// TableName 获取表名
func (e UserMemberTradeTotal) TableName() string {
	return "user_member_trade_total"
}

type _userMemberTradeTotal struct{}

var TbUserMemberTradeTotal _userMemberTradeTotal

// GetByUID 通过 UID 获取一条
func (s *_userMemberTradeTotal) GetByUID(uid int64) *UserMemberTradeTotal {
	resp := new(UserMemberTradeTotal)
	engine := stat.GetEngine()
	ok, err := engine.Where("uid = ?", uid).Get(resp)
	if err != nil {
		logger.Errorf("通过 UID 获取一条用户交易汇总失败 %s", err.Error())
		return nil
	}
	if !ok {
		return nil
	}
	return resp
}
