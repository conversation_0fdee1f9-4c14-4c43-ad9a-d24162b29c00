package duiba

import (
	"errors"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/stat"
)

const StatDaily = "day"
const StatMonthly = "month"

type bannerClickStat struct{}

var TbBannerClickStat bannerClickStat

func (tb *bannerClickStat) Save(bean interface{}) error {
	switch bean := bean.(type) {
	case *BannerClickDaily:
	case *BannerClickMonthly:
		bean.CreateTime = time.Now().Unix()
		bean.UpdateTime = bean.CreateTime
		_, err := stat.GetEngineMaster().Insert(bean)
		return err
	default:
		return errors.New("unsupport stat type")
	}
	return nil
}

func (tb *bannerClickStat) IncrClickCnt(st string, id int64) error {
	now := time.Now().Unix()
	var bean interface{}
	if st == StatDaily {
		bean = &BannerClickDaily{ID: id, UpdateTime: now}
	} else if st == StatMonthly {
		bean = &BannerClickMonthly{ID: id, UpdateTime: now}
	}
	_, err := stat.GetEngineMaster().ID(id).Incr("click_cnt", 1).Update(bean)
	return err
}

func (tb *bannerClickStat) GetRowByAdsAndDevice(st, date string, ads int64, deviceType int32) (interface{}, error) {
	var table interface{}
	var ok bool
	var err error
	if st == StatDaily {
		table = &BannerClickDaily{}
		ok, err = stat.GetEngine().Where("ads_id = ? and date = ? and device = ?", ads, date, deviceType).Get(table)
	} else if st == StatMonthly {
		table = &BannerClickMonthly{}
		ok, err = stat.GetEngine().Where("ads_id = ? and month = ? and device = ?", ads, date, deviceType).Get(&table)
	} else {
		return nil, errors.New("unsupport type")
	}

	if !ok {
		return nil, nil
	}
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	return table, nil
}
