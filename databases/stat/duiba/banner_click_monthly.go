package duiba

// BannerClickMonthly
type BannerClickMonthly struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk bigint(20) unsigned 'id'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(10) unsigned 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(10) unsigned 'update_time'"`
	// 广告id
	AdsID int64 `xorm:"not null int(10) unsigned 'ads_id'"`
	// 月份，如：201604
	Month string `xorm:"not null char(6) 'month'"`
	// 点击数
	ClickCnt int32 `xorm:"not null int(10) unsigned 'click_cnt'"`
	// 设备 1 iOS 2 Android 3 other
	Device int32 `xorm:"not null tinyint(2) unsigned 'device'"`
}

// TableName 获取表名
func (BannerClickMonthly) TableName() string {
	return "re_duiba_banner_click_month"
}
