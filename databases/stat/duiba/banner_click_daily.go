package duiba

// BannerClickDaily
type BannerClickDaily struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk bigint(20) unsigned 'id'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(10) unsigned 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(10) unsigned 'update_time'"`
	// 广告id
	AdsID int64 `xorm:"not null int(11) unsigned 'ads_id'"`
	// 日期，如：20160420
	Date string `xorm:"not null char(8) 'date'"`
	// 点击数
	ClickCnt int32 `xorm:"not null int(11) unsigned 'click_cnt'"`
	// 设备 1 iOS 2 Android 3 other
	Device int32 `xorm:"not null tinyint(2) unsigned 'device'"`
}

// TableName 获取表名
func (BannerClickDaily) TableName() string {
	return "re_duiba_banner_click_day"
}
