package practice

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
)

// Log 用户练习记录
type Log struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid'"`
	// 程序ID
	ProgramID int64 `xorm:"not null int(11) 'program_id'"`
	// 课程ID
	CourseID int64 `xorm:"not null int(11) 'course_id'"`
	// 第几天
	OrderDay int `xorm:"not null tinyint(4) 'order_day'"`
	// 卡路里
	Calories int `xorm:"not null int(11) 'calories'"`
	// 是否中途退出：1-是，0-否
	IsExit int `xorm:"not null tinyint(1) 'is_exit'"`
	// 练习时长(s)
	PlayTime int `xorm:"not null int(11) 'play_time'"`
	// 练习感受
	PracticeFeel int32 `xorm:"not null tinyint(2) 'practice_feel'"`
	// 练习开始时间
	PracticeStartTime int64 `xorm:"not null int(11) 'practice_start_time'"`
	// 上报类型
	PracticeType int `xorm:"not null int(11) 'practice_type'"`
	// 练习场景 1 课程详情 2 OB课表 3 即刻开练
	SceneType int `xorm:"not null tinyint(1) 'scene_type'"`
	// 场景对应id
	SceneTypeID int `xorm:"not null int(11) 'scene_type_id'"`
	// 场景对应id
	SceneTypeIDIndex int64 `xorm:"not null int(11) 'scene_type_index'"`
	// 练习标签
	PracticeLabels string `xorm:"not null varchar(512) 'practice_labels'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (Log) TableName() string {
	return "user_practice_log"
}

// Save 保存数据
func (p *Log) Save() error {
	p.CreateTime = time.Now().Unix()
	p.UpdateTime = p.CreateTime

	_, err := databases.GetFitness().GetEngineMaster().Insert(p)
	return err
}

// Update 更新数据
func (p *Log) Update() error {
	p.UpdateTime = time.Now().Unix()

	_, err := databases.GetFitness().GetEngineMaster().ID(p.ID).Update(p)
	return err
}

type practiceLog struct{}

// TbPracticeLog 外部引用对象
var TbPracticeLog practiceLog

type SearchQuery struct {
	UID               int64
	PracticeStartTime int64
	ProgramID         int64
	CourseID          int64
	IsInternal        int
}

func (p *practiceLog) GetItemByCond(r *SearchQuery) *Log {
	var table Log
	session := databases.GetFitness().GetEngine().NewSession()
	defer session.Close()
	session = session.Table(table.TableName()).Where("uid = ?", r.UID)
	if r.PracticeStartTime > 0 {
		session = session.And("practice_start_time = ?", r.PracticeStartTime)
	}
	if r.ProgramID > 0 {
		session = session.And("program_id = ?", r.ProgramID)
	}
	if r.CourseID > 0 {
		session = session.And("course_id = ?", r.CourseID)
	}
	ok, err := session.Get(&table)
	if err != nil {
		logger.Error(err)
	}
	if !ok {
		return nil
	}
	return &table
}
