package user

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	df "gitlab.dailyyoga.com.cn/server/srv-task/databases"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// Account 用户账号表
type Account struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk bigint(20) 'id'"`
	// 用户昵称
	Nickname string `xorm:"not null varchar(255) 'nickname'"`
	// 注册方式
	LoginType int `xorm:"not null tinyint(4) 'login_type'"`
	// 性别
	Gender int `xorm:"not null tinyint(1) 'gender'"`
	// 密码
	Password string `xorm:"not null varchar(64) 'password'"`
	// 手机号
	Mobile string `xorm:"not null varchar(24) 'mobile'"`
	// 用户头像
	Avatar string `xorm:"not null varchar(255) 'avatar'"`
	// 会员开始时间
	StartTime int64 `xorm:"not null int(11) 'start_time'"`
	// 会员结束时间
	EndTime int64 `xorm:"not null int(11) 'end_time'"`
	// 语音开始时间
	VoiceStartTime int64 `xorm:"not null int(11) 'voice_start_time'"`
	// 语音结束时间
	VoiceEndTime int64 `xorm:"not null int(11) 'voice_end_time'"`
	// 总卡路里
	Calories int `xorm:"not null int(11) 'calories'"`
	// 总练习时长
	Minutes int `xorm:"not null int(11) 'minutes'"`
	// 总练习天数
	PracticeCount int `xorm:"not null int(11) 'practice_count'"`
	// 总练习天数
	PracticeDays int `xorm:"not null int(11) 'practice_days'"`
	// 课程收藏数
	CollectCourseCount int `xorm:"not null int(11) 'collect_course_count'"`
	// 是否个性化推荐
	IsRecommend int `xorm:"not null tinyint(11) 'is_recommend'"`
	// 是否注销 1 是 2 否
	IsLogoff int `xorm:"not null tinyint(1) 'is_logoff'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (Account) TableName() string {
	return "account"
}

// Save 保存数据
func (a *Account) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	a.IsLogoff = library.No
	_, err := df.GetFitness().GetEngineMaster().Insert(a)
	return err
}

// Save 保存数据
func (a *Account) SaveByTran(session *xorm.Session) error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	a.IsLogoff = library.No
	_, err := session.Insert(a)
	return err
}

// Update 更新数据
func (a *Account) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := df.GetFitness().GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *Account) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

// UpdateByTranMustCols 事务更新数据 会员开始结束时间可能为0
func (a *Account) UpdateByTranMustCols(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).MustCols("start_time", "end_time").Update(a)
	return err
}

type account struct{}

// TbAccount 外部引用对象
var TbAccount account

// GetUserByID 通过UID获取用户详情
func (a *account) GetUserByID(uid int64) *Account {
	var table Account
	ok, err := df.GetFitness().GetEngine().Where("id = ? and is_logoff != ?", uid, library.Yes).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
