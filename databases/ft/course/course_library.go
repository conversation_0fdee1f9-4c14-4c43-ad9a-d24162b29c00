package course

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
)

// DBCourse 课程库
type DBCourse struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 课程标识id
	UniqueID string `xorm:"not null varchar(255) 'unique_id'"`
	// 即刻开练唯一标识id
	TrainNowID string `xorm:"not null varchar(255) 'train_now_id'"`
	// 课程标题
	Title string `xorm:"not null varchar(255) 'title'"`
	// 课程描述
	Desc string `xorm:"not null varchar(255) 'desc'"`
	// 练习注意要点
	Attentions string `xorm:"not null varchar(2048) 'attentions'"`
	// 难度等级
	Level int32 `xorm:"not null tinyint(4) 'level'"`
	// 课程视频地址
	VideoURL string `xorm:"not null varchar(255) 'video_url'"`
	// 课程时长
	Duration float64 `xorm:"not null decimal(10,3) 'duration'"`
	// 是否需要器械
	IsNeedApparatus int32 `xorm:"not null tinyint(1) 'is_need_apparatus'"`
	// 课程封面
	CoverURL string `xorm:"not null varchar(255) 'cover_url'"`
	// 课程横版封面
	HorizontalCoverURL string `xorm:"not null varchar(255) 'horizontal_cover_url'"`
	// 是否VIP
	IsVIP int32 `xorm:"not null tinyint(1) 'is_vip'"`
	// 是否在线
	IsOnline int32 `xorm:"not null tinyint(1) 'is_online'"`
	// 课程种类 1 成品课 2 拼接课
	CourseKind int32 `xorm:"not null tinyint(1) 'course_kind'"`
	// 是否需要热身
	NeedWarmup int32 `xorm:"not null tinyint(1) 'need_warmup'"`
	// 是否需要放松拉伸
	NeedRelax int32 `xorm:"not null tinyint(1) 'need_relax'"`
	// 版本号
	VersionID int64 `xorm:"not null int(11) 'version_id'"`
	// 课程拼接是否生成完成
	IsGenerateDone int32 `xorm:"not null tinyint(1) 'is_generate_done'"`
	// 收藏统计
	CollectCount int `xorm:"not null int(11) 'collect_count'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (DBCourse) TableName() string {
	return "course_library"
}

// Save 保存数据
func (c *DBCourse) Save() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime

	_, err := databases.GetFitness().GetEngineMaster().Insert(c)
	return err
}

// Update 更新数据
func (c *DBCourse) Update() error {
	c.UpdateTime = time.Now().Unix()

	_, err := databases.GetFitness().GetEngineMaster().ID(c.ID).Update(c)
	return err
}

type clibrary struct {
}

var TbCourseLibrary clibrary

// GetItem 获取课程详情
func (s *clibrary) GetItem(courseID int64) *DBCourse {
	var table DBCourse

	ok, err := databases.GetFitness().GetEngine().Where("id = ?", courseID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
