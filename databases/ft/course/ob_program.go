package course

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// ObProgram 用户ob计划信息
type ObProgram struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null bigint(11) 'uid'"`
	// 天数
	Days int64 `xorm:"not null int(11) 'days'"`
	// 计划课程数量
	CourseCount int32 `xorm:"not null int(11) 'course_count'"`
	// 完成课程数量
	FinishCount int32 `xorm:"not null int(11) 'finish_count'"`
	// 开始时间
	BeginTime int64 `xorm:"not null int(11) 'begin_time'"`
	// 测评选项
	ChoiceData string `xorm:"not null text 'choice_data'"`
	// 是否有效
	IsValid int32 `xorm:"not null tinyint(1) 'is_valid'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (ObProgram) TableName() string {
	return "ob_program"
}

// Save 保存数据
func (o *ObProgram) Save() error {
	o.CreateTime = time.Now().Unix()
	o.UpdateTime = o.CreateTime

	_, err := databases.GetFitness().GetEngineMaster().Insert(o)
	return err
}

// Update 更新数据
func (o *ObProgram) Update() error {
	o.UpdateTime = time.Now().Unix()

	_, err := databases.GetFitness().GetEngineMaster().ID(o.ID).Update(o)
	return err
}

type obProgram struct{}

// TbObProgram 外部引用对象
var TbObProgram obProgram

// GetInProcessProgram 获取用户正在进行的ob计划
func (o *obProgram) GetInProcessProgram(uid int64) *ObProgram {
	var table ObProgram

	ok, err := databases.GetFitness().GetEngine().Where("uid = ?",
		uid).And("is_valid = ?", library.Yes).Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItem 获取用户ob计划详情
func (o *obProgram) GetItem(uid, programID int64) *ObProgram {
	var table ObProgram

	ok, err := databases.GetFitness().GetEngine().Where("id = ?", programID).And("uid = ?", uid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetList 获取用户所有ob计划列表
func (o *obProgram) GetList(uid int64) []ObProgram {
	var tables []ObProgram

	err := databases.GetFitness().GetEngine().Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
