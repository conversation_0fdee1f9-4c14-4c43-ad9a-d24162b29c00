package practice

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
)

// Log 用户练习记录
type Log struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid'"`
	// 课程ID
	CourseID int64 `xorm:"not null int(11) 'course_id'"`
	// 动作数
	ActionNum int `xorm:"not null int(11) 'action_num'"`
	// 练习时长(s)
	PlayTime int `xorm:"not null int(11) 'play_time'"`
	// 练习感受
	PracticeFeel int32 `xorm:"not null tinyint(2) 'practice_feel'"`
	// 练习开始时间
	PracticeStartTime int64 `xorm:"not null int(11) 'practice_start_time'"`
	// 上报类型
	PracticeType int `xorm:"not null int(11) 'practice_type'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (Log) TableName() string {
	return "user_practice_log"
}

// Save 保存数据
func (p *Log) Save() error {
	p.CreateTime = time.Now().Unix()
	p.UpdateTime = p.CreateTime

	_, err := databases.GetStretch().GetEngineMaster().Insert(p)
	return err
}

// Update 更新数据
func (p *Log) Update() error {
	p.UpdateTime = time.Now().Unix()

	_, err := databases.GetStretch().GetEngineMaster().ID(p.ID).Update(p)
	return err
}

type practiceLog struct{}

// TbPracticeLog 外部引用对象
var TbPracticeLog practiceLog

type SearchQuery struct {
	UID               int64
	PracticeStartTime int64
	ProgramID         int64
	CourseID          int64
	IsInternal        int
}

func (p *practiceLog) GetItemByCond(r *SearchQuery) *Log {
	var table Log
	session := databases.GetStretch().GetEngine().NewSession()
	defer session.Close()
	session = session.Table(table.TableName()).Where("uid = ?", r.UID)
	if r.PracticeStartTime > 0 {
		session = session.And("practice_start_time = ?", r.PracticeStartTime)
	}
	if r.ProgramID > 0 {
		session = session.And("program_id = ?", r.ProgramID)
	}
	if r.CourseID > 0 {
		session = session.And("course_id = ?", r.CourseID)
	}
	ok, err := session.Get(&table)
	if err != nil {
		logger.Error(err)
	}
	if !ok {
		return nil
	}
	return &table
}
