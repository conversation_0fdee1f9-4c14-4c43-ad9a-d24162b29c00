package playtime

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// Collect 用户练习时长汇总统计
type Collect struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) index(IDX_UID) 'uid' comment('用户ID')"`
	// 练习时长
	PlayTime int64 `xorm:"not null bigint(20) 'play_time' comment('实际练习时长')"`
	// 练习时长（60s向下取整）
	DisplayPlayTime int64 `xorm:"not null bigint(20) 'display_play_time' comment('显示练习时长: 60s向下取整')"`
	// 卡路里
	Calorie int32 `xorm:"not null int(11) 'calorie' comment('卡路里')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (c Collect) TableName() string {
	if c.UID < 1 {
		return ""
	}
	return fmt.Sprintf("st_play_time_collect_%d", c.UID%32)
}

// Save 保存数据
func (c *Collect) Save() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime

	_, err := statrds.GetEngineMaster().Insert(c)
	return err
}

// Update 更新数据
func (c *Collect) Update() error {
	c.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

func (c *Collect) UpdateByCond(uid int64) error {
	table := TbCollect.GetItem(uid)
	if table != nil && table.ID > 0 {
		table.PlayTime = c.PlayTime
		table.DisplayPlayTime = c.DisplayPlayTime
		table.Calorie = c.Calorie
		table.UpdateTime = time.Now().Unix()
		return table.Update()
	}
	return nil
}

func (c *Collect) UpdateMustColsSetCleanCollect() error {
	c.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(c.ID).MustCols("play_time", "display_play_time").Update(c)
	return err
}

func (c *Collect) UpdateMustColsCalorie() error {
	c.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(c.ID).MustCols("calorie").Update(c)
	return err
}

type collect struct{}

// TbCollect 外部引用对象
var TbCollect collect

func (c *collect) GetItem(uid int64) *Collect {
	var table Collect

	table.UID = uid
	ok, err := statrds.GetEngine().Where("uid = ?", uid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
