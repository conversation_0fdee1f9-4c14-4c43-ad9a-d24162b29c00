package playtime

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// Day 用户练习时长按天统计
type Day struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) index(IDX_UID) 'uid' comment('用户ID')"`
	// 练习时长
	PlayTime int64 `xorm:"not null int(11) 'play_time' comment('实际练习时长')"`
	// 练习时长（60s向下取整）
	DisplayPlayTime int64 `xorm:"not null int(11) 'display_play_time' comment('显示练习时长: 60s向下取整')"`
	// 卡路里
	Calorie int32 `xorm:"not null int(11) 'calorie' comment('卡路里')"`
	// 日期：20190423
	DateIndex string `xorm:"not null char(8) index(IDX_DATE_INDEX) 'date_index' comment('日期: 20060102')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (d Day) TableName() string {
	if d.UID < 1 {
		return ""
	}
	return fmt.Sprintf("st_play_time_day_%d", d.UID%32)
}

// Save 保存数据
func (d *Day) Save() error {
	d.CreateTime = time.Now().Unix()
	d.UpdateTime = d.CreateTime

	_, err := statrds.GetEngineMaster().Insert(d)
	return err
}

// Update 更新数据
func (d *Day) Update() error {
	d.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(d.ID).Update(d)
	return err
}

func (d *Day) UpdateByCond(uid int64, dateIndex string) error {
	table := TbDay.GetItem(uid, dateIndex)
	if table != nil && table.ID > 0 {
		table.PlayTime = d.PlayTime
		table.DisplayPlayTime = d.DisplayPlayTime
		table.Calorie = d.Calorie
		table.UpdateTime = time.Now().Unix()
		return table.Update()
	}
	return nil
}

func (d *Day) UpdateMustColsSetCleanDay() error {
	d.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(d.ID).MustCols("play_time", "display_play_time").Update(d)
	return err
}

func (d *Day) UpdateMustColsCalorie() error {
	d.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(d.ID).MustCols("calorie").Update(d)
	return err
}

type day struct{}

// TbDay 外部引用对象
var TbDay day

func (d *day) GetItem(uid int64, dateIndex string) *Day {
	var table Day

	table.UID = uid
	ok, err := statrds.GetEngine().Where("uid = ? and date_index = ?", uid, dateIndex).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (d *day) GetList(uid int64) []Day {
	var tables []Day
	var table Day

	table.UID = uid
	err := statrds.GetEngine().Table(table.TableName()).Where("uid = ?", uid).Asc("date_index").Find(&tables)
	if err != nil {
		return nil
	}

	return tables
}

func (d *day) GetListByTimeRange(uid int64, startDate, endDate string) []Day {
	var tables []Day
	err := statrds.GetEngine().Table(Day{UID: uid}.TableName()).
		Where("uid = ? AND date_index >= ? AND date_index <= ?", uid, startDate, endDate).
		Asc("date_index").
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
