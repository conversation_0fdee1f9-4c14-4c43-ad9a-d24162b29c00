package playtime

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// Year 用户练习时长按年统计
type Year struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid' index(IDX_UID) comment('用户ID')"`
	// 练习时长
	PlayTime int64 `xorm:"not null bigint(20) 'play_time' comment('实际练习时长')"`
	// 练习时长（60s向下取整）
	DisplayPlayTime int64 `xorm:"not null bigint(20) 'display_play_time' comment('显示练习时长：60s向下取整')"`
	// 日期：2019
	DateIndex string `xorm:"not null char(4) 'date_index' index(IDX_YEAR) comment('日期：2016')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (y Year) TableName() string {
	if y.DateIndex == "" {
		return ""
	}
	return fmt.Sprintf("st_play_time_year_%s", y.DateIndex)
}

// Save 事务保存数据
func (y *Year) Save() error {
	y.CreateTime = time.Now().Unix()
	y.UpdateTime = y.CreateTime

	_, err := statrds.GetEngineMaster().Insert(y)
	return err
}

// Update 更新数据
func (y *Year) Update() error {
	y.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(y.ID).Update(y)
	return err
}

func (y *Year) UpdateByCond(uid int64, year string) error {
	table := TbYear.GetItem(uid, year)
	if table != nil && table.ID > 0 {
		table.PlayTime = y.PlayTime
		table.DisplayPlayTime = y.DisplayPlayTime
		table.UpdateTime = time.Now().Unix()
		return table.Update()
	}
	return nil
}

func (y *Year) UpdateMustColsSetCleanYear() error {
	y.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(y.ID).MustCols("play_time", "display_play_time").Update(y)
	return err
}

type year struct{}

// TbYear 外部引用对象
var TbYear year

func (y *year) GetItem(uid int64, dateYear string) *Year {
	var table Year

	table.DateIndex = dateYear
	ok, err := statrds.GetEngine().Where("uid = ? and date_index = ?", uid, dateYear).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
