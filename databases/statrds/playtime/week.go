package playtime

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
)

// Week 用户练习时长按周统计
type Week struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid' index(IDX_UID) comment('用户ID')"`
	// 练习时长
	PlayTime int64 `xorm:"not null int(11) 'play_time' comment('实际练习时长')"`
	// 练习时长（60s向下取整）
	DisplayPlayTime int64 `xorm:"not null int(11) 'display_play_time' comment('显示练习时长：60s向下取整')"`
	// 日期：201904
	DateIndex string `xorm:"not null char(6) index(IDX_DATE_INDEX) 'date_index' comment('日期: 200601')"`
	// 周
	Week string `xorm:"not null char(4) 'week' index(IDX_WEEK) comment('周')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (w *Week) TableName() string {
	if w.Week == "" {
		return ""
	}
	return fmt.Sprintf("st_play_time_week_%s", w.Week)
}

// Save 保存数据
func (w *Week) Save() error {
	w.CreateTime = time.Now().Unix()
	w.UpdateTime = w.CreateTime

	_, err := statrds.GetEngineMaster().Insert(w)
	return err
}

// Update 更新数据
func (w *Week) Update() error {
	w.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(w.ID).Update(w)
	return err
}

func (w *Week) UpdateByCond(uid int64, week string) error {
	table := TbWeek.GetItem(uid, week)
	if table != nil && table.ID > 0 {
		table.PlayTime = w.PlayTime
		table.DisplayPlayTime = w.DisplayPlayTime
		table.UpdateTime = time.Now().Unix()
		return table.Update()
	}
	return nil
}

func (w *Week) UpdateMustColsSetCleanWeek() error {
	w.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(w.ID).MustCols("play_time", "display_play_time").Update(w)
	return err
}

type week struct{}

// TbWeek 外部引用对象
var TbWeek week

func (w *week) GetItem(uid int64, week string) *Week {
	var table Week

	table.Week = week
	ok, err := statrds.GetEngine().Where("uid = ? and week = ?", uid, week).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetListGTValueInFriends 获取在朋友范围内大于指定练习时长的数据列表
func (w *week) GetListGTValueInFriends(week string, uid, value int64) []Week {
	var tables []Week
	var table Week

	followUIDs := user.TbFollow.GetFollowUIDs(uid, false)
	table.Week = week
	err := statrds.GetEngine().
		Table(table.TableName()).
		Where("week = ? AND play_time > ?", week, value).
		In("uid", followUIDs).
		Find(&tables)
	if err != nil {
		logger.Error(err)
	}
	return tables
}

// GetListEQValueAndLtUid 获取排名靠前的重分用户数量
func (w *week) GetListEQValueAndLtUID(week string, uid, value int64) []Week {
	var tables []Week
	var table Week

	followUIDs := user.TbFollow.GetFollowUIDs(uid, false)
	table.Week = week
	err := statrds.GetEngine().
		Table(table.TableName()).
		Where("week = ? AND play_time = ? AND uid < ?", week, value, uid).
		In("uid", followUIDs).
		Find(&tables)
	if err != nil {
		logger.Error(err)
	}
	return tables
}

// GetMaxPlayTimeFriends 从朋友中获取最大练习时长
func (w *week) GetMaxPlayTimeFriends(uid int64, week string) int64 {
	var table Week

	followUIDs := user.TbFollow.GetFollowUIDs(uid, true)
	table.Week = week
	ok, err := statrds.GetEngine().
		Where("week = ?", week).
		In("uid", followUIDs).
		Desc("play_time").
		Limit(1).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return 0
	}
	if !ok {
		return 0
	}
	return table.DisplayPlayTime
}

// GetPlayTimeWeekFriends 获取统计数据列表
func (w *week) GetPlayTimeWeekFriends(uid int64, week string, limit, offset int) []map[string]interface{} {
	var tables []Week
	var table Week

	followUIDs := user.TbFollow.GetFollowUIDs(uid, true)
	table.Week = week
	err := statrds.GetEngine().
		Table(table.TableName()).
		Where("week = ?", week).
		In("uid", followUIDs).
		Desc("play_time").
		Limit(limit, offset).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}

	result := make([]map[string]interface{}, 0)
	if len(tables) > 0 {
		for _, v := range tables {
			m := make(map[string]interface{})
			m["uid"] = v.UID
			m["date_index"] = v.DateIndex
			m["count_value"] = v.DisplayPlayTime
			item := practice.TbMonth.GetItem(v.UID, v.DateIndex)
			if item != nil {
				m["practice_days"] = item.PracticeDays
			} else {
				m["practice_days"] = 0
			}
			result = append(result, m)
		}
	}
	return result
}
