package playtime

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
)

// Month 用户练习时长按月统计
type Month struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid' index(IDX_UID) comment('用户ID')"`
	// 练习时长
	PlayTime int64 `xorm:"not null bigint(20) 'play_time' comment('实际练习时长')"`
	// 练习时长（60s向下取整）
	DisplayPlayTime int64 `xorm:"not null bigint(20) 'display_play_time' comment('显示练习时长：60s向下取整')"`
	// 日期：201904
	DateIndex string `xorm:"not null char(6) index(IDX_DATE_INDEX) 'date_index' comment('日期: 200601')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (m Month) TableName() string {
	if m.DateIndex == "" {
		return ""
	}
	return fmt.Sprintf("st_play_time_month_%s", m.DateIndex)
}

// Save 保存数据
func (m *Month) Save() error {
	m.CreateTime = time.Now().Unix()
	m.UpdateTime = m.CreateTime

	_, err := statrds.GetEngineMaster().Insert(m)
	return err
}

// Update 更新数据
func (m *Month) Update() error {
	m.UpdateTime = time.Now().Unix()
	_, err := statrds.GetEngineMaster().ID(m.ID).Update(m)
	return err
}

func (m *Month) UpdateByCond(uid int64, month string) error {
	table := TbMonth.GetItem(uid, month)
	if table != nil && table.ID > 0 {
		table.PlayTime = m.PlayTime
		table.DisplayPlayTime = m.DisplayPlayTime
		table.UpdateTime = time.Now().Unix()
		return table.Update()
	}
	return nil
}

func (m *Month) UpdateMustColsSetCleanMonth() error {
	m.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(m.ID).MustCols("play_time", "display_play_time").Update(m)
	return err
}

type month struct{}

// TbMonth 外部引用对象
var TbMonth month

func (m *month) GetItem(uid int64, dateMonth string) *Month {
	var table Month

	ok, err := statrds.GetEngine().Table(&Month{DateIndex: dateMonth}).Where("uid = ?", uid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetListGTValue 获取大于指定练习时长的数据列表
func (m *month) GetListGTValue(dateMonth string, value int64) []Month {
	var tables []Month

	err := statrds.GetEngine().
		Table(&Month{DateIndex: dateMonth}).
		Where("play_time > ?", value).
		Find(&tables)
	if err != nil {
		logger.Error(err)
	}
	return tables
}

// GetListGTValueInFriends 获取在朋友范围内大于指定练习时长的数据列表
func (m *month) GetListGTValueInFriends(dateMonth string, uid, value int64) []Month {
	var tables []Month

	followUIDs := user.TbFollow.GetFollowUIDs(uid, false)
	err := statrds.GetEngine().
		Table(&Month{DateIndex: dateMonth}).
		Where("play_time > ?", value).
		In("uid", followUIDs).
		Find(&tables)
	if err != nil {
		logger.Error(err)
	}
	return tables
}

// GetListEQValueAndLtUid 获取排名靠前的重分用户数量
func (m *month) GetListEQValueAndLtUID(dateMonth string, uid, value int64) []Month {
	var tables []Month

	followUIDs := user.TbFollow.GetFollowUIDs(uid, false)
	err := statrds.GetEngine().
		Table(&Month{DateIndex: dateMonth}).
		Where("play_time = ? AND uid < ?", value, uid).
		In("uid", followUIDs).
		Find(&tables)
	if err != nil {
		logger.Error(err)
	}
	return tables
}

// GetMaxPlayTimeAll 从所有人中获取最大练习时长
func (m *month) GetMaxPlayTimeAll(dateMonth string) int64 {
	var table Month

	// Prince 修改表名获取方式，删除Where 条件 2020-03-17 08:57:43
	ok, err := statrds.GetEngine().Table(&Month{DateIndex: dateMonth}).Desc("play_time").Limit(1).Get(&table)
	if err != nil {
		logger.Error(err)
		return 0
	}
	if !ok {
		return 0
	}
	return table.DisplayPlayTime
}

// GetMaxPlayTimeFriends 从朋友中获取最大练习时长
func (m *month) GetMaxPlayTimeFriends(uid int64, dateMonth string) int64 {
	var table Month

	followUIDs := user.TbFollow.GetFollowUIDs(uid, true)
	ok, err := statrds.GetEngine().Table(&Month{DateIndex: dateMonth}).
		In("uid", followUIDs).
		Desc("play_time").
		Limit(1).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return 0
	}
	if !ok {
		return 0
	}
	return table.DisplayPlayTime
}

// GetPlayTimeMonth 获取统计数据列表
func (m *month) GetPlayTimeMonthAll(dateMonth string, limit, offset int) []map[string]interface{} {
	var tables []Month

	err := statrds.GetEngine().
		Table(&Month{DateIndex: dateMonth}).
		Desc("play_time").
		Limit(limit, offset).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}

	result := make([]map[string]interface{}, 0)
	if len(tables) > 0 {
		for _, v := range tables {
			m := make(map[string]interface{})
			m["uid"] = v.UID
			m["date_index"] = v.DateIndex
			m["count_value"] = v.DisplayPlayTime
			item := practice.TbMonth.GetItem(v.UID, v.DateIndex)
			if item != nil {
				m["practice_days"] = item.PracticeDays
			} else {
				m["practice_days"] = 0
			}
			result = append(result, m)
		}
	}
	return result
}

// GetPlayTimeMonth 获取统计数据列表
func (m *month) GetPlayTimeMonthFriends(uid int64, dateMonth string, limit, offset int) []map[string]interface{} {
	var tables []Month

	followUIDs := user.TbFollow.GetFollowUIDs(uid, true)
	err := statrds.GetEngine().
		Table(&Month{DateIndex: dateMonth}).
		In("uid", followUIDs).
		Desc("play_time").
		Limit(limit, offset).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}

	result := make([]map[string]interface{}, 0)
	if len(tables) > 0 {
		for _, v := range tables {
			m := make(map[string]interface{})
			m["uid"] = v.UID
			m["date_index"] = v.DateIndex
			m["count_value"] = v.DisplayPlayTime
			item := practice.TbMonth.GetItem(v.UID, v.DateIndex)
			if item != nil {
				m["practice_days"] = item.PracticeDays
			} else {
				m["practice_days"] = 0
			}
			result = append(result, m)
		}
	}
	return result
}

// GetPlayTimeMonthRank
// 获取月度练习时长排名前50 这个一定会造成慢查询
// 但是除了数据失效或者服务重启 不会频繁执行
func (m *month) GetPlayTimeMonthRank(dateMonth string) []Month {
	var table []Month
	err := statrds.GetEngine().
		Cols("uid,play_time").
		Table(&Month{DateIndex: dateMonth}).
		Desc("play_time").
		Limit(50).Find(&table)
	if err != nil {
		logger.Error(err)
	}
	return table
}
