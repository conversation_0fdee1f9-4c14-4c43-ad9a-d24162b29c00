package sessionplay

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// Expire 过期课程播放数据
type Expire struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 上报类型
	SourceDevice int32 `xorm:"not null int(11) 'source_device' comment('上报类型：1-app;2-投屏')"`
	// 用户ID
	UID int64 `xorm:"not null int(11) index(IDX_COMBINE) 'uid' comment('用户ID')"`
	// 计划ID
	ProgramID int64 `xorm:"not null int(11) index(IDX_COMBINE) 'program_id' comment('计划ID')"`
	// 课程ID
	SessionID int64 `xorm:"not null int(11) index(IDX_COMBINE) 'session_id' comment('课程ID')"`
	// 动作ID
	ActionID string `xorm:"not null varchar(16) 'action_id' comment('动作ID')"`
	// 播放时长
	PlayTime int64 `xorm:"not null int(11) 'play_time' comment('播放时长')"`
	// 卡路里
	Calorie int32 `xorm:"not null int(11) 'calorie' comment('卡路里')"`
	// 是否完成：1-完成，2-未完成
	DoneStatus int32 `xorm:"not null tinyint(1) 'done_status' comment('是否完成: 1-完成，2-未完成')"`
	// 上报时间
	ReportTime int64 `xorm:"not null int(11) index(IDX_COMBINE) 'report_time' comment('上报时间')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (e *Expire) TableName() string {
	return "st_session_play_expire"
}

// Save 保存数据
func (e *Expire) Save() error {
	e.CreateTime = time.Now().Unix()
	e.UpdateTime = e.CreateTime

	_, err := statrds.GetEngineMaster().Insert(e)
	return err
}
