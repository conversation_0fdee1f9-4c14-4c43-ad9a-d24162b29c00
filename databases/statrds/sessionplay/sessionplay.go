package sessionplay

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

var ResourceTypeEnum = struct {
	Program int
	Session int
}{
	Program: 1,
	Session: 2,
}

// Detail 课程播放明细
type Detail struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 上报类型
	SourceDevice int32 `xorm:"not null tinyint(1) 'source_device' comment('上报类型：1-app;2-投屏')"`
	// 用户ID
	UID int64 `xorm:"not null int(11) index(IDX_COMBINE) 'uid' comment('用户ID')"`
	// 计划ID
	ProgramID int64 `xorm:"not null int(11) index(IDX_COMBINE) 'program_id' comment('计划ID')"`
	// 课程ID
	SessionID int64 `xorm:"not null int(11) index(IDX_COMBINE) 'session_id' comment('课程ID')"`
	// 动作ID
	ActionID string `xorm:"not null varchar(16) 'action_id' comment('动作ID')"`
	// 播放时长
	PlayTime int64 `xorm:"not null int(11) 'play_time' comment('播放时长')"`
	// 卡路里
	Calorie int32 `xorm:"not null int(11) 'calorie' comment('卡路里')"`
	// 是否完成：1-完成，2-未完成
	DoneStatus int32 `xorm:"not null tinyint(1) 'done_status' comment('是否完成: 1-完成，2-未完成')"`
	// 上报时间
	ReportTime int64 `xorm:"not null int(11) index(IDX_COMBINE) 'report_time' comment('上报时间')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (s *Detail) TableName() string {
	if s.ReportTime < 1 {
		return ""
	}
	day := time.Unix(s.ReportTime, 0).Format("20060102")
	return fmt.Sprintf("st_session_play_%s", day)
}

// Save 保存数据
func (s *Detail) Save() error {
	if s.CreateTime < 1 {
		s.CreateTime = time.Now().Unix()
	}
	if s.ReportTime < 1 {
		s.ReportTime = s.CreateTime
	}
	s.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().Insert(s)
	return err
}

func (s *Detail) Update() error {
	s.UpdateTime = time.Now().Unix()
	_, err := statrds.GetEngineMaster().ID(s.ID).Update(s)
	return err
}

type sessionPlay struct{}

// TbSessionPlay 外部引用对象
var TbSessionPlay sessionPlay

func (s *sessionPlay) GetItem(uid, programID, sessionID, reportTime int64) *Detail {
	var table Detail

	table.ReportTime = reportTime
	ok, err := statrds.GetEngine().Where("uid = ? and program_id = ? and session_id = ? and report_time = ?",
		uid, programID, sessionID, reportTime).Get(&table)
	if err != nil {
		logger.Error(err)
	}
	if !ok {
		return nil
	}
	return &table
}

func (s *sessionPlay) GetList(uid, programID, sessionID, reportTime int64) []Detail {
	var tables []Detail
	var table Detail

	table.ReportTime = reportTime
	err := statrds.GetEngine().
		Table(table.TableName()).
		Where("uid = ? and program_id = ? and session_id = ? and report_time = ?", uid, programID, sessionID, reportTime).
		Find(&tables)
	if err != nil {
		logger.Error(err)
	}
	return tables
}

func (s *sessionPlay) GetSumByTimeAndSource(uid, startTime int64, sourceType int, sourceValue int64) int64 {
	var tables []Detail
	var table Detail
	var totalTime int64

	table.ReportTime = startTime
	session := statrds.GetEngine().NewSession()
	defer session.Close()
	session = session.Table(table.TableName()).Where("uid = ?", uid)
	if sourceType == ResourceTypeEnum.Program {
		session = session.Where("program_id = ?", sourceValue)
	}
	if sourceType == ResourceTypeEnum.Session {
		session = session.Where("session_id = ?", sourceValue)
	}
	if err := session.Find(&tables); err != nil {
		logger.Error(err)
	}
	if len(tables) > 0 {
		for _, v := range tables {
			totalTime += v.PlayTime
		}
	}

	return totalTime
}
