package sessionplay

import (
	"fmt"
	"time"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

const StatDay = "day"
const StatMonth = "month"
const StatYear = "year"

// Day 课程播放按天统计
type Stat struct {
	StType string `xorm:"-"`
	ID     int64  `xorm:"not null autoincr pk int(11) 'id'"`
	// 课程ID
	SessionID int64 `xorm:"not null int(11) index(IDX_SESSION_ID) 'session_id' comment('课程ID')"`
	// 播放时长
	PlayTime int64 `xorm:"not null int(11) 'play_time' comment('播放时长')"`
	// 日期：20190429
	DateIndex string `xorm:"not null char(8) index(IDX_DATE_INDEX) 'date_index' comment('日期：20060102')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (s *Stat) TableName() string {
	if s.StType == "" {
		return ""
	}
	return fmt.Sprintf("st_session_play_%s", s.StType)
}

// Save 保存数据
func (s *Stat) Save() error {
	s.CreateTime = time.Now().Unix()
	s.UpdateTime = s.CreateTime
	if s.StType == "" {
		return errors.New("st_type unfefined, table not found")
	}

	_, err := statrds.GetEngineMaster().Insert(s)
	return err
}

// Update 更新数据
func (s *Stat) Update() error {
	s.UpdateTime = time.Now().Unix()
	if s.StType == "" {
		return errors.New("st_type unfefined, table not found")
	}

	_, err := statrds.GetEngineMaster().ID(s.ID).Update(s)
	return err
}

type stat struct{}

// TbStat 外部引用对象
var TbStat stat

func (d *stat) GetItem(stType string, sessionID int64, dateIndex string) *Stat {
	table := Stat{StType: stType}

	ok, err := statrds.GetEngine().Where("session_id = ? and date_index = ?",
		sessionID, dateIndex).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
