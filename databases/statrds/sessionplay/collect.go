package sessionplay

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// Collect 课程播放汇总统计
type Collect struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 课程ID
	SessionID int64 `xorm:"not null int(11) 'session_id' index(IDX_SESSION_ID) comment('课程ID')"`
	// 播放时长
	PlayTime int64 `xorm:"not null bigint(20) 'play_time' comment('播放时长')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (Collect) TableName() string {
	return "st_session_play_collect"
}

// Save 保存数据
func (c *Collect) Save() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime

	_, err := statrds.GetEngineMaster().Insert(c)
	return err
}

// Update 更新数据
func (c *Collect) Update() error {
	c.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

type collect struct{}

// TbCollect 外部引用对象
var TbCollect collect

func (c *collect) GetItem(sessionID int64) *Collect {
	var table Collect

	ok, err := statrds.GetEngine().Where("session_id = ?", sessionID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
