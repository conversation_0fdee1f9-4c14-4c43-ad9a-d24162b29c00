package user

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

type GoalPlayTime struct {
	ID              int64 `xorm:"not null int(10) unsigned autoincr pk 'id'"`
	UID             int64 `xorm:"not null int(10) unsigned 'uid'"`
	GoalID          int32 `xorm:"not null int(10) unsigned 'goal_id'"`
	PlayTime        int64 `xorm:"not null int(10) unsigned 'play_time'"`
	DisplayPlayTime int64 `xorm:"not null int(10) unsigned 'display_play_time'"`
	Calorie         int64 `xorm:"not null int(10) unsigned 'calorie'"`
	UpdateTime      int64 `xorm:"not null int(10) unsigned 'update_time'"`
	CreateTime      int64 `xorm:"not null int(10) unsigned 'create_time'"`
}

// TableName 获取表名
func (g *GoalPlayTime) TableName() string {
	return fmt.Sprintf("st_user_goal_play_time_%d", g.UID%32)
}

// Save 保存
func (g *GoalPlayTime) Save() bool {
	g.UpdateTime = time.Now().Unix()
	g.CreateTime = g.UpdateTime
	_, err := statrds.GetEngineMaster().Insert(g)

	if err != nil {
		logger.Error(err)
		return false
	}
	return true
}

// Update 更新
func (g *GoalPlayTime) Update() bool {
	g.UpdateTime = time.Now().Unix()
	_, err := statrds.GetEngineMaster().ID(g.ID).Update(g)
	if err != nil {
		logger.Error(err)
		return false
	}
	return true
}

type goalPlayTime struct{}

var TbGoalPlayTime goalPlayTime

// GetList 获取列表
func (*goalPlayTime) GetList(uid int64, goalIds []int32) []*GoalPlayTime {
	var tables []*GoalPlayTime
	session := statrds.GetEngine().Table((&GoalPlayTime{UID: uid}).TableName()).Where("uid = ?", uid)
	if goalIds != nil {
		session = session.In("goal_id", goalIds)
	}
	err := session.Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
