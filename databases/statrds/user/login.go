package user

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// Login 用户登录日志表
type LoginLog struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid'"`
	// 登录日期
	DayIndex string `xorm:"not null char(8) index(IDX_DATE_INDEX)  'day_index'"`
	// 登录月
	MonthIndex string `xorm:"not null char(6) index(IDX_month_INDEX)  'month_index'"`
	// 数据来源 1 登录，2练习， 3 签到
	OriginalType int32 `xorm:"not null int(2) 'original_type'"`
	// 原始数据 ID
	OriginalID int64 `xorm:"not null int(11) 'original_id'"`
	// date 原始数据 createTime
	OriginalDate int64 `xorm:"not null int(11) 'original_date'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (u *LoginLog) TableName() string {
	return fmt.Sprintf("st_user_login_%d", u.UID%32)
}

type userLoginLog struct {
}

var TbUserLoginLog userLoginLog

func (u *LoginLog) Save() error {
	u.UpdateTime = time.Now().Unix()
	_, err := statrds.GetEngineMaster().Insert(u)

	if err != nil {
		return err
	}
	return nil
}

func (u *userLoginLog) FindOne(uid int64, dayIndex string) (*LoginLog, error) {
	var table LoginLog
	table.UID = uid

	has, err := statrds.GetEngine().Table(table.TableName()).Where("uid = ? AND day_index = ?", uid, dayIndex).Get(&table)

	if err != nil {
		return nil, err
	}

	if has {
		return &table, nil
	}
	return nil, nil
}

// GetTotal 获取用户登录天数
func (u *userLoginLog) GetList(uid int64) []LoginLog {
	var tables []LoginLog
	var table LoginLog

	table.UID = uid
	err := statrds.GetEngine().Table(table.TableName()).Where("uid = ?", uid).Asc("day_index").Find(&tables)
	if err != nil {
		logger.Warn(err)
		return nil
	}

	return tables
}

// GetListByRangeTime 获取某时间段内登录天记录
func (u *userLoginLog) GetListByRangeTime(uid int64, startDate, endDate string) []LoginLog {
	var tables []LoginLog
	var table LoginLog

	table.UID = uid
	err := statrds.GetEngine().
		Table(table.TableName()).
		Where("uid = ? and day_index >= ? and day_index <= ?", uid, startDate, endDate).
		Asc("day_index").
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}

	return tables
}
