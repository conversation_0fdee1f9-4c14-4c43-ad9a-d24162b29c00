package user

import (
	"fmt"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/comm"
)

// UserCollect 用户活跃统计表
type Collect struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid'"`
	// 用户登录次数
	TotalLoginDay int64 `xorm:"not null int(11) 'total_login_day'"`
	// 用户上上次登录日期
	BeforeLastLoginTime int64 `xorm:"not null int(11) 'before_last_login_time'"`
	// 用户登录日期
	LastLoginTime int64 `xorm:"not null int(11) 'last_login_time'"`
	// 用户按天练习天数
	TotalPracticeDay int64 `xorm:"not null int(11) 'total_practice_day'"`
	// 用户练习日期
	LastPracticeTime int64 `xorm:"not null int(11) 'last_practice_time'"`
	// 用户练习分钟数
	TotalPlayTime int64 `xorm:"not null int(11) 'total_play_time'"`
	// 用户练习 play time 更新时间
	LastPlayTime int64 `xorm:"not null int(11) 'last_play_time'"`
	// 周全勤次数
	TotalPracticeWeek int32 `xorm:"not null int(11) 'total_practice_week'"`
	// 最近练习周
	LastPracticeWeek int32 `xorm:"not null int(11) 'last_practice_week'"`
	// 最近练习周练习天数
	CurrentWeekPracticeDay int32 `xorm:"not null int(11) 'current_week_practice_day'"`
	// 用户生日 7.16.1.0徽章版本上线后设置才会有
	Birthday   int32 `xorm:"not null int(11) 'birthday'"`
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
	// 总共获得瑜币数量
	TotalObtainPoints int32 `xorm:"not null int(11) 'total_obtain_points'"`
	// 总共消耗瑜币数量
	TotalConsumePoints int32 `xorm:"not null int(11) 'total_consume_points'"`
}

// TableName 获取表名
func (c *Collect) TableName() string {
	return fmt.Sprintf("st_user_collect_%d", c.UID%32)
}

func (c *Collect) UpdateByUID() error {
	c.UpdateTime = time.Now().Unix()
	_, err := statrds.GetEngineMaster().Where("uid = ?", c.UID).MustCols("current_week_practice_day").Update(c)
	if err != nil {
		return err
	}
	return nil
}

func (c *Collect) Update() error {
	_, err := statrds.GetEngineMaster().ID(c.ID).Update(c)
	if err != nil {
		return err
	}
	return nil
}

func (c *Collect) Save() error {
	c.UpdateTime = time.Now().Unix()
	c.CreateTime = c.UpdateTime
	_, err := statrds.GetEngineMaster().Insert(c)

	if err != nil {
		return err
	}
	return nil
}

func (c *Collect) IncLoginDay(loginTime int64) error {
	c.UpdateTime = time.Now().Unix()
	if comm.FormatStartTime(loginTime) > comm.FormatStartTime(c.LastLoginTime) {
		c.BeforeLastLoginTime = c.LastLoginTime
		c.LastLoginTime = loginTime
	}
	_, err := statrds.GetEngineMaster().Where("uid = ?", c.UID).Cols("update_time",
		"before_last_login_time", "last_login_time").Incr("total_login_day", 1).Update(c)
	if err != nil {
		return err
	}
	return nil
}

type userCollect struct{}

var TbUserCollect userCollect

func (c *userCollect) FindByUID(uid int64) (*Collect, error) {
	var table Collect

	table.UID = uid
	ok, err := statrds.GetEngine().Table(table.TableName()).Where("uid = ?", uid).Get(&table)

	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &table, nil
}

func (c *userCollect) IncPracticeWeek(uid, count int64) error {
	var table Collect
	table.UID = uid
	table.UpdateTime = time.Now().Unix()
	_, err := statrds.GetEngineMaster().Table(table.TableName()).Where("uid = ?", uid).
		Cols("update_time").Incr("total_practice_week", count).Update(&table)
	if err != nil {
		return err
	}
	return nil
}

func (c *userCollect) FindOrCreate(uid int64) (*Collect, error) {
	table, err := c.FindByUID(uid)

	if err != nil {
		return nil, err
	}

	if table != nil {
		return table, nil
	}

	now := time.Now()

	var row Collect
	row.UID = uid
	row.BeforeLastLoginTime = now.Unix()
	row.LastLoginTime = now.Unix()

	if err := row.Save(); err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			table, _ = c.FindByUID(uid)
			if table != nil {
				return table, nil
			}
		}
		return nil, err
	}
	return &row, nil
}
