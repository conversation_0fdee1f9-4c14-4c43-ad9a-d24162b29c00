package practice

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// Period 用户生理期配置表
type Period struct {
	ID          int64  `xorm:"not null autoincr pk int(11) 'id'"`
	UID         int64  `xorm:"not null int(11) 'uid'"`
	StartDate   string `xorm:"not null char(6) 'start_date'"`
	EndDate     string `xorm:"not null char(6) 'end_date'"`
	KeepDay     int32  `xorm:"not null smallint(6) 'keep_day'"`
	CyclePeriod int32  `xorm:"not null int(6) 'cycle_period'"`
	IsValid     int32  `xorm:"not null tinyint(1) 'is_valid'"`
	CreateTime  int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime  int64  `xorm:"not null int(11) 'update_time'"`
}

// TableName 表名
func (Period) TableName() string {
	return "uc_physiology_last_period"
}

type period struct{}

var TbPeriod period

func (*period) GetPeriodByUID(uid int64) *Period {
	var table Period

	ok, err := statrds.GetEngine().Where("uid = ? ", uid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
