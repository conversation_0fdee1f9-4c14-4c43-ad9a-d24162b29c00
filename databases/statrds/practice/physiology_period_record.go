package practice

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// PeriodRecord 用户生理期记录表
type PeriodRecord struct {
	ID         int64  `xorm:"not null autoincr pk int(11) 'id'"`
	UID        int64  `xorm:"not null int(11) 'uid'"`
	StartDate  string `xorm:"not null char(8) 'start_date'"`
	EndDate    string `xorm:"not null char(8) 'end_date'"`
	IsDisPlay  int32  `xorm:"not null tinyint(1) 'is_display'"`
	CreateTime int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64  `xorm:"not null int(11) 'update_time'"`
}

// TableName 表名
func (PeriodRecord) TableName() string {
	return "uc_physiology_period_record"
}

type record struct{}

var TbPeriodRecord record

// GetLastRecord 获取用户据当前时间最近的一条生理期记录
func (*record) GetLastRecord(uid int64) *PeriodRecord {
	table := new(PeriodRecord)
	ok, err := statrds.GetEngine().Where("uid = ? and start_date <= ?", uid, time.Now().Format("20060102")).
		Desc("end_date").Get(table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return table
}
