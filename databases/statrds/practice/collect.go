package practice

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// Collect 用户练习天数汇总统计
type Collect struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid' index(IDX_UID) comment('用户ID')"`
	// 最后一次练习时间
	LastPracticeDate int64 `xorm:"not null int(11) 'last_practice_date' comment('最后一次练习时间')"`
	// 累计练习天数
	TotalPracticeDay int64 `xorm:"not null int(11) 'total_practice_day' comment('累计练习天数')"`
	// 连续练习天数
	ContinuePracticeDay int64 `xorm:"not null int(11) 'continue_practice_day' comment('连续练习天数')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (c Collect) TableName() string {
	if c.UID < 1 {
		return ""
	}
	return fmt.Sprintf("st_practice_days_collect_%d", c.UID%32)
}

// Save 保存数据
func (c *Collect) Save() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime

	_, err := statrds.GetEngineMaster().Insert(c)
	return err
}

// Update 更新数据
func (c *Collect) Update() error {
	c.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

type collect struct{}

// TbCollect 外部引用对象
var TbCollect collect

func (c *collect) GetItem(uid int64) *Collect {
	var table Collect

	table.UID = uid
	ok, err := statrds.GetEngine().Where("uid = ?", uid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
