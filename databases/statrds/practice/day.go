package practice

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// Day 用户练习天数统计
type Day struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid' index(IDX_UID) comment('用户ID')"`
	// 日期：20190428
	DateIndex string `xorm:"not null char(8) 'date_index' comment('日期: 20060102')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (d Day) TableName() string {
	if d.UID < 1 {
		return ""
	}
	return fmt.Sprintf("st_practice_days_%d", d.UID%64)
}

// Save 保存数据
func (d *Day) Save() error {
	d.CreateTime = time.Now().Unix()
	d.UpdateTime = d.CreateTime

	_, err := statrds.GetEngineMaster().Insert(d)
	return err
}

type day struct{}

// TbDay 外部引用对象
var TbDay day

func (d *day) GetItem(uid int64, dateIndex string) *Day {
	var table Day

	table.UID = uid
	ok, err := statrds.GetEngine().Where("uid = ? and date_index = ?", uid, dateIndex).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// 2019-2-2 修改方法增加排序字段
func (d *day) GetList(uid int64, order string) []Day {
	var tables []Day
	var table Day
	table.UID = uid
	session := statrds.GetEngine().NewSession()
	defer session.Close()
	session = session.Table(table.TableName()).
		Where("uid = ?", uid).
		And("date_index <= ?", time.Now().Format("20060102")).
		And("date_index >= ?", "20160101")
	err := session.Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if strings.EqualFold(order, "ASC") {
		sort.Slice(tables, func(i, j int) bool {
			p, _ := strconv.Atoi(tables[i].DateIndex)
			n, _ := strconv.Atoi(tables[j].DateIndex)
			return p < n
		})
	} else {
		sort.Slice(tables, func(i, j int) bool {
			p, _ := strconv.Atoi(tables[i].DateIndex)
			n, _ := strconv.Atoi(tables[j].DateIndex)
			return p > n
		})
	}
	return tables
}

// GetTotal 获取用户练习总天数
func (d *day) GetTotal(uid int64) int64 {
	var table Day

	table.UID = uid
	total, err := statrds.GetEngine().
		Table(table.TableName()).
		Where("uid = ?", uid).
		And("date_index <= ?", time.Now().Format("20060102")).
		Count(&Day{})
	if err != nil {
		logger.Error(err)
		return 0
	}

	return total
}

func (d *day) GetListAscDateIndex(uid int64) []Day {
	var tables []Day
	var table Day

	table.UID = uid
	err := statrds.GetEngine().Table(table.TableName()).Where("uid = ?", uid).Asc("date_index").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (d *day) GetListByRangeTime(uid int64, startDate, endDate string) []Day {
	var tables []Day
	var table Day

	table.UID = uid
	err := statrds.GetEngine().
		Table(table.TableName()).
		Where("uid = ? and date_index >= ? and date_index <= ?", uid, startDate, endDate).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
