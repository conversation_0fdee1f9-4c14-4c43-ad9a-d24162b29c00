package practice

import (
	"fmt"
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

type CalendarP struct {
	ID              int64 `json:"id" xorm:"not null pk autoincr comment('主键id') INT(11) 'id'"`
	UID             int64 `json:"uid" xorm:"not null comment('用户ID') index INT(11) 'uid'"`
	ProgramID       int64 `json:"program_id" xorm:"not null comment('计划ID') INT(11) 'program_id'"`
	SessionID       int64 `json:"session_id" xorm:"not null comment('课程ID') INT(11) 'session_id'"`
	ResourceType    int32 `json:"resource_type" xorm:"not null comment('练习资源类型') TINYINT(4) 'resource_type'"`
	DayIndex        int32 `json:"day_index" xorm:"not null comment('第几天') INT(11) 'day_index'"`
	SessionIndex    int32 `json:"session_index" xorm:"not null comment('课程节数') INT(4) 'session_index'"`
	SubSessionIndex int32 `json:"sub_session_index" xorm:"not null comment('子课程节数') INT(4) 'sub_session_index'"`
	ReportTime      int64 `json:"report_time" xorm:"not null comment('上报时间') INT(11) 'report_time'"`
	StartTime       int64 `json:"start_time" xorm:"not null comment('开始时间') INT(11) 'start_time'"`
	EndTime         int64 `json:"end_time" xorm:"not null default 0 comment('结束时间') INT(11) 'end_time'"`
	EndUpdateTime   int64 `json:"end_update_time" xorm:"not null default 0 comment('结束更新时间') INT(11) 'end_update_time'"`
	Status          int32 `xorm:"tinyint(2) unsigned notnull 'status'" json:"status"` // 状态：1，正常，2过期
	CreateTime      int64 `json:"create_time" xorm:"not null comment('创建时间') INT(11) 'create_time'"`
	UpdateTime      int64 `json:"update_time" xorm:"not null comment('更新时间') INT(11) 'update_time'"`
}

// TableName 获取表名
func (c *CalendarP) TableName() string {
	if c.UID < 1 {
		return ""
	}
	return fmt.Sprintf("uc_calendar_practice_%d", c.UID%32)
}

// update 修改
func (c *CalendarP) UpdateByTransaction(session *xorm.Session) error {
	c.UpdateTime = time.Now().Unix()
	_, err := session.ID(c.ID).Update(c)
	return err
}

type _CalendarP struct{}

// TbCalendarP 外部引用对象
var TbCalendarP _CalendarP

// GetUserLastSession 获取课程
func (u *_CalendarP) GetUserLastSession(uid int64, resourceType int32, status []int32) *CalendarP {
	var table CalendarP
	tableBean := &CalendarP{
		UID: uid,
	}
	ok, err := db.GetEngine().Table(tableBean).Where("uid = ? ", uid).In("status", status).
		Where("resource_type=? ", resourceType).
		Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetCalendarByProgramID
func (u *_CalendarP) GetCalendarByProgramID(uid, programID int64, resourceType, status int32) *CalendarP {
	var table CalendarP
	tableBean := &CalendarP{
		UID: uid,
	}
	ok, err := db.GetEngine().Table(tableBean).Where("uid = ? AND status=? ", uid, status).
		Where("resource_type=? AND program_id=?", resourceType, programID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetCalendarByProgramID
func (u *_CalendarP) GetCalendarByProgramIDType(uid, programID int64, resourceTypes []int32, status int32) *CalendarP {
	var table CalendarP
	tableBean := &CalendarP{
		UID: uid,
	}
	ok, err := db.GetEngine().Table(tableBean).Where("uid = ? AND status=? ", uid, status).
		Where("program_id=?", programID).In("resource_type", resourceTypes).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetCalendarByProgramID
func (u *_CalendarP) GetHasData(uid int64) *CalendarP {
	var table CalendarP
	tableBean := &CalendarP{
		UID: uid,
	}
	ok, err := db.GetEngine().Table(tableBean).Where("uid = ?", uid).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetUseHasSession
func (u *_CalendarP) GetUseHasSession(uid int64, resourceType int32) *CalendarP {
	var table CalendarP
	tableBean := &CalendarP{
		UID: uid,
	}
	ok, err := db.GetEngine().Table(tableBean).Where("uid = ?", uid).
		Where("resource_type=? ", resourceType).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
