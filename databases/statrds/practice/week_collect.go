package practice

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// WeekCollect 周全勤汇总表
type WeekCollect struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid' index(idx_uid) comment('用户UID')"`
	// 周全勤次数
	TotalPracticeWeek int32 `xorm:"not null int(11) 'total_practice_week' comment('周全勤次数')"`
	// 最后一次练习周数
	LastPracticeWeek int32 `xorm:"not null smallint(6) 'last_practice_week' comment('最后一次练习周数')"`
	// 日期：201904
	CurrentWeekPracticeDay int32 `xorm:"not null tinyint(3) 'current_week_practice_day' comment('最后练习周练习天数')"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

func (w WeekCollect) Save() error {
	w.CreateTime = time.Now().Unix()
	w.UpdateTime = w.CreateTime

	_, err := statrds.GetEngineMaster().Insert(w)
	return err
}

func (w WeekCollect) Update() error {
	w.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(w.ID).MustCols("current_week_practice_day").Update(w)
	return err
}

func (w WeekCollect) TableName() string {
	return fmt.Sprintf("st_practice_week_collect_%d", w.UID%32)
}

type weekCollect struct {
}

var TbWeekCollect weekCollect

// GetItem 获取用户周全勤汇总数据记录
func (w *weekCollect) GetItem(uid int64) (*WeekCollect, error) {
	var table WeekCollect

	table.UID = uid
	ok, err := statrds.GetEngine().Where("uid = ?", uid).Get(&table)
	if err != nil {
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &table, nil
}
