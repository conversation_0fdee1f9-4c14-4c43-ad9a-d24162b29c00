package practice

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

type Total struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) index(IDX_UID) 'uid' comment('用户ID')"`
	// 计划ID
	ProgramID int64 `xorm:"not null int(11) 'program_id' comment('计划ID')"`
	// 课程ID
	SessionID int64 `xorm:"not null int(11) 'session_id' comment('课程ID')"`
	// 课程节数
	SessionIndex int32 `xorm:"not null int(4) 'session_index' comment('课程节数')"`
	// 子课程节数
	SubSessionIndex int32 `xorm:"not null int(4) 'sub_session_index' comment('子课程节数')"`
	// 完成次数
	CompleteTimes int32 `xorm:"not null int(4) 'complete_times' comment('完成次数')"`
	// 练习时长
	PlayTime int64 `xorm:"not null int(11) 'play_time' comment('练习时长')"`
	// 卡路里
	Calorie int64 `xorm:"not null int(11) 'calorie' comment('卡路里')"`
	// user_action_log数据表
	UserActionTable int32 `xorm:"not null int(4) 'user_action_table' comment('user_action_log数据表')"`
	// 上报时间
	LastUpdateTime int64 `xorm:"not null int(11)  'last_update_time' comment('最后上报时间')"`
	CreateTime     int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime     int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (p *Total) TableName() string {
	if p.UID < 1 {
		return ""
	}
	return fmt.Sprintf("st_practice_session_total_count_%d", p.UID%32)
}

// Save 保存数据
func (p *Total) Save() error {
	p.CreateTime = time.Now().Unix()
	p.UpdateTime = p.CreateTime
	_, err := statrds.GetEngineMaster().Insert(p)
	return err
}

// Update 更新数据
func (p *Total) Update() error {
	p.UpdateTime = time.Now().Unix()
	_, err := statrds.GetEngineMaster().ID(p.ID).Update(p)
	return err
}

func (p *Total) UpdateMustCols() error {
	p.UpdateTime = time.Now().Unix()
	_, err := statrds.GetEngineMaster().ID(p.ID).MustCols("session_index", "sub_session_index").Update(p)
	return err
}

func (p *Total) UpdateByCond(uid int64) error {
	p.UID = uid
	p.UpdateTime = time.Now().Unix()
	_, err := statrds.GetEngineMaster().
		Where("uid = ?", uid).
		MustCols("play_time", "calorie", "update_time").
		Update(p)
	return err
}

func (p *Total) UpdateMustColsCleanUpdateUserPracticeData() error {
	p.UpdateTime = time.Now().Unix()
	_, err := statrds.GetEngineMaster().ID(p.ID).MustCols("calorie", "play_time").Update(p)
	return err
}

type practiceTotal struct{}

var TbPracticeTotal practiceTotal

// 2020-1-2 获取用户练习次数汇总
func (p *practiceTotal) GetItem(params *Total) *Total {
	var table Total
	table.UID = params.UID

	session := statrds.GetEngine().Where("uid = ?", params.UID)
	session = session.Where("program_id = ?", params.ProgramID)
	if params.SessionID > 0 {
		session = session.Where("session_id = ?", params.SessionID)
	}
	if params.SessionIndex > 0 {
		session = session.Where("session_index = ?", params.SessionIndex)
	}
	if params.UserActionTable > 0 {
		session = session.Where("user_action_table = ?", params.UserActionTable)
	}
	ok, err := session.Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (p *practiceTotal) GetList(params *Total) []Total {
	var table []Total
	tableName := &Total{
		UID: params.UID,
	}
	session := statrds.GetEngine().Table(tableName.TableName()).Where("uid = ?", params.UID)
	session = session.Where("program_id = ?", params.ProgramID)
	session = session.Where("session_id = ?", params.SessionID)
	session = session.Where("session_index = ?", params.SessionIndex)
	if params.UserActionTable > 0 {
		session = session.Where("user_action_table = ?", params.UserActionTable)
	}
	err := session.Find(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return table
}

func (p *practiceTotal) GetListByUID(uid int64) []Total {
	var tables []Total
	session := statrds.GetEngine().Table((&Total{
		UID: uid,
	}).TableName())
	err := session.Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// 获取用户计划所有练习的计划
func (p *practiceTotal) GetUserProgramCompleteTimes(params *Total) []Total {
	var table []Total
	tableName := &Total{
		UID: params.UID,
	}
	session := statrds.GetEngine().Table(tableName.TableName()).Where("uid = ?", params.UID)
	session = session.Where("user_action_table = ?", params.UserActionTable)
	err := session.Find(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return table
}

// 获取用户所有课程
func (p *practiceTotal) GetUserSessionAllCompleteTimes(params *Total) []Total {
	var table []Total
	tableName := &Total{
		UID: params.UID,
	}
	session := statrds.GetEngine().Table(tableName.TableName()).Where("uid = ?", params.UID)
	session = session.Where("program_id = ?", params.ProgramID)
	session = session.Where("user_action_table = ?", params.UserActionTable)
	err := session.Find(&table)

	if err != nil {
		logger.Error(err)
		return nil
	}
	return table
}
