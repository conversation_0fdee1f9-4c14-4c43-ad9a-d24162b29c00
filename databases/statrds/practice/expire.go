package practice

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// Expire 过期用户练习明细数据
type Expire struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) index(IDX_UID) 'uid' comment('用户ID')"`
	// 计划ID
	ProgramID int64 `xorm:"not null int(11) 'program_id' comment('计划ID')"`
	// 课程ID
	SessionID int64 `xorm:"not null int(11) 'session_id' comment('课程ID')"`
	// 课程节数
	SessionIndex int32 `xorm:"not null int(4) 'session_index' comment('课程节数')"`
	// 子课程节数
	SubSessionIndex int32 `xorm:"not null int(4) 'sub_session_index' comment('子课程节数')"`
	// 完成次数
	CompleteTimes int32 `xorm:"not null int(4) 'complete_times' comment('完成次数')"`
	// user_action_log数据表
	UserActionTable int32 `xorm:"not null int(4) 'user_action_table' comment('user_action_log数据表')"`
	// 练习时长
	PlayTime int64 `xorm:"not null int(11) 'play_time' comment('练习时长')"`
	// 卡路里
	Calorie int64 `xorm:"not null int(11) 'calorie' comment('卡路里')"`
	// 上报时间
	ReportTime int64 `xorm:"not null int(11) index(IDX_REPORT_TIME) 'report_time' comment('上报时间')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (e *Expire) TableName() string {
	return "st_user_practice_expire"
}

// Save 保存数据
func (e *Expire) Save() error {
	e.CreateTime = time.Now().Unix()
	e.UpdateTime = e.CreateTime

	_, err := statrds.GetEngineMaster().Insert(e)
	return err
}
