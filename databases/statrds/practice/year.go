package practice

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// Year 用户练习天数按年统计
type Year struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid' index(IDX_UID) comment('用户ID')"`
	// 练习天数
	PracticeDays int32 `xorm:"not null int(11) 'practice_days' comment('练习天数')"`
	// 日期：2019
	DateIndex string `xorm:"not null char(4) index(IDX_DATE_INDEX) 'date_index' comment('日期: 2006')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (y Year) TableName() string {
	if y.DateIndex == "" {
		return ""
	}
	return fmt.Sprintf("st_practice_days_year_%s", y.DateIndex)
}

// Save 保存数据
func (y *Year) Save() error {
	_, err := statrds.GetEngineMaster().Insert(y)
	return err
}

// Update 更新数据
func (y *Year) Update() error {
	_, err := statrds.GetEngineMaster().ID(y.ID).Update(y)
	return err
}

type year struct{}

// TbYear 外部引用对象
var TbYear year

func (y *year) GetItem(uid int64, dateYear string) *Year {
	var table Year

	table.DateIndex = dateYear
	ok, err := statrds.GetEngine().Where("uid = ?", uid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
