package practice

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// Week 用户练习天数按周统计
type Week struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid' index(IDX_UID) comment('用户ID')"`
	// 练习天数
	PracticeDays int32 `xorm:"not null int(11) 'practice_days' comment('练习天数')"`
	// 日期：201904
	DateIndex string `xorm:"not null char(6) index(IDX_DATE_INDEX) 'date_index' comment('日期: 200601')"`
	// 周
	Week string `xorm:"not null char(6) 'week' index(IDX_WEEK) comment('周')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (w Week) TableName() string {
	if w.Week == "" {
		return ""
	}
	return fmt.Sprintf("st_practice_days_week_%s", w.Week)
}

// Save 保存数据
func (w *Week) Save() error {
	_, err := statrds.GetEngineMaster().Insert(w)
	return err
}

// Update 更新数据
func (w *Week) Update() error {
	_, err := statrds.GetEngineMaster().ID(w.ID).Update(w)
	return err
}

type week struct{}

// TbWeek 外部引用对象
var TbWeek week

func (w *week) GetItem(uid int64, week string) *Week {
	var table Week

	table.Week = week
	ok, err := statrds.GetEngine().Where("uid = ? and week = ?", uid, week).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (w *week) GetAll(uid int64, week string) (int64, error) {
	var weeks Week
	count, err := statrds.GetEngine().Table(fmt.Sprintf("st_practice_days_week_%s", week)).
		Where("uid = ? and practice_days >= 7", uid).Count(weeks)

	if err != nil {
		return 0, err
	}

	return count, nil
}
