package practice

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// Month 用户练习天数按月统计
type Month struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid' index(IDX_UID) comment('用户ID')"`
	// 练习天数
	PracticeDays int32 `xorm:"not null int(11) 'practice_days' comment('练习天数')"`
	// 日期：201904
	DateIndex string `xorm:"not null char(6)  'date_index' comment('日期: 200601')"`

	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (m Month) TableName() string {
	if m.DateIndex == "" {
		return ""
	}
	return fmt.Sprintf("st_practice_days_month_%s", m.DateIndex)
}

// Save 保存数据
func (m *Month) Save() error {
	_, err := statrds.GetEngineMaster().Insert(m)
	return err
}

// Update 更新数据
func (m *Month) Update() error {
	_, err := statrds.GetEngineMaster().ID(m.ID).Update(m)
	return err
}

type month struct{}

// TbMonth 外部引用对象
var TbMonth month

func (m *month) GetItem(uid int64, dateMonth string) *Month {
	var table Month

	ok, err := statrds.GetEngine().Table(&Month{DateIndex: dateMonth}).Where("uid = ? ", uid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
