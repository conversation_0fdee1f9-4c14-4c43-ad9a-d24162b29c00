package practice

import (
	"fmt"
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
	lib "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type CalendarPD struct {
	ID int64 `json:"id" xorm:"not null pk autoincr comment('主键id') INT(11) 'id'"`
	// 用户练习日历主表id
	CalendarPracticeID int64 `json:"calendar_practice_id" xorm:"not null INT(11) 'calendar_practice_id'"`
	UID                int64 `json:"uid" xorm:"not null comment('用户ID') index INT(11) 'uid'"`
	DayIndex           int32 `json:"day_index" xorm:"not null comment('第几天') INT(11) 'day_index'"`
	PracticeTime       int64 `json:"practice_time" xorm:"not null default 0 INT(11) 'practice_time'"`
	IsDone             int32 `xorm:"tinyint(2) unsigned notnull 'is_done'" json:"is_done"` // 状态：1：未完成,2：已完成
	Status             int32 `xorm:"tinyint(2) unsigned notnull 'status'" json:"status"`   // 状态：1，正常，2过期
	CreateTime         int64 `json:"create_time" xorm:"not null comment('创建时间') INT(11)"`
	UpdateTime         int64 `json:"update_time" xorm:"not null comment('更新时间') INT(11)"`
}

func (c CalendarPD) TableName() string {
	if c.UID < 1 {
		return ""
	}
	return fmt.Sprintf("uc_calendar_practice_date_%d", c.UID%32)
}

// update 修改
func (c *CalendarPD) UpdateByTransaction(session *xorm.Session) error {
	c.UpdateTime = time.Now().Unix()
	_, err := session.ID(c.ID).Update(c)
	return err
}

type _CalendarPD struct{}

// TbCalendarPD 外部引用对象
var TbCalendarPD _CalendarPD

// GetListBYCalendarDID 获取列表
func (u *_CalendarPD) GetListBYCalendarDID(uid, cpID int64) []*CalendarPD {
	var table []*CalendarPD
	err := db.GetEngine().Table(
		CalendarPD{
			UID: uid,
		}.TableName()).Where("uid = ? AND  calendar_practice_id=?", uid, cpID).
		Where("status = ?", lib.DataStatusEnum.Valid).
		Find(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return table
}

// GetItemByDate 获取日历
func (u *_CalendarPD) GetItemByDate(uid, cpID, practiceTime int64) *CalendarPD {
	var table CalendarPD
	db.GetEngine().ShowSQL(true)
	ok, err := db.GetEngine().Table(
		CalendarPD{
			UID: uid,
		}.TableName()).Where("uid = ? AND  calendar_practice_id=?", uid, cpID).
		Where("status = ? AND practice_time = ? ", lib.DataStatusEnum.Valid, practiceTime).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
