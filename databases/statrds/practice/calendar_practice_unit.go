package practice

import (
	"fmt"
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

type CalendarPU struct {
	ID int64 `json:"id" xorm:"not null pk autoincr comment('主键id') INT(11) 'id'"`
	// 用户练习日历主表id
	CalendarPracticeID int64 `json:"calendar_practice_id" xorm:"not null INT(11) 'calendar_practice_id'"`
	UID                int64 `json:"uid" xorm:"not null comment('用户ID') index INT(11) 'uid'"`
	ProgramID          int64 `json:"program_id" xorm:"not null comment('计划ID') INT(6) 'program_id'"`
	SessionID          int64 `json:"session_id" xorm:"not null comment('课程ID') INT(6) 'session_id'"`
	DayIndex           int32 `json:"day_index" xorm:"not null comment('第几天') INT(11) 'day_index'"`
	SessionIndex       int32 `json:"session_index" xorm:"not null comment('课程节数') INT(4) 'session_index'"`
	SubSessionIndex    int32 `json:"sub_session_index" xorm:"not null comment('子课程节数') INT(4) 'sub_session_index'"`
	ReportTime         int64 `json:"report_time" xorm:"not null default 0 comment('练习时间') INT(11) 'report_time'"`
	IsDone             int32 `xorm:"tinyint(2) unsigned notnull 'is_done'" json:"is_done"` // 状态：1：未完成,2：已完成
	CreateTime         int64 `json:"create_time" xorm:"not null comment('创建时间') INT(11)"`
	UpdateTime         int64 `json:"update_time" xorm:"not null comment('更新时间') INT(11)"`
}

// TableName 获取表名
func (c *CalendarPU) TableName() string {
	if c.UID < 1 {
		return ""
	}
	return fmt.Sprintf("uc_calendar_practice_unit_%d", c.UID%32)
}

// update 修改
func (c *CalendarPU) UpdateByTransaction(session *xorm.Session) error {
	c.UpdateTime = time.Now().Unix()
	_, err := session.ID(c.ID).Update(c)
	return err
}

type _CalendarPU struct{}

// TbCalendarPU 外部引用对象
var TbCalendarPU _CalendarPU

// GetListBYCalendarPracticeID 获取列表
func (u *_CalendarPU) GetListBYCalendarPracticeID(id, uid int64) []*CalendarPU {
	var tables []*CalendarPU
	table := &CalendarPU{
		UID: uid,
	}
	err := db.GetEngine().Table(table).Where("calendar_practice_id = ?", id).Find(&tables)
	if err != nil {
		return nil
	}
	return tables
}

// GetListUnit 获取子进度
func (u *_CalendarPU) GetListUnit(cID, uid, programID, sessionID int64, dayIndex int32) *CalendarPU {
	var table CalendarPU
	tableBean := &CalendarPU{
		UID: uid,
	}
	ok, err := db.GetEngine().Table(tableBean).Where("calendar_practice_id = ? AND program_id = ?", cID, programID).
		Where("session_id = ? AND day_index = ?", sessionID, dayIndex).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
