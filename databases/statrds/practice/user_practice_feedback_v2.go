package practice

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

type UserPracticeFeedbackV2 struct {
	ID             int64  `xorm:"not null autoincr pk int(10) 'id' comment('主键')"`
	UserPracticeID int64  `xorm:"not null int(10) index(IDX_UD) 'user_practice_id' comment('表st_user_practice_主键')"`
	DateIndex      string `xorm:"not null char(6) index(IDX_UD) 'date_index' comment('日期: 200601')"`
	UID            int64  `xorm:"not null int(10) 'uid' comment('用户id')"`
	SourceType     int32  `xorm:"not null tinyint(3) index(IDX_TI) 'source_type' comment('资源类型:见枚举')"`
	SourceID       int64  `xorm:"not null int(10) index(IDX_TI) 'source_id' comment('资源id')"`
	Version        string `xorm:"not null varchar(32) 'version' comment('客户端版本号')"`
	Feel           int32  `xorm:"not null tinyint(3) 'feel' comment('反馈状态:1-很糟糕 2-一般般 3-超级棒 4-不太好 5-还不错')"`
	Labels         string `xorm:"not null varchar(50) 'labels' comment('反馈标签')"`
	Feedback       string `xorm:"not null varchar(800) 'feedback' comment('反馈文案')"`
	CreateTime     int64  `xorm:"not null int(10) index(IDX_CT) 'create_time' comment('创建时间')"`
	UpdateTime     int64  `xorm:"not null int(10) 'update_time' comment('更新时间')"`
}

// TableName 获取表名
func (u *UserPracticeFeedbackV2) TableName() string {
	// 最早的月份分表，小于它放在old表
	if u.DateIndex < "202108" {
		return "st_user_practice_feedback_old"
	}
	return "st_user_practice_feedback_" + u.DateIndex
}

// Save 保存数据
func (u *UserPracticeFeedbackV2) Save() bool {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime

	_, err := statrds.GetEngineMaster().Insert(u)
	if err != nil {
		logger.Error(err)
		return false
	}
	return true
}

// 更新
func (u *UserPracticeFeedbackV2) Update() bool {
	u.UpdateTime = u.CreateTime

	_, err := statrds.GetEngineMaster().ID(u.ID).Update(u)
	if err != nil {
		logger.Error(err)
		return false
	}
	return true
}

// 保存或者更新
func (u *UserPracticeFeedbackV2) SaveOrUpdate() bool {
	table := TbUserPracticeFeedbackV2.GetItem(u.UserPracticeID, u.DateIndex)
	if table != nil {
		table.Version = u.Version
		table.Feel = u.Feel
		table.Labels = u.Labels
		table.Feedback = u.Feedback
		if !table.Update() {
			logger.Errorf("练习反馈更新失败, table: %#v", table)
			return false
		}
		return true
	}
	table = &UserPracticeFeedbackV2{
		UserPracticeID: u.UserPracticeID,
		DateIndex:      u.DateIndex,
		UID:            u.UID,
		SourceType:     u.SourceType,
		SourceID:       u.SourceID,
		Version:        u.Version,
		Feel:           u.Feel,
		Labels:         u.Labels,
		Feedback:       u.Feedback,
	}
	if !table.Save() {
		logger.Errorf("练习反馈保存失败, table: %#v", table)
		return false
	}
	return true
}

type userPracticeFeedbackV2 struct{}

var TbUserPracticeFeedbackV2 userPracticeFeedbackV2

// 查询
func (u userPracticeFeedbackV2) GetItem(userPracticeID int64, dateIndex string) *UserPracticeFeedbackV2 {
	var table UserPracticeFeedbackV2

	table.DateIndex = dateIndex
	ok, err := statrds.GetEngine().Table(table.TableName()).
		Where("user_practice_id = ?", userPracticeID).
		Where("date_index = ?", dateIndex).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
