package practice

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

// LastPractice 用户上次练习记录
type LastPractice struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) index(IDX_UID) 'uid' comment('用户ID')"`
	// 计划ID
	ProgramID int64 `xorm:"not null int(11) 'program_id' comment('计划ID')"`
	// 课程ID
	SessionID int64 `xorm:"not null int(11) 'session_id' comment('课程ID')"`
	// 课程节数
	SessionIndex int32 `xorm:"not null int(4) 'session_index' comment('课程节数')"`
	// 子课程节数
	SubSessionIndex int32 `xorm:"not null int(4) 'sub_session_index' comment('子课程节数')"`
	// user_action_log数据表
	UserActionTable int32 `xorm:"not null int(4) 'user_action_table' comment('user_action_log数据表')"`
	// 上报时间
	ReportTime int64 `xorm:"not null int(11) index(IDX_REPORT_PLAY_TIME) 'report_time' comment('上报时间')"`
	// 客户端跳转类型
	LinkType int32 `xorm:"not null tinyint(4) 'link_type' comment('客户端跳转类型')"`
	// 智能课表规划的练习时间
	SchedulePracticeDate int64 `xorm:"not null int(11) 'schedule_practice_date' comment('智能课表练习日期')"`
	// 训练营课程id
	O2SessionID int64 `xorm:"not null int(11) 'o2_session_id' comment('训练营ID')"`
	// 练习资源类型
	ResourceType int32 `xorm:"not null tinyint(4) 'resource_type' comment('练习资源类型')"`
	CreateTime   int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime   int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (u *LastPractice) TableName() string {
	if u.UID < 1 {
		return ""
	}
	return fmt.Sprintf("st_user_last_practice_%d", u.UID%32)
}

// Save 保存数据
func (u *LastPractice) Save() error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime

	_, err := statrds.GetEngineMaster().Insert(u)
	return err
}

// Update 更新数据
func (u *LastPractice) Update() error {
	u.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(u.ID).Update(u)
	return err
}

// UpdateByTransaction 事务更新数据 含有必须更新的字段，即便为0 也强制更新
func (u *LastPractice) UpdateMustCols() error {
	u.UpdateTime = time.Now().Unix()
	_, err := statrds.GetEngineMaster().ID(u.ID).MustCols("program_id", "session_id", "session_index", "sub_session_index",
		"complete_times", "user_action_table", "report_time", "play_time",
		"calorie", "link_type", "schedule_practice_date", "resource_type").Update(u)
	return err
}

type lastPractice struct{}

// TbLastPractice 外部引用对象
var TbLastPractice lastPractice

func (u *lastPractice) GetByUID(uid int64) *LastPractice {
	var table LastPractice
	table.UID = uid
	ok, err := statrds.GetEngine().Where("uid = ?", uid).Get(&table)
	if err != nil {
		logger.Error(err)
	}
	if !ok {
		return nil
	}
	return &table
}

// GetListByUID 获取列表
func (u *lastPractice) GetListByUID(uid int64) []*LastPractice {
	var tables []*LastPractice
	tableBean := &LastPractice{
		UID: uid,
	}
	err := statrds.GetEngineMaster().Table(tableBean).Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (u *lastPractice) GetListByResourceType(uid int64, resourceType []int32) *LastPractice {
	var table LastPractice

	tableBean := &LastPractice{
		UID: uid,
	}
	ok, err := statrds.GetEngineMaster().Table(tableBean).
		Where("uid = ?", uid).In("resource_type", resourceType).Desc("update_time").Get(&table)
	if err != nil {
		logger.Error(err)
	}
	if !ok {
		return nil
	}
	return &table
}
