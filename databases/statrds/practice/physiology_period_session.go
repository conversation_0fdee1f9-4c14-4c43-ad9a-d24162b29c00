package practice

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
)

type PeriodSession struct {
	ID           int64  `xorm:"not null autoincr pk int(11) 'id'"`
	UID          int64  `xorm:"not null int(11) 'uid'"`
	PracticeDate string `xorm:"not null char(8) 'practice_date'"`
	SessionIndex int32  `xorm:"not null int(6) 'session_index'"`
	SessionID    int64  `xorm:"not null int(6) 'session_id'"`
	IsPractice   int32  `xorm:"not null tinyint(1) 'is_practice'"`
	CreateTime   int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime   int64  `xorm:"not null int(11) 'update_time'"`
}

func (PeriodSession) TableName() string {
	return "uc_physiology_period_session"
}

func (p *PeriodSession) Update() error {
	p.UpdateTime = time.Now().Unix()
	_, err := statrds.GetEngineMaster().ID(p.ID).Update(p)
	if err != nil {
		return err
	}
	return nil
}

func (p *PeriodSession) Save() error {
	p.UpdateTime = time.Now().Unix()
	p.CreateTime = p.UpdateTime
	_, err := statrds.GetEngineMaster().Insert(p)
	if err != nil {
		return err
	}
	return nil
}

type psession struct{}

var TbPeriodSession psession

// UpdateDatePeriodSessionDone 更新生理期课表进度
func (psession) UpdateDatePeriodSessionDone(uid, practiceDate, sessionID int64) {
	practiceDateStr := time.Unix(practiceDate, 0).Format("20060102")
	sql := "update uc_physiology_period_session set is_practice = ? where uid = ? " +
		"and practice_date = ? and session_id = ? limit 1"
	_, err := statrds.GetEngineMaster().Exec(sql, 1, uid, practiceDateStr, sessionID)
	if err != nil {
		logger.Error(err)
	}
}
