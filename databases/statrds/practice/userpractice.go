package practice

import (
	"fmt"
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/comm"
)

// UserPractice 用户练习明细
type UserPractice struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) index(IDX_UID) 'uid' comment('用户ID')"`
	// 计划ID
	ProgramID int64 `xorm:"not null int(11) 'program_id' comment('计划ID')"`
	// 课程ID
	SessionID int64 `xorm:"not null int(11) 'session_id' comment('课程ID')"`
	// 课程节数
	SessionIndex int32 `xorm:"not null int(4) 'session_index' comment('课程节数')"`
	// 子课程节数
	SubSessionIndex int32 `xorm:"not null int(4) 'sub_session_index' comment('子课程节数')"`
	// 完成次数
	CompleteTimes int32 `xorm:"not null int(4) 'complete_times' comment('完成次数')"`
	// user_action_log数据表
	UserActionTable int32 `xorm:"not null int(4) 'user_action_table' comment('user_action_log数据表')"`
	// 上报时间
	ReportTime int64 `xorm:"not null int(11) index(IDX_REPORT_PLAY_TIME) 'report_time' comment('上报时间')"`
	// 练习时长
	PlayTime int64 `xorm:"not null int(11) index(IDX_REPORT_PLAY_TIME) 'play_time' comment('练习时长')"`
	// 卡路里
	Calorie int64 `xorm:"not null int(11) 'calorie' comment('卡路里')"`
	// 练习感受
	Feel int32 `xorm:"not null tinyint(4) 'feel' comment('练习感受')"`
	// 练习动作数
	Actions int32 `xorm:"not null tinyint(4) 'actions' comment('练习动作数')"`
	// 是否投屏练习
	Screen int32 `xorm:"not null tinyint(2) 'screen' comment('是否投屏练习')"`
	// 客户端跳转类型
	LinkType   int32 `xorm:"not null tinyint(4) 'link_type' comment('客户端跳转类型')"`
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (u *UserPractice) TableName() string {
	if u.ReportTime < 1 {
		return ""
	}
	month := time.Unix(u.ReportTime, 0).Format("200601")
	return fmt.Sprintf("st_user_practice_%s", month)
}

// Save 保存数据
func (u *UserPractice) Save() error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime

	_, err := statrds.GetEngineMaster().Insert(u)
	return err
}

// Update 更新数据
func (u *UserPractice) Update() error {
	u.UpdateTime = time.Now().Unix()
	_, err := statrds.GetEngineMaster().ID(u.ID).Update(u)
	return err
}

func (u *UserPractice) UpdateMustColsCalorie() error {
	u.UpdateTime = time.Now().Unix()

	_, err := statrds.GetEngineMaster().ID(u.ID).MustCols("calorie").Update(u)
	return err
}

// UpdateByTransaction 事务更新数据
func (u *UserPractice) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()

	_, err := session.ID(u.ID).Update(u)
	return err
}

type userPractice struct{}

// TbUserPractice 外部引用对象
var TbUserPractice userPractice

func (u *userPractice) GetByID(id, reportTime int64) *UserPractice {
	var table UserPractice
	table.ReportTime = reportTime
	session := statrds.GetEngine().NewSession()
	defer session.Close()
	session = session.Table(table.TableName()).Where("id = ?", id)
	ok, err := session.Get(&table)
	if err != nil {
		logger.Error(err)
	}
	if !ok {
		return nil
	}
	return &table
}

// GetList 2020-03-06加入user_action_table 用于处理离线数据，当用户完成是11的时候离线上报数据为40的时候序号就会有问题
func (u *userPractice) GetList(req *UserPractice, reportTime int64) []UserPractice {
	var tables []UserPractice

	days := TbDay.GetList(req.UID, "desc")
	if days == nil || len(days) < 1 {
		return tables
	}
	monthArr := make([]string, 0)
	for _, v := range days {
		tUnix, _ := time.Parse("20060102", v.DateIndex)
		if tUnix.Unix() < reportTime {
			continue
		}
		month := v.DateIndex[:6]
		exist := false
		for _, val := range monthArr {
			if val == month {
				exist = true
				break
			}
		}
		if !exist {
			monthArr = append(monthArr, month)
		}
	}
	if len(monthArr) > 0 {
		for _, m := range monthArr {
			var practices []UserPractice
			tableName := fmt.Sprintf("st_user_practice_%s", m)
			session := statrds.GetEngine().NewSession()
			session = session.Table(tableName).Where("uid = ?", req.UID)
			if req.ProgramID > 0 {
				session = session.Where("program_id = ?", req.ProgramID)
			}
			if req.SessionID > 0 {
				session = session.Where("session_id = ?", req.SessionID)
			}
			session.Where("user_action_table =?", req.UserActionTable)
			err := session.Where("report_time >= ?", reportTime).Find(&practices)
			if err != nil {
				logger.Error(err)
			}
			tables = append(tables, practices...)
			session.Close()
		}
	}
	return tables
}

func (u *userPractice) GetItem(req *UserPractice) *UserPractice {
	var table UserPractice
	table.ReportTime = req.ReportTime
	session := statrds.GetEngine().NewSession()
	defer session.Close()
	session = session.Table(table.TableName()).Where("uid = ?", req.UID)
	if req.ReportTime > 0 {
		session = session.And("report_time = ?", req.ReportTime)
	}
	if req.ProgramID > 0 {
		session = session.And("program_id = ?", req.ProgramID)
	}
	if req.SessionID > 0 {
		session = session.And("session_id = ?", req.SessionID)
	}
	if req.SessionIndex > 0 {
		session = session.And("session_index = ?", req.SessionIndex)
	}
	if req.UserActionTable > 0 {
		session = session.And("user_action_table = ?", req.UserActionTable)
	}
	ok, err := session.Get(&table)
	if err != nil {
		logger.Error(err)
	}
	if !ok {
		return nil
	}
	return &table
}

// 2020-03-12 water 获取
func (u *userPractice) GetItemByReportTime(req *UserPractice, startTime, endTime int64) *UserPractice {
	var table UserPractice
	month := time.Unix(req.ReportTime, 0).Format("200601")
	tableName := fmt.Sprintf("st_user_practice_%s", month)
	session := statrds.GetEngine().NewSession()
	defer session.Close()
	session = session.Table(tableName).Where("uid = ?", req.UID)
	if req.ProgramID > 0 {
		session = session.Where("program_id = ?", req.ProgramID)
	}
	if req.SessionID > 0 {
		session = session.Where("session_id = ?", req.SessionID)
	}
	if req.UserActionTable > 0 {
		session = session.Where("user_action_table = ?", req.UserActionTable)
	}
	if startTime > 0 {
		session = session.Where("report_time >= ?", startTime)
	}
	if endTime > 0 {
		session = session.Where("report_time <= ?", endTime)
	}
	ok, err := session.Get(&table)
	if err != nil {
		logger.Error(err)
	}
	if !ok {
		return nil
	}
	return &table
}

func (u *userPractice) GetCompleteTimes(req *UserPractice) (sessionTimes, programTimes map[int64]int32) {
	completeTimesSession := make(map[int64]int32)
	completeTimesProgram := make(map[int64]int32)
	monthArr := make([]string, 0)
	days := TbDay.GetList(req.UID, "desc")
	if days == nil || len(days) < 1 {
		return completeTimesSession, completeTimesProgram
	}
	for _, v := range days {
		month := v.DateIndex[:6]
		exist := false
		for _, val := range monthArr {
			if val == month {
				exist = true
				break
			}
		}
		if !exist {
			monthArr = append(monthArr, month)
		}
	}
	if len(monthArr) == 0 {
		return completeTimesSession, completeTimesProgram
	}

	for _, m := range monthArr {
		var tables []UserPractice
		tableName := fmt.Sprintf("st_user_practice_%s", m)
		session := statrds.GetEngine().NewSession()
		session = session.Table(tableName).Where("uid = ?", req.UID)
		err := session.Cols("program_id", "session_id", "complete_times", "session_index",
			"user_action_table").Find(&tables)
		if err != nil {
			logger.Debugf("error: %s", err.Error())
		}
		if len(tables) == 0 {
			continue
		}
		for _, item := range tables {
			if item.CompleteTimes == 0 {
				continue
			}
			if item.UserActionTable == 11 {
				completeTime, ok := completeTimesProgram[item.ProgramID]
				if !ok {
					completeTimesProgram[item.ProgramID] = item.CompleteTimes
					continue
				}
				if completeTime < item.CompleteTimes {
					completeTimesProgram[item.ProgramID] = item.CompleteTimes
				}
				continue
			}
			if item.ProgramID == 0 && item.SessionIndex == 0 {
				completeTime, ok := completeTimesSession[item.SessionID]
				if !ok {
					completeTimesSession[item.SessionID] = item.CompleteTimes
					continue
				}
				if completeTime < item.CompleteTimes {
					completeTimesSession[item.SessionID] = item.CompleteTimes
				}
			}
		}
		session.Close()
	}
	return completeTimesSession, completeTimesProgram
}

func (u *userPractice) GetNotCollectList(startTime int64) []UserPractice {
	var tables []UserPractice

	tableName := (&UserPractice{ReportTime: startTime}).TableName()
	// Prince 强制使用索引 2020-03-18 13:41:45
	sql := fmt.Sprintf("SELECT * FROM %s FORCE INDEX (`IDX_REPORT_PLAY_TIME`) WHERE"+
		" `report_time` >= %d AND `play_time` < 1 ORDER BY `id` ASC", tableName, startTime)
	err := statrds.GetEngine().SQL(sql).Find(&tables)
	if err != nil {
		logger.Error(err)
	}
	return tables
}

// 2019年7月12日 14:03:39 water 修复训练营排行榜
func (u *userPractice) GetListByRangeTime(req *UserPractice, startTime, endTime int64) []UserPractice {
	var tables []UserPractice

	startTimeDate := time.Unix(startTime, 0).Format("20060102")
	endTimeDate := time.Unix(endTime, 0).Format("20060102")

	days := TbDay.GetListByRangeTime(req.UID, startTimeDate, endTimeDate)
	if len(days) < 1 {
		return tables
	}
	monthArr := make([]string, 0)
	for _, v := range days {
		tUnix, _ := time.Parse("20060102", v.DateIndex)
		if tUnix.Unix() < startTime {
			continue
		}
		month := v.DateIndex[:6]
		exist := false
		for _, val := range monthArr {
			if val == month {
				exist = true
				break
			}
		}
		if !exist {
			monthArr = append(monthArr, month)
		}
	}
	if len(monthArr) > 0 {
		for _, m := range monthArr {
			var practices []UserPractice
			tableName := fmt.Sprintf("st_user_practice_%s", m)
			session := statrds.GetEngine().NewSession()
			session = session.Table(tableName).Where("uid = ?", req.UID)
			if req.ProgramID > 0 {
				session = session.Where("program_id = ?", req.ProgramID)
			}
			if req.SessionID > 0 {
				session = session.Where("session_id = ?", req.SessionID)
			}
			if req.UserActionTable > 0 {
				session = session.Where("user_action_table = ?", req.UserActionTable)
			}
			session = session.Where("report_time <= ?", endTime)
			err := session.Where("report_time >= ?", startTime).Find(&practices)
			if err != nil {
				logger.Error(err)
			}
			tables = append(tables, practices...)
			session.Close()
		}
	}
	return tables
}

// GetListByTimeIndex
func (u *userPractice) GetListByTimeIndex(uid, startTime int64) []UserPractice {
	var practices []UserPractice
	month := time.Unix(startTime, 0).Format("200601")

	tableName := fmt.Sprintf("st_user_practice_%s", month)
	session := statrds.GetEngine().NewSession()
	session = session.Table(tableName).Where("uid = ?", uid)
	session = session.Where("report_time <= ?", startTime+86400)
	err := session.Where("report_time >= ?", startTime).Find(&practices)
	if err != nil {
		logger.Error(err)
	}
	session.Close()

	return practices
}

// 2019-02-02  water 获取用户的某个课程或者计划全部的数据
func (u *userPractice) GetAllList(req *UserPractice) []UserPractice {
	var tables []UserPractice
	days := TbDay.GetList(req.UID, "ASC")
	if days == nil || len(days) < 1 {
		return tables
	}

	monthArr := make([]string, 0)
	for _, v := range days {
		month := v.DateIndex[:6]
		exist := false
		for _, val := range monthArr {
			if val == month {
				exist = true
				break
			}
		}
		if !exist {
			monthArr = append(monthArr, month)
		}
	}
	logger.Debug("查出来的月份", monthArr)
	if len(monthArr) > 0 {
		for _, m := range monthArr {
			var practices []UserPractice
			tableName := fmt.Sprintf("st_user_practice_%s", m)
			session := statrds.GetEngine().NewSession()
			session = session.Table(tableName).Where("uid = ?", req.UID)
			if req.ProgramID > 0 {
				session = session.Where("program_id = ?", req.ProgramID)
			}
			if req.SessionID > 0 {
				session = session.Where("session_id = ?", req.SessionID)
			}
			if req.SessionIndex > 0 {
				session = session.Where("session_index = ?", req.SessionIndex)
			}
			if req.UserActionTable > 0 {
				session = session.Where("user_action_table = ?", req.UserActionTable)
			}
			err := session.Find(&practices)
			if err != nil {
				logger.Error(err)
			}
			tables = append(tables, practices...)
			session.Close()
		}
	}
	return tables
}

// GetLiveBroadcastItem 获取正在直播的数据
func (u *userPractice) GetLiveBroadcastItem(req *UserPractice) *UserPractice {
	var table UserPractice
	month := time.Unix(req.ReportTime, 0).Format("200601")
	tableName := fmt.Sprintf("st_user_practice_%s", month)
	session := statrds.GetEngine().NewSession()
	session = session.Table(tableName).Where("uid = ?", req.UID)
	if req.ProgramID > 0 {
		session = session.Where("program_id = ?", req.ProgramID)
	}
	if req.SessionID > 0 {
		session = session.Where("session_id = ?", req.SessionID)
	}
	if req.UserActionTable > 0 {
		session = session.Where("user_action_table = ?", req.UserActionTable)
	}
	session = session.Where("report_time >= ?", comm.FormatStartTime(req.ReportTime))
	session = session.Where("report_time <= ?", comm.FormatEndTime(req.ReportTime))
	ok, err := session.Get(&table)
	if err != nil {
		logger.Error(err)
	}
	if !ok {
		return nil
	}
	session.Close()
	return &table
}

// GetListByReportTime 	确定用户练习的数据
func (u *userPractice) GetListByReportTime(uid, reportTime int64) []UserPractice {
	var practices []UserPractice
	monthFormat := time.Unix(reportTime, 0).Format("200601")
	tableName := fmt.Sprintf("st_user_practice_%s", monthFormat)
	session := statrds.GetEngine().NewSession()
	session = session.Table(tableName).Where("uid = ?", uid)
	err := session.Where("report_time = ?", reportTime).Find(&practices)
	if err != nil {
		logger.Error(err)
	}
	session.Close()
	return practices
}
