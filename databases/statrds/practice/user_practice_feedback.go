package practice

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type UserPracticeFeedback struct {
	ID             int64  `xorm:"not null autoincr pk int(10) 'id'"`
	UserPracticeID int64  `xorm:"not null int(10) 'user_practice_id'"`
	DateIndex      string `xorm:"not null char(6) 'date_index'"`
	UID            int64  `xorm:"not null int(10) 'uid'"`
	SourceType     int32  `xorm:"not null tinyint(3) 'source_type'"`
	SourceID       int64  `xorm:"not null int(10) 'source_id'"`
	Version        string `xorm:"not null varchar(32) 'version'"`
	Feel           int32  `xorm:"not null tinyint(3) 'feel'"`
	Labels         string `xorm:"not null varchar(50) 'labels'"`
	Feedback       string `xorm:"not null varchar(800) 'feedback'"`
	CreateTime     int64  `xorm:"not null int(10) 'create_time'"`
	UpdateTime     int64  `xorm:"not null int(10) 'update_time'"`
}

// TableName 获取表名
func (u *UserPracticeFeedback) TableName() string {
	return "user_practice_feedback"
}

// Save 保存数据
func (u *UserPracticeFeedback) Save() bool {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime

	_, err := usercenter.GetEngineMaster().Insert(u)
	if err != nil {
		logger.Error(err)
		return false
	}
	return true
}

// 更新
func (u *UserPracticeFeedback) Update() bool {
	u.UpdateTime = u.CreateTime

	_, err := usercenter.GetEngineMaster().ID(u.ID).Update(u)
	if err != nil {
		logger.Error(err)
		return false
	}
	return true
}

// 保存或者更新
func (u *UserPracticeFeedback) SaveOrUpdate() bool {
	table := TbUserPracticeFeedback.GetItem(u.UserPracticeID, u.DateIndex)
	if table != nil {
		table.Version = u.Version
		table.Feel = u.Feel
		table.Labels = u.Labels
		table.Feedback = u.Feedback
		if !table.Update() {
			logger.Error("练习反馈更新失败, table: %#v", table)
			return false
		}
		return true
	}
	table = &UserPracticeFeedback{
		UserPracticeID: u.UserPracticeID,
		DateIndex:      u.DateIndex,
		UID:            u.UID,
		SourceType:     u.SourceType,
		SourceID:       u.SourceID,
		Version:        u.Version,
		Feel:           u.Feel,
		Labels:         u.Labels,
		Feedback:       u.Feedback,
	}
	if !table.Save() {
		logger.Errorf("练习反馈保存失败, table: %#v", table)
		return false
	}
	return true
}

type userPracticeFeedback struct{}

var TbUserPracticeFeedback userPracticeFeedback

// 查询
func (u userPracticeFeedback) GetItem(userPracticeID int64, dateIndex string) *UserPracticeFeedback {
	var table UserPracticeFeedback
	ok, err := usercenter.GetEngine().
		Where("user_practice_id = ?", userPracticeID).
		Where("date_index = ?", dateIndex).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
