package practice

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
)

// PlayDay 用户练习按天统计
type PlayDay struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) index(IDX_UID) 'uid' comment('用户ID')"`
	// 练习时长
	PlayTime int64 `xorm:"not null int(11) 'play_time' comment('实际练习时长')"`
	// 卡路里
	Calorie int32 `xorm:"not null int(11) 'calorie' comment('卡路里')"`
	// 练习次数
	PracticeCount int64 `xorm:"not null int(11) 'practice_count' comment('实际练习次数')"`
	// 日期：20190423
	DateIndex  string `xorm:"not null char(8) 'date_index'"`
	CreateTime int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64  `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (d PlayDay) TableName() string {
	if d.UID < 1 {
		return ""
	}
	var tableNum int64 = 32
	return fmt.Sprintf("st_user_play_day_%d", d.UID%tableNum)
}

// Save 保存数据
func (d *PlayDay) Save() error {
	d.CreateTime = time.Now().Unix()
	d.UpdateTime = d.CreateTime

	_, err := databases.GetChildren().GetEngineMaster().Insert(d)
	return err
}

// Update 更新数据
func (d *PlayDay) Update() error {
	d.UpdateTime = time.Now().Unix()

	_, err := databases.GetChildren().GetEngineMaster().ID(d.ID).Update(d)
	return err
}

type palyday struct{}

// TbPlayDay 外部引用对象
var TbPlayDay palyday

func (d *palyday) GetItem(uid int64, dateIndex string) *PlayDay {
	var table PlayDay

	table.UID = uid
	ok, err := databases.GetChildren().GetEngine().Where("uid = ? and date_index = ?", uid, dateIndex).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (d *palyday) GetList(uid int64) []PlayDay {
	var tables []PlayDay
	var table PlayDay

	table.UID = uid
	err := databases.GetChildren().GetEngine().Table(table.TableName()).
		Where("uid = ?", uid).Asc("date_index").Find(&tables)
	if err != nil {
		return nil
	}

	return tables
}

func (d *palyday) GetListByTimeRange(uid int64, startDate, endDate string) []*PlayDay {
	var tables []*PlayDay
	err := databases.GetChildren().GetEngine().Table(PlayDay{UID: uid}.TableName()).
		Where("uid = ? AND date_index >= ? AND date_index <= ?", uid, startDate, endDate).
		Asc("date_index").
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (d *palyday) GetMapByTimeRange(uid, startTime, endTime int64) map[string]*PlayDay {
	startDate := time.Unix(startTime, 0).Format("20060102")
	endDate := time.Unix(endTime, 0).Format("20060102")
	var tables []*PlayDay
	err := databases.GetChildren().GetEngine().Table(PlayDay{UID: uid}.TableName()).
		Where("uid = ? AND date_index >= ? AND date_index <= ?", uid, startDate, endDate).
		Find(&tables)
	res := make(map[string]*PlayDay)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if len(tables) == 0 {
		return res
	}
	for k := range tables {
		res[tables[k].DateIndex] = tables[k]
	}
	return res
}
