package practice

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
)

// Day 用户练习日期
type Day struct {
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid'"`
	// 练习日期
	DateIndex string `xorm:"not null varchar(16) 'date_index'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (Day) TableName() string {
	return "user_practice_day"
}

// Save 保存数据
func (p *Day) Save() error {
	p.CreateTime = time.Now().Unix()
	p.UpdateTime = p.CreateTime
	_, err := databases.GetChildren().GetEngineMaster().Insert(p)
	return err
}

// Update 更新数据
func (p *Day) Update() error {
	p.UpdateTime = time.Now().Unix()

	_, err := databases.GetChildren().GetEngineMaster().ID(p.ID).Update(p)
	return err
}

type practiceDay struct{}

// TbPracticeDay 外部引用对象
var TbPracticeDay practiceDay

// GetItem 获取练习天数详情
func (p *practiceDay) GetItem(uid int64, dateIndex string) *Day {
	var table Day

	ok, err := databases.GetChildren().GetEngine().
		Where("uid = ?", uid).And("date_index = ?", dateIndex).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetList 获取练习天数列表
func (p *practiceDay) GetList(uid int64) []Day {
	var tables []Day

	if err := databases.GetChildren().GetEngine().
		Where("uid = ?", uid).Asc("id").Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (p *practiceDay) GetListAscDateIndex(uid int64) []Day {
	var tables []Day
	var table Day

	table.UID = uid
	err := databases.GetChildren().GetEngine().Table(table.TableName()).
		Where("uid = ?", uid).Asc("date_index").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
