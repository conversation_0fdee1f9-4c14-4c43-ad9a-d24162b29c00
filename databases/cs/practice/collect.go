package practice

import (
	"fmt"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
)

// Collect 用户统计表
type Collect struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid'"`
	// 用户练习秒数
	TotalPlayTime int64 `xorm:"not null int(11) 'total_play_time'"`
	// 用户总消耗卡路里
	TotalCalorie int64 `xorm:"not null int(11) 'total_calorie'"`
	// 用户按天练习天数
	TotalPracticeDay int64 `xorm:"not null int(11) 'total_practice_day'"`
	// 最近连续练习天数
	LastContinuePracticeDay int64 `xorm:"not null int(11) 'last_continue_practice_day'"`
	CreateTime              int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime              int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (c *Collect) TableName() string {
	var tableNum int64 = 32
	return fmt.Sprintf("st_user_collect_%d", c.UID%tableNum)
}

func (c *Collect) Update() error {
	_, err := databases.GetChildren().GetEngineMaster().ID(c.ID).Update(c)
	if err != nil {
		return err
	}
	return nil
}

func (c *Collect) Save() error {
	c.UpdateTime = time.Now().Unix()
	c.CreateTime = c.UpdateTime
	_, err := databases.GetChildren().GetEngineMaster().Insert(c)

	if err != nil {
		return err
	}
	return nil
}

type userCollect struct{}

var TbUserCollect userCollect

func (c *userCollect) FindByUID(uid int64) *Collect {
	var table Collect
	table.UID = uid
	ok, err := databases.GetChildren().GetEngine().Table(table.TableName()).Where("uid = ?", uid).Get(&table)
	if err != nil {
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (c *userCollect) FindOrCreate(uid int64) (*Collect, error) {
	table := c.FindByUID(uid)

	if table != nil {
		return table, nil
	}

	var row Collect
	row.UID = uid
	row.LastContinuePracticeDay = 1
	row.TotalPracticeDay = 1
	if err := row.Save(); err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			table = c.FindByUID(uid)
			if table != nil {
				return table, nil
			}
		}
		return nil, err
	}
	return &row, nil
}
