package course

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// ObProgramCourse 用户ob计划课程列表
type ObProgramCourse struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// ob计划ID
	ProgramID int64 `xorm:"not null int(11) 'ob_program_id'"`
	// 课程ID
	CourseID int64 `xorm:"not null int(11) 'course_id'"`
	// 第几天
	Day int32 `xorm:"not null int(11) 'day'"`
	// 参与分表的UID
	UID int64 `xorm:"-"`
	// 练习时间
	PracticeDate int64 `xorm:"not null tinyint(1) 'practice_date'"`
	// 是否练习
	IsPractice int32 `xorm:"not null tinyint(1) 'is_practice'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (o *ObProgramCourse) TableName() string {
	return fmt.Sprintf("ob_program_course_%d", o.UID%32)
}

// Save 保存数据
func (o *ObProgramCourse) Save() error {
	o.CreateTime = time.Now().Unix()
	o.UpdateTime = o.CreateTime

	_, err := databases.GetChildren().GetEngineMaster().
		Table(&ObProgramCourse{ProgramID: o.ProgramID, UID: o.UID}).Insert(o)
	return err
}

// Update 更新数据
func (o *ObProgramCourse) Update() error {
	o.UpdateTime = time.Now().Unix()

	_, err := databases.GetChildren().GetEngineMaster().
		Table(&ObProgramCourse{ProgramID: o.ProgramID, UID: o.UID}).ID(o.ID).Update(o)
	return err
}

type obProgramCourse struct{}

// TbObProgramCourse 外部引用对象
var TbObProgramCourse obProgramCourse

// GetItem 获取ob计划课程详情
func (o *obProgramCourse) GetItem(uid, programID, courseID int64, orderDay int) *ObProgramCourse {
	var table ObProgramCourse

	ok, err := databases.GetChildren().GetEngine().
		Table((&ObProgramCourse{ProgramID: programID, UID: uid}).TableName()).
		Where("ob_program_id = ?", programID).
		And("course_id = ? ", courseID).
		And("day = ?", orderDay).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItem 获取ob计划课程详情
func (o *obProgramCourse) GetItemByPracticeDate(uid, programID, courseID, practiceDate int64) *ObProgramCourse {
	var table ObProgramCourse

	ok, err := databases.GetChildren().GetEngine().
		Table((&ObProgramCourse{ProgramID: programID, UID: uid}).TableName()).
		Where("ob_program_id = ?", programID).
		And("course_id = ? ", courseID).
		And("practice_date = ?", practiceDate).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetTotalFinish 获取ob计划完成数量
func (o *obProgramCourse) GetTotalFinish(uid, programID int64) int {
	var tables []ObProgramCourse

	err := databases.GetChildren().GetEngine().
		Table((&ObProgramCourse{ProgramID: programID, UID: uid}).TableName()).Where("ob_program_id = ?",
		programID).And("is_practice = ?", library.Yes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return 0
	}
	return len(tables)
}
