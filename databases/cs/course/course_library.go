package course

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
)

// DBCourse 课程库
type DBCourse struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 课程标题
	Title string `xorm:"not null varchar(255) 'title'"`
	// 课程描述
	Desc string `xorm:"not null varchar(255) 'desc'"`
	// 难度等级
	Level int32 `xorm:"not null tinyint(4) 'level'"`
	// 课程视频地址
	VideoURL string `xorm:"not null varchar(255) 'video_url'"`
	// 课程封面
	CoverURL string `xorm:"not null varchar(255) 'cover_url'"`
	// 课程横版封面
	HorizontalCoverURL string `xorm:"not null varchar(255) 'horizontal_cover_url'"`
	// 课程时长
	Duration float64 `xorm:"not null decimal(10,3) 'duration'"`
	// 是否VIP
	IsVIP int32 `xorm:"not null tinyint(1) 'is_vip'"`
	// 是否在线
	IsOnline int32 `xorm:"not null tinyint(1) 'is_online'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
	// 练习次数
	PracticeNum int64 `xorm:"not null int(11) 'practice_num'"`
	// 1 横屏 2 竖屏
	IsHorizontal int32 `xorm:"not null tinyint(1) 'is_horizontal'"`
	// 自定义标签
	CustomLabels string `xorm:"not null varchar(256) 'custom_labels'"`
	// 教练ID
	Coach int64 `xorm:"not null int(11) 'coach'"`
	// 注意事项
	Precautions string `xorm:"not null varchar(256) 'precautions'"`
	// 是否新课程
	IsNewCourse int32 `xorm:"not null tinyint(1) 'is_new_course'"`
	// 课程类型
	CourseType int32 `xorm:"not null int(11) 'course_type'"`
}

// TableName 获取表名
func (DBCourse) TableName() string {
	return "course_library"
}

// Save 保存数据
func (c *DBCourse) Save() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime

	_, err := databases.GetChildren().GetEngineMaster().Insert(c)
	return err
}

// Update 更新数据
func (c *DBCourse) Update() error {
	c.UpdateTime = time.Now().Unix()

	_, err := databases.GetChildren().GetEngineMaster().ID(c.ID).Update(c)
	return err
}

type clibrary struct {
}

var TbCourseLibrary clibrary

// GetItem 获取课程详情
func (s *clibrary) GetItem(courseID int64) *DBCourse {
	var table DBCourse

	ok, err := databases.GetChildren().GetEngine().Where("id = ?", courseID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
