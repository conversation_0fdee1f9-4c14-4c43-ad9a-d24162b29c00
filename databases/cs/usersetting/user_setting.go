package usersetting

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases"
)

type UserSetting struct {
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 用户uid
	UID int64 `xorm:"int(11) 'uid'"`
	// 用户设置key
	Key string `xorm:"not null default '0' varchar(128) 'key'"`
	// 用户设置value
	Value string `xorm:"not null default '''' varchar(1024) 'value'"`
	// 是否删除 1是 2否
	IsDelete int32 `xorm:"not null default '1' tinyint(2) unsigned 'is_delete'"`
	// 创建时间
	CreateTime int64 `xorm:"not null default '0' int(11) unsigned 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null default '0' int(11) unsigned 'update_time'"`
}

// TableName 获取表名
func (u UserSetting) TableName() string {
	return fmt.Sprintf("user_setting_%d", u.UID%32)
}

// Save 插入数据
func (u UserSetting) Save() error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime
	_, err := db.GetChildren().GetEngineMaster().Insert(u)
	return err
}

// Update 更新数据
func (u UserSetting) Update() error {
	u.UpdateTime = time.Now().Unix()

	_, err := db.GetChildren().GetEngineMaster().ID(u.ID).Update(u)
	return err
}

type userSetting struct {
}

var TbUserSetting userSetting

// GetItem 获取用户配置信息
func (u *userSetting) GetItem(uid int64, key string) (*UserSetting, error) {
	var table UserSetting
	ok, err := db.GetChildren().GetEngine().Table(
		UserSetting{
			UID: uid,
		}.TableName()).Where("uid = ?", uid).Where("`key` = ?", key).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil, err
	}

	if !ok {
		return nil, nil
	}
	return &table, nil
}
