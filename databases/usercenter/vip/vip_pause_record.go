package vip

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// PauseRecord 会员暂停信息表
type PauseRecord struct {
	ID  int64 `xorm:"int(11) notnull pk autoincr 'id'"` // 订单号
	UID int64 `xorm:"'uid'"`
	// 暂停时的会员等级 1.普通，2.VIP，3.YVIP, 4.SVIP，5.YSVIP
	MemberLevel  library.MemberLevel
	PauseTime    int64
	ContinueTime int64
	CreateTime   int64
	UpdateTime   int64
}

func (*PauseRecord) TableName() string {
	return "vip_pause_record"
}

// Save 保存
// 2018-08-12 10:23:15 庞晓楠 创建
func (v *PauseRecord) Save() error {
	currUnixTime := time.Now().Unix()

	v.UpdateTime = currUnixTime
	if v.ID <= 0 {
		v.CreateTime = currUnixTime
		_, err := db.GetEngineMaster().Insert(v)
		return err
	}
	_, err := db.GetEngineMaster().ID(v.ID).Update(v)
	return err
}

type vipPauseRecord struct{}

var TbVIPPauseRecord vipPauseRecord

// GetInnerRecords 获取一个时间区间内的数据
// 2018-03-13 11:57:01 庞晓楠 创建
func (*vipPauseRecord) GetInnerRecords(t1, t2 int64) (tables []PauseRecord, err error) {
	err = db.GetEngine().Where("pause_time>=? and pause_time<=? or continue_time>=? and continue_time<=?",
		t1, t2, t1, t2).Find(&tables)
	return
}

// GetUserRecords 获取用户暂停记录
// 2018-06-12 11:09:51 庞晓楠 创建
func (*vipPauseRecord) GetUserRecords(uid int64) []PauseRecord {
	var tables []PauseRecord
	err := db.GetEngine().Where("uid=?", uid).OrderBy("id").Find(&tables)
	if err != nil {
		logger.Error(err)
	}
	return tables
}

// DeleteByUID 根据UID删除所有记录，慎用。
// 2018-07-24 09:49:24 庞晓楠 创建
func (vipPauseRecord) DeleteByUID(uid int64) error {
	_, err := db.GetEngineMaster().Where("uid=?", uid).Delete(new(PauseRecord))
	if err != nil {
		logger.Error(err)
		return fmt.Errorf("删除用户会员暂停记录失败, %d, %v", uid, err)
	}
	return nil
}

// IsPause 当前是否暂停状态
// 2018-09-03 15:12:41 庞晓楠 创建
func (vipPauseRecord) IsPause(uid int64) bool {
	tables := TbVIPPauseRecord.GetUserRecords(uid)
	if len(tables) > 0 && tables[len(tables)-1].ContinueTime <= 0 {
		return true
	}
	return false
}
