package vip

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
)

type AdminVipRecord struct {
	ID               int64  `json:"id" xorm:"id"`
	AdminUID         int64  `json:"admin_uid" xorm:"admin_uid"`
	OperatorName     string `json:"operator_name" xorm:"operator_name"` // 操作人名称
	Uids             string `json:"uids" xorm:"uids"`
	SourceType       int64  `json:"source_type" xorm:"source_type"` // 会员来源：101 批量添加会员
	SourceID         int64  `json:"source_id" xorm:"source_id"`
	StartTime        int64  `json:"start_time" xorm:"start_time"`                 // 变更开始时间
	EndTime          int64  `json:"end_time" xorm:"end_time"`                     // 变更结束时间
	VipType          int32  `json:"vip_type" xorm:"vip_type"`                     // 会员类型
	VipDurationType  int32  `json:"vip_duration_type" xorm:"vip_duration_type"`   // 会员期限类型：1天、2月、3年
	VipDurationValue int64  `json:"vip_duration_value" xorm:"vip_duration_value"` // 会员期限值
	PushTitle        string `json:"push_title" xorm:"push_title"`                 // 推送标题
	PushContent      string `json:"push_content" xorm:"push_content"`             // 推送内容
	Remark           string `json:"remark" xorm:"remark"`                         // 备注
	CreateTime       int64  `json:"create_time" xorm:"create_time"`
	UpdateTime       int64  `json:"update_time" xorm:"update_time"`
}

// TableName 表名称
func (*AdminVipRecord) TableName() string {
	return "admin_vip_record"
}

func (p *AdminVipRecord) Save() error {
	p.CreateTime = time.Now().Unix()
	p.UpdateTime = p.CreateTime
	_, err := databases.GetUserCenter().GetEngineMaster().Insert(p)
	return err
}
