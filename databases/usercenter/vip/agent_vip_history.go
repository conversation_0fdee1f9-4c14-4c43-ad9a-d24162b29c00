package vip

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// 活动领奖表
type AgentHistory struct {
	ID int64 `xorm:"not null autoincr pk bigint(20) unsigned 'id'"`
	// 用户uid
	UID int64 `xorm:"not null int(11) default '0' 'uid'"`
	// 手机号
	Mobile string `xorm:"not null default '' varchar(128) 'Mobile'"`
	// 代理商id
	StoreID int64 `xorm:"not null int(11) 'store_id'"`
	// 是否成功
	Success int64 `xorm:"not null tinyint(11) 'is_success'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) unsigned 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) unsigned 'update_time'"`
}

// TableName 获取表名
func (AgentHistory) TableName() string {
	return "vip_agent_history"
}

type agentHistory struct {
}

var TbAgentHistory agentHistory

// GetInfoByUidOrPhone 获取代理商充值记录
func (b *agentHistory) GetInfoByUIDOrPhone(uid int64, phone string, createTime int64) *AgentHistory {
	var table AgentHistory

	_, err := usercenter.GetEngine().Where("(uid = ? or mobile = ?) and is_success=1 and create_time<? ",
		uid, phone, createTime).Asc("id").Limit(1).Get(&table)
	if err != nil {
		logger.Warn("查询代理商信息失败:", err)
		return &table
	}
	return &table
}
