package equity

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// WebPreferentialPackageDetail 特惠套餐详情
type WebPreferentialPackageDetail struct {
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 特惠套餐ID
	PackageID int64 `xorm:"not null int(11) unsigned 'package_id'"`
	// 配置项 0-会员卡,1.名师课堂无限卡,2.减脂塑形无限卡,3.新手进阶无限卡
	EquityType int32 `xorm:"not null tinyint(1) unsigned 'equity_type'"`
	// 详情
	EquityData int32 `xorm:"not null int(11) unsigned 'equity_data'"`
	// 1.正常，2.删除
	Status     int32 `xorm:"not null tinyint(1) 'status'"`
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
	SharePrice int64 `xorm:"not null int(11) 'share_price'"` // 分成价格，单位：分
}

// TableName 获取表名
func (WebPreferentialPackageDetail) TableName() string {
	return "web_preferential_package_detail"
}

type webPreferentialPackageDetail struct {
}

var TbWebPreferentialPackageDetail webPreferentialPackageDetail

// GetItem 获取特惠套餐详情信息
func (w *webPreferentialPackageDetail) GetList(packageID int64) ([]WebPreferentialPackageDetail, error) {
	var table []WebPreferentialPackageDetail
	err := usercenter.GetEngine().Where("package_id = ?", packageID).Asc("equity_type").Find(&table)
	if err != nil {
		logger.Errorf("获取特惠套餐详情信息失败, err: %s", err)
		return nil, err
	}

	return table, nil
}
