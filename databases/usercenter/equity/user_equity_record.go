package equity

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type UserEquityRecord struct {
	ID  int64 `xorm:"int(11) unsigned notnull pk autoincr 'id'" json:"id"`
	UID int64 `xorm:"int(11) unsigned notnull 'uid'" json:"uid"`
	// 权益资源类型：1-名师课堂权益 2-减脂塑形 3-进阶训练营
	EquityType int32 `xorm:"tinyint(1) unsigned notnull 'equity_type'" json:"equity_type"`
	// 开始时间
	StartTime int64 `xorm:"int(11) unsigned notnull 'start_time'" json:"start_time"`
	// 结束时间
	EndTime int64 `xorm:"int(11) unsigned notnull 'end_time'" json:"end_time"`
	// '状态：1-购买，2-赠送，3-后台，4-系统转换'
	RecordType int32 `xorm:"tinyint(2) unsigned notnull 'record_type'" json:"record_type"`
	// 资源期限类型：1-天、2-月、3-年
	EquityDurationType int32 `xorm:"tinyint(2) unsigned notnull 'equity_duration_type'" json:"equity_duration_type"`
	// 操作类型：1-加，2-减
	OperationType int32 `xorm:"tinyint(1) unsigned notnull 'operation_type'" json:"operation_type"`
	// 期限值
	EquityDurationValue int64  `xorm:"int(11) unsigned notnull 'equity_duration_value'" json:"equity_duration_value"`
	Desc                string `xorm:"varchar(255) notnull 'desc'" json:"desc"`
	SourceType          int32  `xorm:"tinyint(2) unsigned notnull default 0 'source_type'" json:"source_type"`
	SourceID            int64  `xorm:"int(11) unsigned notnull default 0 'source_id'" json:"source_id"`
	// 展示状态：1，展示，2不展示
	ShowStatus int32  `xorm:"int(11) unsigned notnull default 1 'show_status'" json:"show_status"`
	Reason     string `xorm:"varchar(255) notnull 'reason'" json:"reason"`
	AdminName  string `xorm:"varchar(20) notnull 'admin_name'" json:"admin_name"`
	CreateTime int64  `xorm:"int(11) notnull 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"int(11) notnull 'update_time'" json:"update_time"`
}

// TableName Get table name
func (u *UserEquityRecord) TableName() string {
	return "user_equity_record"
}

// Save 保存
func (u *UserEquityRecord) SaveByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.Insert(u)
	return err
}

// update 修改
func (u *UserEquityRecord) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type _UserEquityRecord struct{}

// TbUserEquityRecord 外部调用静态变量
var TbUserEquityRecord _UserEquityRecord

func (u *_UserEquityRecord) GetItem(uid int64, equityType int32) *UserEquityRecord {
	var table UserEquityRecord
	ok, err := db.GetEngine().Where("uid=? and equity_type=?", uid, equityType).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
