package equity

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type UserEquityDurationHistory struct {
	ID                   int64 `xorm:"int(11) unsigned notnull pk autoincr 'id'" json:"id"`
	UID                  int64 `xorm:"int(11) unsigned 'uid'" json:"uid"`
	UserEquityDurationID int64 `xorm:"int(11) unsigned 'user_equity_duration_id'" json:"user_equity_duration_id"`
	UserEquityRecordID   int64 `xorm:"int(11) unsigned 'user_equity_record_id'" json:"user_equity_record_id"`
	// 权益资源类型：1-名师课堂权益 2-减脂塑形 3-进阶训练营
	EquityType      int32 `xorm:"tinyint(1) unsigned 'equity_type'" json:"equity_type"`
	StartTime       int64 `xorm:"int(11) unsigned 'start_time'" json:"start_time"`               // 开始时间
	EndTime         int64 `xorm:"int(11) unsigned 'end_time'" json:"end_time"`                   // 结束时间
	ChangeStartTime int64 `xorm:"int(11) unsigned 'change_start_time'" json:"change_start_time"` // 变更后开始时间
	ChangeEndTime   int64 `xorm:"int(11) unsigned 'change_end_time'" json:"change_end_time"`     // 开始时间
	CreateTime      int64 `xorm:"int(11) unsigned 'create_time'" json:"create_time"`
	UpdateTime      int64 `xorm:"int(11) unsigned 'update_time'" json:"update_time"`
}

// TableName Get table name
func (u *UserEquityDurationHistory) TableName() string {
	return "user_equity_duration_history"
}

// Save 保存
func (u *UserEquityDurationHistory) SaveByTransaction(session *xorm.Session) error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime
	_, err := session.Insert(u)
	return err
}

// update 修改
func (u *UserEquityDurationHistory) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type _UserEquityDurationHistory struct{}

// TbUserEquityDurationHistory 外部调用静态变量
var TbUserEquityDurationHistory _UserEquityDurationHistory

func (u *_UserEquityDurationHistory) GetItem(uid int64, equityType int32) *UserEquityDurationHistory {
	var table UserEquityDurationHistory
	ok, err := db.GetEngine().Where("uid=? and equity_type=?", uid, equityType).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
