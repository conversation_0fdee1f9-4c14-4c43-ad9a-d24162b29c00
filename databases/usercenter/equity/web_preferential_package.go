package equity

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// WebPreferentialPackage 特惠套餐
type WebPreferentialPackage struct {
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 产品配置名称
	Name string `xorm:"not null varchar(255) 'name'"`
	// 套餐说明
	PackageDes string `xorm:"not null varchar(900) 'package_des'"`
	// 原价(分)
	OriginalPrice int32 `xorm:"not null default '0' int(6) unsigned 'original_price'"`
	// 售价(分)
	Price int32 `xorm:"not null default '0' int(6) unsigned 'price'"`
	// 分账金额(分)
	SeparatePrice int32 `xorm:"not null default '0' int(6) unsigned 'separate_price'"`
	// 排序字段
	Sort int32 `xorm:"not null default '0' tinyint(3) unsigned 'sort'"`
	// 列表背景图图标
	ListBackgroundIcon string `xorm:"not null varchar(128) 'list_background_icon'"`
	// 详情背景图图标
	DetailBackgroundIcon string `xorm:"not null varchar(128) 'detail_background_icon'"`
	// 页面内容
	PageContent string `xorm:"not null varchar(2048) 'page_content'"`
	// 上线状态：1-已上线，2-已下线
	OnlineStatus int32 `xorm:"not null default '1' tinyint(1) unsigned 'online_status'"`
	// 1 单次购买 2 订阅
	PurchaseType int32 `xorm:"not null default '1' tinyint(1) unsigned 'purchase_type'"`
	// 1.正常，2.删除
	Status     int32 `xorm:"not null default '1' tinyint(1) 'status'"`
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
	OfferType  int64 `json:"offer_type" xorm:"'offer_type'"` // 优惠类型：1-无 2-首购 3-试用 4-试用+首购
	// 首购优惠价
	OfferFirstBuyPrice float64 `xorm:"default 0.00 DECIMAL(10,2) 'offer_first_buy_price'" json:"offer_first_buy_price"`
	// 首购优惠周期
	OfferFirstBuyCycle int `xorm:"not null default 0 TINYINT(1) 'offer_first_buy_cycle'" json:"offer_first_buy_cycle"`
	// 试用价格
	OfferTrialPrice float64 `xorm:"default 0.00 DECIMAL(10,2) 'offer_trial_price'" json:"offer_trial_price"`
	// 试用天数
	OfferTrialDay int `xorm:"not null default 0 INT(5) 'offer_trial_day'" json:"offer_trial_day"`
	// 首购价格(分)
	FirstPrice int32 `xorm:"not null default '0' int(6) unsigned 'first_price'"`
}

// TableName 获取表名
func (WebPreferentialPackage) TableName() string {
	return "web_preferential_package"
}

type webPreferentialPackage struct {
}

var TbWebPreferentialPackage webPreferentialPackage

// GetItem 获取特惠套餐信息
func (w *webPreferentialPackage) GetItem(packageID int64) *WebPreferentialPackage {
	var table WebPreferentialPackage

	ok, err := usercenter.GetEngine().Where("id = ?", packageID).Get(&table)
	if err != nil {
		logger.Errorf("获取特惠套餐信息失败, err: %s", err)
		return nil
	}
	if !ok {
		return nil
	}

	return &table
}
