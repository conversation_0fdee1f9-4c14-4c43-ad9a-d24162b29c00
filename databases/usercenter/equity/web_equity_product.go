package equity

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// WebEquityProduct 特权卡产品列表
type WebEquityProduct struct {
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 产品配置名称
	Name string `xorm:"not null varchar(255) 'name'"`
	// 权益资源类型：1-名师课堂权益 2-减脂塑形 3-进阶训练营
	EquityType int32 `xorm:"not null tinyint(1) unsigned 'equity_type'"`
	// 原价
	OriginalPrice int32 `xorm:"not null default '0' int(6) unsigned 'original_price'"`
	// 售价
	Price int32 `xorm:"not null default '0' int(6) unsigned 'price'"`
	// 主要针对equity_type有关联值保存
	Data int32 `xorm:"not null default '0' int(11) unsigned 'data'"`
	// 资源期限类型：1-天、2-月、3-年
	EquityDurationType int32 `xorm:"not null tinyint(2) unsigned 'equity_duration_type'"`
	// 期限值
	EquityDurationValue int32 `xorm:"not null int(4) 'equity_duration_value'"`
	CreateTime          int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime          int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (WebEquityProduct) TableName() string {
	return "web_equity_product"
}

type webEquityProduct struct {
}

var TbWekEquityProduct webEquityProduct

// GetItem 获取特权卡信息
func (w *webEquityProduct) GetItem(productID int64) *WebEquityProduct {
	var table WebEquityProduct

	ok, err := usercenter.GetEngine().Where("id = ?", productID).Get(&table)
	if err != nil {
		logger.Errorf("获取特权卡产品失败, err: %s", err)
		return nil
	}
	if !ok {
		return nil
	}

	return &table
}
