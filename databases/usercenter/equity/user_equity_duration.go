package equity

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type UserEquityDuration struct {
	ID  int64 `xorm:"int(11) unsigned notnull pk autoincr 'id'" json:"id"`
	UID int64 `xorm:"int(11) unsigned notnull 'uid'" json:"uid"`
	// 权益资源类型：1-名师课堂权益 2-减脂塑形 3-进阶训练营
	EquityType int32 `xorm:"tinyint(1) unsigned notnull 'equity_type'" json:"equity_type"`
	StartTime  int64 `xorm:"bigint(20) unsigned notnull 'start_time'" json:"start_time"` // 开始时间
	EndTime    int64 `xorm:"bigint(20) unsigned notnull 'end_time'" json:"end_time"`     // 结束时间
	Status     int32 `xorm:"tinyint(2) unsigned notnull 'status'" json:"status"`         // 状态：1，正常，2过期
	CreateTime int64 `xorm:"int(11) unsigned notnull 'create_time'" json:"create_time"`
	UpdateTime int64 `xorm:"int(11) unsigned notnull 'update_time'" json:"update_time"`
}

// TableName Get table name
func (u *UserEquityDuration) TableName() string {
	return "user_equity_duration"
}

// Save 保存
func (u *UserEquityDuration) SaveByTransaction(session *xorm.Session) error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime
	_, err := session.Insert(u)
	return err
}

// update 修改
func (u *UserEquityDuration) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

// Update 更新数据
func (u *UserEquityDuration) Update() error {
	u.UpdateTime = time.Now().Unix()
	_, err := db.GetEngineMaster().ID(u.ID).Update(u)
	return err
}

type _UserEquityDuration struct{}

// TbUserEquityDuration 外部调用静态变量
var TbUserEquityDuration _UserEquityDuration

// GetItem 获取数据
func (c *_UserEquityDuration) GetItem(uid int64, equityType int32) *UserEquityDuration {
	var table UserEquityDuration
	ok, err := db.GetEngineMaster().Where("uid = ? and equity_type = ?", uid, equityType).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetUserNumByEndTime 用于定时任务
func (c *_UserEquityDuration) GetUserNumByEndTime(startTime, endTime, status int64) int64 {
	total, err := db.GetEngine().Where("end_time > ? AND end_time <= ? AND status = ?", startTime, endTime, status).
		Count(&UserEquityDuration{})
	if err != nil {
		return 0
	}

	return total
}

// GetUserListByEndTime 用于定时任务
func (c *_UserEquityDuration) GetUserListByEndTime(startTime, endTime, status int64,
	page, pageSize int) []UserEquityDuration {
	var table []UserEquityDuration
	err := db.GetEngine().Where("end_time > ? AND end_time <= ? AND status = ?", startTime, endTime, status).
		Limit(pageSize, page*pageSize).Find(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return table
}

// 判断用户是否开通名师会员权益
func (c *_UserEquityDuration) ValidUserIsOpenKolEquityDuration(uid int64, equity int32) bool {
	var info UserEquityDuration
	now := time.Now().Unix()
	isOk, err := db.GetEngine().
		Where("uid = ? and equity_type = ? ", uid, equity).
		Where("status = 1 and start_time <= ? AND end_time >= ?", now, now).
		Get(&info)
	if err != nil {
		logger.Error(err)
		return false
	}
	return isOk
}

// GetList 获取用户权益列表
func (c *_UserEquityDuration) GetList(uid int64) []*UserEquityDuration {
	var table []*UserEquityDuration
	err := db.GetEngineMaster().Where("uid = ? and status = ?", uid, 1).Find(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return table
}
