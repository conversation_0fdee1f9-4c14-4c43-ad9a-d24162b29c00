package assess

import (
	"fmt"
	"time"
)

type Log struct {
	ID          int64 `xorm:"not null autoincr pk unsigned int(11) unsigned comment('ID 主键') 'id'"`
	AssessID    int64 `xorm:"not null int(11) comment('测评产品id') 'assess_id'"`
	ModuleID    int64 `xorm:"not null int(11) comment('模块id') 'module_id'"`
	DetailID    int64 `xorm:"not null int(11) comment('测评测试项id') 'detail_id'"`
	AnswerID    int64 `xorm:"not null int(11) comment('选择项id') 'answer_id'"`
	ToUserID    int64 `xorm:"not null int(11) comment('参与测评id') 'to_user_id'"`
	UID         int64 `xorm:"not null int(11) comment('用户id') 'uid' "`
	AnswerScore int64 `xorm:"not null int(11) comment('选择项得分') 'answer_score'"`
	CreateTime  int64 `xorm:"not null int(11) comment('创建时间') 'create_time'"`
	UpdateTime  int64 `xorm:"not null int(11) comment('修改时间') 'update_time' "`
}

func (t *Log) TableName() string {
	var month string
	if t.CreateTime > 0 {
		month = time.Unix(t.CreateTime, 0).Format("200601")
	} else if t.UpdateTime > 0 {
		month = time.Unix(t.UpdateTime, 0).Format("200601")
	}
	return fmt.Sprintf("assess_log_%s", month)
}
