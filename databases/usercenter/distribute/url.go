package distribute

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type URL struct {
	ID        int64  `xorm:"not null autoincr unsigned pk int(11) 'id'"`
	SkuCode   string `xorm:"not null varchar(50) 'sku_code'"`
	ShortURL  string `xorm:"not null varchar(255) 'short_url'"`
	OriginURL string `xorm:"not null varchar(255) 'origin_url'"`
	// 1未使用，2已使用，3异常
	Status     int32 `xorm:"not null tinyint(1) 'status'"`
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

func (URL) TableName() string {
	return "distribution_url"
}

func (u *URL) Save() error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime
	u.Status = 1

	_, err := usercenter.GetEngineMaster().Insert(u)
	return err
}
