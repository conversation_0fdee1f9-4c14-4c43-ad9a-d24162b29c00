package badge

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// Category 徽章系列配置表
type Category struct {
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 名称
	Name string `xorm:"not null default '' varchar(128) 'name'"`
	// 描述
	Desc string `xorm:"not null default '' varchar(256) 'desc'"`
	// 徽章系列任务类型 1-累计 2-周期 3-一次性
	Type int32 `xorm:"not null tinyint(2) unsigned 'type'"`
	// 徽章系列任务触发行为 1-练习 2-登录
	ProgressAction int32 `xorm:"not null tinyint(2) unsigned 'progress_action'"`
	// 徽章系列任务模式 1-累计 2-连续
	ProgressMode int32 `xorm:"not null tinyint(2) unsigned 'progress_mode'"`
	// 徽章系列任务单位 1-天 2-分钟
	ProgressUnit int32 `xorm:"not null tinyint(2) unsigned 'progress_unit'"`
	// 徽章系列点亮默认图
	HighlightImageURL string `xorm:"not null default '' varchar(128) 'highlight_image_url'"`
	// 徽章系列点亮默认图名称
	HighlightImageName string `xorm:"not null default '' varchar(128) 'highlight_image_name'"`
	// 徽章系列置灰默认图
	GrayImageURL string `xorm:"not null default '' varchar(128) 'gray_image_url'"`
	// 徽章系列置灰默认图
	GrayImageName string `xorm:"not null default '' varchar(128) 'gray_image_name'"`
	// 是否需要解锁 1-需要 2-不需要
	NeedUnlock int32 `xorm:"not null tinyint(2) unsigned 'need_unlock'"`
	// 解锁配置信息(默认文案就跳转信息)
	UnlockConfig string `xorm:"not null default '' varchar(256) 'unlock_config'"`
	// 按钮文案及跳转信息
	ButtonConfig string `xorm:"not null default '' varchar(256) 'button_config'"`
	// 分享文案
	ShareDesc string `xorm:"not null default '' varchar(256) 'share_desc'"`
	// 排序权重
	Order int32 `xorm:"not null int(11) unsigned 'order'"`
	// 是否展示 1-展示 2-隐藏
	IsShow int32 `xorm:"not null tinyint(2) unsigned 'is_show'"`
	// 状态 1-在线 2-下限
	DeleteStatus int32 `xorm:"not null tinyint(2) unsigned 'delete_status'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) unsigned 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) unsigned 'update_time'"`
	// 触发规则 1-生日 2-记录本 3-挑战赛 4-冥想 5-送生日祝福
	SourceType int32 `xorm:"not null tinyint(3) unsigned 'source_type'"`
	// 徽章下发场景 1-练习后 2-底导Tab 3-徽章墙
	FromTypeList string `xorm:"not null default '' varchar(64) 'from_type_list'"`
	// 开始时间
	StartTime int64 `xorm:"not null int(11) unsigned 'start_time'"`
	// 结束时间
	EndTime int64 `xorm:"not null int(11) unsigned 'end_time'"`
	// 徽章出发规则关联业务id列表
	ResourceContentList string `xorm:"not null default '' varchar(64) 'resource_content_list'"`
	// 徽章生效最小版本号
	MinVersion int64 `xorm:"not null int(11) unsigned 'min_version'"`
}

// TableName 获取表名
func (Category) TableName() string {
	return "badge_category_config"
}

type category struct {
}

var TbCategory category

// GetBadgeCategoryByCategoryID 获取徽章系列详情信息
func (c *category) GetBadgeCategoryByCategoryID(categoryID int64) (*Category, error) {
	var table Category

	ok, err := usercenter.GetEngine().Where("id = ? and is_show = ? and delete_status = ?",
		categoryID, DataStatusEnum.Valid, ShowStatusEnum.Show).Desc("`order`").Get(&table)
	if err != nil {
		logger.Warnf("查询徽章系列详情失败 err: %s", err)
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &table, nil
}

// GetBadgeCategoryList 获取徽章系列列表
func (c *category) GetBadgeCategoryList() ([]*Category, error) {
	var tables []*Category

	err := usercenter.GetEngine().Where("is_show = ? and delete_status = ?", DataStatusEnum.Valid, ShowStatusEnum.Show).
		Desc("`order`").Find(&tables)
	if err != nil {
		logger.Warnf("查询徽章系列列表失败 err: %s", err)
		return nil, nil
	}

	return tables, nil
}

// GetBadgeCategoryItemByID 获取徽章详情
func (c *category) GetBadgeCategoryItemByID(categoryID int64) (*Category, error) {
	var table Category

	ok, err := usercenter.GetEngine().ID(categoryID).Get(&table)
	if err != nil {
		logger.Warnf("查询徽章系列详情失败 err: %s", err)
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &table, nil
}
