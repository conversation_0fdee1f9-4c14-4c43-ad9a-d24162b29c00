package badge

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/cache"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// UserBadge
type UserBadge struct {
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 用户id
	UID int64 `xorm:"not null int(11) unsigned 'uid'"`
	// 徽章系列id
	CategoryID int64 `xorm:"not null int(11) unsigned 'category_id'"`
	// 徽章id
	BadgeID int64 `xorm:"not null int(11) unsigned 'badge_id'"`
	// 获取此徽章需要完成的目标值
	Goal int32 `xorm:"not null int(11) unsigned 'goal'"`
	// 徽章状态 1-未解锁 2-生成中 3-解锁 4-已绝版
	Status int32 `xorm:"not null tinyint(3) unsigned 'status'"`
	// 获得徽章时间
	ReceiveTime int64 `xorm:"not null int(11) unsigned 'receive_time'"`
	// 徽章是否已经下发 1-未下发 2-已下发
	ReadStatus int32 `xorm:"not null tinyint(2) unsigned 'read_status'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) unsigned 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) unsigned 'update_time'"`
	// 徽章类型
	BadgeType int32 `xorm:"not null tinyint(1) unsigned 'badge_type'"`
}

// TableName 获取表名
func (u *UserBadge) TableName() string {
	return fmt.Sprintf("user_badge_list_%d", u.UID%32)
}

func (u *UserBadge) Save() error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime

	_, err := usercenter.GetEngineMaster().Insert(u)
	return err
}

// 检查并写入
func (u *UserBadge) CheckAndSave() error {
	var table UserBadge

	rd := cache.GetLockRedis()
	lockKey := fmt.Sprintf("user_bage:CheckAndSave:uid:%d:category_id:%d:badge_id:%d", u.UID, u.CategoryID, u.BadgeID)
	defer func() {
		if err := rd.Del(context.Background(), lockKey).Err(); err != nil {
			logger.Error(err)
		}
	}()
	if lock, err := rd.SetNX(context.Background(), lockKey, 1, 1*time.Second).Result(); err != nil {
		return err
	} else if !lock {
		return nil
	}

	ok, err := usercenter.GetEngineMaster().
		Where("uid = ? AND category_id = ? AND badge_id = ?", u.UID, u.CategoryID, u.BadgeID).
		Get(&table)
	if err != nil {
		return err
	}

	if ok {
		return nil
	}
	return u.Save()
}

func (u *UserBadge) Update() error {
	if u.ID < 1 {
		return errors.New("未指定id")
	}

	u.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().Where("id = ?", u.ID).Update(u)
	return err
}

// UpdateBadgeReadStatusByTransaction 更新徽章下发状态
func (u *UserBadge) UpdateBadgeReadStatusByTransaction(uid int64, categoryIDList []string, status int32) error {
	var table UserBadge
	categoryIDStr := strings.Join(categoryIDList, ",")

	table.UID = uid
	sql := fmt.Sprintf("UPDATE %s SET read_status = %d, update_time = %d WHERE uid = %d AND category_id IN (%s)",
		table.TableName(), status, time.Now().Unix(), uid, categoryIDStr)
	_, err := usercenter.GetEngineMaster().Exec(sql)
	return err
}

type userBadge struct {
}

var TbUserBadge userBadge

// BatchInsert 批量插入
func (u *userBadge) BatchInsert(data []*UserBadge) error {
	_, err := usercenter.GetEngineMaster().Insert(data)
	if err != nil && !strings.HasPrefix(err.Error(), "Error 1062") {
		return err
	}

	return nil
}

// GetItem 获取用户徽章信息
func (u *userBadge) GetItem(uid, categoryID int64, goal int32) (*UserBadge, error) {
	var table UserBadge

	table.UID = uid
	ok, err := usercenter.GetEngine().Table(table.TableName()).
		Where("uid = ?", uid).Where("category_id = ?", categoryID).Where("goal = ?", goal).Get(&table)
	if err != nil {
		logger.Warnf("获取用户徽章信息失败 err: %s", err)
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &table, nil
}

// GetUserBadgeList 获取用户徽章列表
func (u *userBadge) GetUserBadgeList(uid int64) ([]*UserBadge, error) {
	var tables []*UserBadge

	var table UserBadge
	table.UID = uid

	err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ?", uid).Asc("category_id", "goal").Find(&tables)
	if err != nil {
		logger.Warnf("获取用户徽章列表失败 err: %s", err)
		return nil, err
	}

	return tables, nil
}

// 获取用户最新徽章.
func (u *userBadge) GetUserLatestBadge(uid int64) (*UserBadge, error) {
	var table UserBadge

	table.UID = uid
	ok, err := usercenter.GetEngine().Table(table.TableName()).
		Where("uid = ?", uid).Where("status = ?", 3).Desc("id").Get(&table)
	if err != nil {
		logger.Warnf("获取用户最新徽章出错 err: %s", err)
		return nil, err
	}
	if !ok {
		return nil, nil
	}

	return &table, nil
}

// 获取用户最新徽章信息.
func (u *userBadge) GetlatestBadgeInfo(id int64) (*Badge, error) {
	var table Badge

	table.ID = id
	ok, err := usercenter.GetEngine().Table(table.TableName()).Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Warnf("获取最新徽章信息出错 err: %s", err)
		return nil, err
	}
	if !ok {
		return nil, nil
	}

	return &table, nil
}

// GetUserObtainBadgeNum 获取用户获得的徽章数
func (u *userBadge) GetUserObtainBadgeNum(uid int64) int64 {
	var table UserBadge

	table.UID = uid
	total, err := usercenter.GetEngine().Table(table.TableName()).
		Where("uid = ?", uid).Where("status = ?", 3).Count(&UserBadge{})
	if err != nil {
		return 0
	}

	return total
}

func (u *userBadge) GetUserObtainBadgeNumByCategoryID(uid, categoryID int64) int64 {
	var table UserBadge
	table.UID = uid
	total, err := usercenter.GetEngine().Table(table.TableName()).
		Where("uid = ?", uid).Where("category_id = ?", categoryID).Count(&UserBadge{})
	if err != nil {
		return 0
	}

	return total
}

// GetUserBadgeListByCategoryID 获取用户对应徽章系列下获取的徽章列表
func (u *userBadge) GetUserBadgeListByCategoryID(uid, categoryID int64) ([]UserBadge, error) {
	var tables []UserBadge
	var table UserBadge

	table.UID = uid
	err := usercenter.GetEngine().Table(table.TableName()).
		Where("uid = ?", uid).Where("category_id = ?", categoryID).Find(&tables)
	if err != nil {
		logger.Warnf("获取用户徽章列表失败 err: %s", err)
		return nil, err
	}

	return tables, nil
}

// GetUserBadgeList 获取用户徽章列表
func (u *userBadge) GetUserBadgeListByStatus(uid int64, status int32, page, pageSize int) ([]UserBadge, error) {
	var tables []UserBadge
	var table UserBadge
	offSize := (page - 1) * pageSize

	table.UID = uid
	err := usercenter.GetEngine().Table(table.TableName()).
		Where("uid = ?", uid).
		Where("status = ?", status).
		Desc("receive_time", "category_id", "goal").Limit(pageSize, offSize).Find(&tables)
	if err != nil {
		logger.Warnf("获取用户徽章列表失败 err: %s", err)
		return nil, err
	}

	return tables, nil
}

// GetUserBadgeByBadgeID 获取用户单个徽章
func (u *userBadge) GetUserBadgeByBadgeID(uid, badgeID int64) (*UserBadge, error) {
	var table UserBadge

	table.UID = uid
	ok, err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ? and badge_id = ?", uid, badgeID).Get(&table)
	if err != nil {
		logger.Warnf("获取用户指定徽章列表失败 err: %s", err)
		return nil, err
	}
	if !ok {
		return nil, nil
	}

	return &table, nil
}
