package badge

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// Badge 徽章信息配置表
type Badge struct {
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 徽章系列id
	CategoryID int64 `xorm:"not null int(11) unsigned 'category_id'"`
	// 徽章系列任务类型 1-累计 2-周期 3-一次性
	CategoryType int32 `xorm:"not null tinyint(2) unsigned 'category_type'"`
	// 徽章名称
	Name string `xorm:"not null default '' varchar(128) 'name'"`
	// 徽章描述
	Desc string `xorm:"not null default '' varchar(128) 'desc'"`
	// 完成目标值
	Goal int32 `xorm:"not null int(11) unsigned 'goal'"`
	// 徽章默认高亮图
	HighlightImageURL string `xorm:"not null default '' varchar(128) 'highlight_image_url'"`
	// 徽章默认高亮图名称
	HighlightImageName string `xorm:"not null default '' varchar(128) 'highlight_image_name'"`
	// 徽章默认灰图
	GrayImageURL string `xorm:"not null default '' varchar(128) 'gray_image_url'"`
	// 徽章默认灰图名称
	GrayImageName string `xorm:"not null default '' varchar(128) 'gray_image_name'"`
	// 1-使用徽章系列按钮配置 2-自定义配置
	IsUseCategoryButtonConfig int32 `xorm:"not null tinyint(2) unsigned 'is_use_category_button_config'"`
	// 跳转按钮配置
	ButtonConfig string `xorm:"not null default '' varchar(256) 'button_config'"`
	// 1-使用徽章系列分享文案 2-自定义分享文案
	IsUseCategoryShareConfig int32 `xorm:"not null tinyint(2) unsigned 'is_use_category_share_config'"`
	// 分享文案
	ShareDesc string `xorm:"not null default '' varchar(256) 'share_desc'"`
	// 排序权重
	Order int32 `xorm:"not null int(11) unsigned 'order'"`
	// 是否展示 1-展示 2-隐藏
	IsShow int32 `xorm:"not null tinyint(2) unsigned 'is_show'"`
	// 状态 1-在线 2-删除
	DeleteStatus int32 `xorm:"not null tinyint(2) unsigned 'delete_status'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) unsigned 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) unsigned 'update_time'"`
	// 徽章类型 1-徽章  2-隐藏徽章 3-大徽章
	BadgeType int32 `xorm:"not null tinyint(1) unsigned 'badge_type'"`
	// 是否使用徽章系列出发规则 1-系列默认 2-自定义
	IsUseCategorySourceType int32 `xorm:"not null tinyint(2) unsigned 'is_use_category_source_type'"`
	// 触发规则 1-生日 2-记录本 3-挑战赛 4-冥想 5-送生日祝
	SourceType int32 `xorm:"not null tinyint(1) unsigned 'source_type'"`
	// 徽章出发规则关联业务id列
	ResourceContentList string `xorm:"not null default '' varchar(32) 'resource_content_list'"`
	// 是否使用徽章系列下发场景 1-系列默认 2-自定义
	IsUseCategoryFromTypeList int32 `xorm:"not null tinyint(2) unsigned 'is_use_category_from_type_list'"`
	// 徽章下发场景
	FromTypeList string `xorm:"not null default '' varchar(32) 'from_type_list'"`
	// 独立于徽章系列的开始时间,如果是0则跟着徽章系列时间走
	StartTime int64 `xorm:"'start_time'"`
	// 独立于徽章系列的结束时间,如果是0则跟着徽章系列时间走
	EndTime int64 `xorm:"'end_time'"`
}

// TableName 获取表名
func (Badge) TableName() string {
	return "badge_config"
}

type badge struct {
}

var TbBadge badge

// GetBadgeInfoByID 获取徽章详情信息
func (b *badge) GetBadgeInfoByID(badgeID int64) (*Badge, error) {
	var table Badge

	ok, err := usercenter.GetEngine().Where("id = ? and is_show = ? and delete_status = ?",
		badgeID, DataStatusEnum.Valid, ShowStatusEnum.Show).Get(&table)
	if err != nil {
		logger.Warnf("查询徽章详情信息失败, err: %s", err)
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &table, nil
}

// GetBadgeListByCategoryID 获取徽章系列下徽章列表
func (b *badge) GetBadgeListByCategoryID(categoryID int64) ([]Badge, error) {
	var tables []Badge

	err := usercenter.GetEngine().Where("category_id = ? and is_show = ? and delete_status = ?",
		categoryID, DataStatusEnum.Valid, ShowStatusEnum.Show).Asc("`order`").Find(&tables)
	if err != nil {
		logger.Warnf("获取徽章列表失败, err: %s", err)
		return nil, err
	}

	return tables, nil
}

// GetBadgeList 获取徽章列表
func (b *badge) GetBadgeList() ([]*Badge, error) {
	var tables []*Badge

	err := usercenter.GetEngine().Where("is_show = ? and delete_status = ?", DataStatusEnum.Valid, ShowStatusEnum.Show).
		Asc("category_id", "`order`").Find(&tables)
	if err != nil {
		logger.Warnf("获取徽章列表失败, err: %s", err)
		return nil, err
	}

	return tables, nil
}
