package point

import (
	"time"

	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type Pointtopic struct {
	ID          int64 `xorm:"int(11) unsigned notnull pk autoincr 'id'" json:"id"`
	UID         int64 `xorm:"int(11) unsigned notnull 'uid'" json:"uid"`
	CreateTimes int64 `xorm:"not null int(11) 'createtime'"`
	Points      int32 `xorm:"not null int(11) 'points'"`
	Type        int64 `xorm:"not null tinyint(1) 'type'"`
	ObjID       int64 `xorm:"not null int(11) 'objId'"`
	Prepoints   int64 `xorm:"not null int(11) 'prepoints'"`
	Curpoints   int64 `xorm:"not null int(11) 'curpoints'"`
	CreateTime  int64 `xorm:"not null int(10) 'create_time'"`
	UpdateTime  int64 `xorm:"not null int(10) 'update_time'"`
}

// TableName Get table name
func (u *Pointtopic) TableName() string {
	return "point_topic"
}
func (u *Pointtopic) Insert() error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime
	_, err := db.GetEngineMaster().Insert(u)
	return err
}
