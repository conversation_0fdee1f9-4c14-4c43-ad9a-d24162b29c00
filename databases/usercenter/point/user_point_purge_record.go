package point

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type UserPointPurgeRecord struct {
	ID                 int64 `xorm:"int(11) unsigned notnull pk autoincr 'id'" json:"id"`
	UID                int64 `xorm:"int(11) unsigned notnull 'uid'" json:"uid"`
	LastYearRmainPoint int64 `xorm:"int(11) unsigned notnull 'last_year_remain_point'" json:"last_year_remain_point"`
	EarnPoint          int64 `xorm:"int(11) unsigned notnull 'earn_point'" json:"earn_point"`
	SpendPoint         int64 `xorm:"int(11) unsigned notnull 'spend_point'" json:"spend_point"`
	PurgePoint         int64 `xorm:"int(11) unsigned notnull 'purge_point'" json:"purge_point"`
	Status             int32 `xorm:"int(2) unsigned notnull 'status'" json:"status"`
	CreateTime         int64 `xorm:"not null int(10) 'create_time'"`
	UpdateTime         int64 `xorm:"not null int(10) 'update_time'"`
}

// TableName Get table name
func (u UserPointPurgeRecord) TableName() string {
	year := time.Now().Year()
	return fmt.Sprintf("user_point_purge_record_%d", year)
}

func (u *UserPointPurgeRecord) UpdateUserPurgePointInfo(uid int64) error {
	u.UpdateTime = time.Now().Unix()
	_, err := db.GetEngineMaster().Where("uid=?", uid).Update(u)
	return err
}

type _userPointPurge struct {
}

var TbUserPointPurge _userPointPurge

// GetUserPurgePointInfo 获取清理信息
func (u *_userPointPurge) GetUserPurgePointInfo(uid int64) (*UserPointPurgeRecord, error) {
	var table UserPointPurgeRecord

	ok, err := db.GetEngine().Where("uid=?", uid).Get(&table)
	if err != nil {
		logger.Warnf("查询徽章系列详情失败, err: %s", err)
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &table, nil
}
