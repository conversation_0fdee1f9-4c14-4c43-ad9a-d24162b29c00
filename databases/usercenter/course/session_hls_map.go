package course

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type SessionHLSMap struct {
	ID         int64  `xorm:"not null pk autoincr int(11) 'id'"`
	SourceURL  string `xorm:"not null varchar(255) 'source_url'"`
	SourceKey  string `xorm:"not null bigint(20) 'source_key'"`
	HLS        string `xorm:"not null varchar(255) 'hls_url'"`
	CreateTime int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64  `xorm:"not null int(11) 'update_time'"`
}

func (SessionHLSMap) TableName() string {
	return "session_hls_map"
}

func (s *SessionHLSMap) Save() error {
	s.CreateTime = time.Now().Unix()
	s.UpdateTime = s.CreateTime

	_, err := usercenter.GetEngineMaster().Insert(s)
	return err
}
