package course

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type SessionExtendM3u8 struct {
	ID        int64  `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	SessionID int64  `xorm:"int(11) default 0 'ProgramId'" json:"session_id"`
	Name      string `xorm:"not null varchar(128) 'name'" json:"name"`
	M3U8URL   string `xorm:"not null varchar(255) 'm3u8_url'" json:"m3u8_url"`
	SourceKey int64  `xorm:"not null bigint(20) 'source_key'" json:"source_key"`
	SourceURL string `xorm:"not null varchar(255) 'source_url'" json:"source_url"`
	SortOrder int64  `xorm:"not null int(5) default 1 'sort_order'" json:"sort_order"`
	IsDel     int64  `xorm:"not null int(5) default 1 'is_del'" json:"is_del"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) default 0 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) default 0 'update_time'" json:"update_time"`
}

func (SessionExtendM3u8) TableName() string {
	return "session_extend_m3u8"
}

func (s *SessionExtendM3u8) Save() error {
	s.CreateTime = time.Now().Unix()
	s.UpdateTime = s.CreateTime
	_, err := usercenter.GetEngineMaster().Insert(s)
	return err
}

func (s *SessionExtendM3u8) UpdateBySessionID() error {
	s.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().Where("ProgramId=?", s.SessionID).Update(s)
	return err
}

type sessionExtendM3u8 struct{}

// TbSessionExtendM3u8 外部调用对象
var TbSessionExtendM3u8 sessionExtendM3u8

// SessionExtendM3u8 获取需要组成课程的数据
func (s *sessionExtendM3u8) GetList(sessionID, stats int64) []SessionExtendM3u8 {
	var table []SessionExtendM3u8
	err := usercenter.GetEngine().Where("ProgramId=? AND is_del=?", sessionID, stats).OrderBy("`sort_order` ASC").
		Find(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return table
}
