package course

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	coursedb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// Program_session_extend---计划扩展表
type ProgramExtend struct {
	ID int64 `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	// 计划id
	ProgramID int64 `xorm:"int(11) default 0 'programId'" json:"program_id"`
	// 计划id
	SessionID int64 `xorm:"int(11) default 0 'sessionId'" json:"session_id"`
	// 中文标题
	CnTitle string `xorm:"varchar(128) default NULL 'CNtitle'" json:"cn_title"`
	// 英文标题
	EnTitle string `xorm:"varchar(128) default NULL 'ENtitle'" json:"en_title"`
	// 计划下每一节课程的卡路里
	Calorie int32 `xorm:"int(11) default 0 'calorie'" json:"calorie"`
	// 计划下课程描述目前只有名师课堂有这个字段，后期拆分注意力了 alex 2019.04.22
	SessionDesc string `xorm:"varchar(255) default 0 'session_desc'" json:"session_desc"`
	// 所属课程强度标识， 0,1,2
	Intensity int32 `xorm:"tinyint(1) default 1 'intensity'" json:"intensity"`
	// 链接会员数据
	LinkIsVip int32 `xorm:"tinyint(1) default 0 'link_is_vip'" json:"link_is_vip"`
	// 英文标题
	LinkTitle string `xorm:"varchar(128) default NULL 'link_title'" json:"link_title"`
	// 见链接类型中定义
	LinkType int32 `xorm:"tinyint(1) default 0 'link_type'" json:"link_type"`
	// 英文标题
	LinkContent string `xorm:"varchar(128) default NULL 'link_content'" json:"link_content"`
	// 视频尺寸：1=>4:3；2=>16:9
	ScreenScale     int32 `xorm:"tinyint(1) default 2 'screen_scale'" json:"screen_scale"`
	SubSessionIndex int32 `xorm:"tinyint(1) default 1 'sub_session_index'" json:"sub_session_index"`
	// 是否免费试看1否0是
	IsFree int32 `xorm:"tinyint(1) default 0 'is_free'" json:"is_free"`
	// 排序
	Order int32 `xorm:"int(11) default 1000 'order'" json:"order"`
	// 创建时间
	CreateTime int64 `xorm:"int(11) notnull default 0 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"int(11) notnull default 0 'update_time'" json:"update_time"`
}

type programExtend struct{}

// TbProgramExtend 外部调用对象
var TbProgramExtend programExtend

// TableName Get table name
func (ProgramExtend) TableName() string {
	return "program_session_extend"
}
func (t *ProgramExtend) Save() error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime
	_, err := coursedb.GetEngineMaster().Insert(t)
	return err
}

// 事务保存
func (t *ProgramExtend) SaveByTransaction(session *xorm.Session) error {
	if session == nil {
		session = coursedb.GetEngineMaster().NewSession()
	}
	_, err := session.Insert(t)
	return err
}

// 事务更新
func (t *ProgramExtend) UpdateByTransaction(session *xorm.Session) error {
	t.UpdateTime = time.Now().Unix()
	_, err := session.ID(t.ID).Update(t)
	return err
}

// 事务批量插入
func (tb *programExtend) BatchInsert(session *xorm.Session, list []*ProgramExtend) error {
	if session == nil {
		session = coursedb.GetEngineMaster().NewSession()
	}
	_, err := session.Insert(list)
	return err
}

// 课程更新
func (t *ProgramExtend) Update() error {
	t.UpdateTime = time.Now().Unix()
	_, err := coursedb.GetEngineMaster().ID(t.ID).Update(t)
	return err
}

// 2019年7月6日 14:45:00 water ID事务删除
func (t *ProgramExtend) DeleteByIDTransaction(session *xorm.Session) error {
	_, err := session.Delete(t)
	return err
}

// 事务删除
func (t *ProgramExtend) DeleteByTransaction(session *xorm.Session) error {
	if t.ProgramID > 0 {
		session = session.Where("programId = ?", t.ProgramID)
	}
	_, err := session.Delete(t)
	return err
}

// 通过事务删除本次没有提交的课程
func (t *ProgramExtend) DeleteWithTransactionBySessionIDs(session *xorm.Session, newSessionIDs []int64) error {
	_, err := session.Where("programId = ?", t.ProgramID).NotIn("sessionId", newSessionIDs).Delete(t)

	return err
}

// 根据计划id查询计划扩展列表
func (tb *programExtend) GetProgramExtendList(programID int64) []ProgramExtend {
	var tables []ProgramExtend
	err := coursedb.GetEngine().Where("programId=?", programID).Asc(`order`).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// 查询需要删除的ProgramExtends
func (tb *programExtend) GetNeedDeleteProgramExtends(programID int64, sessionIDs []int64) []ProgramExtend {
	var tables []ProgramExtend
	err := coursedb.GetEngine().Where("programId=?", programID).NotIn("sessionId", sessionIDs).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// 事务内查询单行数据
func (tb *programExtend) GetRowWithTransactionByProgramIDSessionID(session *xorm.Session,
	programID, sessionID int64) *ProgramExtend {
	var table ProgramExtend

	ok, err := session.Where("programId=?", programID).Where("sessionId=?", sessionID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// 获取计划下的课程列表
func (tb *programExtend) GetProgramSessionList(programID int64) []*Session {
	var tables []*Session

	err := coursedb.GetEngine().Table((&ProgramExtend{}).TableName()).Alias("p").
		Select("s.*").
		Join("LEFT", []string{(&Session{}).TableName(), "s"}, "p.sessionid = s.Programid").
		Where("p.programid = ?", programID).
		Asc(`p.Order`).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetSessionCount 获取课程数量
func (tb *programExtend) GetSessionCount(programID int64) int64 {
	var table ProgramExtend

	cnt, err := coursedb.GetEngine().Where("programId = ?", programID).Count(&table)
	if err != nil {
		logger.Error(err)
		return 0
	}
	return cnt
}

func (tb *programExtend) GetLastSession(programID int64) *ProgramExtend {
	var table ProgramExtend

	ok, err := coursedb.GetEngine().Where("programId = ?", programID).Desc("order").Desc("sub_session_index").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// 事务内查询单行数据
func (tb *programExtend) GetRowByID(id int64) *ProgramExtend {
	var table ProgramExtend
	ok, err := coursedb.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
