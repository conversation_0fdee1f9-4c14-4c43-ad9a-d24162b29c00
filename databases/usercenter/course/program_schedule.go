package course

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type ProgramSchedule struct {
	ID               int64  `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	UID              int64  `xorm:"not null  int(11) 'user_id'" json:"uid"`
	ProgramID        int64  `xorm:"not null  int(11) 'program_id'" json:"program_id"`
	SessionID        int64  `xorm:"not null  int(11) 'session_id'" json:"session_id"`
	SessionIndex     int64  `xorm:"not null  int(11) 'session_index'" json:"session_index"`
	Progress         string `xorm:"not null  varchar(256) 'progress'" json:"progress"`
	UploadTime       int64  `xorm:"not null  int(11) 'upload_time'" json:"upload_time"`
	LastPracticeTime int64  `xorm:"not null  int(11) 'last_practice_time'" json:"last_practice_time"`
	Status           int32  `xorm:"not null tinyint(2) 'status'" json:"status"`
	CreateTime       int64  `xorm:"not null int(11) default 0 'create_time'" json:"create_time"` // 创建时间
	UpdateTime       int64  `xorm:"not null int(11) default 0 'update_time'" json:"update_time"` // 更新时间
}

func (p *ProgramSchedule) TableName() string {
	if p.UID < 1 {
		return ""
	}
	return fmt.Sprintf("program_schedule_%d", p.UID%32)
}

// Save 保存
func (p *ProgramSchedule) Save() error {
	_, err := usercenter.GetEngineMaster().Insert(p)
	return err
}

// Update 更新
func (p *ProgramSchedule) Update() error {
	p.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().Id(p.ID).Update(p)
	return err
}

// UpdateMustCols 含有必须更新的字段 即时为0 也更新
func (p *ProgramSchedule) UpdateMustCols() error {
	p.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().Id(p.ID).MustCols("session_id", "session_index", "progress").Update(p)
	return err
}

type _ProgramSchedule struct{}

// TbProgramSchedule 外部引用对象
var TbProgramSchedule _ProgramSchedule

// GetItemByUID
func (p *_ProgramSchedule) GetItemByUID(uid, programID int64) *ProgramSchedule {
	var table ProgramSchedule
	tableName := &ProgramSchedule{
		UID: uid,
	}
	ok, err := usercenter.GetEngine().Table(tableName.TableName()).
		Where("user_id = ? AND program_id=? ", uid, programID).OrderBy("update_time DESC").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// 获取用户已加入的计划列表中排序最靠前的的一个计划
func (p *_ProgramSchedule) GetUserLastProgram(uid int64, seriesType int32) *ProgramSchedule {
	var table ProgramSchedule
	tableName := &ProgramSchedule{
		UID: uid,
	}
	ok, err := usercenter.GetEngine().Table(tableName.TableName()).Alias("a").
		Join("LEFT", []string{"sessioninfo", "b"}, "a.program_id = b.SessionId").
		Where("a.user_id = ? and b.series_type = ?", uid, seriesType).
		In("a.status", []int32{1, 4}).
		OrderBy("a.upload_time DESC").Limit(1).Get(&table)
	if err != nil {
		logger.Errorf("err:%v,uid:%v,seriesType:%v", err, uid, seriesType)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
