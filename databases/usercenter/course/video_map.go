package course

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type VideoMap struct {
	ID             int64  `xorm:"not null pk autoincr int(11) 'id'"`
	QiniuURL       string `xorm:"not null varchar(255) 'qiniu_url'"`
	QiniuKey       int64  `xorm:"not null bigint(20) 'qiniu_key'"`
	AipaiURL       string `xorm:"not null varchar(255) 'aipai_url'"`
	<PERSON>pai<PERSON>ey       int64  `xorm:"not null bigint(20) 'aipai_key'"`
	QiniuSourceURL string `xorm:"not null varchar(255) 'qiniu_source_url'"`
	QiniuBucket    string `xorm:"not null varchar(32) 'qiniu_bucket'"`
	VideoTitle     string `xorm:"not null varchar(64) 'video_title'"`
	VideoType      int32  `xorm:"not null tinyint(4) 'video_type'"`
	FileType       int32  `xorm:"not null tinyint(4) 'file_type'"`
	Status         int32  `xorm:"not null tinyint(2) 'status'"`
	CreateTime     int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime     int64  `xorm:"not null int(11) 'update_time'"`
}

func (v *VideoMap) TableName() string {
	return "video_map"
}

type videoMap struct{}

var TbVideoMap videoMap

func (v *videoMap) GetAllVideoURL(offset, limit int) []VideoMap {
	var tables []VideoMap
	err := usercenter.GetEngine().
		Where("file_type = ?", 1).
		Where("video_type = ?", 1).
		Where("qiniu_url != ''").Where("aipai_url != ''").
		Limit(limit, offset).
		Find(&tables)

	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (v *videoMap) GetRowByKey(key string, isQiniu, isAipai bool) *VideoMap {
	if !isQiniu && !isAipai {
		return nil
	}
	var table VideoMap

	var ok bool
	var err error
	usercenter.GetEngine().ShowSQL(true)
	if isQiniu {
		ok, err = usercenter.GetEngine().Where("qiniu_key = ?", key).Get(&table)
	}
	if isAipai {
		ok, err = usercenter.GetEngine().Where("aipai_key = ?", key).Get(&table)
	}
	if err != nil {
		logger.Error(err)
		return nil
	}
	usercenter.GetEngine().ShowSQL(false)
	if !ok {
		return nil
	}
	return &table
}
