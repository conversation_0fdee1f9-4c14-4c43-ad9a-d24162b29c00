package course

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type SessionSchedule struct {
	ID               int64 `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	UserID           int64 `xorm:"not null  int(11) 'user_id'" json:"user_id"`
	SessionID        int64 `xorm:"not null  int(11) 'session_id'" json:"session_id"`
	UploadTime       int64 `xorm:"not null  int(11) 'upload_time'" json:"upload_time"`
	LastPracticeTime int64 `xorm:"not null  int(11) 'last_practice_time'" json:"last_practice_time"`
	Status           int32 `xorm:"not null tinyint(2) 'status'" json:"status"`
	CreateTime       int64 `xorm:"not null int(11) default 0 'create_time'" json:"create_time"` // 创建时间
	UpdateTime       int64 `xorm:"not null int(11) default 0 'update_time'" json:"update_time"` // 更新时间
}

func (s SessionSchedule) TableName() string {
	return "session_schedule"
}

// Save 保存
func (s *SessionSchedule) Save() error {
	_, err := usercenter.GetEngineMaster().Insert(s)
	return err
}

// Update 更新
func (s *SessionSchedule) Update() error {
	s.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().Id(s.ID).Update(s)
	return err
}

type _SessionSchedule struct{}

// TbSessionSchedule 外部引用对象
var TbSessionSchedule _SessionSchedule

// GetItem 获取收藏数据
func (s *_SessionSchedule) GetItemByUID(uid, sessionID int64) *SessionSchedule {
	var table SessionSchedule
	ok, err := usercenter.GetEngine().
		Where("user_id = ? AND session_id=? AND status = 1 ", uid, sessionID).OrderBy("create_time DESC").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
