package course

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	coursedb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// 练习表
type Session struct {
	SessionID      int64  `xorm:"int(11) notnull pk autoincr 'ProgramId'" json:"session_id"`
	EnName         string `xorm:"varchar(64) default NULL 'PenglishName'" json:"en_name"`
	EnDesc         string `xorm:"text  'PenglishDes'"  json:"en_desc"`
	CnName         string `xorm:"varchar(64) default NULL 'PchineseName'" json:"cn_name"`
	CnDesc         string `xorm:"text  'PchineseDes'"  json:"cn_desc"`
	AndroidPackage string `xorm:"varchar(64) default NULL 'snameandroid'" json:"android_package"`
	IosPackage     string `xorm:"varchar(64) default NULL 'snameios'" json:"ios_package"`
	Logo           string `xorm:"varchar(128) default NULL 'logo'" json:"logo"`
	// TV课程LOGO
	LogoTv string `xorm:"varchar(128) default NULL 'logo_tv'" json:"logo_tv"`
	// 课程封面V3
	LogoCover string `xorm:"varchar(128) default NULL 'logo_cover'" json:"logo_cover"`
	// 课程详情顶部图片--pad
	LogoTop    string `xorm:"varchar(128) default NULL 'logo_top'" json:"logo_top"`
	LogoTopPad string `xorm:"varchar(128) default NULL 'logo_top_pad'" json:"logo_top_pad"`
	// 课程封面v4-phone
	LogoNewCover string `xorm:"varchar(128) default NULL 'logo_new_cover'" json:"logo_new_cover"`
	// 课程封面v4-pad
	LogoNewCoverPad string `xorm:"varchar(128) default NULL 'logo_new_cover_pad'" json:"logo_new_cover_pad"`
	// 课程详情v4-phone
	LogoNewDetail string `xorm:"varchar(128) default NULL 'logo_new_detail'" json:"logo_new_detail"`
	// 课程详情v4-pad
	LogoNewDetailPad string `xorm:"varchar(128) default NULL 'logo_new_detail_pad'" json:"logo_new_detail_pad"`
	// 冥想课程封面
	LogoMeditation string `xorm:"varchar(128) default NULL 'logo_meditation'" json:"logo_meditation"`
	// 冥想课程pad封面
	LogoMeditationPad string `xorm:"varchar(128) default NULL 'logo_meditation_pad'" json:"logo_meditation_pad"`
	// 内容类型：1.体式，2.冥想
	ContentType int32 `xorm:"tinyint(2) notnull default 1 'content_type'" json:"content_type"`
	IsSession   int64 `xorm:"tinyint(2) notnull default 1 'is_session'" json:"is_session"`
	SeriesType  int64 `xorm:"tinyint(2) notnull default 1 'series_type'" json:"series_type"`
	// 1：普通类型 , 2：切片类型
	FromType int64 `xorm:"tinyint(2) notnull default 1 'from_type'" json:"from_type"`
	// 等级
	Level int32 `xorm:"int(2) default 0 'Level'" json:"level"`
	// 分类
	Goal string `xorm:"varchar(255) notnull 'Goal'" json:"goal"`
	// 扩展
	Extension string `xorm:"text  default NULL 'extr'" json:"extension"`
	// 课程功效-v631
	Effect string `xorm:"text  'effect'" json:"effect"`
	// 建议练习周期v631
	Period string `xorm:"text  'period'" json:"period"`
	// 适用人群v631
	ApplicationPeople string `xorm:"text  'application_people'" json:"application_people"`
	Attention         string `xorm:"text  'attention'" json:"attention"`
	Part              string `xorm:"varchar(12) notnull 'part'" json:"part"`
	IsVip             int32  `xorm:"tinyint(1) notnull 'is_vip'" json:"is_vip"`
	// 会员等级角标:0-无;1-普通用户;2-普通非年费会员;3-普通年费会员;4-高级非年费会员;5-高级年费会员
	MemberLevelFlag int32 `xorm:"tinyint(1) notnull default 0 'member_level_flag'" json:"member_level_flag"`
	// 会员等级:2-普通非年费会员;3-普通年费会员;4-高级非年费会员;5-高级年费会员
	MemberLevel string `xorm:"varchar(32) notnull default '' 'member_level'" json:"member_level"`
	// 适用人群
	ApplyGender string `xorm:"text  'apply_gender'" json:"apply_gender"`
	// 教练
	CoachID int32 `xorm:"int(11) default NULL 'chId'" json:"coach_id"`
	// 卡路里系数
	CalorieRatio int32 `xorm:"int(11) default NULL 'calorie_ratio'" json:"calorie_ratio"`
	// 时长
	Duration      int32  `xorm:"int(11) default NULL 'duration'" json:"duration"`
	Calorie       int32  `xorm:"int(11) notnull 'calorie'" json:"calorie"`
	Online        int32  `xorm:"tinyint(11) notnull 'environ'" json:"online"`
	EnStreamMedia string `xorm:"varchar(255) default NULL 'stream_media_en'" json:"en_stream_media"`
	// 中文流媒体
	CnStreamMedia string `xorm:"varchar(255) default NULL 'stream_media_cn'" json:"cn_stream_media"`
	// 流媒体时长
	StreamMediaDuration int64 `xorm:"int(11) notnull default 0 'stream_media_duration'" json:"stream_media_duration"`
	// 创建时间
	CreateTime int64 `xorm:"int(11) notnull default 0 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"int(11) notnull default 0 'update_time'" json:"update_time"`
	// 链接相关
	LinkIsVip     int32  `xorm:"tinyint(1) notnull default 4  'link_is_vip'" json:"link_is_vip"`
	LinkTitle     string `xorm:"varchar(128) default NULL 'link_title'" json:"link_title"`
	LinkType      int32  `xorm:"tinyint(1) notnull default 4  'link_type'" json:"link_type"`
	LinkContent   string `xorm:"varchar(128) default NULL 'link_content'" json:"link_content"`
	StrengthRatio string `xorm:"varchar(20) default NULL 'strength_ratio'" json:"strength_ratio"`
	// 首次上线时间
	FirstSaleTime int64 `xorm:"int(11) notnull default 0 'uptime'" json:"first_sale_time"`
	// 预览视频 url
	PreviewURL         string `xorm:"varchar(128) default NUll 'preview_url'" json:"preview_url"`
	SpecialImage       string `xorm:"varchar(128) default NUll 'special_image'" json:"special_image"`
	SpecialImageDetail string `xorm:"varchar(128) default NUll 'special_image_detail'" json:"special_image_detail"`
	// 课程关联小视频
	VideoID int64 `xorm:"int(11) notnull default 0  'video_id'" json:"video_id"`
	// 适合人群(新)
	DescSuitablePeople string `xorm:"varchar(256) default '' 'desc_suitable_people'" json:"desc_suitable_people"`
	// 效果(新)
	DescEffect string `xorm:"varchar(256) default '' 'desc_effect'" json:"desc_effect"`
	// 练习部位(新)
	DescPart string `xorm:"varchar(256) default '' 'desc_part'" json:"desc_part"`
	// 是否支持背景音乐（Now 冥想专用）
	NowMedGbMusic int32 `xorm:"tinyint(1) notnull default 0 'now_med_gb_music'" json:"now_med_gb_music"`
	// 精讲视频
	SmallVideoConfig string `xorm:"varchar(256) default '' 'small_video_config'" json:"small_video_config"`
}

type ProgramSessionExtend struct {
	SessionID       int64  `xorm:"int(11) notnull pk autoincr 'ProgramId'" json:"session_id"`
	CnName          string `xorm:"varchar(64) default NULL 'PchineseName'" json:"cn_name"`
	CnDesc          string `xorm:"text  'PchineseDes'"  json:"cn_desc"`
	LogoNewCover    string `xorm:"varchar(128) default NULL 'logo_new_cover'" json:"logo_new_cover"`
	LogoNewCoverPad string `xorm:"varchar(128) default NULL 'logo_new_cover_pad'" json:"logo_new_cover_pad"`
	SeriesType      int64  `xorm:"tinyint(2) notnull default 1 'series_type'" json:"series_type"`
	IsVip           int32  `xorm:"tinyint(1) notnull 'isVip'" json:"isVip"`
	MemberLevelFlag int32  `xorm:"tinyint(1) notnull default 0 'member_level_flag'" json:"member_level_flag"`
	MemberLevel     string `xorm:"varchar(32) notnull default '' 'member_level'" json:"member_level"`
	Intensity       string `xorm:"varchar(1000) default NULL 'Intensity'" json:"Intensity"`
	Extension       string `xorm:"text  default NULL 'extr'" json:"extension"`
	Tag             string `xorm:"tinyint(1) notnull default 0 'Tag'" json:"Tag"`
	ContentType     int32  `xorm:"tinyint(2) notnull default 1 'content_type'" json:"content_type"`
}

type SessionDetail struct {
	SessionID      int64  `xorm:"int(11) notnull pk autoincr 'ProgramId'" json:"session_id"`
	EnName         string `xorm:"varchar(64) default NULL 'PenglishName'" json:"en_name"`
	EnDesc         string `xorm:"text  'PenglishDes'" json:"en_desc"`
	CnName         string `xorm:"varchar(64) default NULL 'PchineseName'" json:"cn_name"`
	CnDesc         string `xorm:"text  'PchineseDes'" json:"cn_desc"`
	AndroidPackage string `xorm:"varchar(64) default NULL 'snameandroid'" json:"android_package"`
	IosPackage     string `xorm:"varchar(64) default NULL 'snameios'" json:"ios_package"`
	Logo           string `xorm:"varchar(128) default NULL 'logo'" json:"logo"`
	// TV课程LOGO
	LogoTv string `xorm:"varchar(128) default NULL 'logo_tv'" json:"logo_tv"`
	// 课程封面V3
	LogoCover string `xorm:"varchar(128) default NULL 'logo_cover'" json:"logo_cover"`
	// 课程详情顶部图片--pad
	LogoTop    string `xorm:"varchar(128) default NULL 'logo_top'" json:"logo_top"`
	LogoTopPad string `xorm:"varchar(128) default NULL 'logo_top_pad'" json:"logo_top_pad"`
	// 课程封面v4-phone
	LogoNewCover string `xorm:"varchar(128) default NULL 'logo_new_cover'" json:"logo_new_cover"`
	// 课程封面v4-pad
	LogoNewCoverPad string `xorm:"varchar(128) default NULL 'logo_new_cover_pad'" json:"logo_new_cover_pad"`
	// 课程详情v4-phone
	LogoNewDetail string `xorm:"varchar(128) default NULL 'logo_new_detail'" json:"logo_new_detail"`
	// 课程详情v4-pad
	LogoNewDetailPad string `xorm:"varchar(128) default NULL 'logo_new_detail_pad'" json:"logo_new_detail_pad"`
	// 冥想课程封面
	LogoMeditation string `xorm:"varchar(128) default NULL 'logo_meditation'" json:"logo_meditation"`
	// 冥想课程pad封面
	LogoMeditationPad string `xorm:"varchar(128) default NULL 'logo_meditation_pad'" json:"logo_meditation_pad"`
	// 内容类型：1.体式，2.冥想
	ContentType int32 `xorm:"tinyint(2) notnull default 1 'content_type'" json:"content_type"`
	IsSession   int64 `xorm:"tinyint(2) notnull default 1 'is_session'" json:"is_session"`
	SeriesType  int64 `xorm:"tinyint(2) notnull default 1 'series_type'" json:"series_type"`
	// 1：普通类型 , 2：切片类型
	FromType int64 `xorm:"tinyint(2) notnull default 1 'from_type'" json:"from_type"`
	// 等级
	Level int32 `xorm:"int(2) default 0 'Level'" json:"level"`
	// 分类
	Goal string `xorm:"varchar(255) notnull 'Goal'" json:"goal"`
	// 扩展
	Extension string `xorm:"text  default NULL 'extr'" json:"extension"`
	// 课程功效-v631
	Effect string `xorm:"text  'effect'" json:"effect"`
	// 建议练习周期v631
	Period string `xorm:"text  'period'" json:"period"`
	// 适用人群v631
	ApplicationPeople string `xorm:"text  'application_people'" json:"application_people"`
	Attention         string `xorm:"text  'attention'" json:"attention"`
	Part              string `xorm:"varchar(12) notnull 'part'" json:"part"`
	IsVip             int32  `xorm:"tinyint(1) notnull 'is_vip'" json:"is_vip"`
	// 会员等级角标:0-无;1-普通用户;2-普通非年费会员;3-普通年费会员;4-高级非年费会员;5-高级年费会员
	MemberLevelFlag int32 `xorm:"tinyint(1) notnull default 0 'member_level_flag'" json:"member_level_flag"`
	// 会员等级:2-普通非年费会员;3-普通年费会员;4-高级非年费会员;5-高级年费会员
	MemberLevel string `xorm:"varchar(32) notnull default '' 'member_level'" json:"member_level"`
	// 适用人群
	ApplyGender string `xorm:"text  'apply_gender'" json:"apply_gender"`
	// 教练
	CoachID int32 `xorm:"int(11) default NULL 'chId'" json:"coach_id"`
	// 卡路里系数
	CalorieRatio int32 `xorm:"int(11) default NULL 'calorie_ratio'" json:"calorie_ratio"`
	// 时长
	Duration      int32  `xorm:"int(11) default NULL 'duration'" json:"duration"`
	Calorie       int32  `xorm:"int(11) notnull 'calorie'" json:"calorie"`
	Online        int32  `xorm:"tinyint(11) notnull 'environ'" json:"online"`
	EnStreamMedia string `xorm:"varchar(255) default NULL 'stream_media_en'" json:"en_stream_media"`
	// 中文流媒体
	CnStreamMedia string `xorm:"varchar(255) default NULL 'stream_media_cn'" json:"cn_stream_media"`
	// 创建时间
	CreateTime int64 `xorm:"int(11) notnull default 0 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"int(11) notnull default 0 'update_time'" json:"update_time"`
	// 链接相关
	LinkIsVip   int32  `xorm:"tinyint(1) notnull default 4  'link_is_vip'" json:"link_is_vip"`
	LinkTitle   string `xorm:"varchar(128) default NULL 'link_title'" json:"link_title"`
	LinkType    int32  `xorm:"tinyint(1) notnull default 4  'link_type'" json:"link_type"`
	LinkContent string `xorm:"varchar(128) default NULL 'link_content'" json:"link_content"`
	// app 版本信息
	MinVersion string `json:"min_version"`
	MaxVersion string `json:"max_version"`
	// app下载地址
	DownLoadLink  string `xorm:"varchar(128) default NULL 'DownLoadLink'" json:"downLoad_link"`
	DownLoadLink1 string `xorm:"varchar(128) default NULL 'DownLoadLink1'" json:"downLoad_link1"`
	DownLoadLink2 string `xorm:"varchar(128) default NULL 'DownLoadLink2'" json:"downLoad_link2"`
	// 首次上线时间
	FirstSaleTime int64 `xorm:"int(11) notnull default 0 'uptime'" json:"first_sale_time"`
	// 装备信息
	EquipmentIDString string `xorm:"varchar(128) default NULL 'equipment_id_string'"  json:"equipment_id_string"`
	// tv流媒体链接
	TvVideoURL      string `json:"tv_video_url"`
	CommonExtension string `json:"common_extension"`
	// 支持林军的版本号升级 alex 2019.08.21
	TvMinVersion  string `json:"tv_min_version"`
	TvMaxVersion  string `json:"tv_max_version"`
	StrengthRatio string `xorm:"varchar(20) default NULL 'strength_ratio'" json:"strength_ratio"`
	CourseVersion string `xorm:"varchar(128) default NULL 'course_version'"  json:"course_version"`
	// 预览视频链接
	PreviewURL         string `xorm:"varchar(128) default '' 'preview_url'" json:"preview_url"`
	SpecialImage       string `xorm:"varchar(128) default '' 'special_image'" json:"special_image"`
	SpecialImageDetail string `xorm:"varchar(128) default '' 'special_image_detail'" json:"special_image_detail"`
	// 课程关联小视频
	VideoID int64 `xorm:"int(11) notnull default 0  'video_id'" json:"video_id"`
	// 适合人群(新)
	DescSuitablePeople string `xorm:"varchar(256) default '' 'desc_suitable_people'" json:"desc_suitable_people"`
	// 效果(新)
	DescEffect string `xorm:"varchar(256) default '' 'desc_effect'" json:"desc_effect"`
	// 练习部位(新)
	DescPart string `xorm:"varchar(256) default '' 'desc_part'" json:"desc_part"`
	// 是否支持背景音乐（Now 冥想专用）
	NowMedGbMusic int32 `xorm:"tinyint(1) notnull default 0 'now_med_gb_music'" json:"now_med_gb_music"`
	// 精讲小视频
	SmallVideoConfig string `xorm:"varchar(256) default '' 'small_video_config'" json:"small_video_config"`
}

type session struct{}

// TbSession 外部调用对象
var TbSession session

// TableName Get table name
func (Session) TableName() string {
	return "programinfo"
}

// SearchRequest 搜索字段
type SearchRequest struct {
	// 课程id
	SessionID int64
	// 课程名字
	CourseName string
	// 教练id
	CoachID int32
	// 等级
	Level int32
	// 是否散课
	IsSession int32
	// 分类
	CategoryID int32
	// 是否会员
	IsVip int32
	// 课程类型
	ResourceType int32
	// 课程环境
	IsOnline int32
	// 课程平台
	Platform int32
	// 课程语言版本
	SupportLanguage int32
	// 页码
	PageSize int
	// 偏移量
	Offset int
	// 计划下增加课程搜索条件为 EnName 2019.04.13
	EnName string
	// 1：普通类型 , 2：切片类型
	FromType int32
	// 内容类型：ContentType， 1： 形体，2：冥想，3：正念冥想
	ContentType int32
}

// 获取课程列表数据
func GetSessionList(request *SearchRequest) []Session {
	var tables []Session
	session := coursedb.GetEngine().NewSession()
	defer session.Close()
	session = session.Table("programinfo").
		Alias("a").
		Distinct("a.ProgramId").
		Desc("ProgramId").
		Select("a.ProgramId,a.uptime,a.PchineseName,a.PenglishName,a.PenglishDes,a.PchineseDes,a.snameandroid,"+
			"a.snameios,a.Created,a.logo,a.logo_tv,a.logo_cover,a.logo_top,a.logo_top_pad,a.logo_new_cover,"+
			"a.logo_new_cover_pad,a.logo_new_detail,a.logo_new_detail_pad,a.is_session,a.`level`,a.goal,a.series_type,"+
			"a.is_vip,a.calorie,a.update_time,a.link_is_vip,a.chId").
		Join("LEFT", []string{"session_extend", "d"}, "d.ProgramId=a.`ProgramId`")
	if request.SessionID > 0 {
		session = session.Where("a.ProgramId=?", request.SessionID)
	}
	if request.CourseName != "" {
		session = session.Where("a.PchineseName LIKE ?", "%"+request.CourseName+"%")
	}
	if request.EnName != "" {
		session = session.Where("a.PenglishName LIKE ?", "%"+request.EnName+"%")
	}
	if request.CoachID > 0 {
		session = session.Where("a.chId=?", request.CoachID)
	}
	if request.IsSession > -1 {
		session = session.Where("a.is_session=?", request.IsSession)
	}
	if request.Level > 0 {
		session = session.Where("a.Level=?", request.Level)
	}
	if request.ResourceType > 0 {
		session = session.Where("a.series_type=?", request.ResourceType)
	}
	// 2019年11月9日 14:42:51 water 1：普通类型 , 2：切片类型
	if request.FromType > 0 {
		session = session.Where("a.from_type=?", request.FromType)
	}
	// session_extend扩展表
	if request.IsVip > 0 {
		session = session.Where("a.is_vip=?", request.IsVip)
	}
	if request.ContentType > 0 {
		session = session.Where("a.content_type=?", request.ContentType)
	}
	if request.SupportLanguage > 0 {
		session = session.Where("d.Version=?", request.SupportLanguage)
	}
	// 测试/线上
	if request.IsOnline > -1 {
		session = session.Where("d.IsOnline=?", request.IsOnline)
	}
	// 安卓/ios/tv
	if request.Platform > 0 {
		// 区分是安卓与tv
		if request.Platform == 5 {
			session = session.Where("d.DeviceType=? and Version=?", 1, 91)
		} else {
			session = session.Where("d.DeviceType=? and Version=?", request.Platform, 1)
		}
	}
	// 分类
	if request.CategoryID > 0 {
		session = session.In("a.goal", request.CategoryID)
	}
	err := session.Limit(request.PageSize, request.Offset).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// 事务新增数据
func (t *Session) SaveByTransaction(session *xorm.Session) error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime
	_, err := session.Insert(t)
	return err
}
func (t *Session) UpdateByTransaction(session *xorm.Session) error {
	t.UpdateTime = time.Now().Unix()
	_, err := session.ID(t.SessionID).Update(t)
	return err
}

// 2019-12-11 water 修复因UpdateByTransaction内加入MustCols造成的问题
func (t *Session) UpdateInfoByTransactionHasMc(session *xorm.Session) error {
	t.UpdateTime = time.Now().Unix()
	_, err := session.ID(t.SessionID).MustCols("preview_url", "video_id",
		"desc_suitable_people", "desc_effect", "desc_part").Update(t)
	return err
}

// 根据ids获取数据
func (t *session) GetSessionListByIds(sessionIds []int32,
	deviceType int32, languageVersion int64) ([]ProgramSessionExtend, error) {
	var tables []ProgramSessionExtend
	err := coursedb.GetEngine().Table("programinfo").Alias("a").
		Select("a.ProgramId,a.PchineseName,a.PchineseDes,a.logo_new_cover,"+
			"a.logo_new_cover_pad,b.isVip,a.series_type,a.member_level_flag,a.member_level,"+
			"a.extr,a.content_type,b.Intensity,b.Tag").
		Join("LEFT", []string{"session_extend", "b"}, "a.`ProgramId` = b.`ProgramId`").
		Where("a.environ=?", 1).Where("b.IsOnline=?", 1).In("a.ProgramId", sessionIds).
		Where("b.Version=?", languageVersion).Where("b.DeviceType=?", deviceType).
		Where("a.is_session=?", 1).Where("a.content_type !=?", 3).
		Find(&tables)
	return tables, err
}

func (t *session) GetSessionListByIdsByVersion(sessionIds []int32,
	deviceType int32, languageVersion, version int64) ([]ProgramSessionExtend, error) {
	var tables []ProgramSessionExtend
	err := coursedb.GetEngine().Table("programinfo").Alias("a").
		Select("a.ProgramId,a.PchineseName,a.PchineseDes,a.logo_new_cover,a.logo_new_cover_pad,"+
			"b.isVip,a.series_type,a.member_level_flag,a.member_level,a.extr,a.content_type,b.Intensity,b.Tag").
		Join("LEFT", []string{"session_extend", "b"}, "a.`ProgramId` = b.`ProgramId`").
		Where("a.environ=?", 1).Where("b.IsOnline=?", 1).In("a.ProgramId", sessionIds).
		Where("b.Version=?", languageVersion).Where("b.DeviceType=?", deviceType).
		Where("b.min_version <= ? AND b.max_version >=?", version, version).
		Where("a.is_session=?", 1).Where("a.content_type !=?", 3).
		Find(&tables)
	return tables, err
}

// 获取最新推荐课程
func (t *session) GetRecommendList(deviceType int32, pageSize, offset int) []Session {
	var tables []Session
	session := coursedb.GetEngine().NewSession()
	defer session.Close()
	session.Table("programinfo").Alias("a").
		Select("a.ProgramId,a.PchineseName,a.PchineseDes,a.logo_new_cover,a.logo_new_cover_pad,"+
			"b.isVip,a.series_type,a.member_level_flag,a.member_level,a.extr").
		Join("LEFT", []string{"session_extend", "b"}, "a.`ProgramId` = b.`ProgramId`").
		Where("a.environ=?", 1).Where("b.IsOnline=?", 1).Where("b.Version=?", 1).
		Where("b.DeviceType=?", deviceType).Where("a.is_session=?", 1).Where("b.Tag=?", 2).
		Desc("new_practice_remind_time")
	if pageSize > 0 {
		session.Limit(pageSize, offset)
	}

	err := session.Find(&tables)
	if err != nil {
		return nil
	}
	return tables
}

// GetSessionByID 通过课程ID获取课程详情
func (t *session) GetSessionByID(id int64) *Session {
	var table Session

	ok, err := coursedb.GetEngine().Where("ProgramId = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetSessionByIDs 通过课程ID列表获取课程详情
func (t *session) GetSessionByIDs(ids []int64) []Session {
	var tables []Session

	err := coursedb.GetEngine().In("ProgramId", ids).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// 后台课程详情页接口，现在产品整合到一个页面优化了操作流程，所以这块实现方式比较麻烦，下一次优化表涉及再次优化
// alex 2019.05.10 删除注释的代码
func GetSessionDetail(sessionID int64) *SessionDetail {
	var table SessionDetail
	sql := "select a.ProgramId,a.uptime,a.PchineseName,a.PenglishName,a.PenglishDes,a.PchineseDes,a.snameandroid," +
		"a.snameios,a.Created,a.is_vip,a.strength_ratio,a.logo_new_cover,a.logo_new_cover_pad,a.logo_new_detail," +
		"a.logo_new_detail_pad,a.is_session,a.`level`,a.goal,a.series_type,a.update_time,a.chId,a.content_type," +
		"a.effect,a.period,a.application_people,a.attention,a.logo_meditation,a.logo_meditation_pad,a.apply_gender," +
		"a.calorie,a.link_title,a.link_type,a.link_content,a.link_is_vip,a.create_time,a.update_time," +
		"a.stream_media_en,a.special_image,a.special_image_detail,a.stream_media_cn,a.member_level_flag," +
		"a.member_level,a.duration,a.calorie_ratio,a.environ,a.extr,a.preview_url,a.desc_suitable_people," +
		"a.desc_effect,a.desc_part, a.now_med_gb_music,a.small_video_config, (SELECT GROUP_CONCAT(equipment_id) as " +
		"equipment_id_string FROM allied_equipment_extra where source_id=? and source_type=1) as equipment_id_string," +
		"d.DownLoadLink,d.DownLoadLink1,d.DownLoadLink2,d.min_version,d.max_version,d.course_version,e.tv_video_url," +
		"e.tv_min_version,e.tv_max_version,d.common_extension,a.video_id,a.from_type from programinfo as a " +
		" left join (select ProgramId, DownLoadLink,DownLoadLink1,DownLoadLink2,min_version,max_version," +
		"Vc as course_version,Intensity as common_extension from session_extend where DeviceType=2 and Version=1) d" +
		" on d.ProgramId=a.`ProgramId` " +
		"left join (select ProgramId, tv_video_url,min_version as tv_min_version,max_version as tv_max_version " +
		"from session_extend where Version=91) e on e.ProgramId=a.`ProgramId` where a.ProgramId=?"
	ok, err := coursedb.GetEngine().SQL(sql, sessionID, sessionID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

type MakePageInfo struct {
	ProgramID int64 `xorm:"int(11) notnull pk autoincr 'ProgramId'" json:"ProgramId"` //
	ChID      int64 `xorm:"int(11) default NULL 'chId'" json:"chId"`
	// 所属课程强度标识， 0,1,2
	Intensity         string `xorm:"varchar(128) 'intensity'" json:"intensity"`
	Snameandroid      string `xorm:"varchar(64) default NULL 'snameandroid'" json:"snameandroid"` //
	PenglishName      string `xorm:"varchar(64) default NULL 'PenglishName'" json:"PenglishName"` //
	PchineseName      string `xorm:"varchar(64) default NULL 'PchineseName'" json:"PchineseName"` //
	Effect            string `xorm:"text  'effect'" json:"effect"`                                // 课程功效-v631
	Period            string `xorm:"text  'period'" json:"period"`                                // 建议练习周期v631
	ApplicationPeople string `xorm:"text  'application_people'" json:"application_people"`        // 适用人群v631
	Attention         string `xorm:"text  'attention'" json:"attention"`                          // 注意事项v631
	Snameios          string `xorm:"varchar(64) default NULL 'snameios'" json:"snameios"`         //

}

// 生成课程包用的字段

func (t *session) GetMakePageInfo(programID int64) *MakePageInfo {
	var table MakePageInfo
	ok, err := coursedb.GetEngine().Table("programinfo").
		Join("LEFT", "session_extend", "programinfo.ProgramId = session_extend.ProgramId").
		Where("programinfo.ProgramId=? AND session_extend.DeviceType=2 ", programID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
