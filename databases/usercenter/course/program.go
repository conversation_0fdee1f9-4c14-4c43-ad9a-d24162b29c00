package course

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	coursedb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// 计划相关 alex
type Program struct {
	ProgramID int64 `xorm:"int(11) notnull pk autoincr 'SessionId'" json:"program_id"`
	// 英文名称
	EnName string `xorm:"varchar(64) default NULL 'SenglishName'" json:"en_name"`
	// 英文描述
	EnDesc string `xorm:"text  'SenglishDes'"  json:"en_desc"`
	CnName string `xorm:"varchar(64) default NULL 'SchineseName'" json:"cn_name"`
	// 名师课程中使用,课程官网介绍 alex 2109.04.22
	CnDesc string `xorm:"text  'SchineseDes'"   json:"cn_desc"`
	// 分类
	ChineseProfile string `xorm:"varchar(255) notnull 'SChineseProfile'" json:"chinese_profile"`
	AndroidPackage string `xorm:"varchar(64) default NULL 'snameandroid'" json:"android_package"`
	IosPackage     string `xorm:"varchar(64) default NULL 'snameios'" json:"ios_package"`
	Logo           string `xorm:"varchar(128) default NULL 'logo'" json:"logo"`
	// TV课程LOGO
	LogoTv string `xorm:"varchar(128) default NULL 'logo_tv'" json:"logo_tv"`
	// 课程封面V3
	LogoCover string `xorm:"varchar(128) default NULL 'logo_cover'" json:"logo_cover"`
	// 课程封面v4-phone
	LogoNewCover string `xorm:"varchar(128) default NULL 'logo_new_cover'" json:"logo_new_cover"`
	// 课程封面v4-pad
	LogoNewCoverPad string `xorm:"varchar(128) default NULL 'logo_new_cover_pad'" json:"logo_new_cover_pad"`
	// 课程详情v4-phone
	LogoNewDetail string `xorm:"varchar(128) default NULL 'logo_new_detail'" json:"logo_new_detail"`
	// 课程详情v4-pad
	LogoNewDetailPad string `xorm:"varchar(128) default NULL 'logo_new_detail_pad'" json:"logo_new_detail_pad"`
	// 官网图片
	WebImages string `xorm:"varchar(256) default NULL 'web_images'" json:"web_images"`
	// 视频截图
	// 只有官网的名师课堂封面在使用，防止第一帧获取不到 alex 2109.04.22
	ShortVideoImage string `xorm:"varchar(256) default NULL 'short_video_image'" json:"short_video_image"`
	// 只有在官网使用，其他地方请勿使用，已经跟课程组核对过 alex 2019.04.22
	WebImagesExtra string `xorm:"varchar(256) default NULL 'web_images_extra'" json:"web_images_extra"`
	// 内容类型：1.体式，2.冥想
	ContentType int32 `xorm:"tinyint(2) notnull default 1 'content_type'" json:"content_type"`
	SeriesType  int64 `xorm:"tinyint(2) notnull default 1 'series_type'" json:"series_type"`
	// 等级
	Level int32 `xorm:"int(2) default 0 'Level'" json:"level"`
	// 分类
	Goal string `xorm:"varchar(255) notnull 'Goal'" json:"goal"`
	// 扩展
	Extension string `xorm:"text  default NULL 'extr'" json:"extension"`
	// 课程功效-v631
	Effect string `xorm:"text  'effect'" json:"effect"`
	// 建议练习周期v631
	Period string `xorm:"text  'period'" json:"period"`
	// 适用人群v631
	ApplicationPeople string `xorm:"text  'application_people'" json:"application_people"`
	Attention         string `xorm:"text  'attention'" json:"attention"`
	// 是否会员专享，0：NO， 1：YES
	IsVip int32 `xorm:"tinyint(1) notnull 'isVip'" json:"is_vip"`
	// 会员等级角标:0-无;1-普通用户;2-普通非年费会员;3-普通年费会员;4-高级非年费会员;5-高级年费会员
	MemberLevelFlag int32 `xorm:"tinyint(1) notnull default 0 'member_level_flag'" json:"member_level_flag"`
	// 会员等级:2-普通非年费会员;3-普通年费会员;4-高级非年费会员;5-高级年费会员
	MemberLevel string `xorm:"varchar(32) notnull default '' 'member_level'" json:"member_level"`
	// 适用人群
	ApplyGender string `xorm:"text  'apply_gender'" json:"apply_gender"`
	// 教练id
	CoachID int32 `xorm:"int(11) default NULL 'chId'" json:"coach_id"`
	Calorie int32 `xorm:"int(11) notnull 'calorie'" json:"calorie"`
	Online  int32 `xorm:"tinyint(11) notnull 'environ'" json:"online"`
	// 购买类型1:用户等级匹配，2单独购买
	PurchaseType int32 `xorm:"tinyint(1) notnull  default 0 'purchase_type'" json:"purchase_type"`
	// 计划价格
	Price float64 `xorm:"float(14) default null 'price'" json:"price"`
	// 名师课堂中使用，给老师分成使用 alex 2109.04.22
	CategoryID int32 `xorm:"int(11) notnull 'category_id'" json:"category_id"`
	// 原价
	OriginalPrice float64 `xorm:"float(14) default null 'original_price'" json:"original_price"`
	// 最大使用瑜币数
	MaxPayPoints        int32  `xorm:"int(11) notnull default 0  'max_pay_points'" json:"max_pay_points"`
	ActivityProductName string `xorm:"varchar(256) default '' 'activity_product_name'" json:"activity_product_name"`
	// 0通用，1优惠券与瑜币互斥，2优惠券与瑜币不互斥
	MutexType int32 `xorm:"int(11) notnull default 0  'mutex_type'" json:"mutex_type"`
	// 是否有试看1,有试看，0，无试看练习节数(计划特有字段)
	HasFree int32 `xorm:"tinyint(11) notnull  default 0 'has_free'" json:"has_free"`
	// 练习节数(计划特有字段)
	IncludeCount int32 `xorm:"int(11) notnull  default 0 'IncludeCount'" json:"IncludeCount"`
	// 最大完成天数
	DoneDaysMax int32 `xorm:"int(11) notnull  default 0 'done_days_max'" json:"done_days_max"`
	// 视频时长单位：秒
	ShortVideoDuration int64 `xorm:"int(11) notnull default 0 'short_video_duration'" json:"short_video_duration"`
	// 中英文切换，1支持2，不支持
	// 目前只有名师课堂涉及到这个语言开关  alex 2019.04.22
	LanguageSwitch int32 `xorm:"int(11) notnull  default 2 'language_switch'" json:"language_switch"`
	// 多少人练习 修改 alex 2018.12.12
	PracticeCount int32 `xorm:"int(11) notnull  default 0 'practice_count'" json:"practice_count"`
	// 创建时间
	CreateTime int64 `xorm:"int(11) notnull default 0 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64  `xorm:"int(11) notnull default 0 'update_time'" json:"update_time"`
	Tag        string `xorm:"tinyint(1) notnull default 0 'Tag'" json:"Tag"`
	// 名师课堂单独需要短视频链接 2019.04.22
	ShortVideo string `xorm:"varchar(255) notnull 'short_video'" json:"short_video"`
	// 名师课堂单独需要富文本 2019.04.22
	RichContent   string `xorm:" text 'rich_content'" json:"rich_content"`
	FirstSaleTime int64  `xorm:"int(11) notnull default 0 'uptime'" json:"first_sale_time"`
	// 794增加预售结束时间 alex 2019.06.17
	PreSaleEndTime int64 `xorm:"int(11) notnull default 0 'pre_sale_end_time'" json:"pre_sale_end_time"`
	// 计划预览 url
	PreviewURL         string `xorm:"varchar(256) default '' 'preview_url'" json:"preview_url"`
	SpecialImage       string `xorm:"varchar(128) default '' 'special_image'" json:"special_image"`
	SpecialImageDetail string `xorm:"varchar(128) default '' 'special_image_detail'" json:"special_image_detail"`
	// 适合人群(新)
	DescSuitablePeople string `xorm:"varchar(256) default '' 'desc_suitable_people'" json:"desc_suitable_people"`
	// 效果(新)
	DescEffect string `xorm:"varchar(256) default '' 'desc_effect'" json:"desc_effect"`
	// 练习部位(新)
	DescPart string `xorm:"varchar(256) default '' 'desc_part'" json:"desc_part"`
}

type program struct{}

// TbProgram 外部调用对象
var TbProgram program

// TableName Get table name
func (Program) TableName() string {
	return "sessioninfo"
}

// SearchRequest 搜索字段
type ProgramSearchRequest struct {
	// 计划id
	ProgramID int64
	// 计划名字
	ProgramName string
	// 教练id
	CoachID int32
	// 等级
	Level int32
	// 分类
	CategoryID int32
	// 是否会员
	IsVip int32
	// 是否会员新的判断标准 alex 2019.04.11
	MemberLevelFlag int32
	// 计划类型
	PracticeType int32
	// 计划语言版本
	SupportLanguage int32
	// 页码
	PageSize int
	// 偏移量
	Offset int
	// 内容类型：ContentType， 1： 形体，2：冥想，3：正念冥想
	ContentType int32
}

// 获取课程列表数据
func GetProgramList(request *ProgramSearchRequest) []Program {
	var tables []Program
	session := coursedb.GetEngine().NewSession()
	defer session.Close()
	session = session.Table("sessioninfo").
		Alias("a").
		Distinct("a.SessionId").
		Desc("SessionId").
		Select("a.SessionId,a.uptime,a.SChineseName,a.SenglishName,a.SenglishDes,a.SChineseDes,a.snameandroid,"+
			"a.snameios,a.Created,a.logo_new_cover,a.logo_new_cover_pad,a.logo_new_detail,a.logo_new_detail_pad,"+
			"a.web_images,a.series_type,a.IncludeCount,a.done_days_max,a.goal,a.apply_gender,a.calorie,a.update_time,"+
			"a.chId,a.pre_sale_end_time,a.content_type").
		Join("LEFT", []string{"program_extend_info", "b"}, "a.SessionId = b.programId")
	if request.ProgramID > 0 {
		session = session.Where("a.SessionId=?", request.ProgramID)
	}
	if request.ProgramName != "" {
		session = session.Where("a.SChineseName LIKE ?", "%"+request.ProgramName+"%")
	}
	if request.CoachID > 0 {
		session = session.Where("a.chId=?", request.CoachID)
	}
	if request.Level > 0 {
		session = session.Where("a.Level=?", request.Level)
	}
	if request.PracticeType > 0 {
		session = session.Where("a.series_type=?", request.PracticeType)
	}
	if request.ContentType > 0 {
		session = session.Where("a.content_type=?", request.ContentType)
	}
	// 新的会员判断标准
	if request.MemberLevelFlag > 0 {
		if request.MemberLevelFlag > 1 {
			session = session.Where("a.member_level_flag>?", 1)
		} else {
			session = session.Where("a.member_level_flag<?", 2)
		}
	}
	if request.SupportLanguage > 0 {
		session = session.Where("b.Version=?", request.SupportLanguage)
	}
	err := session.Limit(request.PageSize, request.Offset).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// 事务新增数据
func (t *Program) SaveByTransaction(session *xorm.Session) error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime
	_, err := session.Insert(t)
	return err
}
func (t *Program) UpdateByTransaction(session *xorm.Session) error {
	t.UpdateTime = time.Now().Unix()
	_, err := session.ID(t.ProgramID).Update(t)
	return err
}

// 2019-12-11 water 修复因UpdateByTransaction内加入MustCols造成的问题
func (t *Program) UpdateInfoByTransactionHasMc(session *xorm.Session) error {
	t.UpdateTime = time.Now().Unix()
	_, err := session.ID(t.ProgramID).MustCols("calorie", "preview_url", "desc_effect", "desc_part",
		"desc_suitable_people", "isVip", "short_video").Update(t)
	return err
}

// 保存官网数据,因为要置空
func (t *Program) ResetContent(session *xorm.Session) error {
	_, err := session.Cols("SChineseDes").Where("SessionId=?", t.ProgramID).Update(t)
	return err
}

// 根据ids返回计划
func (t *program) GetProgramListByIds(programIds []int32, deviceType int32, languageVersion int64) ([]Program, error) {
	var tables []Program
	err := coursedb.GetEngine().Table("sessioninfo").Alias("a").
		Select("a.SessionId,a.SChineseName,a.SChineseDes,a.isVip,a.IncludeCount,a.logo_new_cover,a.logo_cover_pad,"+
			"a.logo_new_cover_pad,a.calorie,a.member_level_flag,a.series_type,a.member_level_flag,a.SChineseProfile,"+
			"a.member_level,practice_count,b.Tag").
		Join("LEFT", []string{"program_extend_info", "b"}, "a.`SessionId` = b.`programId`").
		Where("a.environ=?", 1).Where("b.isOnline=?", 1).
		Where("b.deviceType=?", deviceType).Where("b.Version=?", languageVersion).Where("a.content_type !=?", 3).
		In("a.SessionId", programIds).OrderBy("b.`peoOrder` ASC, a.LikeCount DESC").Find(&tables)
	return tables, err
}

// GetProgramListByIdsByVersion 加入版本号判断
func (t *program) GetProgramListByIdsByVersion(programIds []int32,
	deviceType int32, languageVersion, version int64) ([]Program, error) {
	var tables []Program
	err := coursedb.GetEngine().Table("sessioninfo").Alias("a").
		Select("a.SessionId,a.SChineseName,a.SChineseDes,a.isVip,a.IncludeCount,a.logo_new_cover,a.logo_cover_pad,"+
			"a.logo_new_cover_pad,a.calorie,a.member_level_flag,a.series_type,a.member_level_flag,a.SChineseProfile,"+
			"a.member_level,practice_count,b.Tag").
		Join("LEFT", []string{"program_extend_info", "b"}, "a.`SessionId` = b.`programId`").
		Where("a.environ=?", 1).Where("b.isOnline=?", 1).
		Where("b.deviceType=?", deviceType).Where("b.Version=?", languageVersion).
		Where("b.min_version <= ? AND b.max_version >=?", version, version).Where("a.content_type !=?", 3).
		In("a.SessionId", programIds).OrderBy("b.`peoOrder` ASC, a.LikeCount DESC").Find(&tables)
	return tables, err
}

// GetNewProgramList 获取最新计划,只是为了封面取出数据做判断，可以用分页数据判断
func (t *program) GetNewProgramList(deviceType int32, pageSize, offset int) []Program {
	var tables []Program
	session := coursedb.GetEngine().NewSession()
	defer session.Close()
	session = session.Table("sessioninfo").Alias("a").
		Select("a.SessionId,a.SChineseName,a.SChineseDes,a.isVip,a.IncludeCount,a.logo_cover,a.logo_new_cover,"+
			"a.logo_cover_pad,a.logo_new_cover_pad,a.calorie,a.member_level_flag,a.series_type,a.SChineseProfile,"+
			"a.member_level,practice_count").
		Join("LEFT", []string{"program_extend_info", "b"}, "a.`SessionId` = b.`programId`").
		Where("a.environ=?", 1).Where("b.isOnline=?", 1).Desc("b.new_practice_remind_time").
		Where("b.deviceType=?", deviceType).Where("b.Version=?", 1).Where("series_type<>?", 3).
		Where("b.tag=?", 2).OrderBy("b.`peoOrder` ASC, a.LikeCount DESC,b.new_practice_remind_time DESC")
	if pageSize > 0 {
		session.Limit(pageSize, offset)
	}
	if err := session.Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetProgramByID 通过计划ID获取计划详情
func (t *program) GetProgramByID(id int64) *Program {
	var table Program

	ok, err := coursedb.GetEngine().Where("SessionId = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// 查询计划主信息，未涉及到资源分化信息
// alex 2019.05.09
func (t *program) GetProgramByIDs(ids []int64) []Program {
	var tables []Program
	err := coursedb.GetEngine().In("SessionId", ids).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetProgramBySeriesType 根据类型获取计划
// 2017-12-21 15:06:22 朱家琪 创建
func (t *program) GetProgramBySeriesType(seriesType int64) []Program {
	var tables []Program
	err := coursedb.GetEngine().
		Select("SessionId,SChineseName,SChineseDes,logo_new_cover,logo_new_cover_pad,IncludeCount,chId,"+
			"short_video_image,short_video,member_level_flag,web_images,content_type").
		Join("left", "program_extend_info", "sessioninfo.SessionId=program_extend_info.programId").
		Where("sessioninfo.series_type = ? AND sessioninfo.environ = 1 AND program_extend_info.DeviceType = ? AND "+
			"program_extend_info.isOnline = 1", seriesType, 2).
		OrderBy("program_extend_info.peoOrder asc").
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
