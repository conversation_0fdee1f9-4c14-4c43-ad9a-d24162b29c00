package union

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// 联合会员活动主表
type ActivityRecord struct {
	ID               int64  `xorm:"not null autoincr pk int(11) 'id'"`
	UID              int64  `xorm:"not null int(11) default '0' 'uid'"`
	OrderID          string `xorm:"not null default '' varchar(64) 'order_id'"`           // 订单号
	ActivityID       int64  `xorm:"not null int(11) default '0' 'activity_id'"`           // 联合会员活动ID
	InnerProductType int32  `xorm:"not null tinyint(1) default '0' 'inner_product_type'"` // 内部关联产品类型 1 会员产品 2 特惠套餐
	InnerProductID   int64  `xorm:"not null int(11) default '0' 'inner_product_id'"`      // 内部商品ID
	PartnerID        int64  `xorm:"not null tinyint(1) default '0' 'partner_id'"`         // 合作方
	Status           int32  `xorm:"not null tinyint(1) default '0' 'status'"`             // 订单状态0 预下单 1 已支付
	OuterProductID   int64  `xorm:"not null int(11) default '0' 'outer_product_id'"`      // 合作方商品ID
	ReceiveMobile    string `xorm:"not null default '' varchar(32) 'receive_mobile'"`     // 领取手机号
	ReceiveResult    int32  `xorm:"not null tinyint(1) default '0' 'receive_result'"`     // 领取状态 0未领取 1 成功 2 否
	ValidTime        int64  `xorm:"not null int(11)  default '0' 'valid_time'"`           // 有效领取时间
	ReceiveTime      int64  `xorm:"not null int(11)  default '0' 'receive_time'"`         // 有效领取时间
	ReceiveMsg       string `xorm:"not null default '' varchar(256) 'receive_msg'"`       // 第三方返回信息
	CreateTime       int64  `xorm:"not null int(11)  default '0' 'create_time'"`
	UpdateTime       int64  `xorm:"not null int(11) default '0' 'update_time'"`
}

func (ActivityRecord) TableName() string {
	return "union_member_activity_record"
}

// update 修改
func (a *ActivityRecord) UpdateByTransaction(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

type activityRecord struct {
}

var TbActivityRecord activityRecord

// 根据订单ID查询记录列表
func (a *activityRecord) GetRecordListByOrderID(orderID string) []*ActivityRecord {
	var tables []*ActivityRecord
	err := db.GetEngine().
		Where(" order_id = ? ", orderID).
		Find(&tables)
	if err != nil {
		logger.Error("查询记录失败:", orderID, err)
		return nil
	}
	return tables
}
