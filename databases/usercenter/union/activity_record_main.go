package union

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// 联合会员活动主表
type ActivityRecordMain struct {
	ID          int64  `xorm:"not null autoincr pk int(11) 'id'"`
	UID         int64  `xorm:"not null int(11) default '0' 'uid'"`
	OrderID     string `xorm:"not null default '' varchar(64) 'order_id'"`               // 订单号
	ActivityID  int64  `xorm:"not null int(11) default '0' 'activity_id'"`               // 联合会员活动ID
	ProductType int32  `xorm:"not null tinyint(1) default '0' 'condition_product_type'"` // 内部关联产品类型 1 会员产品 2 特惠套餐
	ProductID   int64  `xorm:"not null int(11) default '0' 'condition_product_id'"`      // 内部商品ID
	Status      int32  `xorm:"not null tinyint(1) default '0' 'status'"`                 // 订单状态0 预下单 1 已支付
	CreateTime  int64  `xorm:"not null int(11)  default '0' 'create_time'"`
	UpdateTime  int64  `xorm:"not null int(11) default '0' 'update_time'"`
}

func (ActivityRecordMain) TableName() string {
	return "union_member_activity_record_main"
}

// update 修改
func (a *ActivityRecordMain) UpdateByTransaction(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

type recordMain struct {
}

var TbRecordMain recordMain

// 根据订单ID查询记录
func (a *recordMain) GetRecordByOrderID(orderID string) *ActivityRecordMain {
	var table ActivityRecordMain
	ok, err := db.GetEngine().
		Where(" order_id = ? ", orderID).
		Get(&table)
	if err != nil {
		logger.Warn("查询记录失败:", err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
