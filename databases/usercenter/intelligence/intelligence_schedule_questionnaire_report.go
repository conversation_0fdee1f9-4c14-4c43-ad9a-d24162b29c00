package intelligence

import (
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// IntelligenceScheduleQuestionnaireReport 智能课程表用户表
type ScheduleQuestionnaireReport struct {
	ID         int64 `xorm:"not null autoincr pk int(10) unsigned 'id'"`
	CreateTime int64 `xorm:"not null default '0' int(11) unsigned 'create_time'"`
	UpdateTime int64 `xorm:"not null default '0' int(11) unsigned 'update_time'"`
	// 用户id
	UID int64 `xorm:"not null default '0' int(11) unsigned 'uid'"`
	// 目标类型 1->减脂塑形;2->身体调理;3->体态纠正;4->入门到进阶
	GoalType string `xorm:"not null varchar(64) 'goal_type'"`
	// 性别
	Sex string `xorm:"not null varchar(64) 'sex'"`
	// 体型
	BodyType string `xorm:"not null varchar(64) 'body_type'"`
	// 部位偏好：1全身、2腰腹、3上肢、4臀腿
	SitePreference string `xorm:"not null varchar(64) 'site_preference'"`
	// 选择所处的瑜伽阶段
	YogaStage string `xorm:"not null varchar(64) 'yoga_stage'"`
	// 练习时长，1：10~20分钟；2：20~40分钟；3：40分钟以上
	Duration string `xorm:"not null varchar(64) 'duration'"`
	// 年龄
	Age string `xorm:"not null varchar(64) 'age'"`
	// 身高
	Height string `xorm:"not null varchar(64) 'height'"`
	// 体重
	Weight string `xorm:"not null varchar(64) 'weight'"`
	// 想解决的身体问题
	PhysicalProblems string `xorm:"not null varchar(64) 'physical_problems'"`
	// 是否有久坐的情况
	Sedentary string `xorm:"not null varchar(64) 'sedentary'"`
	// 想解决的体态问题
	Posture string `xorm:"not null varchar(64) 'posture'"`
	// 想解决的体态问题
	Skill string `xorm:"not null varchar(64) 'skill'"`
	// 扩展问题
	ExtQuestions string `xorm:"not null text 'ext_questions'"`
}

// TableName 获取表名
func (ScheduleQuestionnaireReport) TableName() string {
	return "intelligence_schedule_questionnaire_report"
}

type _intelligenceScheduleQuestionnaireReport struct{}

var TbIntelligenceScheduleQuestionnaireReport _intelligenceScheduleQuestionnaireReport

func (*_intelligenceScheduleQuestionnaireReport) GetIntelligenceByUID(
	uid int64) ([]*ScheduleQuestionnaireReport, error) {
	var tables []*ScheduleQuestionnaireReport
	err := usercenter.GetEngine().Where("uid = ?", uid).Find(&tables)

	if err != nil {
		return tables, err
	}
	return tables, nil
}
