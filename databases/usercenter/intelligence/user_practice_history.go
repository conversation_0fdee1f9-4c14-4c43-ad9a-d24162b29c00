package intelligence

import (
	"github.com/go-xorm/xorm"
)

type UserPracticeHistory struct {
	ID int64 `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	// 用户ID
	UserID int64 `xorm:"int(11) notnull default 0 'user_id'" json:"user_id"`
	// 练习日期
	PracticeDate int64 `xorm:"int(11) notnull default 0 'practice_date'" json:"practice_date"`
	// 课程id
	SessionID int64 `xorm:"int(11) notnull default 0 'session_id'" json:"session_id"`
	// 记录上传时间
	UploadTime int64 `xorm:"int(11) notnull default 0 'upload_time'" json:"upload_time"`
	// 创建时间
	CreateTime int64 `xorm:"int(11) notnull default 0 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"int(11) notnull default 0 'update_time'" json:"update_time"`
}
type userPracticeHistory struct{}

// TbUserSession 外部调用对象
var TbUserPracticeHistory userPracticeHistory

// TableName Get table name
func (UserPracticeHistory) TableName() string {
	return "intelligence_schedule_user_practice_history"
}

// 记录神策上报数据
func (t *UserPracticeHistory) SaveByTransaction(session *xorm.Session) error {
	_, err := session.Insert(t)
	return err
}
