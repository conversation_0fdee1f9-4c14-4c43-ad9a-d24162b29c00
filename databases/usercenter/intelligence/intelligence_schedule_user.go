package intelligence

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// ScheduleUser 智能课程表用户表
type ScheduleUser struct {
	ID int64 `xorm:"not null autoincr pk int(10) unsigned 'id'"`
	// 用户id
	UserID int64 `xorm:"not null default '0' int(11) unsigned 'user_id'"`
	// 课程表编号
	ScheduleID int64 `xorm:"not null default '0' int(11) unsigned 'schedule_id'"`
	// 课程表总共多少天
	SessionDays int32 `xorm:"not null default '0' tinyint(1) unsigned 'session_days'"`
	// 课表难度等级默认-1:初级2:中级3:高级
	Level int32 `xorm:"not null default '1' tinyint(1) unsigned 'level'"`
	// 课表开始时间
	StartTime int64 `xorm:"not null default '0' int(11) unsigned 'start_time'"`
	// 课表结束时间
	EndTime int64 `xorm:"not null default '0' int(11) unsigned 'end_time'"`
	// 目标id
	GoalID int64 `xorm:"not null default '0' int(11) unsigned 'goal_id'"`
	// 目标名称
	GoalName string `xorm:"not null varchar(80) 'goal_name'"`
	// 测试专用
	NowTimestamp int64 `xorm:"not null default '0' int(11) unsigned 'now_timestamp'"`
	// 课程表结束时间
	ScheduleEndTime int64 `xorm:"not null default '0' int(11) unsigned 'schedule_end_time'"`
	// 数据上报用
	QuitTime int64 `xorm:"not null int(11) unsigned 'quit_time'"`
	// 报告生成状态默认-1:待生成 2：已生成
	ReportStatus int32 `xorm:"not null default '1' tinyint(1) unsigned 'report_status'"`
	// 课表状态默认-1:进行中2:完成3:循环4:退出
	Status int32 `xorm:"not null default '1' tinyint(1) unsigned 'status'"`
	// 完成时间
	CompleteTime int64 `xorm:"int(11) notnull default 0 'complete_time'" json:"complete_time"`
	// 1:删除2:未删除
	DeleteStatus int32 `xorm:"not null default '2' tinyint(1) unsigned 'delete_status'"`
	CreateTime   int64 `xorm:"not null default '0' int(11) unsigned 'create_time'"`
	UpdateTime   int64 `xorm:"not null default '0' int(11) unsigned 'update_time'"`
}

// TableName 获取表名
func (ScheduleUser) TableName() string {
	return "intelligence_schedule_user"
}

// 更新课程
func (s *ScheduleUser) Update() error {
	_, err := usercenter.GetEngineMaster().ID(s.ID).Update(s)
	return err
}

type _intelligenceScheduleUser struct{}

var TbIntelligenceScheduleUser _intelligenceScheduleUser

func (*_intelligenceScheduleUser) GetIntelligenceUser(uid, programId int64) (*ScheduleUser, error) {
	var table ScheduleUser
	has, err := usercenter.GetEngine().Where("user_id=? and id=?", uid, programId).Get(&table)

	if !has || err != nil {
		return nil, err
	}
	return &table, nil
}

func (*_intelligenceScheduleUser) GetIntelligenceScheduleUserDetail(id int64) *ScheduleUser {
	var table ScheduleUser
	ok, err := usercenter.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*_intelligenceScheduleUser) GetIntelligenceScheduleUserIngDetail(uid int64) *ScheduleUser {
	var table ScheduleUser
	ok, err := usercenter.GetEngine().Where("user_id = ? ", uid).
		In("status", []int32{1, 2}).
		Where("delete_status =?", 2).
		Desc("id").
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
