package intelligence

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type UserSchedule struct {
	ID int64 `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	// 用户ID
	UserID int64 `xorm:"int(11) notnull default 0 'user_id'" json:"user_id"`
	// 课程表索引
	ScheduleID int64 `xorm:"int(11) notnull default 0 'schedule_id'" json:"schedule_id"`
	// 难度等级
	Level int32 `xorm:"tinyint(11) notnull  default 1 'level'" json:"level"`
	// 课程开始
	StartTime int64 `xorm:"int(11) notnull default 0 'start_time'" json:"start_time"`
	// 课程节数
	EndTime int64 `xorm:"int(11) notnull default 0 'end_time'" json:"end_time"`
	// 1:进行中2:完成3:循环4:退出
	Status int32 `xorm:"tinyint(11) notnull  default 0 'status'" json:"status"`
	// 报告状态
	ReportStatus int32 `xorm:"tinyint(11) notnull  default 0 'report_status'" json:"report_status"`
	// 完成时间
	CompleteTime int64 `xorm:"int(11) notnull default 0 'complete_time'" json:"complete_time"`
	// 课表是否删除1:删除2:未删除
	DeleteStatus int32 `xorm:"tinyint(11) notnull  default 0 'delete_status'" json:"delete_status"`
	// 创建时间
	CreateTime int64 `xorm:"int(11) notnull default 0 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"int(11) notnull default 0 'update_time'" json:"update_time"`
}

type UserScheduleQuery struct {
	UID          int64 `json:"uid"`
	Status       int32
	ReportStatus int32
}

type userSchedule struct{}

// TbUserSession 外部调用对象
var TbUserSchedule userSchedule

// TableName Get table name
func (UserSchedule) TableName() string {
	return "intelligence_schedule_user"
}

// 更新习数据
func (t *UserSchedule) UpdateByTransaction(session *xorm.Session) (err error) {
	t.UpdateTime = time.Now().Unix()
	_, err = session.Where("id=?", t.ID).Update(t)
	return
}
func (t *userSchedule) GetDetail(request *UserScheduleQuery) *UserSchedule {
	var table UserSchedule
	ok, err := usercenter.GetEngine().Where("user_id=? and delete_status=?", request.UID, 2).Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
