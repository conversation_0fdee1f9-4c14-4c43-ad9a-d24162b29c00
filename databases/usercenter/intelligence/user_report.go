package intelligence

import (
	"github.com/go-xorm/xorm"
)

type UserReport struct {
	ID int64 `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	// 用户ID
	UserID int64 `xorm:"int(11) notnull default 0 'user_id'" json:"user_id"`
	// 用户课程表
	UserScheduleID int64 `xorm:"int(11) notnull default 0 'user_schedule_id'" json:"user_schedule_id"`
	// 已完成课程数
	FinishedSessionCount int64 `xorm:"int(8)) notnull  default 0 'finished_session_count'" json:"finished_session_count"`
	// 课程表包含的课程总数
	TotalSessionCount int64 `xorm:"int(8) notnull  default 0 'total_session_count'" json:"total_session_count"`
	// 练习时长，单位分钟
	PracticeDuration int64 `xorm:"int(8) notnull default 0 'practice_duration'" json:"practice_duration"`
	// 练习产生的卡路里
	Calories int64 `xorm:"int(11) notnull default 0 'calories'" json:"calories"`
	// 创建时间
	CreateTime int64 `xorm:"int(11) notnull default 0 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"int(11) notnull default 0 'update_time'" json:"update_time"`
}
type userReport struct{}

// TbUserSession 外部调用对象
var TbUserReport userReport

// TableName Get table name
func (UserReport) TableName() string {
	return "intelligence_schedule_user_report"
}

// 更新练习报告
func (t *UserReport) UpdateByTransaction(session *xorm.Session) (err error) {
	sql := "update `intelligence_schedule_user_report` set finished_session_count=?, total_session_count=?, practice_duration=? ,calories=? ," +
		"update_time=? where user_id=? and user_schedule_id=?"
	_, err = session.Exec(sql, t.FinishedSessionCount, t.TotalSessionCount, t.PracticeDuration, t.Calories, t.UpdateTime, t.UserID, t.UserScheduleID)
	return
}
