package intelligence

import (
	"strconv"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type UserSession struct {
	ID               int64 `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	UserID           int64 `json:"user_id" xorm:"not null default 0 'user_id' comment('用户id') index(ids_user_practice_date) INT(11)"`
	ScheduleID       int64 `json:"schedule_id" xorm:"not null default 0 'schedule_id' comment('课程表id(冗余字段)') INT(11)"`
	UserScheduleID   int64 `json:"user_schedule_id" xorm:"not null default 0 'user_schedule_id'  comment('用户课程表id') index INT(11)"`
	PracticeDate     int64 `json:"practice_date" xorm:"not null default 0 'practice_date' comment('练习日期') index(ids_user_practice_date) INT(11)"`
	SessionId        int64 `json:"session_id" xorm:"not null default 0 'session_id' comment('课程id') INT(11)"`
	DayOrder         int32 `json:"day_order" xorm:"not null default 0 'day_order' comment('上报需要') TINYINT(1)"`
	DayType          int32 `json:"day_type" xorm:"not null default 1 'day_type' comment('课程默认-1:练习日 2:休息日') TINYINT(1)"`
	SessionIndex     int32 `json:"session_index" xorm:"not null default 0 'session_index' comment('课程索引') TINYINT(1)"`
	PracticeCount    int32 `json:"practice_count" xorm:"not null default 0 'practice_count' comment('练习次数') TINYINT(1)"`
	PracticeDuration int32 `json:"practice_duration" xorm:"not null default 0 'practice_duration' comment('时长合计：单位分钟') INT(11)"`
	Calories         int32 `json:"calories" xorm:"not null default 0 'calories' comment('卡路里合计:单位千卡') INT(11)"`
	Status           int32 `json:"status" xorm:"not null default 1 'status'  comment('课表中课程状态-默认1:正常 2:已过期') TINYINT(1)"`
	CreateTime       int64 `json:"create_time" xorm:"not null default 0 'create_time'  INT(11)"`
	UpdateTime       int64 `json:"update_time" xorm:"not null default 0 'update_time' INT(11)"`
}

type userSession struct{}

// TbUserSession 外部调用对象
var TbUserSession userSession

// TableName Get table name
func (UserSession) TableName() string {
	return "intelligence_schedule_user_session"
}

// 根据用户ID返回课程表数据
// alex 2019.06.30
func (*userSession) ScheduleSessionList(UID int64) (data []*UserSession) {
	err := usercenter.GetEngine().Where("user_id=?", UID).Find(&data)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return
}

// 实时更新课程中数据
// alex 2019.07.01
func (t *UserSession) UpdateByTransaction(session *xorm.Session) (err error) {
	sql := "update `intelligence_schedule_user_session` set practice_count=practice_count+?, practice_duration=practice_duration+?, calories=calories+?," +
		"update_time=? where user_id=? and practice_date=? and session_index=?"
	_, err = session.Exec(sql, t.PracticeCount, t.PracticeDuration, t.Calories, t.UpdateTime, t.UserID, t.PracticeDate, t.SessionIndex)
	return
}

// 更新课程
func (t *UserSession) Update() error {
	_, err := usercenter.GetEngineMaster().ID(t.ID).Update(t)
	return err
}


// 课程表中最后一天的课程信息
// 慢查询修改 alex 2019.08.28
func (t *userSession) SessionByPracticeDate(userID, practiceDate int64) (data []*UserSession) {
	err := usercenter.GetEngine().Where("user_id=? and practice_date=?", userID, practiceDate).Find(&data)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return
}

// 求某个课程表中当前练习数据各项值
// alex 2019.07.01
func (t *userSession) CollectUserSession(UID int64, userScheduleID int64) map[string]int64 {
	resp := make(map[string]int64)
	sql := "select sum(practice_duration) as practice_duration,sum(calories) as calories,sum(case WHEN practice_count>=1 then 1 else 0 end ) as practice_count " +
		"from intelligence_schedule_user_session where user_id=? and user_schedule_id=? and day_type=?"
	resultArray, err := usercenter.GetEngineMaster().Query(sql, UID, userScheduleID, 1)
	if err != nil {
		logger.Error(err)
		return nil
	}

	convertToInt64 := func(i string) int64 {
		d, err := strconv.Atoi(i)
		if err != nil {
			return 0
		}
		return int64(d)
	}

	if len(resultArray) > 0 {
		for _, v := range resultArray {
			calories := convertToInt64(string(v["calories"]))
			practiceCount := convertToInt64(string(v["practice_count"]))
			practiceDuration := convertToInt64(string(v["practice_duration"]))
			resp["practice_count"] = practiceCount
			resp["calories"] = calories
			resp["practice_duration"] = practiceDuration
		}
	}
	return resp
}

func (t *userSession) GetScheduleSessionByDay(userID, practiceDate int64, sessionIndex int32) (data *UserSession) {
	var table UserSession
	ok, err := usercenter.GetEngine().Where("`user_id` = ? and practice_date = ? AND session_index = ?", userID, practiceDate, sessionIndex).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// 根据智能课表id获取智能课表的课程列表
func (t *userSession) GetScheduleSessionByUserScheduleID(userScheduleID int64) []*UserSession {
	var tables []*UserSession
	err := usercenter.GetEngine().Where("`user_schedule_id` = ?", userScheduleID).OrderBy("`day_order` asc,`session_index` asc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}