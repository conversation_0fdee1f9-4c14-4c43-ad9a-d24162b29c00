package iap

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	databases "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type HmsIapOrder struct {
	ID                   int64  `xorm:"pk autoincr not null int(11) 'id'"`
	PurchaseOrderID      string `xorm:"not null default '' varchar(256) 'purchase_order_id'"`
	PurchaseToken        string `xorm:"not null default '' varchar(256) 'purchase_token'"`
	SubGroupGenerationID string `xorm:"not null default '' varchar(256) 'sub_group_generation_id'"`
	SubScriptionID       string `xorm:"not null default '' varchar(256) 'sub_scription_id'"`
	ProductID            string `xorm:"default '' comment('商品ID') varchar(64) 'product_id'"`
	OrderID              string `xorm:"default '' comment('订单号') varchar(64) 'order_id'"`
	UID                  int64  `xorm:"default 0 comment('用户uid') bigint(20) 'uid'"`
	PurchaseTime         int64  `xorm:"not null default 0 comment('购买时间') int(10) 'purchase_time'"`
	ExpireTime           int64  `xorm:"not null int(11) 'expire_time'"`
	CancelTime           int64  `xorm:"not null default 0 comment('退款时间') int(10) 'cancel_time'"`
	CreateTime           int64  `xorm:"not null default 0 comment('创建时间') int(10) 'create_time'"`
	UpdateTime           int64  `xorm:"not null default 0 comment('更新时间') int(10) 'update_time'"`
	IsTrialPeriod        int32  `xorm:"not null default 0 tinyint(1) 'is_trial_period'"`
	IsUpgraded           int    `xorm:"not null tinyint(1) 'is_upgraded'"`
}

func (a *HmsIapOrder) TableName() string {
	return "hms_iap_order_list"
}

// Save 保存数据
func (a *HmsIapOrder) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *HmsIapOrder) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// SaveByTran 事务保存数据
func (a *HmsIapOrder) SaveByTran(session *xorm.Session) error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := session.Insert(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *HmsIapOrder) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

type hmsIapOrder struct{}

var TbHmsIapOrder hmsIapOrder

func (*hmsIapOrder) GetItemByTran(purchaseToken, purchaseOrderID string) *HmsIapOrder {
	var table HmsIapOrder
	ok, err := databases.GetEngine().Where("purchase_token = ? and purchase_order_id =?",
		purchaseToken, purchaseOrderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetLastItemBySubGroupGenerationID 根据原始交易id获取最后一条数据
func (*hmsIapOrder) GetLastItemBySubGroupGenerationID(subGroupGenerationID string) *HmsIapOrder {
	var table HmsIapOrder
	ok, err := databases.GetEngine().Where("sub_group_generation_id = ?",
		subGroupGenerationID).Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*hmsIapOrder) GetItemByWebItemID(originalTranID, webItemID string) *HmsIapOrder {
	var table HmsIapOrder
	ok, err := databases.GetEngine().Where("original_tran_id = ? and web_item_id = ?",
		originalTranID, webItemID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*hmsIapOrder) GetListByOriginal(originalTranID string) []*HmsIapOrder {
	var tables []*HmsIapOrder
	err := databases.GetEngine().Where("original_tran_id = ?", originalTranID).Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*hmsIapOrder) RefundIosOrder(orderID string) *HmsIapOrder {
	var table HmsIapOrder
	ok, err := databases.GetEngine().Where("order_id = ? and cancel_time > 0", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*hmsIapOrder) GetListByUID(uid int64) []*HmsIapOrder {
	var tables []*HmsIapOrder
	err := databases.GetEngine().Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*hmsIapOrder) GetItemByOrderID(orderID string) *HmsIapOrder {
	var table HmsIapOrder
	ok, err := databases.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*hmsIapOrder) GetNoOfferByOrig(originalTranID string) []*HmsIapOrder {
	var tables []*HmsIapOrder
	err := databases.GetEngine().Where("original_tran_id = ? and is_trial_period = 0 "+
		"and is_in_intro_offer_period = 0", originalTranID).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*hmsIapOrder) GetIOSOrderByProductID(uid int64, productIDArr []string) []*HmsIapOrder {
	var tables []*HmsIapOrder
	err := databases.GetEngine().Where("uid = ?", uid).In("product_id", productIDArr).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
