package iap

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	databases "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type HmsIapRequestLog struct {
	ID                   int64  `xorm:"pk autoincr not null int(11) 'id'"`
	UID                  int64  `xorm:"default 0 comment('用户uid') bigint(20) 'uid'"`
	SubGroupGenerationID string `xorm:"not null default '' varchar(256) 'sub_group_generation_id'"`
	OrderID              string `xorm:"not null default '' comment('预下单ID') varchar(64) 'order_id'"`
	PurchaseOrderID      string `xorm:"not null default '' varchar(128) 'purchase_order_id'"`
	PurchaseToken        string `xorm:"not null default '' varchar(128) 'purchase_token'"`
	Request              string `xorm:"not null longtext 'request'"`
	ReceiptDetail        string `xorm:"not null comment('iap交易') longtext 'receipt_detail'"`
	ProcessResult        string `xorm:"not null  text 'process_result'"`
	IsDeal               int32  `xorm:"not null default 2 comment('是否处理，1 是，2 否') tinyint(1) 'is_deal'"`
	CreateTime           int64  `xorm:"not null default 0 comment('创建时间') int(10) 'create_time'"`
	UpdateTime           int64  `xorm:"not null default 0 comment('更新时间') int(10) 'update_time'"`
}

func (*HmsIapRequestLog) TableName() string {
	return "hms_iap_request_log"
}

// Save 保存数据
func (a *HmsIapRequestLog) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *HmsIapRequestLog) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *HmsIapRequestLog) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

type hmsIapRequest struct{}

var TbHmsIapRequest hmsIapRequest

func (*hmsIapRequest) GetListByPurchaseToken(purchaseToken string) []*HmsIapRequestLog {
	var tables []*HmsIapRequestLog
	err := databases.GetEngine().Where("purchase_token = ?", purchaseToken).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*hmsIapRequest) GetItemByID(id int64) *HmsIapRequestLog {
	var table HmsIapRequestLog
	ok, err := databases.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetNotDealList 获取未处理的ios receipt上报
func (*hmsIapRequest) GetNotDealList(start, end int64) []*HmsIapRequestLog {
	var tables []*HmsIapRequestLog
	err := databases.GetEngine().Where("is_deal = ? and create_time >= ? and create_time <= ?", library.No, start, end).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetListByUID 根据UID获取IOS上报数据
func (*hmsIapRequest) GetListByUID(uid int64) []*HmsIapRequestLog {
	var tables []*HmsIapRequestLog
	err := databases.GetEngine().Where("uid = ? ", uid).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
