package push

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// Detail 消息记录按月表
type Detail struct {
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 消息接受人uid
	UID int64 `xorm:"not null default '0' int(11) unsigned index(IDX_UID_MSGTYPE_ISREAD) 'uid'"`
	// 创建时间
	CreateTime int64 `xorm:"not null default '0' int(11) unsigned 'create_time' comment('创建时间')"`
	// 消息发送者uid
	FromUID int64 `xorm:"not null default '0' int(11) unsigned index(IDX_FROMUID) 'from_uid' comment('消息发送者uid')"`
	// 消息类型，1：瑜小蜜，2：评论，3：新增粉丝，4：赞帖子，5：回复了帖子，6：系统消息，7：申请信息，8：审核推送
	MsgType int32 `xorm:"not null default '0' tinyint(3) unsigned index(IDX_UID_MSGTYPE_ISREAD) 'msg_type'"`
	// 资源对象id
	ObjID string `xorm:"default '0' varchar(64) 'obj_id' comment('资源对象id')"`
	// 资源内容
	Content string `xorm:"text 'content' comment('资源内容')"`
	// 是否阅读 0-未阅读 1-已阅读 2-已清除
	IsRead int32 `xorm:"not null default '0' tinyint(3) unsigned index(IDX_UID_MSGTYPE_ISREAD) 'is_read'"`
	// 对客户端是否隐藏 0-不隐藏 1-隐藏
	IsClientHide int32 `xorm:"not null default '0' tinyint(3) unsigned 'is_client_hide'"`
	// 是否已回复 0-未回复 1-已回复
	IsReply int32 `xorm:"not null default '0' tinyint(3) unsigned 'is_reply' comment('是否已回复 0-未回复 1-已回复')"`
	// 消息id
	PushID int64 `xorm:"not null bigint(20) unsigned index(IDX_PUSH_ID) 'push_id' comment('消息id')"`
	// 接受端 0-全部 1-安卓 2-IOS
	Platform int32 `xorm:"not null default '0' tinyint(3) unsigned 'platform' comment('接受端 0-全部 1-安卓 2-IOS')"`
	// 渠道类型 1-普通 2-电商 3-瑜乐大学
	ChannelType int32 `xorm:"not null default '1' tinyint(3) unsigned 'channel_type'"`
	// 渠道类型二级分类
	ChannelTypeID int64 `xorm:"not null default '0' int(11) unsigned 'channel_type_id' comment('渠道类型二级分类')"`
	// 推送标签
	Tags string `xorm:"not null default '''' varchar(255) 'tags' comment('推送标签')"`
	// 点击来源类型
	SourceType int32 `xorm:"not null default '0' tinyint(4) 'source_type' comment('点击来源类型')"`
	// 点击来源id
	SourceID int64 `xorm:"not null default '0' int(11) 'source_id' comment('点击来源id')"`
	// 客服id
	AdminID int64 `xorm:"not null default '0' int(11) unsigned index(IDX_ADMIN_ID) 'admin_id' comment('客服id')"`
	// 当次客服反馈结果
	FeedStatus int32 `xorm:"not null default '0' tinyint(2) 'feed_status' comment('当次客服反馈结果')"`

	// 查询时间
	UpdateTime int64 `xorm:"not null default '0' int(11) unsigned 'update_time' comment('查询时间')"`
}

// TableName 获取表名
func (d *Detail) TableName() string {
	month := time.Unix(d.CreateTime, 0).Format("200601")
	return fmt.Sprintf("notice_push_detail_%s", month)
}

// DelForUidMessage 清理推送
func (d *Detail) DelForUIDMessage(msgType []int32, start, end int64) error {
	session := usercenter.GetEngineMaster().Table(d.TableName())
	if d.FromUID > 0 {
		session.Where("from_uid = ?", d.FromUID)
	} else {
		session.Where("uid = ?", d.UID)
	}
	session.Where("create_time >= ? AND create_time < ?", start, end)
	_, err := session.In("msg_type", msgType).
		In("is_read", []int32{0, 1}).
		Update(&Detail{IsRead: 2})
	return err
}
