package push

import (
	"errors"
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type Info struct {
	ID            int64  `xorm:"not null autoincr pk unsigned int(11) 'id'"`
	PushID        int64  `xorm:"not null unsigned bigint(11) 'push_id'"`
	Title         string `xorm:"not null varchar(255) 'title'"`
	Content       string `xorm:"not null varchar(255) 'content'"`
	Extras        string `xorm:"not null varchar(255) 'extras'"`
	IsAlias       int    `xorm:"not null tinyint(1) 'is_alias'"`
	IsTag         int    `xorm:"not null tinyint(1) 'is_tag'"`
	IsAll         int    `xorm:"not null tinyint(1) 'is_all'"`
	CurrentStatus int    `xorm:"not null tinyint(1) 'current_status'"`
	CreateTime    int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime    int64  `xorm:"not null int(11) 'update_time'"`
}

func (Info) TableName() string {
	return "message_push_info"
}

func (i *Info) Save() error {
	i.CreateTime = time.Now().Unix()
	i.UpdateTime = i.CreateTime
	i.CurrentStatus = CurrentStatus.NotStart

	if i.IsAll != DBIsField.Yes {
		i.IsAll = DBIsField.No
	}
	if i.IsAlias != DBIsField.Yes {
		i.IsAlias = DBIsField.No
	}
	if i.IsTag != DBIsField.Yes {
		i.IsTag = DBIsField.No
	}

	_, err := usercenter.GetEngineMaster().Insert(i)
	return err
}

func (i *Info) Update() error {
	if i.ID < 1 && i.PushID < 1 {
		return errors.New("未指定id或push_id")
	}
	i.UpdateTime = time.Now().Unix()

	if i.ID > 0 {
		_, err := usercenter.GetEngineMaster().ID(i.ID).Update(i)
		return err
	}
	_, err := usercenter.GetEngineMaster().Where("push_id = ?", i.PushID).Update(i)
	return err
}
