package push

import (
	"fmt"
	"time"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type Target struct {
	ID         int64  `xorm:"not null autoincr pk unsigned bigint(20) 'id'"`
	PushID     int64  `xorm:"not null int(10) 'push_id'"`
	Alias      string `xorm:"not null varchar(255) 'alias'"`
	Tag        string `xorm:"not null varchar(255) 'tag'"`
	Status     int    `xorm:"not null tinyint(1) 'status'"`
	CreateTime int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64  `xorm:"not null int(11) 'update_time'"`
}

func (t *Target) TableName() string {
	var month string
	if t.CreateTime > 0 {
		month = time.Unix(t.CreateTime, 0).Format("200601")
	} else if t.UpdateTime > 0 {
		month = time.Unix(t.UpdateTime, 0).Format("200601")
	}
	return fmt.Sprintf("message_push_target_%s", month)
}

func (t *Target) Save() error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime
	t.Status = CurrentStatus.NotStart

	_, err := usercenter.GetEngineMaster().Insert(t)
	return err
}

func (t *Target) Update() error {
	if t.ID < 1 && t.PushID < 1 {
		return errors.New("未指定id或push_id")
	}
	t.UpdateTime = time.Now().Unix()

	if t.ID > 1 {
		_, err := usercenter.GetEngineMaster().ID(t.ID).Update(t)
		return err
	}
	_, err := usercenter.GetEngineMaster().Where("push_id = ?", t.PushID).Update(t)
	return err
}

type target struct{}

var TbTarget target

func (t *target) BatchUpdate(id []int64, bean *Target) error {
	bean.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().In("id", id).Update(bean)
	return err
}

func (t *target) BatchInsert(data []*Target) error {
	_, err := usercenter.GetEngineMaster().Insert(data)
	return err
}
