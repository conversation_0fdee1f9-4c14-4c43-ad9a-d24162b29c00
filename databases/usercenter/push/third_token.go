package push

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type ThirdToken struct {
	ID         int64               `xorm:"not null autoincr pk int(11) 'id'"`
	UID        int64               `xorm:"not null int(11) 'uid'"`
	DeviceID   string              `xorm:"not null varchar(255) 'deviceId'"`
	Status     int32               `xorm:"not null tinyint(1) 'status'"`
	Channel    library.ChannelType `xorm:"not null int(11) 'channel'"`
	Version    string              `xorm:"not null varchar(10) 'version'"`
	Type       int32               `xorm:"not null int(11) 'type'"`
	Token      string              `xorm:"not null varchar(255) 'token'"`
	CreateTime int64               `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64               `xorm:"not null int(11) 'update_time'"`
}

func (ThirdToken) TableName() string {
	return "push_third_token"
}

var TbThirdToken ThirdToken

func (t *ThirdToken) Save() error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime
	_, err := usercenter.GetEngineMaster().Insert(t)
	return err
}

func (t *ThirdToken) DeleteByUID(uid int64) error {
	_, err := usercenter.GetEngineMaster().Where("uid = ?", uid).Delete(t)
	return err
}

func (t *ThirdToken) DeleteByDeviceIDAndChannel(deviceID string, channel library.ChannelType) error {
	_, err := usercenter.GetEngineMaster().Where("deviceId = ?", deviceID).Where("channel = ?", channel).Delete(t)
	return err
}
