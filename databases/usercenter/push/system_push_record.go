package push

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type SystemPushRecord struct {
	ID         int64 `xorm:"not null autoincr pk unsigned int(11) 'id'"`
	PushID     int64 `xorm:"not null unsigned int(11) 'push_id'"`
	UID        int64 `xorm:"not null unsigned int(11) 'uid'"`
	Status     int32 `xorm:"not null tinyint(1) 'status'"`
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

func (SystemPushRecord) TableName() string {
	return "system_push_record"
}

type systemPushRecord struct{}

var TbSystemPushRecord systemPushRecord

func (s *systemPushRecord) GetByPushID(pushID int64, limit, offset int) []SystemPushRecord {
	logger.Info("待推送pushId", pushID)
	var tables []SystemPushRecord
	err := usercenter.GetEngine().Where("push_id = ?", pushID).Limit(limit, offset).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func UpdateSendStatus(pushID int64, uidList []string, status int32) error {
	if len(uidList) == 0 {
		return nil
	}
	_, err := usercenter.GetEngineMaster().Where("push_id = ?", pushID).
		In("uid", uidList).Update(&SystemPushRecord{Status: status, UpdateTime: time.Now().Unix()})
	if err != nil {
		return err
	}
	return nil
}
