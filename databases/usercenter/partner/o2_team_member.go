package partner

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type O2TeamMember struct {
	ID             int64            `xorm:"not null autoincr pk bigint(20) 'id'"`
	TeamID         int64            `xorm:"not null bigint(20) 'team_id'"`
	UID            int64            `xorm:"not null bigint(20) 'uid'"`
	RoleID         O2TeamMemberRole `xorm:"not null unsigned tinyint(3) 'team_member_role_id'"`
	ReceiveRemind  int32            `xorm:"not null int(2) default '1' 'receive_remind'"`
	ReceiveCheerup int32            `xorm:"not null int(2) default '1' 'receive_cheerup'"`
	// 1:正常，2:删除
	Status     int32 `xorm:"not null tinyint(3) 'status'"`
	SortOrder  int32 `xorm:"not null int(11) default '999' 'sort_order'"`
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

type O2TeamMemberRole int32

var O2TeamMemberRoleID = struct {
	Captain   O2TeamMemberRole
	Member    O2TeamMemberRole
	Master    O2TeamMemberRole
	Teacher   O2TeamMemberRole
	Probation O2TeamMemberRole
}{
	Captain:   1, // 队长
	Member:    2, // 普通学员
	Master:    3, // 主老师
	Teacher:   4, // 副老师
	Probation: 5, // 试用学员
}

func (O2TeamMember) TableName() string {
	return "yoga_o2_partner_team_member"
}

type o2TeamMember struct{}

var TbO2TeamMember o2TeamMember

func (o *o2TeamMember) GetTeamMember(teamID int64, roleID O2TeamMemberRole) []O2TeamMember {
	var tables []O2TeamMember
	err := usercenter.GetEngine().Where("team_id = ?", teamID).Where("team_member_role_id = ?", roleID).
		Where("status = ?", 1).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
