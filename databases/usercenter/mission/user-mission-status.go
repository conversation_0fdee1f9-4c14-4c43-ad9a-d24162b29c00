package mission

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// UserMissionStatus 用户账号表
type UserMissionStatus struct {
	ID          uint32 `xorm:"not null autoincr pk int(11) 'id'"`
	CreatedTime int64  `xorm:"not null int(11) 'create_time'"`
	UpdatedTime int64  `xorm:"not null int(11) 'update_time'"`
	IsDel       int64  `xorm:"'is_del'"`
	// 用户UID
	UID int64 `xorm:"not null bigint(20) 'uid'"`
	// 任务id
	MissionID uint32 `xorm:"not null int(10) 'mission_id'"`
	// 任务完成状态（已完成：1；未完成：2）
	Status int `xorm:"not null tinyint(1) 'status'"`
}

//
// TableName
//  @Description: 获取表名
//  @receiver m
//  @return string
//
func (m *UserMissionStatus) TableName() string {
	return "user_mission_status"
}

//
// Update
//  @Description: 更新数据
//  @receiver m
//  @return int64
//  @return error
//
func (m *UserMissionStatus) Update() (int64, error) {
	count, err := usercenter.GetEngineMaster().ID(m.ID).Update(m)
	if err != nil {
		logger.Error(err)
		return 0, err
	}

	return count, err
}

type userMissionStatus struct{}

var TbUserMissionStatus userMissionStatus

type UserMissionStatusListRequest struct {
	Ids    []uint32
	UID    int64
	Status int
}

//
// List
//  @Description: 获取列表数据
//  @receiver m
//  @param param
//  @return []*UserMissionStatus
//  @return error
//
func (m *userMissionStatus) List(param *UserMissionStatusListRequest) ([]*UserMissionStatus, error) {
	var tables []*UserMissionStatus

	db := usercenter.GetEngine().Table(&UserMissionStatus{})
	// 批量查询
	if param.Ids != nil && len(param.Ids) != 0 {
		db = db.In("id", param.Ids)
	}
	if param.UID != 0 {
		db = db.Where("uid = ?", param.UID)
	}
	if param.Status != 0 {
		db = db.Where("status = ?", param.Status)
	}

	err := db.
		Desc("id").
		Find(&tables)

	if err != nil {
		logger.Error(err)
		return tables, err
	}

	return tables, err
}

//
// Update
//  @Description: 批量更新用户任务状态
//  @receiver m
//  @param uid 用户uid
//  @param missionIds 任务id列表
//  @param status 任务状态
//  @return int64
//  @return error
//
func (m *userMissionStatus) Update(uid int64, missionIds []uint32, status int) (int64, error) {
	now := time.Now().Unix()
	count, err := usercenter.GetEngineMaster().
		Where("uid = ?", uid).
		In("mission_id", missionIds).
		Update(&UserMissionStatus{
			Status:      status,
			UpdatedTime: now,
		})
	if err != nil {
		logger.Error(err)
		return 0, err
	}

	return count, err
}
