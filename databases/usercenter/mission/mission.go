package mission

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

//
// Mission
//  @Description: 用户任务状态表
//
type Mission struct {
	ID          uint32 `xorm:"not null autoincr pk int(11) 'id'"`
	CreatedTime int64  `xorm:"not null BIGINT 'create_time'"`
	UpdatedTime int64  `xorm:"not null BIGINT 'update_time'"`
	IsDel       int64  `xorm:"'is_del'"`
	// 任务类型（练习时长：1；练习天数：2）
	Type int `xorm:"'type'"`
	// 实现目标
	Target int `xorm:"'target'"`
	// 未领取icon
	NotReceivedIcon string `xorm:"'not_received_icon'"`
	// 已领取icon
	ReceivedIcon string `xorm:"'received_icon'"`
	// 弹框主标题
	FrameMainTitle string `xorm:"'frame_main_title'"`
	// 弹框副标题
	FrameSubtitle string `xorm:"'frame_subtitle'"`
	// 奖励名称
	RewardName string `xorm:"'reward_name'"`
}

//
// TableName
//  @Description: 获取表名
//  @receiver m
//  @return string
//
func (m *Mission) TableName() string {
	return "mission"
}

type mission struct{}

var TbMission mission

type ListRequest struct {
	Ids  []uint32
	Type int
}

//
// List
//  @Description: 获取列表数据
//  @receiver m
//  @param param
//  @return []*Mission
//  @return error
//
func (m *mission) List(param *ListRequest) ([]*Mission, error) {
	var tables []*Mission

	db := usercenter.GetEngine().Table(&Mission{})
	// 批量查询
	if param.Ids != nil && len(param.Ids) != 0 {
		db = db.In("id", param.Ids)
	}
	if param.Type != 0 {
		db = db.Where("type = ?", param.Type)
	}

	err := db.
		Desc("id").
		Find(&tables)

	if err != nil {
		logger.Error(err)
		return tables, err
	}

	return tables, err
}
