package live

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type UserDuration struct {
	ID        int64 `xorm:"int(11) unsigned notnull pk autoincr 'id'" json:"id"`     // 自增ID
	UID       int64 `xorm:"int(11) unsigned notnull 'uid'" json:"uid"`               // 用户ID
	ProductID int64 `xorm:"int(11) unsigned notnull 'product_id'" json:"product_id"` // 产品 ID
	// 产品类型：1-课包（配合quantity）
	ProductType int32 `xorm:"tinyint(1) unsigned notnull 'product_id'" json:"product_type"`
	// 原始数量
	OriginalQuantity int `xorm:"smallint(5) unsigned notnull 'original_quantity'" json:"original_quantity"`
	// 产品数量
	Quantity  int `xorm:" smallint(5) unsigned notnull 'quantity'" json:"quantity"`
	StartTime int `xorm:"int(11) unsigned not null 'start_time'" json:"start_time"` // 开始时间
	EndTime   int `xorm:"int(11) unsigned not null 'end_time'" json:"end_time"`     // 结束时间
	Exp       int `xorm:"int(5) unsigned NOT NULL 'exp'" json:"exp"`                // 有效限期天
	// 状态:1-正常;2-失效;3-关闭
	Status     library.CommonStatus `xorm:"tinyint(1) unsigned NOT NULL 'status'" json:"status"`
	CreateTime int64                `xorm:"int(11) unsigned notnull 'create_time'" json:"create_time"`
	UpdateTime int64                `xorm:"int(11) unsigned notnull 'update_time'" json:"update_time"`
}

// TableName Get table name
func (v *UserDuration) TableName() string {
	return "live_user_duration"
}

type _LiveUserDuration struct{}

// TbLiveUserDuration 外部调用静态变量
var TbLiveUserDuration _LiveUserDuration

//  CheckUserLiveCardStatus 检查用户直播卡状态
//  直播卡状态：yes/no
//	yes：目前有卡
//	no：目前无卡～
func (s *_LiveUserDuration) CheckUserLiveCardStatus(uid int64) bool {
	var tables []UserDuration
	// 取出用户所有直播卡的记录,包括历史记录
	err := db.GetEngine().Where("uid = ? AND status = ?", uid, library.CommonStatusEnum.Normal).Find(&tables)
	if err != nil {
		logger.Error(err)
		return false
	}
	// 从未开过卡的用户
	if len(tables) == 0 {
		return false
	}
	return true
}

// GetUserLastUsedItem 获取用户最后一条正在使用或者已经过期的直播卡记录
func (s *_LiveUserDuration) GetUserLastUsedItem(uid int64) *UserDuration {
	item := new(UserDuration)
	ok, err := db.GetEngine().Where("uid = ? and end_time > 0 ", uid).
		OrderBy("id desc").Get(item)
	if err != nil {
		logger.Error("获取直播卡过期天数失败 %s", err)
		return nil
	}
	if !ok {
		return nil
	}
	return item
}

// GetUserEffectiveList 获取用户有效的课程包列表
func (s *_LiveUserDuration) GetUserEffectiveList(uid int64) []*UserDuration {
	var table []*UserDuration
	err := db.GetEngine().Where("uid = ?", uid).
		Where("product_type = ? AND status = ?", 1, 1).
		Find(&table)
	if err != nil {
		logger.Error("获取用户有效的课程包列表 %s", err)
		return nil
	}
	return table
}
