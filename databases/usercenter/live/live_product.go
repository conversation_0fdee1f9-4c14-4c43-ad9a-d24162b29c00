package live

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type Product struct {
	ID int64 `xorm:"int(11) unsigned notnull pk autoincr 'id'" json:"id"`
	// 终端类型：1:app,2:tv
	TerminalType int32 `xorm:"tinyint(2) unsigned notnull 'terminal_type'" json:"terminal_type"`
	// 设备类型:10:全部,1:安卓,2:iOS
	DeviceType int32 `xorm:"tinyint(2) unsigned notnull 'device_type'" json:"device_type"`
	// 产品配置名称
	Name string `xorm:"varchar(255)  notnull 'name'" json:"name"`
	// 角标文案
	IconText string `xorm:"varchar(255)  notnull 'icon_text'" json:"icon_text"`
	// 产品类型：1-课包（配合quantity）
	Type int32 `xorm:"tinyint(2) unsigned notnull 'type'" json:"type"`
	// 产品数量
	Quantity int32 `xorm:"smallint unsigned notnull 'quantity'" json:"quantity"`
	// 0:默认，1:天，2:月，3:年
	DurationType int32 `xorm:"tinyint(3) unsigned notnull 'duration_type'" json:"duration_type"`
	// duration_type 对应的值
	DurationValue int64  `xorm:"int(11) unsigned notnull 'duration_value'" json:"duration_value"`
	Desc          string `xorm:"varchar(255) notnull 'desc'" json:"desc"`                     // 产品介绍
	IosProductID  string `xorm:"varchar(255) notnull 'ios_product_id'" json:"ios_product_id"` // IOS产品ID
	Price         int64  `xorm:"int(6) unsigned notnull 'price'" json:"price"`                // IOS产品ID
	VipDiscount   int64  `xorm:"int(6) unsigned notnull 'vip_discount'" json:"vip_discount"`  // 会员立减
	Status        int32  `xorm:"tinyint(2) unsigned notnull 'status'" json:"status"`          // 状态：1，正常，2过期
	IsSubscribe   int32  `xorm:"'is_subscribe'"`                                              // 是否属于订阅 1:是 2:不是
	// 安卓订阅,终端细分,1:小米,3:安卓微信 注:当选择安卓且为订阅类型时,才有该值,默认没有则为 0
	TerminalDetail int32 `xorm:"'terminal_detail'"`
	// 第三方产品ID,作用与 ios_product_id 字段一样,但由于历史原因
	// 因此终端类型为 ios 的依然使用 ios_product_id,但其余的第三方产品ID将都用该字段
	ThirdPartProductID   string `xorm:"'third_part_product_id'"`
	SubscribeDescription string `xorm:"'subscribe_description'"` // 自动续订说明
	CreateTime           int64  `xorm:"int(11) unsigned notnull 'create_time'" json:"create_time"`
	UpdateTime           int64  `xorm:"int(11) unsigned notnull 'update_time'" json:"update_time"`
}

// TableName Get table name
func (v *Product) TableName() string {
	return "live_product"
}

type _LiveProduct struct{}

// TbLiveProduct 外部调用静态变量
var TbLiveProduct _LiveProduct

// GetItemsByIDs 通过 ID 列表获取记录
func (v *_LiveProduct) GetItemsByIDs(ids []int64) []*Product {
	var table []*Product
	if err := db.GetEngine().In("id", ids).Find(&table); err != nil {
		logger.Error(err)
	}
	return table
}
