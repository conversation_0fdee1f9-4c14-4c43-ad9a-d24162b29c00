package live

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type Session struct {
	// ID 自增id
	ID                int64 `xorm:"int(11) unsigned notnull pk autoincr 'id'" json:"id"`
	LiveSessionMainID int64 `xorm:"int(11) unsigned notnull 'live_session_main_id'" json:"live_session_main_id"`
	LiveCoachID       int64 `xorm:"int(11) unsigned notnull 'live_coach_id'" json:"live_coach_id"`
	SessionStartTime  int64 `xorm:"bigint(20) unsigned notnull 'session_start_time'" json:"session_start_time"` // 开始时间
	SessionEndTime    int64 `xorm:"bigint(20) unsigned notnull 'session_end_time'" json:"session_end_time"`     // 结束时间
	MemberQuota       int64 `xorm:"int(6) unsigned notnull 'member_quota'" json:"member_quota"`
	// 0.未开始，1.直播中 2.已结束 3.看回放
	LiveStatus     int32  `xorm:"tinyint(2) unsigned notnull 'live_status'" json:"live_status"`
	GroupID        string `xorm:"varchar(255) notnull 'group_id'" json:"group_id"`
	VideoURL       string `xorm:"varchar(512) notnull 'video_url'" json:"video_url"`
	TaskID         string `xorm:"varchar(255) notnull 'task_id'" json:"task_id"`
	IsOnline       int32  `xorm:"tinyint(2) unsigned notnull 'is_online'" json:"is_online"`       // IsOnline 1上 2下
	IsPush         int32  `xorm:"tinyint(2) unsigned notnull default 0 'is_push'" json:"is_push"` // 1-已发送 0-未发送
	CreateTime     int64  `xorm:"int(11) unsigned notnull 'create_time'" json:"create_time"`
	UpdateTime     int64  `xorm:"int(11) unsigned notnull 'update_time'" json:"update_time"`
	GoodsGroupIDs  string `json:"goods_group_ids" xorm:"'goods_group_ids' not null default '' VARCHAR(100)"`
	IsFree         int32  `json:"is_free" xorm:"'is_free' notnull default 1 tinyint(2)"`
	FreePersonType int32  `xorm:"tinyint(2) unsigned notnull 'free_person_type'" json:"free_person_type"`
	FreePerson     string `xorm:"varchar(255) notnull default '' 'free_person'" json:"free_person"`
}

// TableName Get table name
func (c *Session) TableName() string {
	return "live_session"
}

type _LiveSession struct{}

// TbLiveSession 外部调用静态变量
var TbLiveSession _LiveSession

func (c *_LiveSession) GetItem(id int64) *Session {
	var table Session
	ok, err := db.GetEngineMaster().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
