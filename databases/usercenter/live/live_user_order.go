package live

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type UserOrder struct {
	ID int64 `xorm:"int(11) unsigned notnull pk autoincr 'id'" json:"id"`
	// 用户ID
	UID int64 `xorm:"int(11) unsigned notnull 'uid'" json:"uid"`
	// 产品类型：1-课包（配合quantity）
	ProductType int32 `xorm:"tinyint(1) unsigned notnull 'product_type'" json:"product_type"`
	// 订单ID
	OrderID       string `xorm:"varchar(128) notnull 'order_id'" json:"order_id"`
	ProductID     int64  `xorm:"int(11) unsigned notnull 'product_id'" json:"product_id"`
	PaymentCode   int32  `xorm:"tinyint(2) unsigned notnull 'payment_code'" json:"payment_code"`
	PaymentMethod string `xorm:"varchar(16) notnull 'payment_method'" json:"payment_method"`
	// 订单支付价格
	OrderAmountTotal float64 `xorm:"decimal(10,2) unsigned notnull 'order_amount_total'" json:"order_amount_total"`
	// 产品原价
	ProductAmountTotal float64 `xorm:"decimal(10,2) unsigned notnull 'product_amount_total'" json:"product_amount_total"`
	// 订单状态：0-等待付款，1-已付款，2-退款
	OrderStatusID int32 `xorm:"tinyint(1) unsigned notnull 'order_status_id'" json:"order_status_id"`
	// 是不是vip 1-是
	IsVip int32 `xorm:"tinyint(1) unsigned notnull 'is_vip'" json:"is_vip"`
	// 开通状态状态 0-等待添加 1-添加成功 2-退款退权益
	Status int32 `xorm:"tinyint(1) unsigned notnull 'status'" json:"status"`
	// 版本号
	Version string `xorm:"varchar(20) notnull 'version'" json:"version"`
	// 渠道号
	PlatformType int64 `xorm:"int(11) unsigned notnull 'platform_type'" json:"platform_type"`
	CreateTime   int64 `xorm:"int(11) unsigned notnull 'create_time'" json:"create_time"`
	UpdateTime   int64 `xorm:"int(11) unsigned notnull 'update_time'" json:"update_time"`
}

// TableName Get table name
func (v *UserOrder) TableName() string {
	return "live_user_order"
}

type _LiveUserOrder struct{}

// TbLiveUserOrder 外部调用静态变量
var TbLiveUserOrder _LiveUserOrder

// 检查用户是否购买过直播课
func (s *_LiveUserOrder) CheckUserBuyLiveCardRecord(uid int64) bool {
	var tables []UserOrder

	err := db.GetEngine().
		Where("uid = ?", uid).
		Where("status >= ?", 1).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return false
	}
	if len(tables) == 0 {
		return false
	}
	return true
}
