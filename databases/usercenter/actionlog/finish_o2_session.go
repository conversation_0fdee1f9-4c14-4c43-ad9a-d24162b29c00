package actionlog

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	actionlog "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type FinishO2Session struct {
	ID          int64  `xorm:"not null autoincr pk int(11) 'id'"`
	UID         int64  `xorm:"not null int(11) 'uid'"`
	ObjID       int32  `xorm:"not null int(11) 'objId'"`
	O2SessionID int32  `xorm:"not null int(11) 'o2_session_id'"`
	CreateTime  int32  `xorm:"not null int(11) 'createtime'"`
	Points      int32  `xorm:"not null int(11) 'points'"`
	Status      int32  `xorm:"not null int(11) 'status'"`
	Channel     int32  `xorm:"not null int(11) 'channel'"`
	Version     string `xorm:"not null int(11) 'version'"`
	UploadTime  int32  `xorm:"not null int(11) 'practice_current_time'"`
}

func (FinishO2Session) TableName() string {
	return fmt.Sprintf("user_action_log_%d", actionlog.FinishO2Session)
}

type finishO2Session struct{}

var TbFinishO2Session finishO2Session

// GetLogByTime 根据时间和用户id获取一条记录数据
func (p finishO2Session) GetLogByTime(uid, startTime, endTime int64) *FinishO2Session {
	var table FinishO2Session
	ok, err := usercenter.GetEngine().Where("uid = ?", uid).
		And("createtime between ? and ?", startTime, endTime).
		OrderBy("id desc").
		Get(&table)
	if err != nil {
		return nil
	}

	if !ok {
		return nil
	}
	return &table
}

// GetLogOfObjByTime 根据时间和用户id获取一条记录数据
func (p finishO2Session) GetLogOfObjByTime(uid, o2SessionID, startTime, endTime int64) *FinishO2Session {
	if o2SessionID == 0 {
		return nil
	}
	var table FinishO2Session
	ok, err := usercenter.GetEngine().Where("uid = ?", uid).
		And("o2_session_id = ?", o2SessionID).
		And("createtime between ? and ?", startTime, endTime).
		OrderBy("id desc").
		Get(&table)
	if err != nil {
		return nil
	}

	if !ok {
		return nil
	}
	return &table
}

func (p *finishO2Session) FindByTimeRange(uid, startTime, endTime int64) []FinishO2Session {
	var tables []FinishO2Session

	err := usercenter.GetEngine().
		Where("uid = ?", uid).
		Where("createtime >= ?", startTime).Where("createtime <= ?", endTime).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
