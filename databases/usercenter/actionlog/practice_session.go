package actionlog

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	actionlog "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type PracticeSession struct {
	ID         int64  `xorm:"not null autoincr pk int(11) 'id'"`
	UID        int64  `xorm:"not null int(11) 'uid'"`
	ObjID      int32  `xorm:"not null int(11) 'objId'"`
	CreateTime int32  `xorm:"not null int(11) 'createtime'"`
	Points     int32  `xorm:"not null int(11) 'points'"`
	Status     int32  `xorm:"not null int(11) 'status'"`
	Channel    int32  `xorm:"not null int(11) 'channel'"`
	Version    string `xorm:"not null int(11) 'version'"`
	UploadTime int32  `xorm:"not null int(11) 'practice_current_time'"`
}

func (PracticeSession) TableName() string {
	return fmt.Sprintf("user_action_log_%d", actionlog.PracticeFinish)
}

type practiceSession struct{}

var TbPracticeSession practiceSession

// GetLogByTime 根据时间和用户id获取一条记录数据
func (p practiceSession) GetLogByTime(uid, startTime, endTime int64) *PracticeSession {
	var table PracticeSession
	ok, err := usercenter.GetEngine().Where("uid = ?", uid).
		And("createtime between ? and ?", startTime, endTime).
		OrderBy("id desc").
		Get(&table)
	if err != nil {
		return nil
	}

	if !ok {
		return nil
	}
	return &table
}

// GetLogOfObjByTime 根据时间和用户id获取一条记录数据
func (p practiceSession) GetLogOfObjByTime(uid, objID, startTime, endTime int64) *PracticeSession {
	if objID == 0 {
		return nil
	}
	var table PracticeSession
	ok, err := usercenter.GetEngine().Where("uid = ?", uid).
		Where("objId = ?", objID).
		Where("createtime >= ?", startTime).Where("createtime <= ?", endTime).
		OrderBy("id desc").
		Get(&table)
	if err != nil {
		return nil
	}

	if !ok {
		return nil
	}
	return &table
}

func (p *practiceSession) FindByTimeRange(uid, startTime, endTime int64) []PracticeSession {
	var tables []PracticeSession

	err := usercenter.GetEngine().Where("uid = ?", uid).Where("createtime >= ?", startTime).
		Where("createtime <= ?", endTime).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
