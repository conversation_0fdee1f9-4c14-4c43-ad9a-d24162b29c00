package actionlog

import (
	"fmt"
	"time"

	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	actionlog "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// OperationPoint 操作积分
type OperationPoint struct {
	ID          int64  `xorm:"not null autoincr int(11) 'id'"`
	UID         int64  `xorm:"not null int(11) 'uid'"`
	ObjID       int32  `xorm:"not null int(11) 'objId'"`
	CreateTimes int64  `xorm:"not null int(11) 'createtime'"`
	Points      int32  `xorm:"not null int(11) 'points'"`
	Status      int32  `xorm:"not null tinyint(1) 'status'"`
	Channel     int32  `xorm:"not null int(11) 'channel'"`
	Version     string `xorm:"not null int(11) 'version'"`
	Reason      string `xorm:"varchar(128) not null default '''' 'reason'" json:"reason"`
	CreateTime  int64  `xorm:"not null int(10) 'create_time'"`
	UpdateTime  int64  `xorm:"not null int(10) 'update_time'"`
}

func (OperationPoint) TableName() string {
	return fmt.Sprintf("user_action_log_%d", actionlog.OfficialOperation)
}

func (b *OperationPoint) Insert() error {
	currUnixTime := time.Now().Unix()
	b.CreateTime = currUnixTime
	b.UpdateTime = currUnixTime
	_, err := db.GetEngineMaster().Insert(b)
	return err
}
