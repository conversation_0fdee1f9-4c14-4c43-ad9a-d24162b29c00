package actionlog

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	actionlog "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// FirstPractice 首次练习完课程
type FirstPractice struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户id
	UID int64 `xorm:"default '0' int(11) 'uid'"`
	// 操作对象id
	ObjID int32 `xorm:"default '0' int(11) 'objId'"`
	// 计划id
	SessionID int32 `xorm:"default '0' int(11) 'session_id'"`
	// 创建时间
	CreateTime int64 `xorm:"default '0' int(11) 'createtime'"`
	// 获取的分数
	Points int32 `xorm:"default '0' int(11) 'points'"`
	// 积分变化类型，0：增加；1：减少
	Status int32 `xorm:"default '0' tinyint(1) 'status'"`
	// 渠道标示
	Channel int32 `xorm:"default '0' int(11) 'channel'"`
	// 版本号
	Version string `xorm:"default '0' varchar(10) 'version'"`
}

// TableName 获取表名
func (FirstPractice) TableName() string {
	return fmt.Sprintf("user_action_log_%d", actionlog.PracticeFinishFirstTime)
}

type firstPractice struct {
}

var TbFirstPractice firstPractice

func (t *firstPractice) GetUserFirstPracticeInfo(uid int64) (*FirstPractice, error) {
	var table FirstPractice

	ok, err := usercenter.GetEngine().Where("uid=?", uid).Get(&table)
	if err != nil {
		logger.Error("获取用户首训信息失败", uid)
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &table, nil
}

func (t *firstPractice) GetUserFirstPracticeBatch(uidList []int64) ([]FirstPractice, error) {
	var tables []FirstPractice

	err := usercenter.GetEngine().In("uid", uidList).Find(&tables)
	logger.Info(err, tables)
	if err != nil {
		return nil, err
	}

	return tables, nil
}
