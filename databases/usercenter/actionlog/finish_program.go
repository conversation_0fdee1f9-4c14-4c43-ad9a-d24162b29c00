package actionlog

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	actionlog "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type FinishProgram struct {
	ID         int64  `xorm:"not null autoincr pk int(11) 'id'"`
	UID        int64  `xorm:"not null int(11) 'uid'"`
	ObjID      int32  `xorm:"not null int(11) 'objId'"`
	CreateTime int32  `xorm:"not null int(11) 'createtime'"`
	Points     int32  `xorm:"not null int(11) 'points'"`
	Status     int32  `xorm:"not null int(11) 'status'"`
	Channel    int32  `xorm:"not null int(11) 'channel'"`
	Version    string `xorm:"not null int(11) 'version'"`
	UploadTime int32  `xorm:"not null int(11) 'practice_current_time'"`
}

func (FinishProgram) TableName() string {
	return fmt.Sprintf("user_action_log_%d", actionlog.FinishPlan)
}

type finishProgram struct{}

var TbFinishProgram finishProgram

// GetLogByTime 根据时间和用户id获取一条记录数据
func (p finishProgram) GetLogByTime(uid, startTime, endTime int64) *FinishProgram {
	var table FinishProgram
	ok, err := usercenter.GetEngine().Where("uid = ?", uid).
		Where("createtime >= ?", startTime).Where("createtime <= ?", endTime).
		OrderBy("id desc").
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}

	return &table
}

func (p *finishProgram) FindByTimeRange(uid, startTime, endTime int64) []FinishProgram {
	var tables []FinishProgram
	err := usercenter.GetEngine().
		Where("uid = ?", uid).
		Where("createtime >= ?", startTime).Where("createtime <= ?", endTime).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetLogOfObjByTime 根据时间和用户id获取一条记录数据
func (p finishProgram) GetLogOfObjByTime(uid, objID, startTime, endTime int64) *FinishProgram {
	if objID == 0 {
		return nil
	}
	var table FinishProgram
	ok, err := usercenter.GetEngine().Where("uid = ?", uid).
		And("objId = ?", objID).
		And("createtime between ? and ?", startTime, endTime).
		OrderBy("id desc").
		Get(&table)
	if err != nil {
		return nil
	}

	if !ok {
		return nil
	}
	return &table
}

// GetUserProgramCompleteTimes 获取练习次数
func (p finishProgram) GetUserProgramCompleteTimes(uid, programID int64) int64 {
	var table FinishProgram
	times, err := usercenter.GetEngine().Where("uid = ?", uid).
		Where("objId = ?", programID).Count(&table)
	if err != nil {
		logger.Error(err)
		return 0
	}
	return times
}
