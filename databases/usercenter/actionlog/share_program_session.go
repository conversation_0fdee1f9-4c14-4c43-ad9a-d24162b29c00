package actionlog

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type ShareProgramSession struct {
	ActionType int    `xorm:"-"`
	ID         int64  `xorm:"not null autoincr pk int(11) 'id'"`
	UID        int64  `xorm:"not null int(11) 'uid'"`
	ObjID      int32  `xorm:"not null int(11) 'objId'"`
	CreateTime int32  `xorm:"not null int(11) 'createtime'"`
	Points     int32  `xorm:"not null int(11) 'points'"`
	Status     int32  `xorm:"not null int(11) 'status'"`
	Channel    int32  `xorm:"not null int(11) 'channel'"`
	Version    string `xorm:"not null int(11) 'version'"`
}

func (s ShareProgramSession) TableName() string {
	if s.ActionType == 0 {
		return ""
	}
	return fmt.Sprintf("user_action_log_%d", s.ActionType)
}

type shareProgramSession struct{}

var TbShareProgramSession shareProgramSession

// GetLogByTime 根据时间和用户id获取一条记录数据
func (p shareProgramSession) GetLogByTime(action int, uid, startTime, endTime int64) *ShareProgramSession {
	table := ShareProgramSession{ActionType: action}
	ok, err := usercenter.GetEngine().Where("uid = ?", uid).
		And("createtime between ? and ?", startTime, endTime).
		OrderBy("id desc").
		Get(&table)
	if err != nil {
		return nil
	}

	if !ok {
		return nil
	}
	return &table
}
