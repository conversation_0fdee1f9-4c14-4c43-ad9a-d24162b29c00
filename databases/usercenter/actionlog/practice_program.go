package actionlog

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	actionlog "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type PracticeProgram struct {
	ID              int64  `xorm:"not null autoincr pk int(11) 'id'"`
	UID             int64  `xorm:"not null int(11) 'uid'"`
	ObjID           int32  `xorm:"not null int(11) 'objId'"`
	CreateTime      int32  `xorm:"not null int(11) 'createtime'"`
	Points          int32  `xorm:"not null int(11) 'points'"`
	Status          int32  `xorm:"not null int(11) 'status'"`
	Channel         int32  `xorm:"not null int(11) 'channel'"`
	Version         string `xorm:"not null int(11) 'version'"`
	SessionID       int32  `xorm:"not null int(11) 'session_id'"`
	UploadTime      int32  `xorm:"not null int(11) 'practice_current_time'"`
	SaveTime        int32  `xorm:"not null int(11) 'create_time'"`
	UpdateTime      int32  `xorm:"not null int(11) 'update_time'"`
	SessionIndex    int32  `xorm:"not null int(11) 'session_index'"`
	SubSessionIndex int32  `xorm:"not null int(11) 'sub_session_index'"`
}

func (PracticeProgram) TableName() string {
	return fmt.Sprintf("user_action_log_%d", actionlog.FinishSessionInProgram)
}

type practiceProgram struct{}

var TbPracticeProgram practiceProgram

// GetLogByTime 根据时间和用户id获取一条记录数据
func (p practiceProgram) GetLogOfObjByTime(uid, objID, startTime, endTime int64) *PracticeProgram {
	if objID == 0 {
		return nil
	}
	var table PracticeProgram
	ok, err := usercenter.GetEngine().Where("uid = ?", uid).
		And("objId = ?", objID).
		And("createtime between ? and ?", startTime, endTime).
		OrderBy("id desc").
		Get(&table)
	if err != nil {
		return nil
	}

	if !ok {
		return nil
	}
	return &table
}

func (p *practiceProgram) FindByTimeRange(uid, startTime, endTime int64) []PracticeProgram {
	var tables []PracticeProgram

	err := usercenter.GetEngine().
		Where("uid = ?", uid).
		Where("createtime >= ?", startTime).Where("createtime <= ?", endTime).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
