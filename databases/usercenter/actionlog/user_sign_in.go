package actionlog

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	actionlog "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// UserSignIn 今日签到
type UserSignIn struct {
	ID         int64  `xorm:"not null autoincr int(11) 'id'"`
	UID        int64  `xorm:"not null int(11) 'uid'"`
	ObjID      int32  `xorm:"not null int(11) 'objId'"`
	CreateTime int64  `xorm:"not null int(11) 'createtime'"`
	Points     int32  `xorm:"not null int(11) 'points'"`
	Status     int32  `xorm:"not null tinyint(1) 'status'"`
	Channel    int32  `xorm:"not null int(11) 'channel'"`
	Version    string `xorm:"not null int(11) 'version'"`
}

func (UserSignIn) TableName() string {
	return fmt.Sprintf("user_action_log_%d", actionlog.SignIn)
}

type userSignIn struct{}

var TbUserSignIn userSignIn

func (u userSignIn) GetUserSignInByUID(uid, startTime, endTime int64) []UserSignIn {
	var tables []UserSignIn
	err := usercenter.GetEngine().
		Where("uid = ?", uid).
		Where("createtime >= ?", startTime).Where("createtime <= ?", endTime).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
