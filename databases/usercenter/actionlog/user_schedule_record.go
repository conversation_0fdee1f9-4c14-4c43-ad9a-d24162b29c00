package actionlog

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	actionlog "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// UserScheduleRecord 自定义课程表记录表
type UserScheduleRecord struct {
	ID         int64  `xorm:"not null autoincr int(11) 'id'"`
	UID        int64  `xorm:"not null int(11) 'uid'"`
	ObjID      int32  `xorm:"not null int(11) 'objId'"`
	UploadTime int32  `xorm:"not null int(11) 'practice_current_time'"`
	Points     int32  `xorm:"not null int(11) 'points'"`
	Status     int32  `xorm:"not null tinyint(1) 'status'"`
	Channel    int32  `xorm:"not null int(11) 'channel'"`
	Version    string `xorm:"not null int(11) 'version'"`
	CreateTime int64  `xorm:"not null int(11) 'createtime'"`
}

func (UserScheduleRecord) TableName() string {
	return fmt.Sprintf("user_action_log_%d", actionlog.LeaderUserScheduleUnit)
}

type userScheduleRecord struct{}

var TbUserScheduleRecord userScheduleRecord

func (tb *userScheduleRecord) FindByTimeRange(uid, startTime, endTime int64) []UserScheduleRecord {
	var tables []UserScheduleRecord

	err := usercenter.GetEngine().
		Where("uid = ?", uid).
		Where("createtime >= ?", startTime).Where("createtime <= ?", endTime).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
