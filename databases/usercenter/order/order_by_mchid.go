package order

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type ByMchid struct {
	ID         int64  `xorm:"not null pk autoincr INT(11) 'id'"`
	MchID      string `xorm:"not null varchar(128) 'mchid'"`
	OrderID    string `xorm:"not null varchar(128) 'order_id'"`
	PayType    int    `xorm:"not null int(11) 	'pay_type'"`
	CreateTime int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64  `xorm:"not null int(11) 'update_time'"`
}

func (ByMchid) TableName() string {
	return "order_by_mchid"
}

type orderByMchid struct{}

var TbOrderByMchid orderByMchid

func (orderByMchid) GetMchidByOrderID(orderID string) *ByMchid {
	var table ByMchid
	ok, err := db.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
