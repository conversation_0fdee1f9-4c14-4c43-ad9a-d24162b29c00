package order

import db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"

type YogaO2WeixinpayCallback struct {
	OrderWeixinpayAsyncNotifyID int    `xorm:"not null pk autoincr INT(11) 'order_weixinpay_async_notify_id'" json:"order_weixinpay_async_notify_id"` //nolint
	OrderID                     string `xorm:"not null default '0' VARCHAR(32) 'order_id'" json:"order_id"`
	AppID                       string `xorm:"not null default '' VARCHAR(32) 'appid'" json:"appid"`
	MchID                       string `xorm:"not null default '' VARCHAR(32) 'mch_id'" json:"mch_id"`
	DeviceInfo                  string `xorm:"not null default '' VARCHAR(32) 'device_info'" json:"device_info"`
	NonceStr                    string `xorm:"not null default '' VARCHAR(32) 'nonce_str'" json:"nonce_str"`
	Sign                        string `xorm:"not null default '' VARCHAR(32) 'sign'" json:"sign"`
	ResultCode                  string `xorm:"not null default '' VARCHAR(16) 'result_code'" json:"result_code"`
	ErrCode                     string `xorm:"not null default '' VARCHAR(32) 'err_code'" json:"err_code"`
	ErrCodeDes                  string `xorm:"not null default '' VARCHAR(128) 'err_code_des'" json:"err_code_des"`
	Openid                      string `xorm:"not null default '' VARCHAR(128) 'openid'" json:"openid"`
	IsSubscribe                 string `xorm:"not null default '' VARCHAR(1) 'is_subscribe'" json:"is_subscribe"`
	TradeType                   string `xorm:"not null default '' VARCHAR(16) 'trade_type'" json:"trade_type"`
	BankType                    string `xorm:"not null default '' VARCHAR(16) 'bank_type'" json:"bank_type"`
	TotalFee                    int    `xorm:"not null INT(11) 'total_fee'" json:"total_fee"`
	FeeType                     string `xorm:"not null default '' VARCHAR(8) 'fee_type'" json:"fee_type"`
	CashFee                     int    `xorm:"not null INT(11) 'cash_fee'" json:"cash_fee"`
	CashFeeType                 string `xorm:"not null default '' VARCHAR(16) 'cash_fee_type'" json:"cash_fee_type"`
	CouponFee                   int    `xorm:"not null INT(11) 'coupon_fee'" json:"coupon_fee"`
	CouponCount                 int    `xorm:"not null INT(11) 'coupon_count'" json:"coupon_count"`
	TransactionID               string `xorm:"not null default '' VARCHAR(32) 'transaction_id'" json:"transaction_id"`
	OutTradeNo                  string `xorm:"not null default '' VARCHAR(32) 'out_trade_no'" json:"out_trade_no"`
	Attach                      string `xorm:"not null default '' VARCHAR(128) 'attach'" json:"attach"`
	TimeEnd                     string `xorm:"not null default '' VARCHAR(14) 'time_end'" json:"time_end"`
	CreateTime                  int    `xorm:"default 0 INT(10) 'create_time'" json:"create_time"`
	UpdateTime                  int    `xorm:"default 0 INT(10) 'update_time'" json:"update_time"`
}

func (o *YogaO2WeixinpayCallback) TableName() string {
	return "yoga_o2_weixinpay_callback"
}

type yogaO2WeixinpayCallback struct{}

var TbYogaO2WeixinpayCallback yogaO2WeixinpayCallback

// GetByOrderID 根据订单号获取订单信息
func (*yogaO2WeixinpayCallback) GetByOrderID(orderID string) *YogaO2WeixinpayCallback {
	var table YogaO2WeixinpayCallback
	ok, err := db.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		return nil
	}

	if !ok {
		return nil
	}

	return &table
}
