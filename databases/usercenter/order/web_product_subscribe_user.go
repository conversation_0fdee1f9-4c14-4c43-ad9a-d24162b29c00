package order

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type WebProductSubscribeUser struct {
	ID                   int64  `xorm:"pk autoincr BIGINT(30) 'id'" json:"id"`
	UID                  int64  `xorm:"not null BIGINT(30) 'uid'" json:"uid"`
	ProductID            int    `xorm:"not null INT(6) 'product_id'" json:"product_id"`
	PurchaseType         int    `xorm:"not null TINYINT(2) 'purchase_type'" json:"purchase_type"`
	CategoryID           int    `xorm:"not null INT(6) 'category_id'" json:"category_id"`
	DeviceType           int    `xorm:"not null TINYINT(2) 'device_type'" json:"device_type"`
	ThirdPartType        int    `xorm:"not null TINYINT(2) 'third_part_type'" json:"third_part_type"`
	DisplayType          int    `xorm:"not null TINYINT(2) 'display_type'" json:"display_type"`
	ThirdPartExpiresTime int64  `xorm:"BIGINT(30) 'third_part_expires_time'" json:"third_part_expires_time"`
	OrderID              string `xorm:"not null varchar(40) 'order_id'" json:"order_id"`
	OriginalOrderID      string `xorm:"not null varchar(40) 'original_order_id'" json:"original_order_id"`
	ThirdPartTrID        string `xorm:"varchar(40) NOT NULL 'third_part_transaction_id'" json:"third_part_transaction_id"`
	WebOrderType         int    `xorm:"not null INT(6) 'web_order_type'" json:"web_order_type"`
	Status               int    `xorm:"not null default 0 TINYINT(2) 'status'" json:"status"`
	CreateTime           int64  `xorm:"not null BIGINT(30) 'create_time'" json:"create_time"`
	UpdateTime           int64  `xorm:"not null BIGINT(30) 'update_time'" json:"update_time"`
}

// TableName Get table name
func (w *WebProductSubscribeUser) TableName() string {
	return "web_product_subscribe_user"
}

func (w *WebProductSubscribeUser) UpdateByTransaction(session *xorm.Session) error {
	w.UpdateTime = time.Now().Unix()
	_, err := session.ID(w.ID).Update(w)
	return err
}

type wPSU struct{}

var TbWPSubU wPSU

// GetWebSubscribeUser 获取会员订阅信息
func (*wPSU) GetWebSubscribeUser(uid int64) *WebProductSubscribeUser {
	var table WebProductSubscribeUser
	ok, err := usercenter.GetEngine().Where("uid = ? AND status = ?", uid, library.WebOrderPayStatusEnum.Yes).
		OrderBy("web_order_type DESC").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetAllWebSubscribeUser 获取所有会员订阅信息
func (*wPSU) GetAllWebSubscribeUser(uid int64) []*WebProductSubscribeUser {
	var tables []*WebProductSubscribeUser
	err := usercenter.GetEngine().Where("uid = ?", uid).
		OrderBy("web_order_type DESC").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
