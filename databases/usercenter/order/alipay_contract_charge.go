package order

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type AlipayContractCharge struct {
	ID      int64  `xorm:"not null pk autoincr INT(11) 'id'"`
	OrderID string `xorm:"not null varchar(128) 'order_id'"`
	// 商品ID
	ProductID int64 `xorm:"not null int(10) 'product_id'"`
	// 用户UID
	UID int64 `xorm:"not null bigint(20) 'uid'"`
	// 商户签约号
	ContractCode string `xorm:"not null varchar(50) 'contract_code'"`
	// 用户签约成功后协议号
	ContractID string `xorm:"not null varchar(100) 'contract_id'"`
	// 扣款时间
	ChargeDate string `xorm:"not null varchar(10) 'charge_date'"`
	// 状态，0, 未支付，1：支付成功
	Status int `xorm:"not null tinyint(3) 'status'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (AlipayContractCharge) TableName() string {
	return "web_order_alipay_contract_charge"
}

type alipayCharge struct{}

var TbAlipayCharge alipayCharge

func (*alipayCharge) GetItemByOrderID(orderID string) *AlipayContractCharge {
	var table AlipayContractCharge
	ok, err := db.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*alipayCharge) GetPaidByOrderID(orderID string) *AlipayContractCharge {
	var table AlipayContractCharge
	ok, err := db.GetEngine().Where("order_id = ?", orderID).And("status = ?", library.Yes).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetChargeListByUIDContractCode 获取用户扣款记录
func (*alipayCharge) GetChargeListByUIDContractCode(uid int64, contractCode string) []*AlipayContractCharge {
	tables := make([]*AlipayContractCharge, 0)
	err := db.GetEngine().Where("contract_code = ?", contractCode).
		And("status = ? ", 1). // 已支付
		And("uid = ?", uid).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*alipayCharge) GetIsTryByOrderID(orderID, contractCode string) *AlipayContractCharge {
	var table AlipayContractCharge
	ok, err := db.GetEngine().Where(" contract_code =? and order_id != ?", contractCode, orderID).
		And("status = ? ", 1). // 已支付
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
