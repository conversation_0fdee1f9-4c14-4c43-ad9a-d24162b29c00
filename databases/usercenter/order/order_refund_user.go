package order

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type RefundUser struct {
	ID              int    `xorm:"not null pk autoincr INT(10) 'id'" json:"id"`
	NickName        string `xorm:"not null default '' VARCHAR(50) 'nick_name'" json:"nick_name"`
	OrderID         string `xorm:"not null default '' VARCHAR(64) 'order_id'" json:"order_id"`
	UID             int64  `xorm:"not null default 0 INT(11) 'uid'" json:"uid"`
	PhoneNumber     string `xorm:"not null default '' VARCHAR(50) 'phone_number'" json:"phone_number"`
	DebitMethod     int    `xorm:"not null default 1 TINYINT(1) 'debit_method'" json:"debit_method"`
	DebitTime       string `xorm:"not null default '' VARCHAR(50) 'debit_time'" json:"debit_time"`
	DebitAmount     string `xorm:"not null default 0.00 DECIMAL(10,2) 'debit_amount'" json:"debit_amount"`
	SpecificIssues  string `xorm:"not null default '' VARCHAR(500) 'specific_issues'" json:"specific_issues"`
	OrderPictureURL string `xorm:"not null default '' VARCHAR(1000) 'order_picture_url'" json:"order_picture_url"`
	Status          int    `xorm:"not null default 1 TINYINT(1) 'status'" json:"status"`
	CreateTime      int64  `xorm:"not null default 0 INT(10) 'create_time'" json:"create_time"`
	UpdateTime      int64  `xorm:"not null default 0 INT(10) 'update_time'" json:"update_time"`
	Remark          string `xorm:"default '' VARCHAR(100) 'remark'" json:"remark"`
	ReasonType      int    `xorm:"not null default 1 INT(11) 'reason_type'" json:"reason_type"`
	DenialReason    string `xorm:"not null default '' VARCHAR(500) 'denial_reason'" json:"denial_reason"`
}

// TableName 获取表名
func (RefundUser) TableName() string {
	return "order_refund_user"
}

func (u *RefundUser) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type orderRefundUser struct{}

var TbOrderRefundUser orderRefundUser

func (t *orderRefundUser) GetOrderListAll(uid int64) []*RefundUser {
	var tables []*RefundUser
	err := usercenter.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
