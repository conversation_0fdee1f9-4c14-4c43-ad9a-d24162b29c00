package order

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type ThirdKdtSubOrder struct {
	ID                               int    `json:"id" xorm:"not null pk autoincr INT(11)"`
	ThirdKdtOrderID                  int    `json:"third_kdt_order_id" xorm:"index(third_kdt_order_id) INT(11)"`
	NumIID                           int    `json:"num_iid" xorm:"not null index(third_kdt_order_id) INT(11)"`
	PayTime                          int    `json:"pay_time" xorm:"not null INT(11)"`
	OuterUserID                      int    `json:"outer_user_id" xorm:"not null comment('用户ID ') index INT(11)"`
	IsDelete                         int    `json:"is_delete" xorm:"not null default 2 TINYINT(1)"`
	CreateTime                       int    `json:"create_time" xorm:"not null INT(11)"`
	UpdateTime                       int    `json:"update_time" xorm:"not null INT(11)"`
	ShopType                         int    `json:"shop_type" xorm:"not null default 1 TINYINT(3)"`
	Tid                              string `json:"tid" xorm:"not null comment('交易编号') VARCHAR(255)"`
	TotalFee                         string `json:"total_fee" xorm:"not null comment('总价') DECIMAL(10,2)"`
	Payment                          string `json:"payment" xorm:"not null comment('真实支付') DECIMAL(10,2)"`
	DiscountFee                      string `json:"discount_fee" xorm:"not null comment('优惠') DECIMAL(10,2)"`
	RefundedFee                      string `json:"refunded_fee" xorm:"not null default 0.00 DECIMAL(10,2)"`
	RefundedTime                     int    `json:"refunded_time" xorm:"not null default 0 INT(11)"`
	FinancialFee                     string `json:"financial_fee" xorm:"not null default 0.00 DECIMAL(10,2)"`
	HasRecordEvent                   int    `json:"has_record_event" xorm:"not null default 0 TINYINT(3)"`
	RepurchasedType                  int    `json:"repurchased_type" xorm:"not null default 1 TINYINT(2)"`
	RecencyPurchaseO2Product         int    `json:"recency_purchase_o2_product" xorm:"not null default 0 INT(11)"`
	SourceID                         int    `json:"source_id" xorm:"not null comment('有赞的来源id') INT(11)"`
	SubOrderID                       string `json:"sub_order_id" xorm:"not null comment('拼接子订单id') VARCHAR(255)"`
	ManuallyRepurchasedType          int    `json:"manually_repurchased_type" xorm:"not null default 0 TINYINT(2)"`
	ManuallyRecencyPurchaseO2Product int    `json:"manually_recency_purchase_o2_product" xorm:"not null INT(11)"`
}

func (ThirdKdtSubOrder) TableName() string {
	return "third_kdt_sub_order"
}

type thirdKdtSubOrder struct{}

var TbThirdKdtSubOrder thirdKdtSubOrder

type KdtShopType int32

var KdtShopTypeEnum = struct {
	OR, O2 int
}{
	OR: 1, // 电商
	O2: 2, // o2
}

// GetOrderTotalCountByUIDAndShopType 根据 UID 和业务类型获取用户的累计订单数
func (s *thirdKdtSubOrder) GetOrderTotalCountByUIDAndShopType(uid, shopType int) int {
	total, err := usercenter.GetEngine().Where("outer_user_id = ? and shop_type = ? and is_delete = ? ",
		uid, shopType, library.DeleteStatusEnum.Valid).Count(ThirdKdtSubOrder{})
	if err != nil {
		logger.Errorf("获取用户的有赞订单总数失败 %s", err.Error())
		return 0
	}
	return int(total)
}
