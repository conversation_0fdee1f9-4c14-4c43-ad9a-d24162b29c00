package order

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type WebOrderAlipayContract struct {
	ID               int64  `xorm:"pk autoincr not null comment('主键') int(10) 'id'"`
	OrderID          string `xorm:"not null default '' comment('订单号') varchar(64) 'order_id'"`
	UID              int64  `xorm:"not null default 0 comment('用户uid') int(10) 'uid'"`
	ProductID        int64  `xorm:"not null default 0 comment('会员产品id') int(10) 'product_id'"`
	ChangeType       int32  `xorm:"not null default 0 comment('变更类型:0-等待签约,1-签约,2-解约') tinyint(3) 'change_type'"`
	ContractCode     string `xorm:"not null default '' comment('商户签约号') varchar(50) 'contract_code'"`
	ContractID       string `xorm:"not null default '' comment('用户签约成功后的协议号') varchar(100) 'contract_id'"`
	AppID            string `xorm:"not null default '2016012701123319' comment('支付宝app_id') varchar(100) 'app_id'"`
	PeriodType       string `xorm:"not null default '' comment('周期类型') varchar(10) 'period_type'"`
	Period           int32  `xorm:"not null default 0 comment('周期数') tinyint(3) 'period'"`
	ExecuteTime      string `xorm:"not null default '' comment('首次扣款时间') varchar(50) 'execute_time'"`
	IsHistoryDiscard int32  `xorm:"not null default 0 comment('是否历史放弃的订阅1-是') tinyint(2) 'is_history_discard'"`
	OfferType        int32  `xorm:"not null default 1 TINYINT(1) 'offer_type'" json:"offer_type"`
	CreateTime       int64  `xorm:"not null default 0 comment('创建时间') int(10) 'create_time'"`
	UpdateTime       int64  `xorm:"not null default 0 comment('更新时间') int(10) 'update_time'"`
}

func (w *WebOrderAlipayContract) TableName() string {
	return "web_order_alipay_contract"
}

type alipayContract struct{}

var TbAlipayContract alipayContract

func (*alipayContract) GetValidChargeByCode(contractCode string) *WebOrderAlipayContract {
	var table WebOrderAlipayContract
	ok, err := db.GetEngine().Where("contract_code = ?", contractCode).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*alipayContract) GetValidChargeByOrder(orderID string) *WebOrderAlipayContract {
	var table WebOrderAlipayContract
	ok, err := db.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
