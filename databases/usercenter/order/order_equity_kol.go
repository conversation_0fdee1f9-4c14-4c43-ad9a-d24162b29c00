//nolint:dupl
package order

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// EquityKol kol特权
type EquityKol struct {
	ID                 int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	UID                int64  `xorm:"not null INT(11) 'uid'" json:"uid"`
	OrderID            string `xorm:"not null VARCHAR(128) 'order_id'" json:"order_id"`
	ProductID          int    `xorm:"not null INT(11) 'product_id'" json:"product_id"`
	PaymentCode        int    `xorm:"not null default 0 SMALLINT(6) 'payment_code'" json:"payment_code"`
	PaymentMethod      string `xorm:"not null VARCHAR(16) 'payment_method'" json:"payment_method"`
	OrderAmountTotal   string `xorm:"not null default 0.00 DECIMAL(10,2) 'order_amount_total'" json:"order_amount_total"`
	ProductAmountTotal string `xorm:"not null DECIMAL(10,2) 'product_amount_total'" json:"product_amount_total"`
	OrderStatusID      int    `xorm:"not null TINYINT(2) 'order_status_id'" json:"order_status_id"`
	Status             int    `xorm:"not null TINYINT(1) 'status'" json:"status"`
	RenewType          int    `xorm:"not null TINYINT(3) 'renew_type'" json:"renew_type"`
	Version            string `xorm:"not null default '' VARCHAR(20) 'version'" json:"version"`
	PlatformType       int    `xorm:"default 0 INT(11) 'platform_type'" json:"platform_type"`
	CreateTime         int    `xorm:"not null INT(10) 'create_time'" json:"create_time"`
	UpdateTime         int64  `xorm:"not null INT(10) 'update_time'" json:"update_time"`
}

func (u *EquityKol) TableName() string {
	return "order_equity_kol"
}
func (u *EquityKol) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type orderEquityKol struct{}

var TbOrderEquityKol orderEquityKol

func (t *orderEquityKol) GetOrderListAll(uid int64) []*EquityKol {
	var tables []*EquityKol
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
