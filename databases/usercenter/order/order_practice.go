package order

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// OrderPractice 课程订单
// nolint
type OrderPractice struct {
	ID int `xorm:"not null pk autoincr INT(11) 'id'"`

	OrderID            string  `xorm:"'order_id'"`
	UID                int64   `xorm:"'uid'"`
	ProductID          int     `xorm:"'product_id'"`
	PaymentCode        int     // 购买代码
	PaymentMethod      string  // 购买方式
	OrderAmountTotal   float64 // 订单支付价格
	ProductAmountTotal float64 // 产品原价
	OrderStatusID      int64   `xorm:"order_status_id"` // 订单状态：1=》代付款2=》已付款
	Status             int     // 状态
	PracticeType       int     // 1-普通练习，2-KOL，3-O2,4-单独售卖，5-选修课
	RenewType          int     // 1:续费；2开通
	SourceType         int     // 来源类型：0.未上报来源，1.课程，2.计划，3.他人空间皇冠，4.个人界面皇冠（自己的），5.个人界面的主开通入口，6.音乐专辑，7.活动，8.其他
	SourceID           int64   `xorm:"source_id"` // 来源对象id
	Version            string  // 版本号
	PlatformType       int     // 渠道号
	CreateTime         int     // 订单创建时间
	UpdateTime         int64   `xorm:"update_time"` // 订单状态修改时间
}

func (u *OrderPractice) TableName() string {
	return "order_practice"
}
func (u *OrderPractice) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type orderPractice struct{}

var TbOrderPractice orderPractice

func (t *orderPractice) GetOrderListAll(uid int64) []*OrderPractice {
	var tables []*OrderPractice
	err := usercenter.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
