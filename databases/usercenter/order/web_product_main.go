package order

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type WebProductMain struct {
	ID                 int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	Name               string `xorm:"not null VARCHAR(255) 'name'" json:"name"`
	Image              string `xorm:"not null VARCHAR(255) 'image'" json:"image"`
	Price              string `xorm:"not null DECIMAL(18,2) 'price'" json:"price"`
	PriceDiscountText  string `xorm:"not null CHAR(32) 'price_discount_text'" json:"price_discount_text"`
	IosProductName     string `xorm:"not null VARCHAR(255) 'ios_product_name'" json:"ios_product_name"`
	Tag                int32  `xorm:"not null TINYINT(3) 'tag'" json:"tag"`
	ProductPackageName string `xorm:"not null VARCHAR(255) 'product_package_name'" json:"product_package_name"`
	DefaultPayment     int32  `xorm:"not null TINYINT(3) 'default_payment'" json:"default_payment"`
	Status             int32  `xorm:"not null default 1 TINYINT(1) 'status'" json:"status"`
	SortOrder          int32  `xorm:"not null default 0 TINYINT(3) 'sort_order'" json:"sort_order"`
	WebOrderType       int32  `xorm:"not null TINYINT(2) 'web_order_type'" json:"web_order_type"`
	CreateTime         int64  `xorm:"not null INT(10) 'create_time'" json:"create_time"`
	UpdateTime         int64  `xorm:"not null INT(10) 'update_time'" json:"update_time"`
	Numbers            int64  `xorm:"not null default 0 INT(10) 'numbers'" json:"numbers"`
	SalesStatus        int32  `xorm:"not null default -1 TINYINT(4) 'sales_status'" json:"sales_status"`
	LinkType           int32  `xorm:"not null default 0 TINYINT(3) 'link_type'" json:"link_type"`
	LinkValue          string `xorm:"not null VARCHAR(255) 'link_value'" json:"link_value"`
	NeedAddress        int32  `xorm:"not null default 1 TINYINT(1) 'need_address'" json:"need_address"`
	DescriptionH5      string `xorm:"not null TEXT 'description_h5'" json:"description_h5"`
	ShowType           int32  `xorm:"not null default 0 TINYINT(2) 'show_type'" json:"show_type"`
	LinkText           string `xorm:"not null default '' VARCHAR(255) 'link_text'" json:"link_text"`
}

// TableName 获取表名
func (WebProductMain) TableName() string {
	return "web_product_main"
}

type webProductMain struct{}

var TbWebProductMain webProductMain

// GetItem 获取组合商品套餐
func (w *webProductMain) GetItem(productID int64) *WebProductMain {
	var table WebProductMain
	ok, err := db.GetEngine().ID(productID).Get(&table)
	if err != nil {
		logger.Error("获取组合商品套餐信息失败:", err)
		return nil
	}
	if !ok {
		return nil
	}

	return &table
}
