package order

import (
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type FullGiftBaseOrder struct {
	// @text 自增ID
	ID int64 `xorm:"int(11) not null pk autoincr 'id'"`
	// @text uid
	UID int64 `xorm:"int(11) null 'uid'"`
	// @text 订单id
	OrderID string `xorm:"string null 'order_id'"`
	// @text 订单类型
	OrderType int64 `xorm:"int(11) not null 'order_type'"`
	// @text 产品名称
	ProductName string `xorm:"string not null 'product_name'"`
	// @text 产品id
	ProductID string `xorm:"string not null 'product_id'"`
	// @text 状态
	Status int64 `xorm:"tinyint(32) not null 'status'"`
	// @text 拆单金额
	TrueMoney int64 `xorm:"int(11) not null 'true_money'"`
	// @text 下单时间
	CreateOrderTime int64 `xorm:"int(11) not null 'create_order_time'"`
	// @text 支付时间
	PayOrderTime int64 `xorm:"int(11) not null 'pay_order_time'"`
	// @text 创建时间
	CreateTime int64 `xorm:"int(11) default null 'create_time'" `
	// @text 修改时间
	UpdateTime int64 `xorm:"int(11) default null 'update_time'" `
}

// @text 获取表名
func (t *FullGiftBaseOrder) TableName() string {
	return "activity_full_gift_base_order"
}

// @text 新增一条新数据
func (t *FullGiftBaseOrder) InsertNew() error {
	// @text 新增一条数据
	_, err := db.GetEngineMaster().InsertOne(t)
	return err
}
