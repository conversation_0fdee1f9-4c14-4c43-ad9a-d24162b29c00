package order

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// nolint
type YogaO2Total struct {
	ID           int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	OrderID      string `xorm:"not null VARCHAR(128) 'order_id'" json:"order_id"` // 订单号
	Code         string `xorm:"not null VARCHAR(32) 'code'" json:"code"`          // 状态
	Title        string `xorm:"not null VARCHAR(32) 'title'" json:"title"`        // 标题
	Value        string `xorm:"not null VARCHAR(32) 'value'" json:"value"`        // 内容
	SortOrder    int    `xorm:"not null INT(5) 'sort_order'" json:"sort_order"`   // 排序方式
	CreateTime   int64  `xorm:"not null INT(10) 'create_time'" json:"create_time"`
	UpdateTime   int64  `xorm:"not null INT(10) 'update_time'" json:"update_time"`
	IsMain       int    `xorm:"not null default 2 TINYINT(1) 'is_main'" json:"is_main"`               // 默认子订单
	OrderType    int    `xorm:"not null default 0 TINYINT(3) 'order_type'" json:"order_type"`         // 1会员，2娱乐大学，3，挑战赛，4口袋通，5，电商
	IsMainTotal  int    `xorm:"not null default 0 TINYINT(3) 'is_main_total'" json:"is_main_total"`   // 1,主订单用于区分总和
	OrderIndexID int64  `xorm:"not null default 0 BIGINT(30) 'order_index_id'" json:"order_index_id"` // 子订单ID
}

func (u *YogaO2Total) TableName() string {
	return "yoga_o2_total"
}

func (u *YogaO2Total) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type yogaO2Total struct{}

var TbYogaO2Total yogaO2Total

func (t *yogaO2Total) GetMainTotalByOrderID(orderID string) *YogaO2Total {
	var table YogaO2Total
	has, err := db.GetEngine().Where("order_id=? AND code=?", orderID, "VOUCHER").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !has {
		return nil
	}
	return &table
}
