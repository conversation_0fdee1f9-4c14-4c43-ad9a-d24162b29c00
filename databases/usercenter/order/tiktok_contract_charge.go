package order

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type TiktokContractCharge struct {
	ID int64 `xorm:"not null pk autoincr unsigned INT(10) 'id'"`
	// 订单号
	OrderID string `xorm:"not null varchar(64) 'order_id'"`
	// 用户UID
	UID int64 `xorm:"not null unsigned bigint(20) 'uid'"`
	// 商户签约号
	ContractCode string `xorm:"not null varchar(50) 'contract_code'"`
	// 商品ID
	ProductID int64 `xorm:"not null unsigned int(10) 'product_id'"`
	// 订单状态，0: 未支付，1：支付成功，2：支付失败
	Status int `xorm:"not null tinyint(1) 'status'"`
	// 扣款申请时间
	ChargeDate string `xorm:"not null varchar(10) 'charge_date'"`
	// 订单金额
	OrderAmount float64 `xorm:"not null decimal(10,2) 'order_amount'"`
	// 用户签约成功后的协议号
	ContractID string `xorm:"not null varchar(100) 'contract_id'"`
	// 平台侧代扣单的单号
	PayOrderID string `xorm:"not null varchar(100) 'pay_order_id'"`
	// 创建时间
	CreateTime int64 `xorm:"not null unsigned int(10) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null unsigned int(10) 'update_time'"`
}

// TableName 获取表名
func (a *TiktokContractCharge) TableName() string {
	return "web_order_tiktok_contract_charge"
}

// Save 保存数据
func (a *TiktokContractCharge) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := usercenter.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *TiktokContractCharge) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *TiktokContractCharge) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

type TiktokPayCharge struct{}

var TbTiktokCharge TiktokPayCharge

func (*TiktokPayCharge) GetLastChargeByCode(contractCode string) *TiktokContractCharge {
	var table TiktokContractCharge
	ok, err := usercenter.GetEngine().Where("contract_code = ?", contractCode).OrderBy("id desc").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*TiktokPayCharge) GetItemByOrderID(orderID string) *TiktokContractCharge {
	var table TiktokContractCharge
	ok, err := usercenter.GetEngine().Where("order_id = ?", orderID).OrderBy("id desc").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetChargeListByRange 获取用户扣款记录
func (*TiktokPayCharge) GetChargeListByRange(start, end int64, contractCode string) []*TiktokContractCharge {
	tables := make([]*TiktokContractCharge, 0)
	err := usercenter.GetEngine().Where("contract_code = ?", contractCode).
		And("create_time >= ? and create_time <= ?", start, end).OrderBy("id asc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetValidChargeByCode 根据签约号获取所有的扣款记录
func (*TiktokPayCharge) GetValidChargeByCode(contractCode string) []*TiktokContractCharge {
	tables := make([]*TiktokContractCharge, 0)
	err := usercenter.GetEngine().Where("contract_code = ? and status = ?", contractCode, library.Yes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*TiktokPayCharge) GetLastSuccessCharge(contractCode string, maxID int64) *TiktokContractCharge {
	var table TiktokContractCharge
	ok, err := usercenter.GetEngine().Where("contract_code = ? and id < ?",
		contractCode, maxID).And("status = ?", library.Yes).OrderBy("id desc").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetListByIDRange 根据id区间获取扣款记录
func (*TiktokPayCharge) GetListByIDRange(contractCode string, minID, maxID int64) []*TiktokContractCharge {
	tables := make([]*TiktokContractCharge, 0)
	err := usercenter.GetEngine().Where("contract_code = ?", contractCode).
		And("id > ? and id < ?", minID, maxID).OrderBy("id asc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetListByUID 根据UID获取扣款记录
func (*TiktokPayCharge) GetListByUID(uid int64) []*TiktokContractCharge {
	tables := make([]*TiktokContractCharge, 0)
	err := usercenter.GetEngine().Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*TiktokPayCharge) GetSuccessOrderByOrderID(orderID string) *TiktokContractCharge {
	var table TiktokContractCharge
	ok, err := usercenter.GetEngine().Where("order_id = ? AND status = ?", orderID, library.Yes).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*TiktokPayCharge) GetOtherSuccessOrder(orderID, contractCode string) *TiktokContractCharge {
	var table TiktokContractCharge
	ok, err := usercenter.GetEngine().Where("order_id != ? AND contract_code = ? AND status = ?",
		orderID, contractCode, library.Yes).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
