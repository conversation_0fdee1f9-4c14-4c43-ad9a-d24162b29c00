package order

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type WebProductMainToProduct struct {
	ID                     int                         `xorm:"int(11) notnull pk autoincr 'id'"` //
	WebProductMainID       int                         `xorm:"'web_product_main_id'"`            // 产品ID
	WebProductSourceID     int64                       `xorm:"'web_product_source_id'"`          // 子产品ID
	WebProductSourceType   library.WebProductSoureType // 1.会员，2.自售，3.asn
	CreateTime             int                         //
	UpdateTime             int                         //
	WebProductDividedPrice float64                     // 子产品的分账价格，为0取原价
}

// TableName Get table name
func (t *WebProductMainToProduct) TableName() string {
	return "web_product_main_to_product"
}

type webProductMainToProduct struct{}

var TbWebProductMainToProduct webProductMainToProduct

// GetByWebProductMainID 根据 web_product_main的id获取数据
// 2018-03-13 16:56:18 庞晓楠 创建
func (*webProductMainToProduct) GetByWebProductMainID(id int64) []WebProductMainToProduct {
	var tables []WebProductMainToProduct
	if err := usercenter.GetEngine().
		Where("web_product_main_id=?", id).
		OrderBy("web_product_source_type").Find(&tables); err != nil {
		logger.Error(err)
	}
	return tables
}
