package order

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type WebOrderWeixinContract struct {
	ID            int    `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	RequestSerial int64  `xorm:"not null BIGINT(20) 'request_serial'" json:"request_serial"`
	ContractCode  string `xorm:"default '' VARCHAR(64) 'contract_code'" json:"contract_code"`
	OrderID       string `xorm:"VARCHAR(64) 'order_id'" json:"order_id"`
	UID           int64  `xorm:"not null INT(11) 'uid'" json:"uid"`
	MchID         string `xorm:"default '' VARCHAR(64) 'mch_id'" json:"mch_id"`
	PlanID        string `xorm:"default '' VARCHAR(64) 'plan_id'" json:"plan_id"`
	OpenID        string `xorm:"default '' VARCHAR(32) 'openid'" json:"openid"`
	ChangeType    int    `xorm:"default 2 TINYINT(1) 'change_type'" json:"change_type"`
	ContractID    string `xorm:"default '' VARCHAR(32) 'contract_id'" json:"contract_id"`
	CreateTime    int64  `xorm:"not null default 0 INT(11) 'create_time'" json:"create_time"`
	UpdateTime    int64  `xorm:"default 0 INT(11) 'update_time'" json:"update_time"`
	ProductID     int    `xorm:"not null default 0 INT(11) 'productid'" json:"productid"`
	OrderIndex    int    `xorm:"not null default 0 INT(11) 'order_index'" json:"order_index"`
}

// TableName Get table name
func (u *WebOrderWeixinContract) TableName() string {
	return "web_order_weixin_contract"
}
func (u *WebOrderWeixinContract) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type webWxContract struct{}

var TbWebWxContract webWxContract

// GetByContractCode 根据签约协议号获取签约信息
func (*webWxContract) GetByContractCode(contractCode string) (*WebOrderWeixinContract, error) {
	var table WebOrderWeixinContract
	ok, err := db.GetEngine().Where("contract_code=?", contractCode).Get(&table)
	if err != nil {
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &table, nil
}

func (t *webWxContract) GetOrderListAll(uid int64) []*WebOrderWeixinContract {
	var tables []*WebOrderWeixinContract
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
