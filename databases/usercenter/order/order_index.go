package order

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type Index struct {
	ID              int64  `xorm:"not null autoincr pk int(11) 'id'"`
	UID             int64  `xorm:"not null int(11) 'uid'"`
	OrderID         string `xorm:"not null varchar(128) 'order_id'"`
	OrderType       int32  `xorm:"not null tinyint(3) 'order_type'"`
	OrderIndexID    int64  `xorm:"not null int(11) 'order_index_id'"`
	HasVip          int32  `xorm:"not null tinyint(1) 'has_vip'"`
	HasO2           int32  `xorm:"not null tinyint(1) 'has_o2'"`
	FundStatus      int32  `xorm:"not null tinyint(3) 'fund_status'"`
	HasEshop        int32  `xorm:"not null tinyint(1) 'has_eshop'"`
	HasProgram      int32  `xorm:"not null tinyint(1) 'has_program'"`
	HasOwnEshop     int32  `xorm:"not null tinyint(1) 'has_own_eshop'"`
	OrderMainID     int64  `xorm:"not null bigint(30) 'order_main_id'"`
	IsMain          int32  `xorm:"not null tinyint(1) 'is_main'"`
	HasPractice     int32  `xorm:"not null tinyint(3) 'has_practice'"`
	Status          int32  `xorm:"not null tinyint(1) 'status'"`
	HasYogaCurrency int32  `xorm:"not null tinyint(3) 'has_yoga_currency'"`
	CreateTime      int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime      int64  `xorm:"not null int(11) 'update_time'"`
}

func (u *Index) TableName() string {
	return "order_index"
}
func (u *Index) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type index struct{}

var TbOrderIndex index

// GetByOrderID 根据订单号查询
func (i *index) GetByOrderID(orderID string) []Index {
	var tables []Index
	err := db.GetEngine().Where("order_id = ?", orderID).Find(&tables)
	if err != nil {
		logger.Error("查询子订单失败", orderID, err)
		return nil
	}
	return tables
}

func (i *index) GetOrderListAll(uid int64) []*Index {
	var tables []*Index
	err := db.GetEngine().Where("uid=? and order_status_id=? ",
		uid, library.WebOrderPayStatusEnum.Yes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (i *index) GetOrderListAllByOrder(orders []string) []*Index {
	var tables []*Index
	err := db.GetEngine().In("order_id", orders).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
