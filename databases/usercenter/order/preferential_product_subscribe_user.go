package order

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type PreferentialProductSubscribeUser struct {
	ID              int64  `xorm:"pk autoincr BIGINT(30) 'id'" json:"id"`
	UID             int64  `xorm:"not null BIGINT(30) 'uid'" json:"uid"`
	ProductID       int    `xorm:"not null INT(6) 'product_id'" json:"product_id"`
	ExpiresTime     int64  `xorm:"BIGINT(30) 'third_part_expires_time'" json:"third_part_expires_time"`
	OrderID         string `xorm:"not null varchar(40) 'order_id'" json:"order_id"`
	OriginalOrderID string `xorm:"not null varchar(40) 'original_order_id'" json:"original_order_id"`
	TranID          string `xorm:"varchar(40) 'third_part_transaction_id'" json:"third_part_transaction_id"`
	Status          int    `xorm:"not null default 0 TINYINT(2) 'status'" json:"status"`
	CreateTime      int64  `xorm:"not null BIGINT(30) 'create_time'" json:"create_time"`
	UpdateTime      int64  `xorm:"not null BIGINT(30) 'update_time'" json:"update_time"`
}

// TableName Get table name
func (p *PreferentialProductSubscribeUser) TableName() string {
	return "preferential_product_subscribe_user"
}

func (p *PreferentialProductSubscribeUser) UpdateByTransaction(session *xorm.Session) error {
	p.UpdateTime = time.Now().Unix()
	_, err := session.ID(p.ID).Update(p)
	return err
}

type pPSU struct{}

var TbPPSubU pPSU

// GetWebSubscribeUser 获取会员订阅信息
func (*pPSU) GetWebSubscribeUser(uid int64) *PreferentialProductSubscribeUser {
	var table PreferentialProductSubscribeUser
	ok, err := usercenter.GetEngine().
		Where("uid=? AND status=?", uid, library.WebOrderPayStatusEnum.Yes).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetAllPSubscribeUser 获取所有特惠套餐订阅信息
func (*pPSU) GetAllPSubscribeUser(uid int64) []*PreferentialProductSubscribeUser {
	var tables []*PreferentialProductSubscribeUser
	err := usercenter.GetEngine().
		Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
