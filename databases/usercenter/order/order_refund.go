package order

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type Refund struct {
	ID               int32   `xorm:"pk autoincr not null comment('id主键') int(11) 'id'"`
	OrderID          string  `xorm:"not null comment('订单号') varchar(64) 'order_id'"`
	UID              int64   `xorm:"not null comment('用户uid') int(11) 'uid'"`
	OrderTime        int32   `xorm:"not null default 0 comment('下单时间') int(11) 'order_time'"`
	PayTime          int32   `xorm:"not null default 0 comment('支付时间') int(11) 'pay_time'"`
	ProductID        int32   `xorm:"not null default 0 comment('产品包id') int(11) 'product_id'"`
	ProductName      string  `xorm:"default 'NULL' comment('产品名称') varchar(50) 'product_name'"`
	OrderAmountTotal float64 `xorm:"not null default '0.00' comment('产品原价') 'order_amount_total'"`
	Price            float64 `xorm:"not null default '0.00' comment('实际支付金额') 'price'"`
	PaymentMethod    string  `xorm:"not null comment('支付方式') varchar(50) 'payment_method'"`
	// 1 待审核  2 已拒绝  3 退费中  4 退费成功  5 退费失败  6支付成功（现有订单状态）
	Status      int8    `xorm:"not null default 1  tinyint(1) 'status'"`
	Amount      float64 `xorm:"not null default '0.00' comment('退费金额') 'amount'"`
	Reason      string  `xorm:"default 'NULL' comment('退费理由') varchar(500) 'reason'"`
	CreateTime  int64   `xorm:"not null default 0 comment('创建时间') int(10) 'create_time'"`
	UpdateTime  int64   `xorm:"not null default 0 comment('更新时间') int(10) 'update_time'"`
	Msg         string  `xorm:"not null default '' comment('三方返回结果') varchar(5000) 'msg'"`
	OutRefundNo string  `xorm:"not null default '' comment('退款单号') varchar(72) 'out_refund_no'"`
	OrderType   int32   `xorm:"default 0 comment('订单类型') int(11) 'order_type'"`
	RefundTime  int32   `xorm:"not null comment('退款时间') int(10) 'refund_time'"`
	CommitID    int32   `xorm:"not null default 0 comment('提交人') int(11) 'commit_id'"`
	PayDevice   int8    `xorm:"not null default 1 comment('支付设备') tinyint(4) 'pay_device'"`
	InfoSources int8    `xorm:"not null default 1 comment('信息来源 1 管理员提交 2 用户提交') tinyint(1) 'info_sources'"`
	// 1 OB流程（默认选项）2挑战赛失败 3重复支付 4系统续订 5 个人原因 6更换权益 7测试 8其他
	ReasonType       int32 `xorm:"not null default 1 int(11) 'reason_type'"`
	VipDurationType  int8  `xorm:"not null default 0 comment('1 天 2 月') tinyint(1) 'vip_duration_type'"`
	VipDurationValue int32 `xorm:"default 0 comment('会员扣减时长') int(11) 'vip_duration_value'"`
	KolDurationType  int8  `xorm:"not null default 0 comment('1 天 2 月') tinyint(1) 'kol_duration_type'"`
	KolDurationValue int32 `xorm:"not null default 0 comment('扣减kol权益值') int(11) 'kol_duration_value'"`
}

// TableName 获取表名
func (Refund) TableName() string {
	return "order_refund"
}

func (u *Refund) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type orderRefund struct{}

var TbOrderRefund orderRefund

func (t *orderRefund) GetOrderListAll(uid int64) []*Refund {
	var tables []*Refund
	err := usercenter.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
