package order

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// Main 订单总表
// nolint
type Main struct {
	ID         int64  `xorm:"not null pk autoincr INT(11) 'id'"`
	UID        int64  `xorm:"not null int(11) 'uid'"`           // 用户ID
	OrderID    string `xorm:"not null varchar(128) 'order_id'"` // 订单号
	CreateTime int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64  `xorm:"not null int(11) 'update_time'"`
	// 1.web_order, 2.yoga_o2_order, 3.h2_order, 4.third_kdt_order
	OrderType int `xorm:"not null tinyint(3) 'order_type'"`
	// order_type 表的主键
	OrderIndexID               int64                     `xorm:"not null int(11) 'order_index_id'"`
	HasVIP                     int                       `xorm:"'has_vip'"` // 是否包含会员
	HasO2                      int                       // 是否包含瑜乐大学
	FundStatus                 int                       // 是否包含自控相关，非0为自控的类型
	HasEshop                   int                       // 是否包含电商
	HasProgram                 int                       // 是否包含计划售卖，非0为计划的售卖类型
	Status                     library.WebOrderPayStatus `xorm:"not null tinyint(3) 'status'"` // 0未付款，1已付款，2已取消
	ProductAmountTotal         float64                   // 产品金额
	HasOwnEshop                int                       // 是否包含自由电商
	PaymentCode                int                       // 购买代码
	PaymentMethod              string                    // 购买方式
	ExpressNumber              string                    // 快递单号
	UserReceivingInformationID int64                     `xorm:"'user_receiving_information_id'"` // 收货地址id
	UseWallet                  int                       // 是否使用钱包支付
	HasPractice                int                       // 是否包含课程，非0为售卖的课程类型
	ExpressCompany             string                    // 物流公司
	HasYogaCurrency            int                       // 是否包含瑜伽币
	OrderAmountTotal           float64                   `xorm:"not null default 0.00 DECIMAL(10,2) 'order_amount_total'" json:"order_amount_total"`
}

func (m *Main) TableName() string {
	return "order_main"
}

func (m *Main) UpdateByTransaction(session *xorm.Session) error {
	m.UpdateTime = time.Now().Unix()
	_, err := session.ID(m.ID).Update(m)
	return err
}

type orderMain struct{}

var TbOrderMain orderMain

// GetByOrderID 根据订单号获取数据
// 2018-08-16 10:40:17 庞晓楠 创建
func (orderMain) GetByOrderID(orderID string) *Main {
	var table Main
	ok, err := usercenter.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// BatchGetByOrderIDs 批量获取订单号数据
func (orderMain) BatchGetByOrderIDs(orderIDs []string) []*Main {
	resp := make([]*Main, 0)
	err := usercenter.GetEngine().In("order_id", orderIDs).Find(&resp)
	if err != nil {
		logger.Error("批量获取用户订单数据失败 %s", err.Error())
		return nil
	}
	return resp
}

// GetAmountByIDs 通过多个订单号计算出这批订单消费的总金额
func (orderMain) GetAmountByOrderIDs(orderIDs []string) float64 {
	sum, err := usercenter.GetEngine().In("order_id", orderIDs).Sum(&Main{}, "order_amount_total")
	if err != nil {
		logger.Error("批量获取用户订单数据失败 %s", err.Error())
		return 0
	}
	return sum
}

// GetYogaWalletPayedOrdersByInnerTime 根据时间区间获取Y币钱包支付订单
// 2018-08-29 17:19:53 庞晓楠
func (orderMain) GetYogaWalletPayedOrdersByInnerTime(t1, t2 int64) []Main {
	var tables []Main
	err := usercenter.GetEngine().Where("update_time>=? and update_time<=? and status=? and order_id like '%'",
		t1, t2, library.WebOrderPayStatusEnum.Yes).
		In("payment_code", library.PaymentCodeEnum.YogaWalletCodes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

/**
 * @desc	根据时间区间查询搭配购订单列表
 * @param   t1月开始时间
 * @param	t2月结束时间
 * @return  当月搭配购订单列表
 */
func (orderMain) GetYogaMatchPurchaseOrdersByTime(t1, t2 int64) []Main {
	var tables []Main
	err := usercenter.GetEngine().Where("status = ? and create_time >= ?"+
		" and create_time < ? and payment_method= ? and order_id like 'MA%'", 1, t1, t2, "钱包支付").Find(&tables)
	if err != nil {
		logger.Error("查询搭配购订单失败", err)
		return nil
	}

	return tables
}

/**
 * @desc	根据时间区间查询打包购订单列表
 * @param   t1月开始时间
 * @param	t2月结束时间
 * @return  当月打包购订单列表
 */
func (orderMain) GetYogaPkgPurchaseOrdersByTime(t1, t2 int64) []Main {
	var tables []Main
	err := usercenter.GetEngine().Where("status = ? and create_time >= ? "+
		"and create_time < ? and payment_method= ? and order_id like 'MC%'", 1, t1, t2, "钱包支付").Find(&tables)
	if err != nil {
		logger.Error("查询打包购订单失败", err)
		return nil
	}

	return tables
}

func (orderMain) GetOrderListAll(uid int64) []*Main {
	var tables []*Main
	err := usercenter.GetEngine().Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (orderMain) GetOrderListAllByOrder(orders []string) []*Main {
	var tables []*Main
	err := usercenter.GetEngine().In("order_id", orders).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
