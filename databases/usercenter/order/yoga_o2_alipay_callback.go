package order

import db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"

type YogaO2AlipayCallback struct {
	ID               int    `xorm:"not null pk autoincr INT(10) 'id'" json:"id"`
	Discount         string `xorm:"not null VARCHAR(16) 'discount'" json:"discount"`
	PaymentType      int    `xorm:"not null TINYINT(2) 'payment_type'" json:"payment_type"`
	Subject          string `xorm:"not null VARCHAR(255) 'subject'" json:"subject"`
	TradeNo          string `xorm:"not null VARCHAR(128) 'trade_no'" json:"trade_no"`
	BuyerEmail       string `xorm:"not null VARCHAR(64) 'buyer_email'" json:"buyer_email"`
	GmtCreate        string `xorm:"not null VARCHAR(32) 'gmt_create'" json:"gmt_create"`
	NotifyType       string `xorm:"not null VARCHAR(64) 'notify_type'" json:"notify_type"`
	Quantity         int    `xorm:"not null TINYINT(16) 'quantity'" json:"quantity"`
	OutTradeNo       string `xorm:"not null VARCHAR(32) 'out_trade_no'" json:"out_trade_no"`
	NotifyTime       string `xorm:"not null VARCHAR(32) 'notify_time'" json:"notify_time"`
	TradeStatus      string `xorm:"not null VARCHAR(64) 'trade_status'" json:"trade_status"`
	IsTotalFeeAdjust string `xorm:"not null VARCHAR(16) 'is_total_fee_adjust'" json:"is_total_fee_adjust"`
	TotalFee         string `xorm:"not null VARCHAR(16) 'total_fee'" json:"total_fee"`
	Price            string `xorm:"not null VARCHAR(16) 'price'" json:"price"`
	BuyerID          string `xorm:"not null VARCHAR(32) 'buyer_id'" json:"buyer_id"`
	NotifyID         string `xorm:"not null VARCHAR(64) 'notify_id'" json:"notify_id"`
	UseCoupon        string `xorm:"not null VARCHAR(8) 'use_coupon'" json:"use_coupon"`
	SignType         string `xorm:"not null VARCHAR(8) 'sign_type'" json:"sign_type"`
	Sign             string `xorm:"not null TEXT 'sign'" json:"sign"`
	CreateTime       int    `xorm:"not null INT(11) 'create_time'" json:"create_time"`
	UpdateTime       int    `xorm:"not null INT(11) 'update_time'" json:"update_time"`
}

func (o *YogaO2AlipayCallback) TableName() string {
	return "yoga_o2_alipay_callback"
}

type yogaO2AlipayCallback struct{}

var TbYogaO2AlipayCallback yogaO2AlipayCallback

// GetByOrderID 根据订单号获取订单信息
func (*yogaO2AlipayCallback) GetByOrderID(orderID string) *YogaO2AlipayCallback {
	var table YogaO2AlipayCallback
	ok, err := db.GetEngine().Where("out_trade_no = ?", orderID).Get(&table)
	if err != nil {
		return nil
	}

	if !ok {
		return nil
	}

	return &table
}
