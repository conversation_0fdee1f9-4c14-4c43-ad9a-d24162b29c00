package order

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type YogaCurrency struct {
	ID                 int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	UID                int64  `xorm:"not null INT(11) 'uid'" json:"uid"`
	OrderID            string `xorm:"not null VARCHAR(128) 'order_id'" json:"order_id"`
	ProductID          int    `xorm:"not null INT(11) 'product_id'" json:"product_id"`
	PaymentCode        int    `xorm:"not null default 0 SMALLINT(6) 'payment_code'" json:"payment_code"`
	PaymentMethod      string `xorm:"not null VARCHAR(16) 'payment_method'" json:"payment_method"`
	ExchangeRate       string `xorm:"not null default 1.00 DECIMAL(5,2) 'exchange_rate'" json:"exchange_rate"`
	OrderAmountTotal   string `xorm:"not null default 0.00 DECIMAL(10,2) 'order_amount_total'" json:"order_amount_total"`
	ProductAmountTotal string `xorm:"not null DECIMAL(10,2) 'product_amount_total'" json:"product_amount_total"`
	OrderStatusID      int    `xorm:"not null TINYINT(2) 'order_status_id'" json:"order_status_id"`
	Status             int    `xorm:"not null TINYINT(1) 'status'" json:"status"`
	PracticeType       int    `xorm:"not null default 1 TINYINT(3) 'practice_type'" json:"practice_type"`
	RenewType          int    `xorm:"not null TINYINT(3) 'renew_type'" json:"renew_type"`
	SourceType         int    `xorm:"not null default 0 INT(5) 'source_type'" json:"source_type"`
	SourceID           int    `xorm:"not null default 0 INT(11) 'source_id'" json:"source_id"`
	Version            string `xorm:"not null default '' VARCHAR(20) 'version'" json:"version"`
	PlatformType       int    `xorm:"default 0 INT(11) 'platform_type'" json:"platform_type"`
	CreateTime         int    `xorm:"not null INT(10) 'create_time'" json:"create_time"`
	UpdateTime         int64  `xorm:"not null INT(10) 'update_time'" json:"update_time"`
	YogaCurrency       string `xorm:"not null default 0.00 DECIMAL(10,2) 'yoga_currency'" json:"yoga_currency"`
	PayType            int    `xorm:"default 0 TINYINT(4) 'pay_type'" json:"pay_type"`
}

func (u *YogaCurrency) TableName() string {
	return "order_yoga_currency"
}
func (u *YogaCurrency) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type orderYogaCurrency struct{}

var TbOrderYogaCurrency orderYogaCurrency

func (t *orderYogaCurrency) GetOrderListAll(uid int64) []*YogaCurrency {
	var tables []*YogaCurrency
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
