package order

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// WebOrder 会员订单表
type WebOrder struct {
	ID      int    `xorm:"int(11) notnull pk autoincr 'id'" json:"id"` //
	OrderID string `xorm:"'order_id'"`                                 // 订单号
	UID     int64  `xorm:"'uid'"`                                      // 用户uid
	// ProductID： OrderType是组合商品时，该字段对应web_product_main.id，否则对应web_product.id
	ProductID  int64  `xorm:"'productid'"` // 产品包id
	Points     int    // 购买的积分
	Createtime int64  // 订单创建时间
	Updatetime int64  // 订单状态修改时间
	Status     int    `xorm:"'status'"` // 订单状态，0, 未支付，1：支付成功，2：支付失败；
	Note       string // 备注
	Partner    int    // 11:微信，12支付宝，20，32，支付宝（现在为0）
	CreateTime int    //
	UpdateTime int    //
	RenewType  int    // 1:续费；2开通
	// 来源类型：0.未上报来源，1.课程，2.计划，3.他人空间皇冠，
	// 4.个人界面皇冠（自己的），5.个人界面的主开通入口，6.音乐专辑，7.活动，8.其他
	SourceType    int
	SourceID      int64  `xorm:"'source_id'"` // 来源对象id
	Version       string // 版本号
	StatRenewType int    // 统计口径中的类型 1:续费；2开通
	// 订单类型,10普通会员, 20全能会员，11普通会员+自售，21全能会员+自售，12普通会员补差价
	OrderType                  library.WebOrderType
	ExpressNumber              string // 快递单号
	UserReceivingInformationID int    `xorm:"'user_receiving_information_id'"` // 收货地址id
	ExtraPayDays               int    // 补差价天数
	WebProductMainActivityID   int    `xorm:"'web_product_main_activity_id'"` //
	IsControl                  int    // 是否调控（0-否，1-是）
	PlatformType               int    // 对接支付渠道类型
}

// TableName Get table name
func (u *WebOrder) TableName() string {
	return "web_order"
}

// UpdateByTransaction 修改
func (u *WebOrder) UpdateByTransaction(session *xorm.Session) error {
	u.Updatetime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type webOrder struct{}

var TbWebOrder webOrder

// GetByOrderID 根据订单号获取订单信息
func (*webOrder) GetByOrderID(orderID string) (*WebOrder, error) {
	var table WebOrder
	ok, err := db.GetEngine().Where("order_id=?", orderID).Get(&table)
	if err != nil {
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &table, nil
}

// GetWebOrderList 获取用户全部支付订单
func GetWebOrderList(uid int64) []WebOrder {
	var t []WebOrder
	err := db.GetEngine().Where("uid=? AND status=?", uid, library.WebOrderPayStatusEnum.Yes).Find(&t)
	if err == nil {
		return t
	}

	logger.Error("get WebOrderList fail:", err)
	return nil
}

func (*webOrder) CheckUserBuyVipRecord(uid int64) bool {
	// 获取用户的会员购买记录
	return len(GetWebOrderList(uid)) != 0
}

// GetWebOrderList 获取指定时间支付成功的订单
func (*webOrder) GetWebOrderList(status, startTime, endTime int64) []WebOrder {
	var t []WebOrder
	err := db.GetEngine().Where("status=? AND update_time>=? AND update_time<=?", status, startTime, endTime).Find(&t)
	if err == nil {
		return t
	}
	logger.Error("get GetWebOrderList fail:", err)
	return nil
}

// HasHistoryMember 用户是否购买过会员
func (*webOrder) HasHistoryMember(uid int64) bool {
	count, err := db.GetEngine().Where("uid = ? and status = ? and order_type > ? ",
		uid, library.WebOrderPayStatusEnum.Yes, 0).Count(&WebOrder{})
	if err != nil {
		logger.Error(err)
		return false
	}
	return count > 0
}

func (w *webOrder) GetOrderListAll(uid int64) []*WebOrder {
	var t []*WebOrder
	err := db.GetEngine().Where("uid = ?", uid).Find(&t)
	if err != nil {
		return nil
	}
	return t
}
