package order

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type WebOrderTencentSportsUniteVip struct {
	ID int64 `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	// 订单 ID
	OrderID string `xorm:"varchar(50) notnull 'order_id'" json:"order_id"`
	// 订单类型
	OrderType int `xorm:"tingint(2) notnull 'order_type'" json:"order_type"`
	// 用户授权的 用户 ID
	UserID      string `xorm:"varchar(200) notnull 'user_id'" json:"user_id"`
	AppID       string `xorm:"varchar(100) notnull 'appid'" json:"appid"`
	AccessToken string `xorm:"varchar(256) notnull 'access_token'" json:"access_token"`
	ProductID   int    `xorm:"tingint(4) notnull 'product_id'" json:"product_id"`
	// 产品名称
	ProductName string `xorm:"varchar(50) notnull 'product_name'" json:"product_name"`
	// 腾讯产品类型
	ProductType string `xorm:"varchar(50) notnull 'product_type'" json:"product_type"`
	// 付款金额
	Money float64 `xorm:"decimal(10,2) notnull 'money'" json:"money"`
	// 支付类型
	PayType int `xorm:"tinyint(4) notnull 'pay_type'" json:"pay_type"`
	// 订单状态 0=>充值中, 1=>充值成功, 2=>充值失败
	Status int `xorm:"tinyint(1) notnull 'status'" json:"status"`
	// 充值返回数据
	PayResult  string `xorm:"varchar(200) notnull 'pay_result'" json:"pay_result"`
	CreateTime int    `xorm:"int(10) notnull 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"int(10) notnull 'update_time'" json:"update_time"`
}

func (t *WebOrderTencentSportsUniteVip) Update() error {
	t.UpdateTime = time.Now().Unix()

	_, err := usercenter.GetEngineMaster().ID(t.ID).Update(t)
	return err
}

// TableName Get table name
func (t *WebOrderTencentSportsUniteVip) TableName() string {
	return "web_order_tx_unite_vip"
}

type webOrderTencentSportsUniteVip struct{}

var TbWebOrderTencentSportsUniteVip webOrderTencentSportsUniteVip

// GetByOrderID 根据ID获取数据
func (*webOrderTencentSportsUniteVip) GetByOrderID(id string) (*WebOrderTencentSportsUniteVip, error) {
	var table WebOrderTencentSportsUniteVip
	has, err := usercenter.GetEngine().Where("order_id=?", id).Get(&table)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, nil
	}
	return &table, nil
}
