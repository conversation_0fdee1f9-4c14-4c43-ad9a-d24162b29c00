package order

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type CompleteOrderRequestLog struct {
	ID         int64  `xorm:"not null pk autoincr INT(11) 'id'"`
	OrderID    string `xorm:"not null varchar(128) 'order_id'"` // 订单号
	PayType    int64  `xorm:"not null int(11) 'pay_type'"`
	Request    string `xorm:"not null longtext 'request'"`
	CreateTime int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64  `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (CompleteOrderRequestLog) TableName() string {
	return "complete_order_request_log"
}

type compReqLog struct{}

var TbCompReqLog compReqLog

func (compReqLog) GetItem(orderID string) *CompleteOrderRequestLog {
	var table CompleteOrderRequestLog
	ok, err := db.GetEngine().Where("order_id = ?", orderID).Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (compReqLog) GetItemByAlipay(orderID string) *CompleteOrderRequestLog {
	var table CompleteOrderRequestLog
	ok, err := db.GetEngine().Where("order_id = ?", orderID).In("pay_type", []int{15, 24}).Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetMaxID 获取表中最大ID
func (compReqLog) GetMaxID() (int64, error) {
	var maxID int64
	_, err := db.GetEngine().SQL("SELECT MAX(id) as maxid FROM complete_order_request_log").Get(&maxID)
	if err != nil {
		logger.Error("获取complete_order_request_log表最大ID失败:", err)
		return 0, err
	}
	return maxID, nil
}

// GetListByIDRange 获取指定ID范围内的记录
func (compReqLog) GetListByIDRange(minID, maxID int64) []*CompleteOrderRequestLog {
	var items []*CompleteOrderRequestLog
	err := db.GetEngine().Where("id BETWEEN ? AND ?", minID, maxID).In("pay_type", []int{15, 24}).Find(&items)
	if err != nil {
		logger.Error("查询ID范围complete_order_request_log表数据失败:", err)
		return nil
	}
	return items
}
