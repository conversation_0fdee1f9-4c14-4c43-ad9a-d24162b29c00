//nolint:lll
package order

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type LiveProductSubscribeUser struct {
	ID                             int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	UID                            int64  `xorm:"not null default 0 INT(11) 'uid'" json:"uid"`
	TerminalDetail                 int    `xorm:"not null default 0 TINYINT(1) 'terminal_detail'" json:"terminal_detail"`
	ProductID                      int    `xorm:"not null default 0 INT(11) 'product_id'" json:"product_id"`
	ThirdPartExpiresTime           int    `xorm:"not null default 0 INT(11) 'third_part_expires_time'" json:"third_part_expires_time"`
	CreateTime                     int    `xorm:"not null default 0 INT(11) 'create_time'" json:"create_time"`
	UpdateTime                     int64  `xorm:"not null default 0 INT(11) 'update_time'" json:"update_time"`
	OrderID                        string `xorm:"not null default '0' VARCHAR(255) 'order_id'" json:"order_id"`
	OriginalOrderID                string `xorm:"not null default '0' VARCHAR(255) 'original_order_id'" json:"original_order_id"`
	ThirdPartTransactionID         string `xorm:"not null default '' VARCHAR(40) 'third_part_transaction_id'" json:"third_part_transaction_id"`
	ThirdPartOriginalTransactionID string `xorm:"not null default '' VARCHAR(40) 'third_part_original_transaction_id'" json:"third_part_original_transaction_id"`
	Status                         int    `xorm:"not null default 1 TINYINT(1) 'status'" json:"status"`
}

// TableName Get table name
func (p *LiveProductSubscribeUser) TableName() string {
	return "live_product_subscribe_user"
}

func (p *LiveProductSubscribeUser) UpdateByTransaction(session *xorm.Session) error {
	p.UpdateTime = time.Now().Unix()
	_, err := session.ID(p.ID).Update(p)
	return err
}

type lSU struct{}

var TbLSubU lSU

// GetLiveSubscribeUser 获取会员订阅信息
func (*lSU) GetLiveSubscribeUser(uid int64) []*LiveProductSubscribeUser {
	var tables []*LiveProductSubscribeUser
	err := usercenter.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
