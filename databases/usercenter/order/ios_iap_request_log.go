package order

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// VIPPauseRecord 会员暂停信息表
type IosIapRequestLog struct {
	ID            int64  `xorm:"not null pk autoincr INT(11) 'id'"`
	UID           int64  `xorm:"comment('用户ID') not null INT(11) 'uid'"`
	AppleUID      string `xorm:"comment('预下单ID') not null VARCHAR(128) 'apple_uid'"`
	TransactionID string `xorm:"comment('预下单ID') not null VARCHAR(128) 'transaction_id'"`
	Request       string `xorm:"comment('客户端请求') not null LONGTEXT 'request'"`
	ReceiptOrder  string `xorm:"comment('receipt 对应订单列表') not null LONGTEXT 'receipt_order'"`
	Response      string `xorm:"comment('接口返回值') not null VARCHAR(128) 'response'"`
	Status        int32  `xorm:"comment('订单状态，1：已处理，2：失败') not null default '1' TINYINT(1) 'status'"`
	CreateTime    int64  `xorm:"not null INT(11) 'create_time'"`
	UpdateTime    int64  `xorm:"not null INT(11) 'update_time'"`
}

func (*IosIapRequestLog) TableName() string {
	return "ios_iap_request_log"
}

// Save 保存
// 2018-08-12 10:23:15 庞晓楠 创建
func (v *IosIapRequestLog) Update() error {
	currUnixTime := time.Now().Unix()
	v.UpdateTime = currUnixTime
	_, err := db.GetEngineMaster().ID(v.ID).Update(v)
	return err
}

type iosIapRequestLog struct{}

var TbIosIapRequestLog iosIapRequestLog

func (*iosIapRequestLog) GetRequestRecords(logID int64) *IosIapRequestLog {
	var tables IosIapRequestLog
	_, err := db.GetEngine().Where("id = ?", logID).Get(&tables)
	if err != nil {
		logger.Error(err)
	}
	return &tables
}
