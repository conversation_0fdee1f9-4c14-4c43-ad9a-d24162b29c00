package order

import (
	"github.com/go-xorm/xorm"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type IosIapOrderIDfv struct {
	ID                    int64  `xorm:"not null pk autoincr INT(11) 'id'"`
	UID                   int64  `xorm:"not null INT(11) 'uid'"`
	OrderID               string `xorm:"comment('订单ID') not null VARCHAR(64) 'order_id'"`
	IDfv                  string `xorm:"comment('设备标示') not null VARCHAR(128) 'idfv'"`
	IosProductID          string `xorm:"comment('iOS SKU') not null VARCHAR(64) 'ios_product_id'"`
	Status                int32  `xorm:"not null  'status'"`
	CreateTime            int64  `xorm:"not null INT(11) 'create_time'"`
	UpdateTime            int64  `xorm:"not null INT(11) 'update_time'"`
	PaymentOrderType      int32  `xorm:"not null  'payment_order_type'"`
	TransactionID         string `xorm:"comment('apple 订单号') not null VARCHAR(64) 'transaction_id'"`
	OriginalTransactionID string `xorm:"comment('apple 订单号') not null VARCHAR(64) 'original_transaction_id'"`
	ExpiresDate           int64  `xorm:"not null INT(11) 'expires_date'"`
	IsTrialPeriod         int32  `xorm:"not null INT(11) 'is_trial_period'"`
	IsInIntroOfferPeriod  int32  `xorm:"not null INT(11) 'is_in_intro_offer_period'"`
}

func (*IosIapOrderIDfv) TableName() string {
	return "ios_iap_order_idfv"
}

// UpdateByTransaction 修改
func (u *IosIapOrderIDfv) UpdateByTransaction(session *xorm.Session) error {
	_, err := session.ID(u.ID).Update(u)
	return err
}

type iosIapOrderIDfv struct{}

var TbIosIapOrderIDfv iosIapOrderIDfv

func (i *iosIapOrderIDfv) GetOrderListAll(uid int64) []*IosIapOrderIDfv {
	var t []*IosIapOrderIDfv
	err := db.GetEngine().Where("uid = ?", uid).Find(&t)
	if err != nil {
		return nil
	}
	return t
}
