package order

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type WebProduct struct {
	ID                    int     `json:"id" xorm:"not null pk autoincr INT(11) 'id'"`
	Name                  string  `json:"name" xorm:"not null VARCHAR(255) 'name'"`
	Points                int     `json:"points" xorm:"not null INT(11) 'points'"`
	Price                 string  `json:"price" xorm:"not null DECIMAL(18,2) 'price'"`
	PriceDiscountText     string  `json:"price_discount_text" xorm:"not null CHAR(32) 'price_discount_text'"`
	CreateTime            int     `json:"create_time" xorm:"not null INT(10) 'create_time'"`
	UpdateTime            int     `json:"update_time" xorm:"not null INT(10) 'update_time'"`
	IosProductName        string  `json:"ios_product_name" xorm:"not null VARCHAR(255) 'ios_product_name'"`
	SubscribeText         string  `json:"subscribe_text" xorm:"default '' VARCHAR(256) 'subscribe_text'"`
	ProductID             int     `json:"product_id" xorm:"not null INT(10) 'product_id'"`
	Tag                   int     `json:"tag" xorm:"not null TINYINT(3) 'tag'"`
	ProductPackageName    string  `json:"product_package_name" xorm:"not null VARCHAR(255) 'product_package_name'"`
	DefaultPayment        int     `json:"default_payment" xorm:"not null TINYINT(3) 'default_payment'"`
	Status                int     `json:"status" xorm:"not null default 1 TINYINT(1) 'status'"`
	SortOrder             int     `json:"sort_order" xorm:"not null default 0 TINYINT(3) 'sort_order'"`
	DurationType          int     `json:"duration_type" xorm:"not null default 2 TINYINT(3) 'duration_type'"`
	DurationValue         int     `json:"duration_value" xorm:"not null INT(11) 'duration_value'"`
	WebOrderType          int     `json:"web_order_type" xorm:"not null default 10 TINYINT(3) 'web_order_type'"`
	TerminalType          int     `json:"terminal_type" xorm:"not null default 1 TINYINT(2) 'terminal_type'"`
	Channel               int     `json:"channel" xorm:"not null default 0 INT(11) 'channel'"`
	NeedAddress           int     `json:"need_address" xorm:"default 0 TINYINT(1) 'need_address'"`
	GiftGoodsTitle        string  `json:"gift_goods_title" xorm:"default '' VARCHAR(64) 'gift_goods_title'"`
	TitleColor            string  `json:"title_color" xorm:"default '' VARCHAR(64) 'title_color'"`
	TitleBgColor          string  `json:"title_bg_color" xorm:"default '' VARCHAR(64) 'title_bg_color'"`
	GiftLinkType          int     `json:"gift_link_type" xorm:"default 0 INT(3) 'gift_link_type'"`
	GiftLinkContent       string  `json:"gift_link_content" xorm:"default '' VARCHAR(255) 'gift_link_content'"`
	GiftLinkImg           string  `json:"gift_link_img" xorm:"default '' VARCHAR(255) 'gift_link_img'"`
	CategoryID            int     `json:"category_id" xorm:"not null INT(11) 'category_id'"`
	IdentificationType    int     `json:"identification_type" xorm:"not null TINYINT(2) 'identification_type'"`
	IdentificationTitle   string  `json:"identification_title" xorm:"not null VARCHAR(16) 'identification_title'"`
	IdentificationBgColor string  `json:"identification_bg_color" xorm:"VARCHAR(16) 'identification_bg_color'"`
	PurchaseType          int     `json:"purchase_type" xorm:"not null default 1 TINYINT(2) 'purchase_type'"`
	DeviceType            int     `json:"device_type" xorm:"not null default 10 TINYINT(2) 'device_type'"`
	ThirdPartType         int     `json:"third_part_type" xorm:"not null default 0 TINYINT(2) 'third_part_type'"`
	ThirdPartProductID    string  `json:"third_part_product_id" xorm:"not null VARCHAR(128) 'third_part_product_id'"`
	TrialType             string  `json:"trial_type" xorm:"not null default '1' VARCHAR(128) 'trial_type'"`
	TrialDays             string  `json:"trial_days" xorm:"not null VARCHAR(128) 'trial_days'"`
	DisplayType           int     `json:"display_type" xorm:"not null default 1 TINYINT(2) 'display_type'"`
	Version               int     `json:"version" xorm:"not null default 0 INT(8) 'version'"`
	SingleMonthPrice      float32 `json:"single_month_price" xorm:"FLOAT 'single_month_price'"`
	IsAudit               int     `json:"is_audit" xorm:"not null default 0 TINYINT(1) 'is_audit'"`
	TestingStage          int     `json:"testing_stage" xorm:"not null default 0 TINYINT(1) 'testing_stage'"`
	FirstBuyPrice         float32 `json:"first_buy_price" xorm:"default 0.00 FLOAT(5,2) 'first_buy_price'"`
	PriceDiscountTextTmp  string  `json:"price_discount_text_temporary" xorm:"CHAR(32) 'price_discount_text_temporary'"`
	DiscountTextStartTime int     `json:"discount_text_start_time" xorm:"INT(11) 'discount_text_start_time'"`
	DiscountTextEndTime   int     `json:"discount_text_end_time" xorm:"INT(11) 'discount_text_end_time'"`
	SkuDescribe           string  `json:"sku_describe" xorm:"not null default '' VARCHAR(100) 'sku_describe'"`
	SkuStyle              int     `json:"sku_style" xorm:"not null default 3 TINYINT(1) 'sku_style'"`
	// 1:普通用户,2:普通会员,3:普通年费会员,4:高级会员,5:高级年费会员,6:7.0.0 高级会员,7:7.0.0 高级年费会员
	VipType     int    `json:"vip_type" xorm:"not null TINYINT(3) 'vip_type'"`
	OriginPrice string `json:"origin_price" xorm:"not null DECIMAL(18,2) 'origin_price'"`
	OfferType   int64  `json:"offer_type" xorm:"'offer_type'"` // 优惠类型：1-无 2-首购 3-试用 4-试用+首购
	// 首购优惠价
	OfferFirstBuyPrice float64 `xorm:"default 0.00 DECIMAL(10,2) 'offer_first_buy_price'" json:"offer_first_buy_price"`
	// 首购优惠周期
	OfferFirstBuyCycle int `xorm:"not null default 0 TINYINT(1) 'offer_first_buy_cycle'" json:"offer_first_buy_cycle"`
	// 试用价格
	OfferTrialPrice float64 `xorm:"default 0.00 DECIMAL(10,2) 'offer_trial_price'" json:"offer_trial_price"`
	// 试用天数
	OfferTrialDay int `xorm:"not null default 0 INT(5) 'offer_trial_day'" json:"offer_trial_day"`
}

func (WebProduct) TableName() string {
	return "web_product"
}

type _webProduct struct{}

var TbWebProduct _webProduct

// GetByID 通过 ID 获取一条
func (s *_webProduct) GetByID(id int64) *WebProduct {
	resp := new(WebProduct)
	ok, err := usercenter.GetEngine().Where("id = ?", id).Get(resp)
	if err != nil {
		logger.Errorf("%s", err.Error())
		return nil
	}
	if !ok {
		return nil
	}
	return resp
}
