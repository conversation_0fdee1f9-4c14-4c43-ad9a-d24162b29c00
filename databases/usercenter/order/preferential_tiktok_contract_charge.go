package order

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type PreferentialTiktokContractCharge struct {
	ID int64 `xorm:"not null pk autoincr unsigned INT(10) comment('主键') 'id'"`
	// 订单号
	OrderID string `xorm:"not null varchar(64) default('') comment('订单号') index('idx_order_id') 'order_id'"`
	// 用户UID
	UID int64 `xorm:"not null unsigned bigint(20) default(0) comment('用户uid') index('IDX_UID') 'uid'"`
	// 商户签约号
	ContractCode string `xorm:"not null varchar(50) default('') comment('商户签约号') index('idx_code') 'contract_code'"`
	// 商品ID
	ProductID int64 `xorm:"not null unsigned int(10) default(0) comment('会员产品id') 'product_id'"`
	// 订单状态，0: 未支付，1：支付成功，2：支付失败
	Status int `xorm:"not null tinyint(1) default(0) comment('订单状态，0, 未支付，1：支付成功，2：支付失败；') 'status'"`
	// 扣款申请时间
	ChargeDate string `xorm:"not null varchar(10) comment('扣款申请时间') 'charge_date'"`
	// 订单金额
	OrderAmount float64 `xorm:"not null decimal(10,2) default(0.00) comment('订单金额') 'order_amount'"`
	// 用户签约成功后的协议号
	ContractID string `xorm:"not null varchar(100) default('') comment('用户签约成功后的协议号') 'contract_id'"`
	// 平台侧代扣单的单号
	PayOrderID string `xorm:"not null varchar(100) default('') comment('平台侧代扣单的单号') 'pay_order_id'"`
	// 创建时间
	CreateTime int64 `xorm:"not null unsigned int(10) default(0) comment('创建时间') 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null unsigned int(10) default(0) comment('更新时间') 'update_time'"`
}

// TableName 获取表名
func (a *PreferentialTiktokContractCharge) TableName() string {
	return "preferential_tiktok_contract_charge"
}

// Save 保存数据
func (a *PreferentialTiktokContractCharge) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := usercenter.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *PreferentialTiktokContractCharge) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *PreferentialTiktokContractCharge) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

type PreferentialTiktokPayCharge struct{}

var TbPreferentialTiktokCharge PreferentialTiktokPayCharge

// GetListByUID 根据UID获取扣款记录
func (*PreferentialTiktokPayCharge) GetListByUID(uid int64) []*PreferentialTiktokContractCharge {
	tables := make([]*PreferentialTiktokContractCharge, 0)
	err := usercenter.GetEngine().Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// 根据订单号获取扣款记录
func (*PreferentialTiktokPayCharge) GetByOrderID(orderID string) *PreferentialTiktokContractCharge {
	table := new(PreferentialTiktokContractCharge)
	ok, err := usercenter.GetEngine().Where("order_id = ?", orderID).Get(table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return table
}
