package order

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type LiveUserOrder struct {
	// ID 自增id
	ID int64 `xorm:"int(11) unsigned notnull pk autoincr 'id'" json:"id"`
	// UID 用户ID
	UID int64 `xorm:"int(11) unsigned notnull 'uid'" json:"uid"`
	// ProductType 产品类型：1-课包（配合quantity）
	ProductType int32 `xorm:"tinyint(1) unsigned notnull 'product_type'" json:"product_type"` // 产品类型：1-课包（配合quantity）
	// OrderID 订单号
	OrderID string `xorm:"varchar(255) notnull 'order_id'" json:"order_id"`
	// ProductID 产品id
	ProductID int64 `xorm:"int(11) unsigned notnull 'product_id'" json:"product_id"`
	// OrderStatusID 订单状态：1-等待付款，2-已付款，3-退款
	OrderStatusID int32 `xorm:"tinyint(1) unsigned notnull default 0 'order_status_id'" json:"order_status_id"`
	// Status 开通状态状态 1-等待添加 2-添加成功 3-退款退权益
	Status     int32 `xorm:"tinyint(1) unsigned notnull default 0 'status'" json:"status"`
	CreateTime int64 `xorm:"int(11) notnull 'create_time'" json:"create_time"`
	UpdateTime int64 `xorm:"int(11) notnull 'update_time'" json:"update_time"`
}

// TableName Get table name
func (u *LiveUserOrder) TableName() string {
	return "live_user_order"
}
func (u *LiveUserOrder) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

// Update 更新数据
func (u *LiveUserOrder) Update() error {
	u.UpdateTime = time.Now().Unix()
	_, err := db.GetEngineMaster().ID(u.ID).Update(u)
	return err
}

type _LiveUserOrder struct{}

// TbLiveUserOrder 外部调用静态变量
var TbLiveUserOrder _LiveUserOrder

// GetItem 查询订单
func (u *_LiveUserOrder) GetItem(orderID string) *LiveUserOrder {
	var table LiveUserOrder
	ok, err := db.GetEngineMaster().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (u *_LiveUserOrder) GetOrderListAll(uid int64) []*LiveUserOrder {
	var tables []*LiveUserOrder
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
