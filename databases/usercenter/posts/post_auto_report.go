package posts

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type PostAutoReport struct {
	// ID 自增id
	ID int64 `xorm:"int(11) unsigned notnull pk autoincr 'id'" json:"id"`
	// ObjID 资源ID
	ObjID int64 `xorm:"int(11) unsigned notnull 'obj_id'" json:"obj_id"`
	// Mobile 举报人手机号
	Mobile string `xorm:"varchar(20) notnull 'mobile'" json:"mobile"`
	// ReportImage 举报图片
	ReportImage string `xorm:"varchar(255) notnull 'report_image'" json:"report_image"`
	// ReportEnu 举报类型
	ReportEnu string `xorm:"varchar(64) notnull 'report_enu'" json:"report_enu"`
	// ReportMsg 举报信息
	ReportMsg string `xorm:"varchar(128) notnull 'report_msg'" json:"report_msg"`
	// Status 审核状态： 0：审核通过(别人可以看到)1：审核通过(只在我的帖子内看到)2-待审核（审核中） 3-机审核不通过 4-人工审核不通过 (post表内isForbidden)
	Status int32 `xorm:"tinyint(2) unsigned notnull 'status'" json:"status"`
	// VerifyTime 审核时间
	VerifyTime string `xorm:"int(11) unsigned notnull default 0 'verify_time'" json:"verify_time"`
	CreateTime int64  `xorm:"int(11) unsigned notnull 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"int(11) unsigned notnull 'update_time'" json:"update_time"`
}

// TableName Get table name
func (p *PostAutoReport) TableName() string {
	return "post_auto_report"
}

// Save 新增记录
func (p *PostAutoReport) Save() error {
	p.CreateTime = time.Now().Unix()
	p.UpdateTime = p.CreateTime
	_, err := db.GetEngineMaster().Insert(p)
	return err
}

// Update 更新记录
func (p *PostAutoReport) Update() error {
	p.UpdateTime = time.Now().Unix()
	_, err := db.GetEngineMaster().ID(p.ID).Update(p)
	return err
}

type _postautoreport struct{}

// TbPostAutoReport 外部调用静态变量
var TbPostAutoReport _postautoreport

func (p *_postautoreport) GetItemByID(id int64) *PostAutoReport {
	var table PostAutoReport
	ok, err := db.GetEngine().Where("id = ?", id).Get(&table)
	if !ok {
		return nil
	}
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &table
}
