package posts

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type Posts struct {
	// ID 自增id
	ID      int64  `xorm:"int(11) unsigned notnull pk autoincr 'Id'" json:"id"`
	Title   string `xorm:"varchar(255) notnull 'Title'" json:"title"`
	Content string `xorm:"text notnull 'Content'" json:"content"`
	Images  string `xorm:"text notnull 'Images'" json:"images"`
	// 是否被禁止， 0：NO，1：YES( 0：审核通过(别人可以看到)1：审核通过(只在我的帖子内看到)2-待审核（审核中）
	// 3-机审核不通过 4-人工审核不通过 (post表内isForbidden))
	IsForbidden int32 `xorm:"tinyint(2) unsigned notnull 'isForbidden'" json:"isForbidden"`
	CreateTime  int64 `xorm:"int(11) unsigned notnull 'create_time'" json:"create_time"`
	UpdateTime  int64 `xorm:"int(11) unsigned notnull 'update_time'" json:"update_time"`
}

// TableName Get table name
func (p *Posts) TableName() string {
	return "posts"
}

// Update 更新记录
func (p *Posts) Update() error {
	p.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().ID(p.ID).Update(p)
	return err
}

type _posts struct {
}

var TbPosts _posts

// GetItem 帖子信息
func (w *_posts) GetItem(productID int64) *Posts {
	var table Posts
	ok, err := usercenter.GetEngine().Where("Id = ?", productID).Get(&table)
	if err != nil {
		logger.Errorf("获取帖子信息失败, err: %s", err)
		return nil
	}
	if !ok {
		return nil
	}

	return &table
}
