package posts

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type PostAutoVerify struct {
	// ID 自增id
	ID int64 `xorm:"int(11) unsigned notnull pk autoincr 'id'" json:"id"`
	// uid
	UID int64 `xorm:"int(11) unsigned notnull 'uid'" json:"uid"`
	// ObjID 资源ID
	ObjID int64 `xorm:"int(11) unsigned notnull 'obj_id'" json:"obj_id"`
	// Mobile 手机号
	Mobile string `xorm:"varchar(20) notnull 'mobile'" json:"mobile"`
	// Moderation 违规信息
	Moderation string `xorm:"varchar(255) notnull 'moderation'" json:"moderation"`
	// TypeEnu 举报类型
	TypeEnu string `xorm:"varchar(64) notnull 'type_enu'" json:"type_enu"`
	// VerifyMsg 审核信息
	VerifyMsg string `xorm:"varchar(128) notnull 'verify_msg'" json:"verify_msg"`
	// Status 审核状态： 0：审核通过(别人可以看到)1：审核通过(只在我的帖子内看到)2-待审核（审核中） 3-机审核不通过
	// 4-人工审核不通过 (post表内isForbidden)
	Status int32 `xorm:"tinyint(2) unsigned notnull 'status'" json:"status"`
	// VerifyTime 审核时间
	VerifyTime int64 `xorm:"int(11) unsigned notnull default 0 'verify_time'" json:"verify_time"`
	CreateTime int64 `xorm:"int(11) unsigned notnull 'create_time'" json:"create_time"`
	UpdateTime int64 `xorm:"int(11) unsigned notnull 'update_time'" json:"update_time"`
}

// TableName Get table name
func (p *PostAutoVerify) TableName() string {
	return "post_auto_verify"
}

// Save 新增记录
func (p *PostAutoVerify) Save() error {
	p.CreateTime = time.Now().Unix()
	p.UpdateTime = p.CreateTime
	_, err := usercenter.GetEngineMaster().Insert(p)
	return err
}

// Update 更新记录
func (p *PostAutoVerify) Update() error {
	p.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().ID(p.ID).Update(p)
	return err
}
