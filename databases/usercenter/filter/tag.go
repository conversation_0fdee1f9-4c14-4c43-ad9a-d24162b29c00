package filter

import (
	"context"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	coursedb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type Tag struct {
	ID int32 `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	// 父ID，0为标签分类
	Pid int32 `xorm:"int(11) notnull 'pid'" json:"pid"`
	// 名称
	Name string `xorm:"not null varchar(256) 'name'" json:"name"`
	// 推荐标签表
	SubTitle string `xorm:"not null varchar(256) 'sub_title'" json:"sub_title"`
	// 标签介绍
	Description string `xorm:"not null varchar(256) 'description'" json:"description"`
	// 标签排序
	SortOrder int64 `xorm:"int(5) notnull default 0 'sort_order'" json:"sort_order"`
	// 1：删除 2正常
	IsDelete int32 `xorm:"tinyint(1) not null default 2 'is_del'" json:"is_del"`
	// 是否存在icon 1:存在 2不存在
	HasIcon int32 `xorm:"tinyint(1) not null default 2 'has_icon'" json:"has_icon"`
	// 创建时间
	CreateTime int64 `xorm:"int(11) notnull default 0 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"int(11) notnull default 0 'update_time'" json:"update_time"`
}

type tag struct{}

// TbTag 外部调用对象
var TbTag tag

// TableName 表名
func (Tag) TableName() string {
	return "filter_tag"
}

func (*tag) GetTagList(ctx context.Context) ([]Tag, error) {
	var tables []Tag
	err := coursedb.GetEngine().Where("is_del=?", 2).Asc("sort_order").Desc("update_time").Find(&tables)
	if err != nil {
		logger.CError(ctx, err)
		return tables, err
	}
	return tables, nil
}

// GetTagsByPID 获取指定pid下所有tag
func (*tag) GetTagsByPID(pid int64) []*Tag {
	var tables []*Tag
	err := coursedb.GetEngine().Where("pid = ?", pid).
		Where("is_del=?", 2).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
