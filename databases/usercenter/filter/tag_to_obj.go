package filter

import (
	"context"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	coursedb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type TagResource struct {
	ID int32 `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	// tagId
	TagID int32 `xorm:"int(11) notnull 'tag_id'" json:"tag_id"`
	// 资源id
	ObjectID int32 `xorm:"not null varchar(256) 'obj_id'" json:"object_id"`
	// 资源类型
	ObjectType int32 `xorm:"not null varchar(256) 'obj_type'" json:"object_type"`
	// 创建时间
	CreateTime int64 `xorm:"int(11) notnull default 0 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"int(11) notnull default 0 'update_time'" json:"update_time"`
}

type tagResource struct{}

// TbTagResource 外部调用对象
var TbTagResource tagResource

// TableName 表名
func (t *TagResource) TableName() string {
	return "filter_tag_to_obj"
}

// 根据tagId返回资源列表
func (*tagResource) GetTagResourceList(ctx context.Context, tagID []int32) ([]TagResource, error) {
	var tables []TagResource
	err := coursedb.GetEngine().In("tag_id", tagID).Find(&tables)
	if err != nil {
		logger.CError(ctx, err)
		return tables, err
	}
	return tables, nil
}

type TagList struct {
	ID       int64  `xorm:"id" json:"id"`
	Pid      int64  `xorm:"pid" json:"pid"`
	ObjID    int64  `xorm:"obj_id" json:"obj_id"`
	Title    string `xorm:"title" json:"title"`
	SubTitle string `xorm:"sub_title" json:"sub_title"`
}

// GetTagResourceItemList 获取关联标签列表
func (*tagResource) GetTagResourceItemList(ids []int32, objType int32) []TagList {
	var tables []TagList
	session := coursedb.GetEngine().
		Table("filter_tag_to_obj").Alias("fto").
		Select("fto.obj_id, ft.name as title, ft.sub_title, ft.id, ft.pid").
		Join("LEFT", []string{"filter_tag", "ft"}, "fto.tag_id = ft.id").
		Where("ft.is_del = ?", library.DataStatusEnum.Delete).
		And("ft.pid <> ?", 0)
	if len(ids) > 0 {
		session = session.In("fto.obj_id", ids)
	}
	if objType != 0 {
		session = session.And("fto.obj_type = ?", objType)
	}
	if err := session.Asc("id").Find(&tables); err != nil {
		logger.Error(err)
	}
	return tables
}
