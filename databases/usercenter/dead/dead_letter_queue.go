package dead

type Queue struct {
	ID         int    `json:"id" xorm:"'id' not null pk autoincr comment('主键') INT(10)"`
	Key        int    `json:"key" xorm:"not null default 0 comment('业务标识:1:苹果广告上报神策') TINYINT(1)"`
	Content    string `json:"content" xorm:"not null default '' comment('业务详情') VARCHAR(2000)"`
	Status     int    `json:"status" xorm:"not null default 1 comment('1未处理,2:已处理') TINYINT(1)"`
	CreateTime int    `json:"create_time" xorm:"created not null default 0 comment('创建时间') INT(11)"`
	UpdateTime int    `json:"update_time" xorm:"updated not null default 0 comment('修改时间') INT(11)"`
}

// TableName Get table name
func (Queue) TableName() string {
	return "dead_letter_queue"
}
