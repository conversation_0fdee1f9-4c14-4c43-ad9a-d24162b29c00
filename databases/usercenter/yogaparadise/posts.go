package yogaparadise

import (
	"context"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	dbusercenter "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// Posts 帖子
type Posts struct {
	ID         int64 `xorm:"int(11) notnull pk autoincr 'Id'" json:"Id"`       //
	UID        int64 `xorm:"int(11) notnull default 0 'UserID'" json:"UserID"` // 年份
	Images     string
	Created    int64 `xorm:"int(11) not null 'Created'"`                         // 创建时间
	CreateTime int64 `xorm:"int(10) default 0 'create_time'" json:"create_time"` //
	UpdateTime int64 `xorm:"int(10) default 0 'update_time'" json:"update_time"` //
}

type posts struct{}

var TbPosts posts

// GetItem 获取帖子详情信息
func (p *posts) GetItem(postID int64) (*Posts, error) {
	var table Posts

	ok, err := dbusercenter.GetEngine().Where("id = ?", postID).Get(&table)
	if err != nil {
		logger.Warnf("获取帖子信息失败, err: %s", err)
		return nil, err
	}
	if !ok {
		return nil, nil
	}

	return &table, nil
}

// GetList 获取帖子信息
// 2017-12-22 20:06:22 朱家琪 创建
func (p *posts) GetList(ctx context.Context, uid int64) (tables []Posts) {
	err := dbusercenter.GetEngine().Where(" UserID=? AND Images IS NOT NULL AND Images<>''"+
		" AND IsDel=0 AND isForbidden=0 ", uid).Desc("Created").Limit(3).Find(&tables)
	if err != nil {
		logger.CError(ctx, err)
	}
	return
}

// GetValidListByPage 分页获取可见帖子列表
func (p *posts) GetValidListByPage(uid int64, page, pageSize int) []Posts {
	var tables []Posts
	offSize := (page - 1) * pageSize
	err := dbusercenter.GetEngine().Where(" UserID = ? AND Images IS NOT NULL AND Images<>''"+
		" AND IsDel=0 AND isForbidden=0 AND public_status = 1", uid).
		Desc("Created").Limit(pageSize, offSize).Find(&tables)
	if err != nil {
		logger.Warn(err)
		return nil
	}

	return tables
}
