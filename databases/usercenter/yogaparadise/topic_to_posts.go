package yogaparadise

import (
	"fmt"
	"strconv"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type TopicToPosts struct {
	ID                int64 `xorm:"not null pk autoincr INT(11) 'id'"`
	TopicID           int64 `xorm:"bigint(30) NOT NULL 'topic_id'"`
	PostsID           int64 `xorm:"bigint(30) NOT NULL 'posts_id'"`
	PostLastReplyTime int64 `xorm:"int(10) NOT NULL 'post_last_reply_time'"`
	IsDigest          int64 `xorm:"tinyint(3) NOT NULL DEFAULT '2' 'isDigest'"`
	IsTop             int64 `xorm:"tinyint(3) NOT NULL DEFAULT '2' 'isTop'"`
	SortOrder         int64 `xorm:"smallint(6) NOT NULL DEFAULT '0' 'sort_order'"`
	CreateTime        int64 `xorm:"not null INT(11) 'create_time'"`
	UpdateTime        int64 `xorm:"not null INT(11) 'update_time'"`
}

type topicToPosts struct{}

var TbTopicToPosts topicToPosts

// @text 查询话题下有效帖子个数
func (topicToPosts) GetPostsNumByTopic(topicID int64) (int64, error) {
	var total int64

	QuerySQL := fmt.Sprintf("SELECT COUNT(p.Id) AS `total` FROM `posts` AS p JOIN "+
		"`topic_to_posts` AS t on p.Id = t.posts_id and t.topic_id = %d where p.isDel = %d AND p.isForbidden = %d"+
		" AND p.public_status = %d", topicID, 0, 0, 1)
	res, err := usercenter.GetEngine().QueryString(QuerySQL)

	if err != nil {
		logger.Error(err)
		return 0, err
	}
	for _, v := range res {
		i, _ := strconv.Atoi(v["total"])
		total = int64(i)
	}

	return total, nil
}
