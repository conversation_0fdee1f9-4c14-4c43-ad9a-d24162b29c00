package challenge

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type UserExtTask struct {
	ID         int64 `xorm:"not null autoincr pk int(11) 'id'"`
	UID        int64 `xorm:"not null int(11) 'uid'"`
	ToUserID   int64 `xorm:"int(11) not null 'to_user_id'" json:"to_user_id"`
	ExtTaskID  int64 `xorm:"int(11) not null 'ext_task_id'" json:"ext_task_id"`
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

func (UserExtTask) TableName() string {
	return "self_control_fund_to_user_ext_task"
}

type userExtTask struct{}

// TbUserExtTask 外部调用对象
var TbUserExtTask userExtTask

// Save 保存数据
func (u *UserExtTask) Save() error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime

	_, err := statrds.GetEngineMaster().Insert(u)
	return err
}

// GetItemByUIDToUserID 根据uid和to_user_id获取升级信息
func (u *userExtTask) GetItemByUIDToUserID(uid, toUserID int64) *UserExtTask {
	var table UserExtTask
	ok, err := usercenter.GetEngine().Where("uid = ?", uid).
		Where("to_user_id = ?", toUserID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
