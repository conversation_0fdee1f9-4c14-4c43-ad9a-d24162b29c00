package challenge

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type PracticeHistory struct {
	ID         int64  `xorm:"not null autoincr pk int(11) 'id'"`
	UID        int64  `xorm:"not null int(11) 'uid'"`
	UserID     int64  `xorm:"not null int(11) 'self_control_fund_to_user_id'"`
	LogType    int32  `xorm:"not null int(11) 'user_action_log_type'"`
	LogID      int64  `xorm:"not null int(11) 'user_action_log_id'"`
	ObjID      int64  `xorm:"not null int(11) 'obj_id'"`
	ObjExtra   string `xorm:"not null varchar(64) 'obj_extra'"`
	DateIndex  int64  `xorm:"not null int(10) 'date_index'"`
	CreateTime int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64  `xorm:"not null int(11) 'update_time'"`
}

// TableName 返回表名
func (PracticeHistory) TableName() string {
	return "self_control_fund_to_user_practice_history"
}

// SaveAsNew 添加数据，非事务
func (e *PracticeHistory) SaveAsNew() error {
	e.UpdateTime = time.Now().Unix()

	_, err := usercenter.GetEngineMaster().Insert(e)
	return err
}

type practiceHistory struct{}

var TbPracticeHistory practiceHistory

// GetHistory 根据用户挑战赛id和日期
func (e practiceHistory) GetHistory(toUserID, dateIndex int64) *PracticeHistory {
	var table PracticeHistory
	ok, err := usercenter.GetEngine().Where("self_control_fund_to_user_id = ?", toUserID).
		And("date_index = ?", dateIndex).
		Get(&table)
	if err != nil {
		logger.Warn(err)
		return nil
	}

	if !ok {
		return nil
	}

	return &table
}

func (e practiceHistory) GetAllHistoryCreateTime(toUserID int64) []int64 {
	var tables []PracticeHistory

	if err := usercenter.GetEngine().Where("self_control_fund_to_user_id = ?", toUserID).Find(&tables); err != nil {
		logger.Error(err)
		return nil
	}

	timeList := make([]int64, 0)
	dateIndexInfo := make(map[int64]PracticeHistory)
	for _, v := range tables {
		if _, ok := dateIndexInfo[v.DateIndex]; ok {
			continue
		}
		dateIndexInfo[v.DateIndex] = v
		timeList = append(timeList, v.CreateTime)
	}
	return timeList
}

func (e practiceHistory) GetHistoryCount(toUserID int64) int64 {
	var table PracticeHistory

	cnt, err := usercenter.GetEngineMaster().Where("self_control_fund_to_user_id  = ?", toUserID).Count(&table)
	if err != nil {
		logger.Warn(err)
		return 0
	}
	return cnt
}
