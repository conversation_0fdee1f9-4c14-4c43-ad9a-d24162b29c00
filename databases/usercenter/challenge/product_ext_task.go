package challenge

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type ProductExtTask struct {
	ID           int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	Title        string `xorm:"not null VARCHAR(32) 'title'" json:"title"`
	PopImg       string `xorm:"not null VARCHAR(128) 'pop_img'" json:"pop_img"`
	ButtonImg    string `xorm:"not null VARCHAR(128) 'button_img'" json:"button_img"`
	SuccessDays  string `xorm:"not null INT(11) 'success_days'" json:"success_days"`
	ExtDays      int64  `xorm:"not null INT(11) 'ext_days'" json:"ext_days"`
	PassDays     int32  `xorm:"not null INT(11) 'pass_days'" json:"pass_days"`
	GiftID       int64  `xorm:"not null INT(11) 'gift_id'" json:"gift_id"`
	ChallengeIDs string `xorm:"not null VARCHAR(256) 'challenge_ids'" json:"challenge_ids"`
	SendTime     int8   `xorm:"not null TINYINT(2) 'send_time'" json:"send_time"` // 发放时机：1点击升级 2挑战成功
	RuleExplain  string `xorm:"not null TEXT 'rule_explain'" json:"rule_explain"`
	IsDelete     int8   `xorm:"not null TINYINT(1) 'is_delete'" json:"is_delete"`
	CreateTime   int64  `xorm:"not null INT(11) 'create_time'" json:"create_time"`
	UpdateTime   int64  `xorm:"not null INT(11) 'update_time'" json:"update_time"`
}

func (ProductExtTask) TableName() string {
	return "self_control_fund_product_ext_task"
}

type productExtTask struct{}

// TbProductExtTask 外部调用对象
var TbProductExtTask productExtTask

// GetItemByID 根据id获取附加任务信息
func (p *productExtTask) GetItemByID(id int64) *ProductExtTask {
	var table ProductExtTask
	ok, err := usercenter.GetEngine().
		Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (p *productExtTask) GetValidByChallengeID(challengeID int64) *ProductExtTask {
	var table ProductExtTask
	ok, err := usercenter.GetEngine().
		Where("FIND_IN_SET(?,challenge_ids)", challengeID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
