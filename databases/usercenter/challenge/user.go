package challenge

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type User struct {
	ID              int64  `xorm:"not null autoincr pk int(11) 'id'"`
	UID             int64  `xorm:"not null int(11) 'uid'"`
	ProductID       int64  `xorm:"not null int(11) 'self_control_fund_id'"`
	Status          int32  `xorm:"not null int(11) 'self_control_fund_status'"`
	OrderID         string `xorm:"not null varchar(128) 'order_id'"`
	RefundOrderID   string `xorm:"not null varchar(128) 'refund_order_id'"`
	StartTime       int64  `xorm:"not null int(11) 'start_time'"`
	EndTime         int64  `xorm:"not null int(11) 'end_time'"`
	Days            int32  `xorm:"not null int(11) 'days'"`
	PassDays        int32  `xorm:"not null int(11) 'pass_days'"`
	CreateTime      int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime      int64  `xorm:"not null int(11) 'update_time'"`
	ProductType     int32  `xorm:"not null tinyint(2) 'product_type'"`
	HasFundTransfer int32  `xorm:"not null tinyint(2) 'has_fund_transfer'"`
	CourierNumber   string `xorm:"not null varchar(200) 'courier_number'"`
	ExpressServices string `xorm:"not null varchar(100) 'express_services'"`
	IsShipments     int32  `xorm:"not null tinyint(1) 'is_shipments'"`
}

func (User) TableName() string {
	return "self_control_fund_to_user"
}

// Update 更新
func (u *User) Update() error {
	u.UpdateTime = time.Now().Unix()

	_, err := usercenter.GetEngineMaster().ID(u.ID).Update(u)
	return err
}

// UpdateByTransaction 事务更新
func (u *User) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()

	_, err := session.ID(u.ID).Update(u)
	return err
}

type user struct{}

// TbUser 外部调用对象
var TbUser user

func (u *user) GetListByUserAndProduct(uid, productID int64) []User {
	var tables []User
	err := usercenter.GetEngine().Where("uid = ?", uid).Where("self_control_fund_id = ?", productID).Find(&tables)
	if err != nil {
		logger.Error(err)
	}
	return tables
}

func (u *user) GetJoinedUserList(productID int64) []User {
	var tables []User

	err := usercenter.GetEngine().
		Where("self_control_fund_id = ?", productID).
		And("self_control_fund_status = ?", library.ChallengeToUserStatusEnum.Underway).
		Find(&tables)
	if err != nil {
		logger.Error(err)
	}
	return tables
}

func (u *user) GetJoinedUserCount(productID int64) (int64, error) {
	var table User
	return usercenter.GetEngine().Where("self_control_fund_status != ?", library.ChallengeToUserStatusEnum.WaitPayment).
		Where("self_control_fund_id = ?", productID).Count(&table)
}

// GetListByUserAndStatusForApp 根据用户id和状态获取参加的挑战赛列表，仅限app使用，不找product_type为4的
// 2019-01-24 Sean 创建
func (u *user) GetListByUserAndStatusForApp(uid int64, status library.ChallengeToUserStatus) []*User {
	var tables []*User
	err := usercenter.GetEngine().Where("uid = ?", uid).
		Where("self_control_fund_status = ?", status.ToInt32()).
		And("product_type != ?", library.ChallengeTypeEnum.ShopChallenge).
		Find(&tables)
	if err != nil {
		logger.Error(err)
	}
	return tables
}

func (u *user) GetListByProduct(productID int64, status int32) []User {
	var tables []User

	err := usercenter.GetEngine().
		Where("self_control_fund_id = ?", productID).
		And("self_control_fund_status = ?", status).
		Asc("create_time").
		Find(&tables)
	if err != nil {
		logger.Error(err)
	}
	return tables
}

func (u *user) GetExpressNumber(productID int64) []User {
	var tables []User

	err := usercenter.GetEngine().Where("is_shipments = ?", 0).Where("self_control_fund_id = ?", productID).
		Where("self_control_fund_status != ?", library.ChallengeToUserStatusEnum.WaitPayment).
		Where("courier_number != ''").
		Where("express_services != ''").
		Find(&tables)
	if err != nil {
		logger.Error(err)
	}
	return tables
}

// IsChallengeOnGoing 判断任务当前是否进行中
func (u *User) IsChallengeOnGoing(dateTime time.Time) bool {
	timestamp := dateTime.Unix()
	if timestamp >= u.StartTime && timestamp <= u.EndTime {
		return true
	}
	return false
}

// GetRowByOrderID 根据orderID获取一行数据
func (u *user) GetRowByOrderID(orderID string) *User {
	var table User
	ok, err := usercenter.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// CheckJoinedNewUserChallenge 检查用户是否参加过新手挑战赛或者新手福利赛
func (u *user) CheckJoinedNewUserChallenge(uid int64) bool {
	var tables []User

	err := usercenter.GetEngine().
		Where("uid = ?", uid).
		Where("self_control_fund_status > ?", library.ChallengeToUserStatusEnum.WaitPayment).
		Where("product_type = ? or product_type = ?", library.SelfControlProductTypeEnum.NoviceChallenge, 6).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return false
	}
	if len(tables) == 0 {
		return false
	}
	return true
}

// HasJoinedUserChallenge 判断是否参加过相关类型的挑战赛
func (u *user) HasJoinedUserChallenge(uid int64, typeSlice ...int) []*User {
	var tables []*User
	err := usercenter.GetEngine().
		Where("uid = ?", uid).
		And("self_control_fund_status > ?", library.ChallengeToUserStatusEnum.WaitPayment).
		In("product_type", typeSlice).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return tables
	}
	return tables
}

type UserChallengeInfo struct {
	UID          int64 `xorm:"not null int(11) 'uid'"`
	ProductID    int64 `xorm:"not null int(11) 'self_control_fund_id'"`
	BusinessLine int32 `xorm:"not null default 1 TINYINT(3) 'business_line'"`
	Status       int32 `xorm:"not null int(11) 'self_control_fund_status'"`
}

func (u *user) HasJoinedUserChallengeList(uid int64, typeSlice ...int) []*UserChallengeInfo {
	var tables []*UserChallengeInfo
	err := usercenter.GetEngine().Table("self_control_fund_to_user").Alias("u").
		Select("p.business_line,u.self_control_fund_id").
		Join("LEFT", []string{"self_control_fund_product", "p"}, "u.self_control_fund_id = p.id").
		Where("u.uid = ?", uid).
		And("u.self_control_fund_status > ?", library.ChallengeToUserStatusEnum.WaitPayment).
		In("u.product_type", typeSlice).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// HasUserIngChallengeList 获取正在参加的挑战赛
func (u *user) HasUserIngChallengeList(uid int64) []*User {
	var tables []*User
	err := usercenter.GetEngine().
		Table("self_control_fund_to_user").
		Where("uid = ?", uid).
		And("self_control_fund_status = ?", library.ChallengeToUserStatusEnum.Underway).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
