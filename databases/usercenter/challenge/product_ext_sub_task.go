package challenge

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type ProductExtSubTask struct {
	ID         int64            `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	ExtTaskID  int64            `xorm:"not null INT(20) 'ext_task_id'" json:"ext_task_id"`
	PassDays   int64            `xorm:"not null INT(20) 'pass_days'" json:"pass_days"`
	TimeType   library.TimeType `xorm:"not null default 1 TINYINT(1) 'time_type'" json:"time_type"`
	IsDel      int32            `xorm:"not null default 2 TINYINT(1) 'is_del'" json:"is_del"`
	TaskInfo   string           `xorm:"not null TEXT 'task_info'" json:"task_info"`
	CreateTime int64            `xorm:"not null default 0 INT(11) 'create_time'" json:"create_time"`
	UpdateTime int64            `xorm:"not null default 0 INT(11) 'update_time'" json:"update_time"`
}

func (*ProductExtSubTask) TableName() string {
	return "self_control_fund_product_ext_sub_task"
}

// Save 添加数据，非事务
func (t *ProductExtSubTask) Save() error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime
	_, err := usercenter.GetEngineMaster().Insert(t)
	if err != nil {
		return err
	}
	return nil
}

type extSubTask struct{}

// TbExtSubTask 外部引用对象
var TbExtSubTask extSubTask

func (e *extSubTask) GetList(taskID int64) []ProductExtSubTask {
	var tables []ProductExtSubTask
	err := usercenter.GetEngine().
		Where("ext_task_id = ?", taskID).
		Asc("id").
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (e *extSubTask) GetItem(id int64) *ProductExtSubTask {
	var table ProductExtSubTask
	ok, err := usercenter.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
