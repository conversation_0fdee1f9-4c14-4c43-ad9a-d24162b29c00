package challenge

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type ProductUserSubTask struct {
	ID  int64 `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	UID int64 `xorm:"not null INT(11) 'uid'" json:"uid"`
	// self_control_fund_product 表 主键
	ProductID int64 `xorm:"not null bigint(20) 'self_control_fund_product_id'"`
	// self_control_fund_product_sub_task 表 主键
	SubTaskID int64 `xorm:"not null default 0 INT(11) 'sub_task_id'" json:"sub_task_id"`
	// self_control_fund_product_ext_sub_task 表 主键
	ExtSubTaskID int64 `xorm:"not null default 0 INT(11) 'ext_sub_task_id'" json:"ext_sub_task_id"`
	// 练习时间
	DateIndex int64 `xorm:"not null default 0 INT(11) 'date_index'" json:"date_index"`
	// 任务下标
	PracticeIndex int64 `xorm:"not null default 0 TINYINT(3) 'practice_index'" json:"practice_index"`
	// 练习时长
	PracticeTime int64 `xorm:"not null default 0 INT(11) 'practice_time'" json:"practice_time"`
	Status       int32 `xorm:"not null default 0 TINYINT(2) 'status'" json:"status"`
	CreateTime   int64 `xorm:"not null default 0 INT(11) 'create_time'" json:"create_time"`
	UpdateTime   int64 `xorm:"not null default 0 INT(11) 'update_time'" json:"update_time"`
}

func (*ProductUserSubTask) TableName() string {
	return "self_control_fund_product_user_sub_task"
}

// Save 添加数据，非事务
func (t *ProductUserSubTask) Save() error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime
	_, err := usercenter.GetEngineMaster().Insert(t)
	if err != nil {
		return err
	}
	return nil
}

func (t *ProductUserSubTask) Update() error {
	t.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().ID(t.ID).Cols("practice_time", "status", "update_time").Update(t)
	if err != nil {
		return err
	}
	return nil
}

type userSubTask struct{}

// TbUserSubTask 外部引用对象
var TbUserSubTask userSubTask

func (tb *userSubTask) GetList(taskID, productID, date int64) []*ProductUserSubTask {
	var tables []*ProductUserSubTask
	err := usercenter.GetEngine().
		Where("uid = ?", taskID).
		Where("self_control_fund_product_id = ?", productID).
		And("date_index = ?", date).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (tb *userSubTask) GetAllList(taskID, productID int64) []*ProductUserSubTask {
	var tables []*ProductUserSubTask
	err := usercenter.GetEngine().
		Where("uid = ?", taskID).
		Where("self_control_fund_product_id = ?", productID).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetItemByUIDTaskDate 获取用户指定日期任务完成信息
func (tb *userSubTask) GetItemByUIDTaskDate(uid, subTaskID, dateIndex int64) []*ProductUserSubTask {
	var tables []*ProductUserSubTask
	err := usercenter.GetEngine().Where("uid = ? ", uid).
		And("date_index = ?", dateIndex).
		And("sub_task_id = ?", subTaskID).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
