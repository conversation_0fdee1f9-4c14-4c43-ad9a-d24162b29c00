package challenge

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// TaskComplete 挑战赛任务完成表
type TaskComplete struct {
	ID            int64                        `xorm:"not null autoincr pk unsigned int(11) 'id'"`
	UID           int64                        `xorm:"not null int(11) 'uid'"`
	ProductID     int64                        `xorm:"not null int(11) 'self_control_fund_product_id'"`
	TaskID        int64                        `xorm:"null int(11) 'task_id'"`
	SubTaskID     int64                        `xorm:"null int(11) 'sub_task_id'"`
	ExtSubTaskID  int64                        `xorm:"null int(11) 'ext_sub_task_id'"`
	DateIndex     int64                        `xorm:"not null int(11) 'date_index'"`
	TimeIndex     int64                        `xorm:"not null int(11) 'time_index'"`
	PracticeTime  int64                        `xorm:"not null int(11) 'practice_time'"`
	CompleteIndex string                       `xorm:"not null varchar(512) 'complete_index'"`
	Status        library.ChallengeBoolLibrary `xorm:"not null tinyint(2) 'status'"`
	CreateTime    int64                        `xorm:"not null int(11) 'create_time'"`
	UpdateTime    int64                        `xorm:"not null int(11) 'update_time'"`
}

// TableName 表名
func (TaskComplete) TableName() string {
	return "self_control_fund_product_task_complete"
}

// SaveByTransaction 事务保存
func (t *TaskComplete) SaveByTransaction(session *xorm.Session) error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime

	_, err := session.Insert(t)
	return err
}

// SaveAsNew 添加数据，非事务
func (t *TaskComplete) SaveAsNew(redisKey string) error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime

	_, err := usercenter.GetEngineMaster().Insert(t)
	if err != nil {
		return err
	}

	jsonStr, err := json.Marshal(t)
	if err != nil {
		return err
	}
	logger.Debug(fmt.Sprintf("缓存数据, key:%s, value:%s", redisKey, string(jsonStr)))
	exp := 24 * time.Hour

	if err := cache.GetYogaRedis().Set(context.Background(), redisKey, string(jsonStr), exp).Err(); err != nil {
		logger.Error(err)
	}
	return nil
}

// UpdateDate 更新数据
func (t *TaskComplete) UpdateDate(redisKey string) error {
	t.UpdateTime = time.Now().Unix()

	_, err := usercenter.GetEngineMaster().ID(t.ID).Cols("practice_time", "status", "update_time").Update(t)
	if err != nil {
		return err
	}

	jsonStr, err := json.Marshal(t)
	if err != nil {
		return err
	}
	logger.Debug(fmt.Sprintf("缓存数据, key:%s, value:%s", redisKey, string(jsonStr)))
	exp := 24 * time.Hour

	if err := cache.GetYogaRedis().Set(context.Background(), redisKey, string(jsonStr), exp).Err(); err != nil {
		logger.Error(err)
	}
	return nil
}

type taskComplete struct{}

// TbTaskComplete 外部引用对象
var TbTaskComplete taskComplete

func (tb *taskComplete) GetList(uid, challengeID, dateIndex int64) []TaskComplete {
	var tables []TaskComplete

	err := usercenter.GetEngine().
		Where("self_control_fund_product_id = ?", challengeID).
		And("uid = ?", uid).
		And("date_index = ?", dateIndex).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetItemByUidTaskDate 获取用户指定日期任务完成信息
func (tb *taskComplete) GetItemByUIDTaskDate(uid, taskID, dateIndex int64) *TaskComplete {
	var table TaskComplete
	cacheKey := fmt.Sprintf("%s:%d:%d:%d", library.RedisKeyChallengeTaskComplete, uid, taskID, dateIndex)
	cacheValue, err := cache.GetYogaRedis().Get(context.Background(), cacheKey).Result()
	if err == redis.Nil {
		logger.Debugf("缓存不存在: %s", cacheKey)
	} else if err != nil {
		logger.Error(err)
	}
	if cacheValue != "" {
		err := json.Unmarshal([]byte(cacheValue), &table)
		if err != nil {
			logger.Warn(err)
		} else {
			return &table
		}
	}

	ok, err := usercenter.GetEngine().Where("uid = ?", uid).And("task_id = ?", taskID).And("date_index = ?", dateIndex).
		Get(&table)

	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByUid 获取用户指定日期任务完成信息
func (tb *taskComplete) GetItemByUID(uid, taskID int64) *TaskComplete {
	var table TaskComplete
	cacheKey := fmt.Sprintf("%s:%d:%d", library.RedisKeyChallengeTaskComplete, uid, taskID)
	cacheValue, err := cache.GetYogaRedis().Get(context.Background(), cacheKey).Result()
	if err == redis.Nil {
		logger.Debugf("缓存不存在: %s", cacheKey)
	} else if err != nil {
		logger.Error(err)
	}
	if cacheValue != "" {
		err := json.Unmarshal([]byte(cacheValue), &table)
		if err != nil {
			logger.Warn(err)
		} else {
			return &table
		}
	}

	ok, err := usercenter.GetEngine().Where("uid = ?", uid).And("task_id = ?", taskID).Get(&table)

	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
