package challenge

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type ProductSubTask struct {
	ID         int64            `xorm:"not null pk autoincr INT(20) 'id'" json:"id"`
	TaskID     int64            `xorm:"not null INT(20) 'task_id'" json:"task_id"`
	TimeType   library.TimeType `xorm:"not null default 1 TINYINT(1) 'time_type'" json:"time_type"`
	IsDel      int32            `xorm:"not null default 2 TINYINT(1) 'is_del'" json:"is_del"`
	TaskInfo   string           `xorm:"not null TEXT 'task_info'" json:"task_info"`
	CreateTime int64            `xorm:"not null default 0 INT(11) 'create_time'" json:"create_time"`
	UpdateTime int64            `xorm:"not null default 0 INT(11) 'update_time'" json:"update_time"`
}

func (*ProductSubTask) TableName() string {
	return "self_control_fund_product_sub_task"
}

type subTask struct{}

// TbSubTask 外部引用对象
var TbSubTask subTask

func (tb *subTask) GetList(taskID int64) []*ProductSubTask {
	var tables []*ProductSubTask
	err := usercenter.GetEngine().
		Where("task_id = ?", taskID).
		And("is_del = ?", library.DeleteStatusEnum.Valid).
		Asc("id").
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (tb *subTask) GetItem(id int64) *ProductSubTask {
	var table ProductSubTask
	ok, err := usercenter.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
