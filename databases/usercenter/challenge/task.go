package challenge

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// Task 挑战赛任务表
type Task struct {
	ID           int64  `xorm:"not null autoincr pk unsigned int(11) 'id'"`
	ProductID    int64  `xorm:"not null int(11) 'self_control_fund_product_id'"`
	TaskName     string `xorm:"not null varchar(64) 'task_name'"`
	TaskType     int32  `xorm:"not null tinyint(2) 'task_type'"`
	PracticeTime int64  `xorm:"not null int(11) 'practice_time'"`
	CompleteType int32  `xorm:"not null tinyint(2) 'complete_type'"`
	Status       int32  `xorm:"not null tinyint(2) 'status'"`
	BindType     int32  `xorm:"not null int(11) 'bind_type'"`
	BindID       int64  `xorm:"not null int(11) 'bind_id'"`
	CreateTime   int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime   int64  `xorm:"not null int(11) 'update_time'"`
}

// TableName 表名
func (Task) TableName() string {
	return "self_control_fund_product_task"
}

type task struct{}

// TbTask 外部引用对象
var TbTask task

func (tb *task) GetList(challengeID int64) []Task {
	var tables []Task

	err := usercenter.GetEngine().
		Where("self_control_fund_product_id = ?", challengeID).
		And("status = ?", library.ChallengeBoolLibraryEnum.Yes).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (tb *task) GetItem(challengeID int64, taskType library.ChallengeTaskType) *Task {
	var table Task

	ok, err := usercenter.GetEngine().
		Where("self_control_fund_product_id = ?", challengeID).
		And("status = ?", library.ChallengeBoolLibraryEnum.Yes).
		And("task_type = ?", taskType.ToInt32()).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByCompleteType 根据完成类型获取任务信息
// 2019-01-22 Sean 创建
func (tb *task) GetItemByCompleteType(challengeID int64,
	taskType library.ChallengeTaskType, completeType library.ChallengeCompleteType) *Task {
	var table Task
	ok, err := usercenter.GetEngine().
		Where("self_control_fund_product_id = ?", challengeID).
		And("status = ?", library.ChallengeBoolLibraryEnum.Yes).
		And("task_type = ?", taskType.ToInt32()).
		And("complete_type = ?", completeType.ToInt32()).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByTaskType 根据任务类型获取数据
func (tb *task) GetItemByTaskType(challengeID int64,
	taskType library.ChallengeTaskType) *Task {
	var table Task
	ok, err := usercenter.GetEngine().
		Where("self_control_fund_product_id = ?", challengeID).
		And("status = ?", library.ChallengeBoolLibraryEnum.Yes).
		And("task_type = ?", taskType.ToInt32()).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
