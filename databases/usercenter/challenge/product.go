package challenge

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// Product 挑战赛商品配置
type Product struct {
	ID                    int64                        `xorm:"not null autoincr pk int(11) 'id'"`
	Name                  string                       `xorm:"not null varchar(128) 'name'"`
	BusinessLine          int32                        `xorm:"not null default 1 TINYINT(3) 'business_line'"`
	Description           string                       `xorm:"not null varchar(255) 'description'"`
	PhoneBanner           string                       `xorm:"not null varchar(128) 'phone_banner'"`
	H5Banner              string                       `xorm:"not null varchar(128) 'h5_banner'"`
	PriceType             int32                        `xorm:"not null tinyint(2) 'price_type'"`
	Price                 float32                      `xorm:"not null decimal(10,2) 'price'"`
	Days                  int32                        `xorm:"not null int(11) 'days'"`
	PassDays              int32                        `xorm:"not null int(11) 'pass_days'"`
	PassType              int32                        `xorm:"not null tinyint(3) 'pass_type'"`
	StartTime             int64                        `xorm:"not null int(11) 'start_time'"`
	EndTime               int64                        `xorm:"not null int(11) 'end_time'"`
	ModeType              int32                        `xorm:"not null tinyint(1) 'mode_type'"`
	CreateTime            int64                        `xorm:"not null int(11) 'create_time'"`
	UpdateTime            int64                        `xorm:"not null int(11) 'update_time'"`
	ProductType           int32                        `xorm:"not null tinyint(2) 'product_type'"`
	ConfSuccessMember     int32                        `xorm:"not null int(11) 'conf_success_member'"`
	ConfTotalMember       int32                        `xorm:"not null int(11) 'conf_total_member'"`
	MemberLevel           int32                        `xorm:"not null tinyint(2) 'member_level'"`
	MemberValue           int32                        `xorm:"not null int(11) 'member_value'"`
	ResourceType          int32                        `xorm:"not null tinyint(3) 'resource_type'"`
	ResourceID            int64                        `xorm:"not null int(11) 'resource_id'"`
	ShowStatus            int32                        `xorm:"not null tinyint(2) 'show_status'"`
	IsHiddenForAdmin      int32                        `xorm:"not null tinyint(1) 'is_hidden_for_admin'"`
	Image                 string                       `xorm:"not null varchar(128) 'image'"`
	ImageWithWord         string                       `xorm:"not null varchar(128) 'image_with_word'"`
	ImageBackground       string                       `xorm:"not null varchar(128) 'image_backgroud'"`
	RuleDesc              string                       `xorm:"not null text 'rule_desc'"`
	AndroidVersionMin     string                       `xorm:"not null varchar(16) 'android_version_min'"`
	AndroidVersionMax     string                       `xorm:"not null varchar(16) 'android_version_max'"`
	IOSVersionMin         string                       `xorm:"not null varchar(16) 'ios_version_min'"`
	IOSVersionMax         string                       `xorm:"not null varchar(16) 'ios_version_max'"`
	NeedDailyPracticeTime library.ChallengeBoolLibrary `xorm:"not null tinyint(3) 'need_daily_practice_time'"`
	DailyPracticeTime     int32                        `xorm:"not null int(10) 'daily_practice_time'"`
	ProductSubType        int32                        `xorm:"not null tinyint(2) 'product_sub_type'"`
	ImageLinkType         int32                        `xorm:"not null tinyint(3) 'image_link_type'"`
	ImageLinkValue        string                       `xorm:"not null varchar(255) 'image_link_value'"`
	MaxMembers            int32                        `xorm:"not null int(11) 'max_members'"`
	IsNeedAddress         int32                        `xorm:"not null tinyint(1) 'is_need_address'"`
	HuaweiChannelShow     int32                        `xorm:"not null tinyint(3) 'huawei_channel_show'"`
	FlowRule              string                       `xorm:"not null text 'flow_rule'"`
	HypertextRule         string                       `xorm:"not null text 'hypertext_rule'"`
}

// TableName 获取表名
func (Product) TableName() string {
	return "self_control_fund_product"
}

type product struct{}

// TbProduct 外部调用对象
var TbProduct product

func (p *product) GetItem(id int64) *Product {
	var table Product
	ok, err := usercenter.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (p *product) GetOrderProduct(orderID string) *Product {
	order := TbUser.GetRowByOrderID(orderID)
	if order == nil {
		return nil
	}
	return p.GetItem(order.ProductID)
}

func (p *product) GetShopChallengeItem() *Product {
	var table Product

	ok, err := usercenter.GetEngine().
		Where("product_type = ?", library.ChallengeTypeEnum.ShopChallenge).
		Desc("id").
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetFreeChallengeCountByIds 查询“支付环节”为“不需要”的挑战赛
func (p *product) GetFreeChallengeCountByIds(ids []int64) int64 {
	var table Product

	total, err := usercenter.GetEngine().
		Table(table.TableName()).
		Where("price_type = ?", 3).
		In("id", ids).
		Count(&Product{})
	if err != nil {
		logger.Error(err)
		return total
	}
	return total
}
