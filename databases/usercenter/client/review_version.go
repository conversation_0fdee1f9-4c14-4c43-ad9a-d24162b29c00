package client

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type IosReviewVersionConfig struct {
	// 自增ID
	ID int64 `xorm:"int(11) not null pk autoincr 'id'"`
	// 正式版本号
	Online string `xorm:"string null 'online'"`
	// 灰度版本号
	Gray string `xorm:"string null 'gray'"`
	// 创建时间
	CreateTime int64 `xorm:"int default null 'create_time'"`
	// 修改时间
	UpdateTime int64 `xorm:"int default null 'update_time'"`
}

type reviewVersion struct{}

var TbIosReviewVersionConfig reviewVersion

// @text 获取表名
func (IosReviewVersionConfig) TableName() string {
	return "ios_review_version_config"
}

// @text 获取脚本列表
func (t *reviewVersion) GetReviewVersionConfig() []IosReviewVersionConfig {
	var table []IosReviewVersionConfig
	err := usercenter.GetEngine().Find(&table)
	if err != nil {
		return make([]IosReviewVersionConfig, 0)
	}
	return table
}

func (t *reviewVersion) GetReviewVersionItem() *IosReviewVersionConfig {
	var table IosReviewVersionConfig
	ok, err := usercenter.GetEngine().Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
