package client

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type AdUIDChannel struct {
	ID         int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	UID        int64  `xorm:"not null default 0 BIGINT(20) 'uid'" json:"uid"`
	DeviceID   string `xorm:"not null default '' VARCHAR(128) 'device_id'" json:"device_id"`
	UtmSource  string `xorm:"not null default '' VARCHAR(128) 'utm_source'" json:"utm_source"`
	UtmMedium  string `xorm:"not null default '' VARCHAR(128) 'utm_medium'" json:"utm_medium"`
	IPSource   string `xorm:"not null default '' VARCHAR(128) 'ip_source'" json:"ip_source"`
	IPAreaCode string `xorm:"not null default '' VARCHAR(128) 'ip_areacode'" json:"ip_areacode"`
	IPAdCode   string `xorm:"not null default '' VARCHAR(128) 'ip_adcode'" json:"ip_adcode"`
	Version    string `xorm:"not null default '' VARCHAR(128) 'version'" json:"version"`
	CreateTime int64  `xorm:"not null INT(11) 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"not null INT(11) 'update_time'" json:"update_time"`
}

// TableName 获取表名
func (a *AdUIDChannel) TableName() string {
	return "ad_uid_channel"
}

// Save 保存数据
func (a *AdUIDChannel) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := usercenter.GetEngineMaster().Insert(a)
	return err
}

// Update 修改
func (a *AdUIDChannel) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().ID(a.ID).MustCols("utm_medium").Update(a)
	return err
}

// UpdateUtmMedia 修改
func (a *AdUIDChannel) UpdateUtmMedia() error {
	a.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().ID(a.ID).Cols("utm_medium").Update(a)
	return err
}

type adUIDChannel struct{}

// TbAdUIDChannel 外部调用对象
var TbAdUIDChannel adUIDChannel

func (a *adUIDChannel) GetItemByDeviceID(deviceID string) *AdUIDChannel {
	var t AdUIDChannel
	ok, err := usercenter.GetEngine().Where("device_id = ?", deviceID).Get(&t)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &t
}

func (a *adUIDChannel) GetItemByUID(uid int64) *AdUIDChannel {
	var t AdUIDChannel
	ok, err := usercenter.GetEngine().Where("uid = ?", uid).Get(&t)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &t
}

// GetByUIDList 通过UID获取第三方绑定信息
func (a *adUIDChannel) GetByUIDList(uidList []int64) []*AdUIDChannel {
	var tables []*AdUIDChannel
	err := usercenter.GetEngine().In("uid", uidList).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
