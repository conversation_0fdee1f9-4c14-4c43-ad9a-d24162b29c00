package client

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type Client struct {
	ID         int64  `xorm:"int(10) not null pk autoincr 'id'" json:"id"`
	Key        string `xorm:"varchar(255) not null 'key'" json:"key"`
	KeyName    string `xorm:"varchar(255) not null 'key_name'" json:"key_name"`
	Value      string `xorm:"varchar(5000) not null 'value'" json:"value"`
	CreateTime int64  `xorm:"int(10) not null 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"int(10) not null 'update_time'" json:"update_time"`
}

// @text 获取表名
func (Client) TableName() string {
	return "base_client_config"
}

// Update 更新
func (c *Client) Update() error {
	c.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

type clientConfig struct {
}

var TbBaseClientConfig clientConfig

// @text 获取资源列表
func (c *clientConfig) GetItem(key string) *Client {
	var table Client
	ok, err := usercenter.GetEngine().
		Where("`key` = ?", key).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
