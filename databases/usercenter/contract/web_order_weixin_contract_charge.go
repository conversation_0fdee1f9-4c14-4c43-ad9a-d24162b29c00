// nolint
package contract

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type WebOrderWeixinContractCharge struct {
	ID           int    `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	OrderID      string `xorm:"VARCHAR(64) 'order_id'" json:"order_id"`
	UID          int64  `xorm:"not null INT(11) 'uid'" json:"uid"`
	ContractCode string `xorm:"default '' VARCHAR(64) 'contract_code'" json:"contract_code"`
	ProductID    int    `xorm:"not null default 0 INT(11) 'productid'" json:"productid"`
	Status       int    `xorm:"not null default 0 TINYINT(1) 'status'" json:"status"`
	ContractID   string `xorm:"not null default '' VARCHAR(32) 'contract_id'" json:"contract_id"`
	ChargeDate   string `xorm:"not null DATE 'charge_date'" json:"charge_date"`
	CreateTime   int64  `xorm:"not null default 0 INT(11) 'create_time'" json:"create_time"`
	UpdateTime   int64  `xorm:"default 0 INT(11) 'update_time'" json:"update_time"`
}

func (u *WebOrderWeixinContractCharge) TableName() string {
	return "web_order_weixin_contract_charge"
}
func (u *WebOrderWeixinContractCharge) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type _webOrderWexinContractCharge struct{}

var TbWebOrderWexinContractCharge _webOrderWexinContractCharge

func (t *_webOrderWexinContractCharge) GetChargeListAll(uid int64) []*WebOrderWeixinContractCharge {
	var tables []*WebOrderWeixinContractCharge
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
