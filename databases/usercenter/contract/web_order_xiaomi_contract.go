// nolint
package contract

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type WebOrderXiaomiContract struct {
	ID           int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	OutTradeNo   string `xorm:"not null VARCHAR(32) 'out_trade_no'" json:"out_trade_no"`
	UID          int64  `xorm:"not null INT(11) 'uid'" json:"uid"`
	MUID         string `xorm:"not null CHAR(18) 'm_uid'" json:"m_uid"`
	ChangeType   int    `xorm:"default 0 TINYINT(1) 'change_type'" json:"change_type"`
	ProductID    int    `xorm:"not null INT(5) 'product_id'" json:"product_id"`
	MProductCode string `xorm:"not null VARCHAR(64) 'm_product_code'" json:"m_product_code"`
	OrderIndex   int    `xorm:"not null default 0 INT(11) 'order_index'" json:"order_index"`
	CreateTime   int64  `xorm:"not null INT(11) 'create_time'" json:"create_time"`
	UpdateTime   int64  `xorm:"not null INT(11) 'update_time'" json:"update_time"`
}

func (u *WebOrderXiaomiContract) TableName() string {
	return "web_order_xiaomi_contract"
}
func (u *WebOrderXiaomiContract) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type _webOrderXiaomiContract struct{}

var TbWebOrderXiaomiContract _webOrderXiaomiContract

func (t *_webOrderXiaomiContract) GetOrderListAll(uid int64) []*WebOrderXiaomiContract {
	var tables []*WebOrderXiaomiContract
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
