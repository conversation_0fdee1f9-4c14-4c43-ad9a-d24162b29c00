// nolint
package contract

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type WebOrderNewHuaweiContractCharge struct {
	ID         int    `xorm:"not null pk autoincr INT(10) 'id'" json:"id"`
	OrderID    string `xorm:"not null default '' VARCHAR(64) 'order_id'" json:"order_id"`
	UID        int64  `xorm:"not null default 0 INT(10) 'uid'" json:"uid"`
	ProductID  int    `xorm:"not null default 0 INT(10) 'product_id'" json:"product_id"`
	Status     int    `xorm:"not null default 0 TINYINT(1) 'status'" json:"status"`
	ChargeDate string `xorm:"not null DATE 'charge_date'" json:"charge_date"`
	ContractID string `xorm:"not null default '' VARCHAR(100) 'contract_id'" json:"contract_id"`
	CreateTime int64  `xorm:"not null default 0 INT(10) 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"not null default 0 INT(10) 'update_time'" json:"update_time"`
}

func (u *WebOrderNewHuaweiContractCharge) TableName() string {
	return "web_order_new_huawei_contract_charge"
}
func (u *WebOrderNewHuaweiContractCharge) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type _webOrderNHuaweiContractCharge struct{}

var TbWebOrderNHuaweiContractCharge _webOrderNHuaweiContractCharge

func (t *_webOrderNHuaweiContractCharge) GetChargeListAll(uid int64) []*WebOrderNewHuaweiContractCharge {
	var tables []*WebOrderNewHuaweiContractCharge
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
