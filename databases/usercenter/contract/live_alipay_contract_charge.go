// nolint
package contract

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type LiveAlipayContractCharge struct {
	ID           int    `xorm:"not null pk autoincr INT(10) 'id'" json:"id"`
	OrderID      string `xorm:"not null default '' VARCHAR(64) 'order_id'" json:"order_id"`
	UID          int64  `xorm:"not null default 0 INT(10) 'uid'" json:"uid"`
	ContractCode string `xorm:"not null default '' VARCHAR(50) 'contract_code'" json:"contract_code"`
	Status       int    `xorm:"not null default 0 TINYINT(1) 'status'" json:"status"`
	ChargeDate   string `xorm:"not null default '' VARCHAR(50) 'charge_date'" json:"charge_date"`
	ContractID   string `xorm:"not null default '' VARCHAR(100) 'contract_id'" json:"contract_id"`
	CreateTime   int64  `xorm:"not null default 0 INT(10) 'create_time'" json:"create_time"`
	UpdateTime   int64  `xorm:"not null default 0 INT(10) 'update_time'" json:"update_time"`
}

func (u *LiveAlipayContractCharge) TableName() string {
	return "live_alipay_contract_charge"
}

func (u *LiveAlipayContractCharge) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type _liveOrderAlipayContractCharge struct{}

var TbLiveOrderAlipayContractCharge _liveOrderAlipayContractCharge

func (t *_liveOrderAlipayContractCharge) GetChargeListAll(uid int64) []*LiveAlipayContractCharge {
	var tables []*LiveAlipayContractCharge
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
