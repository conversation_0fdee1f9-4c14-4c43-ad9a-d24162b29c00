package contract

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type WebOrderHuaweiContract struct {
	ID           int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	OrderID      string `xorm:"VARCHAR(64) 'order_id'" json:"order_id"`
	ContractCode string `xorm:"default '' VARCHAR(64) 'contract_code'" json:"contract_code"`
	UID          int64  `xorm:"not null INT(11) 'uid'" json:"uid"`
	ChannelPay   string `xorm:"not null default '' VARCHAR(32) 'channel_pay'" json:"channel_pay"`
	MchID        string `xorm:"not null VARCHAR(64) 'mch_id'" json:"mch_id"`
	AppID        string `xorm:"not null VARCHAR(64) 'app_id'" json:"app_id"`
	ChangeType   int    `xorm:"default 0 TINYINT(1) 'change_type'" json:"change_type"`
	ContractID   string `xorm:"not null default '' VARCHAR(32) 'contract_id'" json:"contract_id"`
	CreateTime   int64  `xorm:"not null default 0 INT(11) 'create_time'" json:"create_time"`
	UpdateTime   int64  `xorm:"default 0 INT(11) 'update_time'" json:"update_time"`
	ProductID    int    `xorm:"not null default 0 INT(11) 'productid'" json:"productid"`
	OrderIndex   int64  `xorm:"not null default 0 INT(11) 'order_index'" json:"order_index"`
}

func (u *WebOrderHuaweiContract) TableName() string {
	return "web_order_huawei_contract"
}
func (u *WebOrderHuaweiContract) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type _webOrderHuaweiContract struct{}

var TbWebOrderHuaweiContract _webOrderHuaweiContract

func (t *_webOrderHuaweiContract) GetOrderListAll(uid int64) []*WebOrderHuaweiContract {
	var tables []*WebOrderHuaweiContract
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
