// nolint
package contract

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type PreferentialOrderNewHuaweiContractCharge struct {
	ID         int    `xorm:"not null pk autoincr INT(10) 'id'" json:"id"`
	OrderID    string `xorm:"not null default '' VARCHAR(64) 'order_id'" json:"order_id"`
	UID        int64  `xorm:"not null default 0 INT(10) 'uid'" json:"uid"`
	ProductID  int    `xorm:"not null default 0 INT(10) 'product_id'" json:"product_id"`
	Status     int    `xorm:"not null default 0 TINYINT(1) 'status'" json:"status"`
	ContractID string `xorm:"not null default '' VARCHAR(100) 'contract_id'" json:"contract_id"`
	CreateTime int64  `xorm:"not null default 0 INT(10) 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"not null default 0 INT(10) 'update_time'" json:"update_time"`
}

func (p *PreferentialOrderNewHuaweiContractCharge) TableName() string {
	return "preferential_order_new_huawei_contract_charge"
}
func (p *PreferentialOrderNewHuaweiContractCharge) UpdateByTransaction(session *xorm.Session) error {
	p.UpdateTime = time.Now().Unix()
	_, err := session.ID(p.ID).Update(p)
	return err
}

type _preferOrderNHuaweiContractCharge struct{}

var TbPreferentialOrderNHuaweiContractCharge _preferOrderNHuaweiContractCharge

func (t *_preferOrderNHuaweiContractCharge) GetChargeListAll(uid int64) []*PreferentialOrderNewHuaweiContractCharge {
	var tables []*PreferentialOrderNewHuaweiContractCharge
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
