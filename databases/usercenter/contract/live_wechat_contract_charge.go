// nolint
package contract

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type LiveWechatContractCharge struct {
	ID           int    `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	OrderID      string `xorm:"default '' VARCHAR(64) 'order_id'" json:"order_id"`
	UID          int64  `xorm:"not null default 0 INT(11) 'uid'" json:"uid"`
	ContractCode string `xorm:"default '' VARCHAR(64) 'contract_code'" json:"contract_code"`
	ProductID    int    `xorm:"not null default 0 INT(11) 'product_id'" json:"product_id"`
	Status       int    `xorm:"not null default 0 TINYINT(1) 'status'" json:"status"`
	ContractID   string `xorm:"not null default '' VARCHAR(32) 'contract_id'" json:"contract_id"`
	ChargeDate   string `xorm:"not null VARCHAR(50) 'charge_date'" json:"charge_date"`
	CreateTime   int64  `xorm:"not null default 0 INT(11) 'create_time'" json:"create_time"`
	UpdateTime   int64  `xorm:"default 0 INT(11) 'update_time'" json:"update_time"`
}

func (u *LiveWechatContractCharge) TableName() string {
	return "live_wechat_contract_charge"
}
func (u *LiveWechatContractCharge) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type _liveOrderWechatContractCharge struct{}

var TbLiveOrderWechatContractCharge _liveOrderWechatContractCharge

func (t *_liveOrderWechatContractCharge) GetChargeListAll(uid int64) []*LiveWechatContractCharge {
	var tables []*LiveWechatContractCharge
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
