package contract

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type WebOrderAlipayContract struct {
	ID           int64  `xorm:"not null pk autoincr INT(10) 'id'" json:"id"`
	OrderID      string `xorm:"not null default '' VARCHAR(64) 'order_id'" json:"order_id"`
	UID          int64  `xorm:"not null default 0 INT(10) 'uid'" json:"uid"`
	ProductID    int    `xorm:"not null default 0 INT(10) 'product_id'" json:"product_id"`
	ChangeType   int    `xorm:"not null default 0 TINYINT(3) 'change_type'" json:"change_type"`
	ContractCode string `xorm:"not null default '' VARCHAR(50) 'contract_code'" json:"contract_code"`
	ContractID   string `xorm:"not null default '' VARCHAR(100) 'contract_id'" json:"contract_id"`
	PeriodType   string `xorm:"not null default '' VARCHAR(10) 'period_type'" json:"period_type"`
	Period       int    `xorm:"not null default 0 TINYINT(3) 'period'" json:"period"`
	ExecuteTime  string `xorm:"not null default '' VARCHAR(50) 'execute_time'" json:"execute_time"`
	CreateTime   int64  `xorm:"not null default 0 INT(10) 'create_time'" json:"create_time"`
	UpdateTime   int64  `xorm:"not null default 0 INT(10) 'update_time'" json:"update_time"`
	// 优惠类型：1-无 2-首购 3-试用 4-试用+首购
	OfferType int `xorm:"not null default 1 TINYINT(1) 'offer_type'" json:"offer_type"`
	// 首购优惠价
	OfferFirstBuyPrice float64 `xorm:"default 0.00 DECIMAL(10,2) 'offer_first_buy_price'" json:"offer_first_buy_price"`
	// 首购优惠周期
	OfferFirstBuyCycle int `xorm:"not null default 0 TINYINT(1) 'offer_first_buy_cycle'" json:"offer_first_buy_cycle"`
	// 试用价格
	OfferTrialPrice float64 `xorm:"default 0.00 DECIMAL(10,2) 'offer_trial_price'" json:"offer_trial_price"`
	// 试用天数
	OfferTrialDay int `xorm:"not null default 0 INT(5) 'offer_trial_day'" json:"offer_trial_day"`
}

func (u *WebOrderAlipayContract) TableName() string {
	return "web_order_alipay_contract"
}
func (u *WebOrderAlipayContract) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type _webOrderAlipayContract struct{}

var TbWebOrderAlipayContract _webOrderAlipayContract

func (t *_webOrderAlipayContract) GetOrderListAll(uid int64) []*WebOrderAlipayContract {
	var tables []*WebOrderAlipayContract
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*_webOrderAlipayContract) GetItemByContractCode(code string) *WebOrderAlipayContract {
	var table WebOrderAlipayContract
	ok, err := db.GetEngine().Where("contract_code = ?", code).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*_webOrderAlipayContract) GetItemByOrderID(orderID string) *WebOrderAlipayContract {
	var table WebOrderAlipayContract
	ok, err := db.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
