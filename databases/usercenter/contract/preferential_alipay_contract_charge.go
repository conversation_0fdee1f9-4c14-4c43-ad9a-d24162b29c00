// nolint
package contract

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type PreferentialAlipayContractCharge struct {
	ID           int    `xorm:"not null pk autoincr INT(10) 'id'" json:"id"`
	OrderID      string `xorm:"not null default '' VARCHAR(64) 'order_id'" json:"order_id"`
	UID          int64  `xorm:"not null default 0 INT(10) 'uid'" json:"uid"`
	ContractCode string `xorm:"not null default '' VARCHAR(50) 'contract_code'" json:"contract_code"`
	ProductID    int    `xorm:"not null default 0 INT(10) 'product_id'" json:"product_id"`
	Status       int    `xorm:"not null default 0 TINYINT(1) 'status'" json:"status"`
	ChargeDate   string `xorm:"not null default '' VARCHAR(50) 'charge_date'" json:"charge_date"`
	ContractID   string `xorm:"not null default '' VARCHAR(100) 'contract_id'" json:"contract_id"`
	CreateTime   int64  `xorm:"not null default 0 INT(10) 'create_time'" json:"create_time"`
	UpdateTime   int64  `xorm:"not null default 0 INT(10) 'update_time'" json:"update_time"`
	// 阶梯金额
	StepAmount float64 `xorm:"not null decimal(10,2) 'step_amount'"`
}

func (u *PreferentialAlipayContractCharge) TableName() string {
	return "preferential_alipay_contract_charge"
}
func (u *PreferentialAlipayContractCharge) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type _preferOrderAlipayContractCharge struct{}

var TbPreferentialOrderAlipayContractCharge _preferOrderAlipayContractCharge

func (t *_preferOrderAlipayContractCharge) GetChargeListAll(uid int64) []*PreferentialAlipayContractCharge {
	var tables []*PreferentialAlipayContractCharge
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// 根据订单号
func (t *_preferOrderAlipayContractCharge) GetSuccessOrderByOrderID(orderID string) *PreferentialAlipayContractCharge {
	var table PreferentialAlipayContractCharge
	ok, err := db.GetEngine().Where("order_id=?", orderID).Where("status=?", library.Yes).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// 根据ContractCode 获取支付成功的订单列表
func (t *_preferOrderAlipayContractCharge) GetSuccessOrderByContractCode(contractCode string) []*PreferentialAlipayContractCharge {
	var tables []*PreferentialAlipayContractCharge
	err := db.GetEngine().Where("contract_code=?", contractCode).Where("status=?", library.Yes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetValidChargeByCode 根据签约号获取所有的扣款记录
func (*_preferOrderAlipayContractCharge) GetValidChargeByCode(contractCode string, maxID int64) []*PreferentialAlipayContractCharge {
	tables := make([]*PreferentialAlipayContractCharge, 0)
	err := db.GetEngine().Where("contract_code = ? and status = ? and id < ?",
		contractCode, library.Yes, maxID).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
