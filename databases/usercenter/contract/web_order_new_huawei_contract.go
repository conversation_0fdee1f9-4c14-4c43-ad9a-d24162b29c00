// nolint
package contract

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type WebOrderNewHuaweiContract struct {
	ID              int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	OrderID         string `xorm:"VARCHAR(64) 'order_id'" json:"order_id"`
	UID             int64  `xorm:"not null INT(11) 'uid'" json:"uid"`
	MchID           string `xorm:"not null VARCHAR(64) 'mch_id'" json:"mch_id"`
	ChangeType      int    `xorm:"default 0 TINYINT(1) 'change_type'" json:"change_type"`
	SignNo          string `xorm:"not null default '' VARCHAR(32) 'sign_no'" json:"sign_no"`
	HuaweiProductID string `xorm:"not null default '' VARCHAR(32) 'huawei_product_id'" json:"huawei_product_id"`
	CreateTime      int64  `xorm:"not null default 0 INT(11) 'create_time'" json:"create_time"`
	UpdateTime      int64  `xorm:"default 0 INT(11) 'update_time'" json:"update_time"`
}

func (u *WebOrderNewHuaweiContract) TableName() string {
	return "web_order_new_huawei_contract"
}
func (u *WebOrderNewHuaweiContract) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type _webOrderNewHuaweiContract struct{}

var TbWebOrderNewHuaweiContract _webOrderNewHuaweiContract

func (t *_webOrderNewHuaweiContract) GetOrderListAll(uid int64) []*WebOrderNewHuaweiContract {
	var tables []*WebOrderNewHuaweiContract
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
