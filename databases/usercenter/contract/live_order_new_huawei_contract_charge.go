// nolint
package contract

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type LiveOrderNewHuaweiContractCharge struct {
	ID         int    `xorm:"not null pk autoincr INT(10) 'id'" json:"id"`
	OrderID    string `xorm:"not null default '' VARCHAR(64) 'order_id'" json:"order_id"`
	UID        int64  `xorm:"not null default 0 INT(10) 'uid'" json:"uid"`
	Status     int    `xorm:"not null default 0 TINYINT(1) 'status'" json:"status"`
	ChargeDate string `xorm:"not null DATE 'charge_date'" json:"charge_date"`
	ContractID string `xorm:"not null default '' VARCHAR(100) 'contract_id'" json:"contract_id"`
	CreateTime int64  `xorm:"not null default 0 INT(10) 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"not null default 0 INT(10) 'update_time'" json:"update_time"`
}

func (l *LiveOrderNewHuaweiContractCharge) TableName() string {
	return "live_order_new_huawei_contract_charge"
}

func (l *LiveOrderNewHuaweiContractCharge) UpdateByTransaction(session *xorm.Session) error {
	l.UpdateTime = time.Now().Unix()
	_, err := session.ID(l.ID).Update(l)
	return err
}

type _liveOrderNHuaweiContractCharge struct{}

var TbLiveOrderNHuaweiContractCharge _liveOrderNHuaweiContractCharge

func (t *_liveOrderNHuaweiContractCharge) GetChargeListAll(uid int64) []*LiveOrderNewHuaweiContractCharge {
	var tables []*LiveOrderNewHuaweiContractCharge
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
