package yogacamp

import "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"

// SkuRelation ...
type SkuRelationValue struct {
	SkuRelationID int64 `xorm:"int(11) 'yoga_camp_session_sku_relation_id'" json:"yoga_camp_session_sku_relation_id"`
	SkuID         int64 `xorm:"int(11) 'sku_id'" json:"sku_id"`
}

type skuRelationValue struct{}

var TbSkuRelationValue skuRelationValue

// TableName Get table name
func (t *SkuRelationValue) TableName() string {
	return "yoga_camp_session_sku_relation_value"
}

// GetOneBySkuId
// 根据sku_id获取对应关系id
func (t *skuRelationValue) GetOneBySkuID(skuID int64) ([]SkuRelationValue, error) {
	var table []SkuRelationValue
	err := usercenter.GetEngine().Where("sku_id=?", skuID).
		Where("status = ?", 1).
		Cols("yoga_camp_session_sku_relation_id").
		Find(&table)
	return table, err
}
