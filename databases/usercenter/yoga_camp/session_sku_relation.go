package yogacamp

import "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"

type SkuRelation struct {
	ID         int64 `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	CategoryID int64 `xorm:"int(11) notnull pk autoincr 'category_id'" json:"category_id"`
}

type skuRelation struct{}

var TbSkuRelation skuRelation

// TableName Get table name
func (t *SkuRelation) TableName() string {
	return "yoga_camp_session_sku_relation"
}

// GetCategoryIdByRelationID 根据关联id获取分组id
func (t *skuRelation) GetCategoryIDByRelationID(relationID int64) ([]SkuRelation, error) {
	var table []SkuRelation
	err := usercenter.GetEngine().
		Where("id = ?", relationID).
		Cols("category_id").
		Find(&table)
	return table, err
}
