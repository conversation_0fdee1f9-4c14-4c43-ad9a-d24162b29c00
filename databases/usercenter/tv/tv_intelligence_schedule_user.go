package tv

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type IntelligenceScheduleUser struct {
	ID           int64 `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	UID          int64 `xorm:"not null default 0 INT(11) 'uid'" json:"uid"`
	Level        int32 `xorm:"not null default 1 TINYINT(1) 'level'" json:"level"`
	StartTime    int64 `xorm:"not null default 0 INT(11) 'start_time'" json:"start_time"`
	EndTime      int64 `xorm:"not null default 0 INT(11) 'end_time'" json:"end_time"`
	Status       int32 `xorm:"not null default 1 TINYINT(1) 'status'" json:"status"`
	DeleteStatus int32 `xorm:"not null default 2 TINYINT(1) 'delete_status'" json:"delete_status"`
	CreateTime   int64 `xorm:"not null default 0 INT(11) 'create_time'" json:"create_time"`
	UpdateTime   int64 `xorm:"not null default 0 INT(11) 'update_time'" json:"update_time"`
	CompleteTime int64 `xorm:"not null default 0 INT(11) 'complete_time'" json:"complete_time"`
}

// TableName 获取表名
func (IntelligenceScheduleUser) TableName() string {
	return "tv_intelligence_schedule_user"
}

// SaveByTransaction 添加
func (c *IntelligenceScheduleUser) SaveByTransaction(session *xorm.Session) error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime
	_, err := session.Insert(c)
	return err
}

// UpdateByTransaction 修改
func (c *IntelligenceScheduleUser) UpdateByTransaction(session *xorm.Session) error {
	c.UpdateTime = time.Now().Unix()
	_, err := session.ID(c.ID).Update(c)
	return err
}

func (c *IntelligenceScheduleUser) Update() error {
	c.UpdateTime = time.Now().Unix()
	_, err := db.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

type _tvIntelligenceScheduleUser struct {
}

var TbTvIntelligenceSUser _tvIntelligenceScheduleUser

// GetByID 根据ID获取数据
func (u *_tvIntelligenceScheduleUser) GetByID(id int64) *IntelligenceScheduleUser {
	var table IntelligenceScheduleUser
	ok, err := db.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetUserSchedule 获取用户智能课表
func (u *_tvIntelligenceScheduleUser) GetUserSchedule(uid int64, status int32) *IntelligenceScheduleUser {
	var table IntelligenceScheduleUser
	ok, err := db.GetEngine().Where("uid = ? AND delete_status = ?", uid, status).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
