package tv

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type IntelligenceScheduleUserSession struct {
	ID             int64     `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	UID            int64     `xorm:"not null default 0 INT(11) 'uid'" json:"uid"`
	ScheduleUserID int64     `xorm:"not null default 0 INT(11) 'schedule_user_id'" json:"schedule_user_id"`
	PracticeDate   time.Time `xorm:"not null default '0000-00-00' DATE 'practice_date'" json:"practice_date"`
	SessionID      int64     `xorm:"not null default 0 INT(11) 'session_id'" json:"session_id"`
	DayIndex       int32     `xorm:"not null default 0 TINYINT(1) 'day_index'" json:"day_index"`
	SessionIndex   int32     `xorm:"not null default 0 TINYINT(1) 'session_index'" json:"session_index"`
	IsDone         int32     `xorm:"not null default 0 TINYINT(3) 'is_done'" json:"is_done"`
	CreateTime     int64     `xorm:"not null default 0 INT(10) 'create_time'" json:"create_time"`
	UpdateTime     int64     `xorm:"not null default 0 INT(10) 'update_time'" json:"update_time"`
}

// TableName 获取表名
func (IntelligenceScheduleUserSession) TableName() string {
	return "tv_intelligence_schedule_user_session"
}

// SaveByTransaction 添加
func (c *IntelligenceScheduleUserSession) SaveByTransaction(session *xorm.Session) error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime
	_, err := session.Insert(c)
	return err
}

// UpdateByTransaction 修改
func (c *IntelligenceScheduleUserSession) UpdateByTransaction(session *xorm.Session) error {
	c.UpdateTime = time.Now().Unix()
	_, err := session.ID(c.ID).Update(c)
	return err
}

type _tvIntelligenceSUserSession struct {
}

var TbTvIntelligenceSUserSession _tvIntelligenceSUserSession

func (c *_tvIntelligenceSUserSession) GetByParams(userScheduleID int64,
	practiceDate string, sessionIndex int32) *IntelligenceScheduleUserSession {
	var table IntelligenceScheduleUserSession
	db.GetEngine().ShowSQL(true)
	ok, err := db.GetEngine().Where("schedule_user_id = ? and practice_date =? and session_index =?",
		userScheduleID, practiceDate, sessionIndex).Get(&table)
	db.GetEngine().ShowSQL(false)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetListByScheduleID 获取列表数据
func (c *_tvIntelligenceSUserSession) GetListByScheduleID(id int64) []*IntelligenceScheduleUserSession {
	var tables []*IntelligenceScheduleUserSession
	err := db.GetEngine().Where("schedule_user_id = ? ", id).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}

	return tables
}
