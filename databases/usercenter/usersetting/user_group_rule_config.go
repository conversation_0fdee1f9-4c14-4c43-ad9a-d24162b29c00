package usersetting

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// UserGroupRuleConfig
type UserGroupRuleConfig struct {
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 规则分群名称
	GroupName string `xorm:"not null default '' varchar(256) 'GroupName'"`
	// 分群规则json
	GroupRule string `xorm:"text 'group_rule'"`
	// 删除状态 1-已删除 2-未删除
	DeleteStatus int32 `xorm:"not null default '1' tinyint(2) unsigned 'delete_status'"`
	// 创建时间
	CreateTime int64 `xorm:"not null default '0' int(11) unsigned 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null default '0' int(11) unsigned 'update_time'"`
}

// TableName 获取表名
func (u UserGroupRuleConfig) TableName() string {
	return "user_group_rule_config"
}

type userGroupRuleConfig struct {
}

var TbUserGroupRuleConfig userGroupRuleConfig

// GetItem 获取对应的分群规则
func (u *userGroupRuleConfig) GetItem(groupIds []string) []UserGroupRuleConfig {
	var tables []UserGroupRuleConfig

	err := usercenter.GetEngine().In("id", groupIds).Cols("id", "group_rule").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
