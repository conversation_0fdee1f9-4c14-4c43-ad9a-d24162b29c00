package top

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// AdsOnVideo 视频前广告配置
type AdsOnVideo struct {
	// 自增ID
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 广告名称
	Name string `xorm:"not null varchar(64) 'name'"`
	// 跳转类型
	LinkType int32 `xorm:"not null tinyint(3) 'link_type'"`
	// 跳转内容
	LinkContent string `xorm:"not null varchar(128) 'link_content'"`
	// 广告类型：1-图片，2-视频，3-gif
	AdsType int32 `xorm:"tinyint(3) 'ads_type'"`
	// 内容
	AdsContent string `xorm:"varchar(255) 'ads_content'"`
	// 设备类型：1=>android, 2=>IOS,3=>TV
	DeviceInfo int32 `xorm:"tinyint(3) 'device_info'"`
	// 屏幕尺寸：1=>phone,2=>pad
	ScreenType int32 `xorm:"default '0' tinyint(3) 'screen_type'"`
	// 广告状态：0-正常投放，1-暂停投放
	Status int32 `xorm:"default '0' tinyint(3) 'status'"`
	// 排序(升序)
	Order int32 `xorm:"default '99999' int(11) 'order'"`
	// 投放方式:1-投放量,2-按时间
	PutInType int32 `xorm:"not null tinyint(1) 'put_in_type'"`
	// 投放量
	PutInNumber int32 `xorm:"default '0' int(10) 'put_in_number'"`
	// 开始投放时间
	BeginTime int64 `xorm:"default '0' int(11) 'begin_time'"`
	// 结束投放时间
	EndTime int64 `xorm:"default '0' int(11) 'end_time'"`
	// ios投放版本，最低值（包含）
	IosMinVersion string `xorm:"not null default '0' varchar(32) 'ios_min_version'"`
	// ios投放版本，最低值（包含）
	AndroidMinVersion string `xorm:"not null default '0' varchar(32) 'android_min_version'"`
	// 是否可以跳过:1-跳过,2-不跳
	IsSkip int32 `xorm:"not null default '1' tinyint(1) 'is_skip'"`
	// 跳过按钮出现前的时长（s）
	SkipBtnShowTime int32 `xorm:"not null default '0' tinyint(2) 'skip_btn_show_time'"`
	// 广告时长 (秒)
	Duration int32 `xorm:"not null int(10) 'duration'"`
	// 点击计数
	ClickCount int32 `xorm:"not null default '0' int(10) 'click_count'"`
	// 1顺序 2概率
	PollingType int32 `xorm:"not null default '1' tinyint(1) unsigned 'polling_type'"`
	// 概率
	Probability int32 `xorm:"not null default '0' tinyint(1) unsigned 'probability'"`
	// 视频内容版本号
	Vc int32 `xorm:"not null default '1' int(11) 'vc'"`
	// 创建时间
	CreateTime int64 `xorm:"int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"int(11) 'update_time'"`
}

func (t *AdsOnVideo) IncrClickCnt() error {
	_, err := db.GetEngineMaster().ID(t.ID).Incr("click_count", 1).Update(t)
	return err
}

type adsOnVideo struct{}

var TbAdsOnVideo adsOnVideo

// TableName 获取表名
func (AdsOnVideo) TableName() string {
	return "ads_on_video"
}

func (t *adsOnVideo) GetTopItem(id int64) *AdsOnVideo {
	var table AdsOnVideo

	ok, err := db.GetEngine().Where("id = ?", id).Get(&table)
	if !ok {
		return nil
	}
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &table
}
