package top

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// Cooperation
type Cooperation struct {
	ID int64 `xorm:"not null autoincr pk bigint(20) 'id'"`
	// idfa——ios设备码
	Idfa string `xorm:"char(36) 'idfa'"`
	// ip地址
	IP string `xorm:"varchar(16) 'ip'"`
	// 用户ID
	UserID int64 `xorm:"bigint(20) 'user_id'"`
	// 状态：1：完成，0：未完成
	Status int32 `xorm:"tinyint(3) 'status'"`
	// 创建时间
	CreateTime int64 `xorm:"int(11) 'create_time'"`
	// 修改时间
	UpdateTime int64 `xorm:"int(11) 'update_time'"`
	// 修改次数
	UpdateTimes int64 `xorm:"int(5) 'update_times'"`
	// 回调地址
	CallbackURL string `xorm:"varchar(255) 'callback_url'"`
	// 1：友钱
	CnType int32 `xorm:"tinyint(3) 'cn_type'"`
}

// TableName 获取表名
func (Cooperation) TableName() string {
	return "cooperation"
}

func (c *Cooperation) Update() error {
	currUnixTime := time.Now().Unix()
	c.UpdateTime = currUnixTime
	_, err := db.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

type cooperation struct{}

var TbCooperation cooperation

func (t *cooperation) GetCooperation(idfa string) *Cooperation {
	var table Cooperation

	ok, err := db.GetEngine().Where("idfa = ? AND status = 0", idfa).Get(&table)
	if !ok {
		return nil
	}
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &table
}
