package top

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// Top 公告板广告表
type Top struct {
	// 自增ID
	ID int64 `xorm:"not null autoincr pk int(11) 'Id'"`
	// 图片地址
	Image string `xorm:"varchar(255) 'Image'"`
	// iphonex图片地址，开屏中使用
	ImageIphonex string `xorm:"varchar(255) 'image_iphonex'"`
	// 内容
	Content string `xorm:"text 'Content'"`
	// 投放端：1=>APP，2-训练营小程序
	AppSource int32 `xorm:"not null default '1' tinyint(2) unsigned 'app_source'"`
	// 资源类型：1=>外部url, 2=>课程, 3=>计划, 4=>帖子详情页, 5=>音乐专辑, 6=>用户空间, 7=>打折,8=>抽奖活动
	SourceType int32 `xorm:"tinyint(3) 'SourceType'"`
	// 频道类型：1=>课程，2=>计划，3=>论坛-精选，4=>首页弹窗，5=>积分商城，6=>练习－全部，7=>开屏广告，8=>新课程首页
	PageType int32 `xorm:"tinyint(3) 'PageType'"`
	// 设备类型：1=>android, 2=>IOS,3=>TV
	DeviceInfo int32 `xorm:"tinyint(3) 'DeviceInfo'"`
	// 投放开关：1=>关闭，0，打开
	IsHidden int32 `xorm:"default '0' tinyint(3) 'IsHidden'"`
	// 排序
	Order int32 `xorm:"default '99999' int(11) 'Order'"`
	// 创建时间
	Created int32 `xorm:"int(11) 'Created'"`
	// 语言：EN=>英文，CN=>中文,TW=>台湾,JP=>日文,KO=>韩文,ES=>西语,CHS=>简体中文,DE=>德语',
	Lang string `xorm:"char(7) 'Lang'"`
	// 版本号：由基数1开始，++
	Vc int32 `xorm:"tinyint(3) 'Vc'"`
	// 屏幕尺寸：1=>phone,2=>pad
	ScreenType int32 `xorm:"default '0' tinyint(3) 'ScreenType'"`
	// 抽奖活动渠道id
	ChannelList string `xorm:"varchar(256) 'channelList'"`
	// 课程名称
	SessionName string `xorm:"varchar(255) 'session_name'"`
	// 课程描述
	SessionDesc string `xorm:"varchar(255) 'session_desc'"`
	// banner 开始时间
	BeginTime int64 `xorm:"default '0' int(11) 'begin_time'"`
	// banner 结束时间
	EndTime int64 `xorm:"default '0' int(11) 'end_time'"`
	// 首页弹窗内容
	IndexText string `xorm:"text 'index_text'"`
	// 标题
	IndexTitle string `xorm:"varchar(255) 'index_title'"`
	// 左按钮
	LeftButton string `xorm:"varchar(255) 'left_button'"`
	// 右按钮
	RightButton string `xorm:"varchar(255) 'right_button'"`
	// 是否需要登录
	NeedLogin int32 `xorm:"default '0' tinyint(3) 'need_login'"`
	// 图片——v3
	Images string `xorm:"varchar(255) 'images'"`
	// 创建时间
	CreateTime int64 `xorm:"int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"int(11) 'update_time'"`
	// 适用版本的最小值（包含）
	MinVersion string `xorm:"not null default '0' varchar(32) 'min_version'"`
	// 适用版本的最大值（包含）
	MaxVersion string `xorm:"not null default '999999' varchar(32) 'max_version'"`
	// 投放方式 1 投放量  2 按时间
	PutInType int32 `xorm:"not null tinyint(1) 'put_in_type'"`
	// 投放量
	PutInNumber int32 `xorm:"not null int(10) 'put_in_number'"`
	// 1跳过 2 不跳
	IsSkip int32 `xorm:"not null default '1' tinyint(1) 'is_skip'"`
	// 时长 (秒)
	Duration int32 `xorm:"not null int(10) 'duration'"`
	// 点击计数
	ClickCount int32 `xorm:"not null default '0' int(10) 'click_count'"`
	// 跳过按钮出现前的时长（s）
	SkipBtnShowTime int32 `xorm:"not null default '0' tinyint(2) 'skip_btn_show_time'"`
	// 画面类型;2-图片；3-视屏
	AdsType int32 `xorm:"not null default '2' tinyint(1) 'ads_type'"`
	// 1顺序 2概率
	PollingType int32 `xorm:"not null default '1' tinyint(1) unsigned 'polling_type'"`
	// 概率
	Probability int32 `xorm:"not null default '0' tinyint(1) unsigned 'probability'"`
	// 后台展示是否隐藏
	IsHiddenForAdmin int32 `xorm:"not null default '0' tinyint(1) 'is_hidden_for_admin'"`
	// 活动内容结束时间
	ContentEndTime int64 `xorm:"default '0' bigint(20) 'content_end_time'"`
	// 用户组的ID
	UserGroupID int64 `xorm:"not null default '0' int(11) unsigned 'user_group_id'"`
	// 上报神策:0-不上报，1-上报
	ReportSc int32 `xorm:"default '0' int(11) 'report_sc'"`
}

func (t *Top) IncrClickCnt() error {
	_, err := db.GetEngineMaster().ID(t.ID).Incr("click_count", 1).Update(t)
	return err
}

type topTop struct{}

var TbTop topTop

// TableName 获取表名
func (Top) TableName() string {
	return "top"
}

func (t *topTop) GetTopItem(id int64) *Top {
	var table Top

	ok, err := db.GetEngine().Where("id = ?", id).Get(&table)
	if !ok {
		return nil
	}
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &table
}
