package yogao2school

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type VoucherToUser struct {
	ID            int64   `xorm:"not null autoincr pk bigint(20) 'id'"`
	VoucherID     int64   `xorm:"not null bigint(20) 'voucher_id'"`
	UID           int64   `xorm:"not null bigint(20) 'uid'"`
	UseStartTime  int64   `xorm:"not null int(10) 'use_start_time'"`
	UseEndTime    int64   `xorm:"not null int(10) 'use_end_time'"`
	VoucherName   string  `xorm:"not null varchar(64) 'voucher_name'"`
	VoucherDesc   string  `xorm:"not null varchar(255) 'voucher_decription'"`
	Discount      float64 `xorm:"not null decimal(14,4) 'discount'"`
	Total         float64 `xorm:"not null decimal(14,4) 'total'"`
	UseType       int32   `xorm:"not null tinyint(2) 'use_type'"`
	ExpiryDay     int32   `xorm:"not null tinyint(2) 'expiry_day'"`
	StartTime     int64   `xorm:"not null int(10) 'start_time'"`
	EndTime       int64   `xorm:"not null int(10) 'end_time'"`
	VoucherStatus int32   `xorm:"not null tinyint(2) 'voucher_status'"`
	ResourceType  int32   `xorm:"not null tinyint(3) 'resource_type'"`
	ResourceValue int64   `xorm:"not null int(11) 'resource_value'"`
	ProductType   int32   `xorm:"not null tinyint(2) 'product_type'"`
	CreateTime    int64   `xorm:"not null int(11) 'create_time'"`
	UpdateTime    int64   `xorm:"not null int(11) 'update_time'"`
}

func (v *VoucherToUser) TableName() string {
	return "yoga_o2_voucher_to_user"
}

func (v *VoucherToUser) Save() error {
	v.CreateTime = time.Now().Unix()
	v.UpdateTime = v.CreateTime

	_, err := usercenter.GetEngineMaster().Insert(v)
	if err != nil {
		return err
	}
	return nil
}
