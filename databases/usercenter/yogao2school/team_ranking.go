package yogao2school

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// 班级成员排行榜
type TeamRanking struct {
	ID             int64  `xorm:"not null autoincr unsigned int(11) 'id'"`
	TeamID         int64  `xorm:"int(11) 'team_id'"`
	UID            int64  `xorm:"not null int(11) 'uid'"`
	Nickname       string `xorm:"not null varchar(64) 'nickName'"`
	WechatNickname string `xorm:"not null varchar(64) 'wx_nickname'"`
	Mobile         string `xorm:"not null char(11) 'mobile'"`
	TotalDuration  int64  `xorm:"not null int(11) default '0' 'practiceTimes'"`
	SessionNum     int64  `xorm:"not null tinyint(4) 'practiceSessionNum'"`
	CreateTime     int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime     int64  `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (tr *TeamRanking) TableName() string {
	return "yoga_o2_team_ranking"
}

// Save 保存数据
func (tr *TeamRanking) Save() error {
	tr.CreateTime = time.Now().Unix()
	tr.UpdateTime = tr.CreateTime

	_, err := usercenter.GetEngineMaster().Insert(tr)
	return err
}
