package yogao2school

import (
	"context"
	"strconv"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type Session struct {
	ID                  int64   `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	SessionName         string  `json:"session_name"`                                   // 课程名
	SessionDescription  string  `json:"session_description"`                            // 课程介绍
	Subtitle            string  `json:"subtitle"`                                       // 副标题
	Introduction        string  `json:"introduction"`                                   // 简介
	ImagePhone          string  `json:"image_phone"`                                    // 手机图片
	ImagePad            string  `json:"image_pad"`                                      // pad图片
	MaxMember           int     `json:"max_member"`                                     // 最大人数
	MinMember           int     `json:"min_member"`                                     // 最小人数
	SessionStartTime    int64   `json:"session_start_time"`                             // 开课时间
	SessionEndTime      int64   `json:"session_end_time"`                               // 节课时间
	EnrollStartTime     int64   `json:"enroll_start_time"`                              // 报名开始时间
	EnrollEndTime       int64   `json:"enroll_end_time"`                                // 报名结束时间
	ProvinceID          int     `xorm:"'province_id'" json:"province_id"`               // 省份编码
	RegionID            int     `xorm:"'region_id'" json:"region_id"`                   // 市区编码
	Address             string  `json:"address"`                                        // 地址
	SessionContentType  int     `json:"session_content_type"`                           // 课程类型（1-html页面/2-）
	SessionContent      string  `json:"session_content"`                                // 课程内容（1-url/2-）
	Price               float32 `json:"price"`                                          // 价格
	Status              int     `json:"status"`                                         // 状态
	SortOrder           int     `json:"sort_order"`                                     // 排序
	StatusEnroll        int     `json:"status_enroll"`                                  // 报名开关(1-开/0-关)
	CategoryID          int     `xorm:"'category_id'" json:"category_id"`               // 分类id
	CreateTime          int64   `json:"create_time"`                                    //
	UpdateTime          int64   `json:"update_time"`                                    //
	SourceType          int     `json:"source_type"`                                    // 资源ID
	SourceID            int     `xorm:"'source_id'" json:"source_id"`                   // 资源type
	EquipmentImagePhone string  `json:"equipment_image_phone"`                          // 课程所需装备图片链接，phone
	EquipmentImagePad   string  `json:"equipment_image_pad"`                            // 课程所需装备图片链接，pad
	EquipmentLinkURL    string  `xorm:"'equipment_link_url'" json:"equipment_link_url"` // 装备图片所指有赞链接
	O2CoachID           int64   `xorm:"'o2_coach_id'" json:"o2_coach_id"`               // 课程老师id
	O2DirectorID        int64   `xorm:"'o2_director_id'" json:"o2_director_id"`         // 班主任ID
	// 结伴延长解散类型：1:天,2:小时,3:月
	PartnerContinueType int `json:"partner_continue_type"`
	// 结伴延长解散时间，配合partner_continue_type使用
	PartnerContinueTime int    `json:"partner_continue_time"`
	QuestionLink        string `json:"question_link"`                // O2课程首次完成跳转问卷链接
	CouponID            int    `xorm:"'coupon_id'" json:"coupon_id"` //
	TeamMemberLimit     int    `json:"team_member_limit"`            //
	IsOnline            int    `json:"is_online"`                    // o2课程类型：1线上，2线下
	MaxPayPoints        int    `json:"max_pay_points"`               // 最大允许使用Yo币兑换金额，RMB
	// 瑜乐大学列表是否展示：1-展示,2-不展示
	DisplayStatus         int                 `json:"display_status"`
	ConfirmButton         string              `xorm:"'confirm_button'" json:"confirm_button"`
	SessionPeriodValidity int                 `xorm:"'session_period_validity'" json:"session_period_validity"`
	ChannelType           int                 `xorm:"default '1' TINYINT(3) 'channel_type'" json:"channel_type"`
	GiftID                int                 `xorm:"default '0' INT(10) 'gift_id'" json:"gift_id"`
	Periods               string              `xorm:"default '' VARCHAR(64) 'periods'" json:"periods"`
	SessionType           library.SessionType `xorm:"default '0' TINYINT(1) 'session_type'" json:"session_type"`
	// 新用户手机图片
	ImagePhoneForNewUser string `xorm:"'image_phone_for_newuser'" json:"image_phone_for_new_user"`
	// 新用户pad图片
	ImagePadForNewUser string `xorm:"'image_pad_for_newuser'" json:"image_pad_for_new_user"`
}

type session struct{}

var TbSession session

// TableName Get table name
func (t *Session) TableName() string {
	return "yoga_o2_session"
}

func (t *Session) getURLParameter(url, key string) string {
	_t := strings.Split(url, "?")
	if len(_t) < 2 {
		return ""
	}
	_t = strings.Split(_t[1], "&")
	for i := 0; i < len(_t); i++ {
		kv := strings.Split(_t[i], "=")
		if len(kv) != 2 {
			continue
		}
		if kv[0] == key {
			return kv[1]
		}
	}
	return ""
}

// GetWebInfo 获取课程对应的webinfo
// 如果课程当前没有配置对应的webinfo，从 SessionContent 里面取对应的 webinfo
// 2018-02-12 15:47:16 庞晓楠 创建
func (t *Session) GetWebInfo(ctx context.Context) *SessionWebInfo {
	webinfo := GetYogaO2SessionWebInfoBySessionID(ctx, t.ID)
	if webinfo != nil {
		return webinfo
	}

	var err error
	var id int64
	id, err = strconv.ParseInt(t.getURLParameter(t.SessionContent, "yoga_o2_session_id"), 10, 64)
	if id <= 0 {
		id, err = strconv.ParseInt(t.getURLParameter(t.SessionContent, "id"), 10, 64)
	}
	if id <= 0 || err != nil {
		return nil
	}

	return GetYogaO2SessionWebInfoBySessionID(ctx, id)
}

// GetLastRYTSession 获取最后一个RYT课程
// 先找上线的最后一个，如果没有上线的，找未上线的最后一个。
// 2017-12-13 18:17:36 庞晓楠 创建
// 2018-01-04 11:02:26 庞晓楠 修改is_online为status，字段判断错误。
func GetLastRYTSession(ctx context.Context) *Session {
	var table Session
	ok, err := usercenter.GetEngine().Where("session_type=?", library.SessionTypeEnum.OfflineRYT).
		Desc("session_start_time").Get(&table)
	if err != nil {
		logger.CError(ctx, err)
		return nil
	}
	if !ok {
		ok, err := usercenter.GetEngine().Where("session_type=? and status=? and session_start_time<?",
			library.SessionTypeEnum.OfflineRYT, 2, time.Now().Unix()).Desc("session_start_time").Get(&table)
		if err != nil {
			logger.CError(ctx, err)
			return nil
		}
		if !ok {
			return nil
		}
	}
	return &table
}

// 根据id数组返回结合 alex
// 2018.12.09 修改下排序规则
func (*session) GetSessionListByIds(ids []int32) []Session {
	var tables []Session
	err := usercenter.GetEngine().In("id", ids).
		OrderBy("sort_order ASC, session_start_time ASC").Where("status = ?", 1).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*session) GetSessionInfoByID(id int32) *Session {
	var table Session
	ok, err := usercenter.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// 返回app内训练营与线下教培
func (*session) GetEffectiveCourse(pageSize, offset int) []Session {
	var tables []Session
	currUnixTime := time.Now().Unix()
	session := usercenter.GetEngine().NewSession()
	defer session.Close()
	session = session.Table("yoga_o2_session").Alias("a").
		Where("enroll_start_time<=? and session_start_time>? and a.status=? and display_status=?",
			currUnixTime, currUnixTime, 1, 1).
		Asc("a.sort_order").Asc("a.session_start_time").Where("a.tag=?", 2)
	if pageSize > 0 {
		session = session.Limit(pageSize, offset)
	}
	err := session.Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetNextSessionByCategoryId
// 根据分类id 取即将开课的课
func (*session) GetNextSessionByCategoryID(categoryID int64) []Session {
	var table []Session
	now := time.Now().Unix()
	if err := usercenter.GetEngine().
		Where("category_id = ?", categoryID).
		Where("session_type = ?", 1).
		Where("session_start_time > ?", now).
		Find(&table); err != nil {
		logger.Error(err)
		return nil
	}
	return table
}

// 根据计划id获取训练营课程id
func (*session) GetSessionInfoBySourceID(sourceID int64) *Session {
	var table Session
	now := time.Now().Unix()
	ok, err := usercenter.GetEngine().
		Where("source_type = ?", library.O2SourceTypeProgram).
		Where("source_id = ?", sourceID).
		Where("status = ?", 1).
		Where("session_start_time >= ?", now).
		Where("enroll_start_time <= ?", now).
		Asc("session_start_time").
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
