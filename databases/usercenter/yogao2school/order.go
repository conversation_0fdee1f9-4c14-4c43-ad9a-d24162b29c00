package yogao2school

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// Order 订单
type Order struct {
	ID            int     `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`                 //
	UID           int     `xorm:"int(11) notnull 'uid'" json:"uid"`                           // 用户ID
	UserName      string  `xorm:"varchar(64) notnull 'user_name'" json:"user_name"`           // 用户名
	Mobile        string  `xorm:"varchar(32) notnull 'mobile'" json:"mobile"`                 // 用户手机号
	OrderID       string  `xorm:"varchar(128) notnull 'order_id'" json:"order_id"`            // 订单号
	PaymentCode   int     `xorm:"tinyint(2) notnull 'payment_code'" json:"payment_code"`      // 购买代码
	PaymentMethod string  `xorm:"varchar(16) notnull 'payment_method'" json:"payment_method"` // 购买方式
	Total         float64 `xorm:"decimal(14,4) notnull 'total'" json:"total"`                 // 价格
	// 订单状态：1=》代付款2=》已付款
	OrderStatusID int `xorm:"tinyint(2) notnull 'order_status_id'" json:"order_status_id"`
	Status        int `xorm:"tinyint(1) notnull 'status'" json:"status"`                   // 状态
	CreateTime    int `xorm:"int(10) notnull 'create_time'" json:"create_time"`            //
	UpdateTime    int `xorm:"int(10) notnull 'update_time'" json:"update_time"`            //
	OrderType     int `xorm:"tinyint(1) notnull default 1 'order_type'" json:"order_type"` // 1-虚拟商品/课程；2-
	RenewType     int `xorm:"tinyint(3) notnull 'renew_type'" json:"renew_type"`           // 1:续费；2开通
	// 来源类型：0.未上报来源，1.课程，2.计划，3.他人空间皇冠，
	// 4.个人界面皇冠（自己的），5.个人界面的主开通入口，6.音乐专辑，7.活动，8.其他
	SourceType int    `xorm:"int(5) notnull default 0 'source_type'" json:"source_type"`
	SourceID   int    `xorm:"int(11) notnull default 0 'source_id'" json:"source_id"`  // 来源对象id
	Version    string `xorm:"varchar(20) notnull default '' 'version'" json:"version"` // 版本号
	// 上次购买的课程id
	RecencyPurchaseO2Product int64 `xorm:"int(11) 'recency_purchase_o2_product'" json:"recency_purchase_o2_product"`
}

// TableName Get table name
func (t *Order) TableName() string {
	return "yoga_o2_order"
}

type order struct{}

var TbOrder order

// GetByOrderID 根据订单号查询
// 2018-08-16 11:56:40 庞晓楠 创建
func (order) GetByOrderID(orderID string) []Order {
	var tables []Order
	err := db.GetEngine().Where("order_id=?", orderID).Find(&tables)
	if err != nil {
		logger.Error(err, orderID)
		return nil
	}
	return tables
}

func GetO2OrderList(uid int64) []Order {
	var t []Order
	err := db.GetEngine().Where("uid=? AND order_status_id=2", uid).Find(&t)
	if err == nil {
		return t
	}

	logger.Error("get GetO2OrderList fail:", err)
	return nil
}

func (*order) CheckUserBuyVioRecord(uid int64) bool {
	// 获取用户的会员购买记录
	if orders := GetO2OrderList(uid); len(orders) == 0 {
		return false
	}
	return true
}

// GetCampList 获取指定时间支付成功的订单
func (o *order) GetCampList(status, startTime, endTime int64) []Order {
	var t []Order
	err := db.GetEngine().Where("order_status_id=? AND update_time>=? AND update_time<=?", status, startTime, endTime).
		Find(&t)
	if err == nil {
		return t
	}
	logger.Error("get GetO2OrderList fail:", err)
	return nil
}
