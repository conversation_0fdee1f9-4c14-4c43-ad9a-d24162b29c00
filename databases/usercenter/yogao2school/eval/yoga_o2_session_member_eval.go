package eval

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type YogaO2SessionMemberEval struct {
	ID         int64 `xorm:"not null pk autoincr int(10) 'id'" json:"id"`
	UID        int64 `xorm:"not null bigint(20) 'uid'"`
	Category   int64 `xorm:"not null int(11) 'category'" json:"category"`             // 分类id
	Level      int64 `xorm:"not null int(10) 'level'" json:"level"`                   // 等级
	SourceID   int64 `xorm:"'source_id'" json:"source_id"`                            // 资源type
	Status     int32 `xorm:"not null tinyint(1) default '1'  'status'" json:"status"` // 是否删除，1为正常，2为删除
	CreateTime int64 `json:"create_time"`
	UpdateTime int64 `json:"update_time"`
}

// TableName Get table name
func (s *YogaO2SessionMemberEval) TableName() string {
	return "yoga_o2_session_member_eval"
}

type yogaO2SessionMemberEval struct{}

var TbYogaO2SessionMemberEval yogaO2SessionMemberEval

// 2019-11-15 获取用户评测结果
func (s *yogaO2SessionMemberEval) GetByUIDCategory(uid, categoryID, status int64) *YogaO2SessionMemberEval {
	db.GetEngine().ShowSQL(true)
	var table YogaO2SessionMemberEval
	ok, err := db.GetEngine().Where("uid=? AND category=? AND status=?", uid, categoryID, status).Get(&table)
	if !ok {
		if err != nil {
			logger.Error(err)
		}
		return nil
	}
	db.GetEngine().ShowSQL(false)
	return &table
}
