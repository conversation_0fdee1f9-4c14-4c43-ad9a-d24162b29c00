package yogao2school

import (
	"context"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// SessionWebInfo 瑜乐学院课程说明动态页数据
type SessionWebInfo struct {
	ID              int64  `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`                     //
	YogaO2SessionID int64  `xorm:"int(11) notnull 'yoga_o2_session_id'" json:"yoga_o2_session_id"` //
	Title           string `xorm:"varchar(128) notnull 'title'" json:"title"`                      //
	Image           string `xorm:"varchar(128) notnull 'image'" json:"image"`                      //
	Keyword         string `xorm:"varchar(128) notnull 'keyword'" json:"keyword"`                  //
	KdtProduct      string `xorm:"varchar(128) notnull 'kdt_product'" json:"kdt_product"`          //
	Contitle        string `xorm:"varchar(128) notnull 'contitle'" json:"contitle"`                //
	DescriptionH5   string `xorm:"mediumtext notnull 'description_h5'" json:"description_h5"`      //
	WebsiteH5       string `json:"website_h5"`                                                     //
	CreateTime      int    `xorm:"int(11) notnull 'create_time'" json:"create_time"`               //
	UpdateTime      int    `xorm:"int(11) notnull 'update_time'" json:"update_time"`               //
}

// TableName Get table name
func (t *SessionWebInfo) TableName() string {
	return "yoga_o2_session_web_info"
}

// GetYogaO2SessionWebInfoBySessionID 根据session ID获取
// 2017-12-27 16:53:44 庞晓楠 修改 添加缓存
func GetYogaO2SessionWebInfoBySessionID(ctx context.Context, id int64) *SessionWebInfo {
	var table SessionWebInfo
	ok, err := usercenter.GetEngine().Where("yoga_o2_session_id=?", id).Get(&table)
	if err != nil {
		logger.CError(ctx, err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetLastRYTSessionWebInfo 获取最后一个RYT课程
// 先找上线的最后一个，如果没有上线的，找未上线的最后一个。
// 2017-12-13 18:17:36 庞晓楠 创建
// 2017-12-27 16:57:57 庞晓楠 修改
//   将获取课程的函数GetYogaO2SessionWebInfoBySessionID单独拉出去
// 2018-02-12 15:51:04 庞晓楠 修改
//   将获取对应课程的逻辑移入 Session::GetWebInfo 函数
func GetLastRYTSessionWebInfo(ctx context.Context) *SessionWebInfo {
	session := GetLastRYTSession(ctx)
	if session == nil {
		return nil
	}

	return session.GetWebInfo(ctx)
}
