package yogao2school

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/library"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type SessionMember struct {
	ID                    int64  `xorm:"'id'" json:"id"`
	SessionID             int32  `xorm:"'session_id'" json:"session_id"`
	SkuID                 int64  `xorm:"'sku_id'" json:"sku_id"`
	OrderID               string `xorm:"'order_id'" json:"order_id"`
	UID                   int64  `xorm:"'uid'"   json:"uid"`
	MemberName            string `xorm:"'member_name'" json:"member_name"`
	MemberMobile          string `xorm:"'member_mobile'" json:"member_mobile"`
	MemberWxNo            string `xorm:"'member_wx_no'"  json:"member_wx_no"`
	ProvinceID            int    `xorm:"'province_id'"   json:"province_id"`
	RegionID              int    `xorm:"'region_id'" json:"region_id"`
	AreaID                int    `xorm:"'area_id'"   json:"area_id"`
	SessionMemberStatusID int    `xorm:"'session_member_status_id'" json:"session_member_status_id"`
	UserReport            int    `xorm:"'user_report'" json:"user_report"`
	Status                int    `xorm:"'status'"   json:"status"`
	EnrollType            int    `xorm:"'enroll_type'" json:"enroll_type"`
	Address               string `xorm:"'address'"  json:"address"`
	CoachID               int64  `xorm:"'coach_id'" json:"coach_id"`
	RealSessionID         int64  `xorm:"'real_session_id'"  json:"real_session_id"`
	InviteUID             int64  `xorm:"'invite_uid'"   json:"invite_uid"`
	SessionType           int64  `xorm:"'session_type'" json:"session_type"`
	CourseType            int    `xorm:"'course_type'" json:"course_type"`
	PlannedCategoryID     int64  `xorm:"'planned_category_id'" json:"planned_category_id"`
	PlannedPeriods        string `xorm:"'planned_periods'" json:"planned_periods"`
	SessionLocationID     int64  `xorm:"'session_location_id'" json:"session_location_id"`
}

func (t *SessionMember) TableName() string {
	return "yoga_o2_session_member"
}

type sessionMember struct{}

var TbSessionMember sessionMember

func (t *sessionMember) GetMemberInfoByOrderID(orderID string) *SessionMember {
	var table SessionMember
	ok, err := usercenter.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (t *sessionMember) GetRowByUIDAndSessionID(uid, sessionID int64) *SessionMember {
	var table SessionMember

	ok, err := usercenter.GetEngine().Where("uid = ?", uid).Where("session_id = ?", sessionID).Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
func (t *sessionMember) GetItem(orderID string) *SessionMember {
	var table SessionMember
	ok, err := usercenter.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if !ok {
		return nil
	}
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &table
}

// GetCountByCondition 根据条件查询数量count
func (t *sessionMember) GetCountByCondition(sessionID int64, status []int32) int64 {
	var table SessionMember
	total, err := usercenter.GetEngine().Where("session_id = ?", sessionID).
		In("session_member_status_id", status).Count(&table)
	if err != nil {
		logger.Error(err)
		return 0
	}
	return total
}

// CheckCampIsValid
// 检查用户训练营是否生效中
func (t *sessionMember) CheckCampIsValid(uid int64) bool {
	var table []SessionMember
	if err := usercenter.GetEngine().Where("uid = ?", uid).
		Where("session_type = ?", 1).
		Where("status = ?", 1).
		Find(&table); err != nil {
		logger.Error(err)
		return false
	}
	if len(table) == 0 {
		return false
	}
	sessionIds := []int32{}
	for i := range table {
		sessionIds = append(sessionIds, table[i].SessionID)
	}
	session := TbSession.GetSessionListByIds(sessionIds)
	now := time.Now().Unix()
	for i := range session {
		// 判断有效期内外和yoga逻辑保持一致,取结课时间判断
		if session[i].SessionEndTime > now {
			return true
		}
	}
	return false
}

// 获取当前用户参与的训练营课程
func GetCampSessions(uid int64, statusArr, cateGoryIds []int32) []SessionMember {
	var tables []SessionMember
	err := usercenter.GetEngine().
		Table("yoga_o2_session_member").Alias("m").
		Join("LEFT", []string{"yoga_o2_session", "s"}, "m.session_id = s.id").
		Where("m.uid = ?", uid).
		Where("s.session_type = ?", 1). // session_type = 1 训练营
		In("m.session_member_status_id", statusArr).
		NotIn("s.category_id", cateGoryIds).
		Find(&tables)

	if err != nil {
		logger.Error(err)
		return nil
	}

	return tables
}

func (*sessionMember) CheckUserCampSessionsRecord(uid int64, statusArr, categoryIds []int32) bool {
	if list := GetCampSessions(uid, statusArr, categoryIds); len(list) == 0 {
		return false
	}
	return true
}

// HavePurchasedO2ByUID 用户是否有购买过训练营的订单
func (*sessionMember) HavePurchasedO2ByUID(uid int64) bool {
	item := new(SessionMember)
	ok, err := usercenter.GetEngine().Where("uid = ? and status = ?", uid, library.DataStatusEnum.Valid).
		In("session_member_status_id", []int{
			2, 3, 7, // 2-已参加 3-已上课 7-已改期
		}).Get(item)
	if err != nil {
		logger.Errorf("判断用户是否有购买过训练营的订单出错 %s", err)
		return false
	}
	return ok
}
