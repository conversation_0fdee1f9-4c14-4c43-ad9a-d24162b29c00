package yogao2school

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type VoucherSendRecord struct {
	ID          int64  `xorm:"not null autoincr pk bigint(20) 'id'"`
	VoucherID   int64  `xorm:"not null bigint(20) 'voucher_id'"`
	SendType    int32  `xorm:"not null tinyint(2) 'send_type'"`
	UIDs        string `xorm:"not null text 'uids'"`
	ProductType int32  `xorm:"not null tinyint(2) 'product_type'"`
	AlreadySent int32  `xorm:"not null int(11) 'already_sent'"`
	TotalCount  int64  `xorm:"not null int(11) 'total_count'"`
	IsBatch     int32  `xorm:"not null tinyint(4) 'is_batch'"`
	CreateTime  int64  `xorm:"not null int(10) 'create_time'"`
	UpdateTime  int64  `xorm:"not null int(10) 'update_time'"`
}

func (v *VoucherSendRecord) TableName() string {
	return "yoga_o2_voucher_send_record"
}

func (v *VoucherSendRecord) IncrAlreadySent(sentCnt int32) error {
	v.UpdateTime = time.Now().Unix()

	_, err := usercenter.GetEngineMaster().ID(v.ID).Incr("already_sent", sentCnt).Update(v)
	return err
}
