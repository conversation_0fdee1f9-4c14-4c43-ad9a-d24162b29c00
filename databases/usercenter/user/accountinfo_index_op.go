package user

import (
	"errors"
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type AccountInfoIndexOp struct {
	ID         int64 `xorm:"int(11) unsigned notnull pk autoincr 'id'" json:"id"`       //
	UID        int64 `xorm:"int(11) unsigned notnull 'uid'" json:"uid"`                 //
	Sort       int32 `xorm:"tinyint(2) unsigned notnull default 0 'sort'" json:"sort"`  // 排序
	CreateTime int64 `xorm:"int(11) unsigned notnull 'create_time'" json:"create_time"` //
	UpdateTime int64 `xorm:"int(11) unsigned notnull 'update_time'" json:"update_time"` //
}

// TableName Get table name
func (u *AccountInfoIndexOp) TableName() string {
	return "accountinfo_index_op"
}

// Save 保存
func (u *AccountInfoIndexOp) SaveByTransaction(session *xorm.Session) error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime
	_, err := session.Insert(u)
	return err
}

// update 修改
func (u *AccountInfoIndexOp) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

func (u *AccountInfoIndexOp) Save() error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime

	_, err := usercenter.GetEngineMaster().Insert(u)
	return err
}

func (u *AccountInfoIndexOp) Update() error {
	if u.ID < 1 {
		return errors.New("未指定id")
	}
	u.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().Where("id = ?", u.ID).Update(u)
	return err
}

// 清理数据
func (u *AccountInfoIndexOp) ClearData() error {
	_, err := usercenter.GetEngineMaster().Where("1=1").Delete(u)
	return err
}

type accountInfoIndexOp struct{}

var TbAccountInfoIndexOp accountInfoIndexOp

func (c *accountInfoIndexOp) FindByUID(uid int) (*AccountInfoIndexOp, error) {
	var table AccountInfoIndexOp
	table.UID = int64(uid)
	ok, err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ?", uid).Get(&table)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &table, nil
}

// 添加更新以及保存
func (c *accountInfoIndexOp) FindOrCreate(uid, sType int) (*AccountInfoIndexOp, error) {
	table, err := c.FindByUID(uid)
	if err != nil {
		return nil, err
	}
	if table != nil {
		table.Sort = int32(sType)
		table.UpdateTime = time.Now().Unix()
		if err := table.Update(); err != nil {
			return nil, err
		}
		return table, nil
	}
	row := &AccountInfoIndexOp{}
	row.UID = int64(uid)
	row.Sort = int32(sType)
	if err := row.Save(); err != nil {
		return nil, err
	}
	return row, nil
}
