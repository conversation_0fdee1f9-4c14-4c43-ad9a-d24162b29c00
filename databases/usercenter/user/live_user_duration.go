package user

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type LiveUserDuration struct {
	ID        int64 `xorm:"int(11) unsigned notnull pk autoincr 'id'" json:"id"`
	UID       int64 `xorm:"int(11) unsigned notnull 'uid'" json:"uid"`
	ProductID int64 `xorm:"int(11) unsigned notnull default 0 'product_id'" json:"product_id"`
	// 产品类型：1-课包（配合quantity）
	ProductType int32 `xorm:"tinyint(1) unsigned notnull 'product_type'" json:"product_type"`
	// 产品数量
	Quantity int32 `xorm:"smallint(4) unsigned notnull 'quantity'" json:"quantity"`
	// 原始产品数量
	OriginalQuantity int32 `xorm:"smallint(4) unsigned notnull 'original_quantity'" json:"original_quantity"`
	// 开始时间
	StartTime int64 `xorm:"bigint(20) unsigned notnull 'start_time'" json:"start_time"`
	// 结束时间
	EndTime int64 `xorm:"bigint(20) unsigned notnull 'end_time'" json:"end_time"`
	// 状态：1，正常，2过期
	Status     int32 `xorm:"tinyint(2) unsigned notnull 'status'" json:"status"`
	Exp        int64 `xorm:"int(5) unsigned notnull default 0 'exp'" json:"exp"`
	CreateTime int64 `xorm:"int(11) unsigned notnull 'create_time'" json:"create_time"`
	UpdateTime int64 `xorm:"int(11) unsigned notnull 'update_time'" json:"update_time"`
}

// TableName Get table name
func (u *LiveUserDuration) TableName() string {
	return "live_user_duration"
}

// Save 保存
func (u *LiveUserDuration) SaveByTransaction(session *xorm.Session) error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime
	_, err := session.Insert(u)
	return err
}

// update 修改
func (u *LiveUserDuration) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).MustCols("quantity").Update(u)
	return err
}

type liveUDuration struct{}

// TbLiveUserDuration 外部调用静态变量
var TbLiveUserDuration liveUDuration

// GetItemList 获取数据
func (c *liveUDuration) GetItemList(uid int64, liveType int32) []*LiveUserDuration {
	var table []*LiveUserDuration
	err := db.GetEngine().Where("uid = ? and product_type = ?", uid, liveType).Asc("id").Find(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return table
}

// GetUserEffectiveList 获取用户有效期内的卡片
func (c *liveUDuration) GetUserEffectiveList(uid int64, status, productType int32) []*LiveUserDuration {
	var table []*LiveUserDuration
	err := db.GetEngine().Where("uid = ? AND product_type = ?  AND status = ?",
		uid, productType, status).Find(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return table
}
