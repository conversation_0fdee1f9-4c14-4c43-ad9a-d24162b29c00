package user

import (
	"sort"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	dbvip "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/vip"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// MemberDuration 用户会员状态历史表
type MemberDuration struct {
	ID          int64                            `xorm:"int(11) notnull pk autoincr 'id'"`
	UID         int64                            `xorm:"int(11) 'uid'"` //
	SourceType  int                              `xorm:"tinyint(2) 'source_type'"`
	SourceID    int64                            `xorm:"int(11) 'source_id'"` //
	MemberLevel library.MemberLevel              `xorm:"tinyint(2) 'member_level'"`
	StartTime   int64                            `xorm:"bigint(20) 'start_time'"`
	EndTime     int64                            `xorm:"bigint(20) 'end_time'"`
	Status      library.UserMemberDurationStatus `xorm:"tinyint(2) 'status'"`
	CreateTime  int64                            `xorm:"int(11) 'create_time'"`
	UpdateTime  int64                            `xorm:"int(11) 'update_time'"`
}

func (u *MemberDuration) TableName() string {
	return "user_member_duration"
}

// Save 保存
// 2018-08-12 14:03:24 庞晓楠 创建
func (u *MemberDuration) Save() error {
	u.UpdateTime = time.Now().Unix()
	if u.ID <= 0 {
		u.CreateTime = u.UpdateTime
		_, err := db.GetEngineMaster().Insert(u)
		return err
	}
	_, err := db.GetEngineMaster().ID(u.ID).Update(u)
	return err
}

// SetExpire 设置过期
// 2018-05-08 15:06:13 庞晓楠 修改，status替换为const
// 2018-05-16 08:53:11 庞晓楠 修改，失败后重试，打详细日志定位Error1205问题。
func (u *MemberDuration) SetExpire() {
	t1 := time.Now().UnixNano()
	u.Status = library.UserMemberDurationStatusEnum.Overdue
	u.UpdateTime = time.Now().Unix()
	_, err := db.GetEngineMaster().ID(u.ID).Cols("status", "update_time").Update(u)
	if err != nil {
		time.Sleep(1e9)
		var table MemberDuration
		_, err := db.GetEngineMaster().Where("id=?", u.ID).Get(&table)
		if err != nil {
			logger.Info("从主库获取用户会员信息失败", err)
		}
		logger.Info("设置用户会员过期，第一次执行失败，用时", (time.Now().UnixNano()-t1)/1e6, "毫秒", table, *u)
		_, err = db.GetEngineMaster().ID(u.ID).Cols("status", "update_time").Update(u)
		if err != nil {
			logger.Error("设置用户会员过期重试失败", err, *u)
		} else {
			logger.Info("设置用户会员过期失败，重试后成功。", *u)
		}
	} else {
		logger.Info("成功设置用户会员过期", *u)
	}
}

// EndTimeExpand 延长结束时间
// 2018-07-17 11:14:27 庞晓楠 创建
func (u *MemberDuration) EndTimeExpand(td time.Duration) error {
	currUnixTime := time.Now().Unix()
	u.EndTime = time.Unix(u.EndTime, 0).Add(td).Unix()
	u.UpdateTime = currUnixTime
	if u.EndTime > currUnixTime {
		u.Status = library.UserMemberDurationStatusEnum.Normal
	} else {
		u.Status = library.UserMemberDurationStatusEnum.Overdue
	}
	_, err := db.GetEngineMaster().ID(u.ID).Update(u)
	return err
}

type _UserMemberDuration struct{}

// TbUserMemberDuration 外部调用静态变量
var TbUserMemberDuration _UserMemberDuration

// GetByUID 获取用户vip信息
// 2018-03-01 13:35:52 庞晓楠 创建
// 2018-05-08 15:04:37 庞晓楠 修改，修改取状态只取正常的。
// 2018-06-12 11:02:30 庞晓楠 修改，会员暂停状态不判断过期。
// 2018-07-17 10:42:11 庞晓楠 修改，增加排序，高等级的排前面
func (u *_UserMemberDuration) GetByUID(uid int64, isVIPPause bool) []MemberDuration {
	var tables []MemberDuration
	err := db.GetEngine().Where("uid=? and status=?", uid, library.UserMemberDurationStatusEnum.Normal).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}

	// 会员暂停的不处理过期
	if isVIPPause {
		return tables
	}

	// 已过期的标记
	var tables2 []MemberDuration
	for i := 0; i < len(tables); i++ {
		if tables[i].EndTime <= time.Now().Unix() {
			tables[i].SetExpire()
		} else {
			tables2 = append(tables2, tables[i])
		}
	}

	// 排序
	return u.Sort(tables2)
}

// DeleteByUID 根据uid删除数据
// 2018-07-17 10:29:29 庞晓楠 创建
func (*_UserMemberDuration) DeleteByUID(uid int64) error {
	_, err := db.GetEngineMaster().Query("delete from user_member_duration where uid=?", uid)
	return err
}

// Sort 生效顺序排序
// 2018-07-23 15:48:36 庞晓楠 创建
func (u *_UserMemberDuration) Sort(tables []MemberDuration) []MemberDuration {
	sort.Slice(tables, func(i, j int) bool {
		return tables[i].MemberLevel.EffectOrderContrast(tables[j].MemberLevel)
	})
	return tables
}

// Add 添加会员
// 这里需要考虑会员暂停情况
// 2018-07-17 11:02:03 庞晓楠 创建
func (u *_UserMemberDuration) Add(uid int64, level library.MemberLevel,
	td time.Duration, tbVIPPause *dbvip.PauseRecord) ([]MemberDuration, error) {
	// 获取所有记录 不管是不是过期
	var tables []MemberDuration
	err := db.GetEngineMaster().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	tables = u.Sort(tables)

	// 按生效顺序放入数组，没有对应顺序的值为空
	maxLevelOrder := level.GetOrder()
	if len(tables) > 0 && tables[0].MemberLevel.GetOrder() > maxLevelOrder {
		maxLevelOrder = tables[0].MemberLevel.GetOrder()
	}
	box := make([]*MemberDuration, maxLevelOrder+1)
	for k := range tables {
		box[tables[k].MemberLevel.GetOrder()] = &tables[k]
	}

	currTime := time.Now()
	currUnixTime := currTime.Unix()

	var virtualCurrUnixTime int64 // 虚拟当前时间戳，如果用户没有会员暂停，取当前时间，否则取暂停时间
	if tbVIPPause == nil {
		virtualCurrUnixTime = currUnixTime
	} else {
		virtualCurrUnixTime = tbVIPPause.PauseTime
	}

	if box[level.GetOrder()] == nil {
		// 获取更高等级的会员结束时间，如果当前插入等级为最高级，开始时间取当前时间
		beginTime := virtualCurrUnixTime
		for i := level.GetOrder() + 1; i <= maxLevelOrder; i++ {
			if box[i] != nil {
				beginTime = box[i].EndTime + 1
				break
			}
		}
		// 需要增加的等级没有历史记录，插入一条
		box[level.GetOrder()] = &MemberDuration{
			UID:         uid,
			MemberLevel: level,
			StartTime:   beginTime,
			EndTime:     time.Unix(beginTime, 0).Add(td).Unix(),
			Status:      library.UserMemberDurationStatusEnum.Normal,
			CreateTime:  currUnixTime,
			UpdateTime:  currUnixTime,
		}
		_, err := db.GetEngineMaster().Insert(box[level.GetOrder()])
		if err != nil {
			logger.Error(err)
			return nil, err
		}
	} else {
		// 有对应等级历史记录的，更新时间。
		// 这里要判断历史记录里的时间是不是已经过期了，过期先设置为当前时间，再经EndTimeExpand处理
		if box[level.GetOrder()].EndTime < virtualCurrUnixTime {
			box[level.GetOrder()].StartTime = virtualCurrUnixTime
			box[level.GetOrder()].EndTime = virtualCurrUnixTime
		}
		err := box[level.GetOrder()].EndTimeExpand(td)
		if err != nil {
			logger.Error(err)
			return nil, err
		}
	}

	// 将低于增加等级的会员向后延期
	var _endTime = box[level.GetOrder()].EndTime
	for i := level.GetOrder() - 1; i >= 0; i-- {
		if box[i] == nil || box[i].EndTime <= virtualCurrUnixTime {
			continue
		}
		box[i].StartTime = _endTime + 1
		err = box[i].EndTimeExpand(td + time.Second*2)
		if err != nil {
			logger.Error(err)
			return nil, err
		}
		_endTime = box[i].EndTime
	}

	// 整理出当前有效的会员
	tables = []MemberDuration{}
	for k := range box {
		if box[k] != nil {
			if box[k].EndTime <= virtualCurrUnixTime {
				// 将过期但没设置过期状态的，设置过期
				if box[k].Status == library.UserMemberDurationStatusEnum.Normal {
					box[k].SetExpire()
				}
				continue
			}
			tables = append(tables, *box[k])
		}
	}
	return u.Sort(tables), nil
}

// 获取指定等级的会员有效期
func (u *_UserMemberDuration) GetEffectiveList(uid int64, levels []int32) []MemberDuration {
	var tables []MemberDuration
	engine := db.GetEngine().Where("uid=?", uid)
	if len(levels) >= 1 {
		engine = engine.In("member_level", levels)
	}
	err := engine.Where("status=?", library.UserMemberDurationStatusEnum.Normal).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}

	return tables
}

// 获取指定等级的会员有效期
func (u *_UserMemberDuration) GeUserLastedDuration(uid int64) *MemberDuration {
	var table MemberDuration
	ok, err := db.GetEngine().Where("uid = ?", uid).Desc("end_time").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// 获取会员索引
func (u *_UserMemberDuration) GetIndexesUserList(levels []int32, limit, offset int) []MemberDuration {
	var tables []MemberDuration
	engine := db.GetEngine().Distinct("uid")
	engine = engine.In("member_level", levels)
	err := engine.Where("status=?", library.UserMemberDurationStatusEnum.Normal).Limit(limit, offset).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
