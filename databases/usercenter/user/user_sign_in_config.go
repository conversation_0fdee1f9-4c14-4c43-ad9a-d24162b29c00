package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type SignInConfig struct {
	ID         int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	UID        int64 `xorm:"int(10) unsigned not null default 0 'uid'"` // 用户id
	IsRemind   int32 `xorm:"tinyint(2) unsigned not null default 0 'is_remind'"`
	Status     int32 `xorm:"tinyint(2) unsigned not null default 0 'status'"`
	CreateTime int64 `xorm:"int(10) unsigned not null default 0 'create_time'"`
	UpdateTime int64 `xorm:"int(10) unsigned not null default 0 'update_time'"`
}

func (m *SignInConfig) TableName() string {
	return "user_sign_in_config"
}

// Save 新增记录
func (m *SignInConfig) Save() error {
	m.CreateTime = time.Now().Unix()
	m.UpdateTime = m.CreateTime

	_, err := usercenter.GetEngineMaster().Insert(m)
	return err
}

// Update 更新记录
func (m *SignInConfig) Update() error {
	m.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().ID(m.ID).Cols("is_remind", "update_time").Update(m)
	return err
}

type userSignInConfig struct{}

var TbUserSignInConfig userSignInConfig

// GetBadgeInfoByID 获取徽章详情信息
func (t *userSignInConfig) GetInfoByID(uid int64) (*SignInConfig, error) {
	var table SignInConfig
	ok, err := usercenter.GetEngine().Where("uid = ? and status = 1", uid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	if !ok {
		return nil, err
	}
	return &table, nil
}
