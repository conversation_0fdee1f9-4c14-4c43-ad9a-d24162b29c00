package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// AccountInfoExtra
type AccountInfoExtra struct {
	ID         int64 `xorm:"not null autoincr pk bigint(20) unsigned 'id'"`
	CreateTime int64 `xorm:"not null int(10) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(10) 'update_time'"`
	UID        int64 `xorm:"int(11) 'uid'"`
	// 邀请码
	InviteCode string `xorm:"not null char(8) 'invite_code'"`
	// 最后登录的ip
	LastIP string `xorm:"varchar(16) 'last_ip'"`
	// 省份
	Province string `xorm:"varchar(32) 'province'"`
	// 城市
	City string `xorm:"varchar(32) 'city'"`
	// 地址
	Address string `xorm:"varchar(256) 'address'"`
	// ios idfa
	Idfa string `xorm:"varchar(128) 'idfa'"`
}

// TableName 获取表名
func (a *AccountInfoExtra) TableName() string {
	return "accountinfo_extra"
}

func (a *AccountInfoExtra) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime

	_, err := usercenter.GetEngineMaster().Insert(a)
	return err
}

func (a *AccountInfoExtra) Update() error {
	a.UpdateTime = time.Now().Unix()

	_, err := usercenter.GetEngineMaster().ID(a.ID).MustCols("last_ip", "province", "city", "Address").Update(a)
	return err
}

type accountInfoExtra struct {
}

var TbAccountInfoExtra accountInfoExtra

func (a accountInfoExtra) GetItem(uid int64) (*AccountInfoExtra, error) {
	var table AccountInfoExtra

	ok, err := usercenter.GetEngine().Where("uid = ?", uid).Get(&table)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}

	return &table, nil
}
