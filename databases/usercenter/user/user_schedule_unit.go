package user

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// ScheduleUnit 用户自定义课程表
type ScheduleUnit struct {
	ID              int64 `xorm:"int(11) notnull pk autoincr 'id'"`
	UserScheduleID  int64 `xorm:"int(11) 'user_schedule_id'"`
	SessionID       int64 `xorm:"int(11) 'session_id'"`
	Intensity       int64 `xorm:"int(11) 'intensity'"`
	SessionIndex    int64 `xorm:"int(11) 'session_index'"`
	SubSessionIndex int64 `xorm:"int(11) 'sub_session_index'"`
	DateType        int64 `xorm:"int(1) 'date_type'"`
	CreateTime      int64 `xorm:"int(10) 'create_time'"`
	UpdateTime      int64 `xorm:"int(10) 'update_time'"`
}

// TableName 获取表名
func (ScheduleUnit) TableName() string {
	return "user_schedule_unit"
}

type scheduleUnit struct{}

// TbScheduleUnit 外部引用对象
var TbScheduleUnit scheduleUnit

func (s *scheduleUnit) GetItem(id int64) *ScheduleUnit {
	var table ScheduleUnit

	ok, err := usercenter.GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
	}
	if !ok {
		return nil
	}
	return &table
}

// GetBatch 批量查询用户自定义课程表信息
func (s *scheduleUnit) GetBatch(ids []int64) []ScheduleUnit {
	var tables []ScheduleUnit

	err := usercenter.GetEngine().In("id", ids).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}

	return tables
}
