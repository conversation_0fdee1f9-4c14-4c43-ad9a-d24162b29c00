package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// Task 后台返送有赞优惠券记录
type ThirdKdtYouZanCouponSendRecord struct {
	ID          int64  `xorm:"not null autoincr pk unsigned int(11) 'id'"`
	Uids        string `xorm:"not null varchar(1024) 'uids'"`         // 用户ID列表
	Coupons     string `xorm:"not null varchar(1024) 'coupons'"`      // 优惠吗ID
	Userype     int32  `xorm:"not null tinyint(1) 'user_type'"`       // 用户类型，1：制定用户，2：用户组
	GroupID     int64  `xorm:"not null int(11) 'group_id'"`           // 用户组ID
	PushTitle   string `xorm:"not null varchar(255) 'push_title'"`    // 推送标题
	PushContent string `xorm:"not null varchar(1024) 'push_content'"` // 推送标题
	AlreadySent int64  `xorm:"not null int(11) 'already_sent'"`       // 实际发送数量
	ShopType    int32  `xorm:"not null tinyint(1) 'shop_type'"`       // 店铺类型 1 电商 2 训练营
	IsSchedule  int32  `xorm:"not null tinyint(1) 'is_schedule'"`     // 是否定时发送 1定时 2立即发送
	StartTime   int64  `xorm:"not null int(11) 'start_time'"`         // 开始发送时间
	CreateTime  int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime  int64  `xorm:"not null int(11) 'update_time'"`
	Status      int32  `xorm:"not null tinyint(1) 'status'"` // 状态，1：已处理，0：未处理
}

// TableName 表名
func (ThirdKdtYouZanCouponSendRecord) TableName() string {
	return "third_kdt_youzan_coupon_send_record"
}

var YouZanCouponUserType = struct {
	Single int32
	Group  int32
}{
	Single: 1,
	Group:  2,
}

func (t *ThirdKdtYouZanCouponSendRecord) Insert() error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime

	_, err := usercenter.GetEngineMaster().Insert(t)
	return err
}

// UpdateRecentPost 更改会话记录为已读
func (t *ThirdKdtYouZanCouponSendRecord) ChangeStatus() error {
	t.Status = 1
	t.UpdateTime = time.Now().Unix()

	_, err := usercenter.GetEngineMaster().ID(t.ID).Update(t)
	return err
}

type thirdKdtYouZanCouponSendRecord struct{}

// TbThirdKdtYouZanCouponSendRecord 外部引用对象
var TbThirdKdtYouZanCouponSendRecord thirdKdtYouZanCouponSendRecord

// GetRecord 获取后台配置记录
func (t *thirdKdtYouZanCouponSendRecord) GetRecord(id int64) *ThirdKdtYouZanCouponSendRecord {
	var table ThirdKdtYouZanCouponSendRecord
	ok, err := usercenter.GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (t *ThirdKdtYouZanCouponSendRecord) IncrAlreadySent(sentCnt int32) error {
	t.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().ID(t.ID).Incr("already_sent", sentCnt).Update(t)
	return err
}
