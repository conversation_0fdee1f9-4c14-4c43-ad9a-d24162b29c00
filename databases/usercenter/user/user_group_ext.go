package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// GroupExt 用户组扩展
// 2018年6月2日 11:31:03 water 创建
type GroupExt struct {
	ID          int64 `xorm:"int(11) notnull pk autoincr 'id'"`
	UID         int64 `xorm:"int(11) 'uid'"`           // 用户id
	UserGroupID int64 `xorm:"int(11) 'user_group_id'"` // 分组ID
	CreateTime  int64 `xorm:"int(11) 'create_time'"`   //
	UpdateTime  int64 `xorm:"int(11) 'update_time'"`   //
}

func (g GroupExt) TableName() string {
	return "user_group_ext"
}

type _UserGroupExt struct{}

// TbUserGroupExt 静态变量外部调用
var TbUserGroupExt _UserGroupExt

// Insert 插入
// 2018-04-19 18:28:25 庞晓楠 创建
func (*_UserGroupExt) Insert(uid, groupID int64) {
	currTimestemp := time.Now().Unix()
	table := GroupExt{
		UID:         uid,
		UserGroupID: groupID,
		CreateTime:  currTimestemp,
		UpdateTime:  currTimestemp,
	}
	_, err := db.GetEngineMaster().Insert(&table)
	if err != nil {
		logger.Error(table, err)
	}
}

// GetUIDList 根据group_id 获取对应的用户ID 列表
func (*_UserGroupExt) GetUIDList(groupID int64) []GroupExt {
	var table []GroupExt

	err := db.GetEngine().Where("user_group_id = ?", groupID).Find(&table)
	if err != nil {
		logger.Error(err)
	}
	return table
}
