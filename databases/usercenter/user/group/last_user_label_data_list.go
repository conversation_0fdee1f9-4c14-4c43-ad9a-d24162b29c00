package group

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// LastUserLabelDataList 用户最近30天标签元数据
type LastUserLabelData struct {
	// 自增id
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 用户uid
	UID int64 `xorm:"not null int(11) unsigned 'uid'"`
	// 计划id
	ProgramID int64 `xorm:"not null default '0' int(11) unsigned 'program_id'"`
	// 课程id
	SessionID int64 `xorm:"not null default '0' int(11) unsigned 'session_id'"`
	// 训练营计划id
	O2SessionID int64 `xorm:"not null default '0' int(11) unsigned 'o2_session_id'"`
	// 课程节数
	SessionIndex int32 `xorm:"not null default '0' int(11) unsigned 'session_index'"`
	// user_action_log数据表
	UserActionTable int32 `xorm:"not null default '0' int(4) unsigned 'user_action_table'"`
	// 关键字
	Keyword string `xorm:"not null varchar(128) 'keyword'"`
	// 支持客户端跳转类型
	LinkType int32 `xorm:"not null default '0' int(4) unsigned 'link_type'"`
	// 开始练习时间
	ReportTime int64 `xorm:"not null default '0' int(11) unsigned 'report_time'"`
	// 事件来源 1-搜索 2-练习 3-浏览
	SourceType int32 `xorm:"not null tinyint(2) unsigned 'source_type'"`
	// 练习资源类型 1-课程 2-计划  3-KOL 4-训练营 5-选修课 6-自定义课程表 7-智能课表
	ResourceType int32 `xorm:"not null tinyint(2) unsigned 'resource_type'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) unsigned 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) unsigned 'update_time'"`
}

// TableName 获取表名
func (l *LastUserLabelData) TableName() string {
	return "last_user_label_data_list"
}

func (l *LastUserLabelData) Save() error {
	timeNow := time.Now().Unix()

	l.CreateTime = timeNow
	l.UpdateTime = timeNow

	_, err := usercenter.GetEngineMaster().Insert(l)
	return err
}

func (l *LastUserLabelData) Update() error {
	l.UpdateTime = time.Now().Unix()

	_, err := usercenter.GetEngineMaster().ID(l.ID).Update(l)
	return err
}

type lastUserLabelDataList struct {
}

var TbLastUserLabelDataList lastUserLabelDataList

// GetList 获取用户标签历史数据列表
func (l *lastUserLabelDataList) GetList(uid int64) ([]LastUserLabelData, error) {
	var tables []LastUserLabelData

	err := usercenter.GetEngine().Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Errorf("获取用户标签元数据失败, err: %s", err)
		return nil, err
	}

	return tables, nil
}

// GetNeedDeleteDataList 获取过期标签元数据列表
func (l *lastUserLabelDataList) GetNeedDeleteDataList(expiredTime int64, pageSize int) ([]LastUserLabelData, error) {
	var tables []LastUserLabelData

	err := usercenter.GetEngine().Where("create_time <= ?", expiredTime).Limit(pageSize, 0).Find(&tables)
	if err != nil {
		logger.Error("获取过期标签元数据失败:", err)
		return nil, err
	}
	return tables, nil
}
