package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type CrackVersionUser struct {
	ID                  int    `xorm:"'id' pk autoincr" json:"id"`
	UID                 int64  `xorm:"'uid'" json:"uid"`
	DeviceID            string `xorm:"'device_id'" json:"device_id"`
	SessionID           int    `xorm:"'session_id'" json:"session_id"`
	ProgramID           int    `xorm:"'program_id'" json:"program_id"`
	SessionIndex        int    `xorm:"'session_index'" json:"session_index"`
	DType               uint8  `xorm:"'d_type' " json:"d_type"`
	Message             string `xorm:"'message'" json:"message"`
	Channel             int    `xorm:"'channel'" json:"channel"`
	Version             string `xorm:"'version'" json:"version"`
	Status              uint8  `xorm:"'status'" json:"status"`
	PracticeCurrentTime int    `xorm:"'practice_current_time'" json:"practice_current_time"`
	CreateTime          int64  `xorm:"'create_time'" json:"create_time"`
	UpdateTime          int64  `xorm:"'update_time'" json:"update_time"`
}

type crackVersionUser struct {
}

func (c *CrackVersionUser) TableName() string {
	return "crack_version_user"
}

var TbCrackVersionUser crackVersionUser

// Save 保存数据
func (c *CrackVersionUser) Save() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime
	_, err := db.GetEngineMaster().Insert(c)
	return err
}

// Update 更新数据
func (c *CrackVersionUser) Update() error {
	c.UpdateTime = time.Now().Unix()
	_, err := db.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

func (c *crackVersionUser) GetItem(uid int64) *CrackVersionUser {
	var table CrackVersionUser
	_, err := db.GetEngine().Where("uid = ? and status = ?", uid, 1).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &table
}

// GetByUIDList 根据uid 获取信息
func (c *crackVersionUser) GetByUIDList(uidList []int64) []*CrackVersionUser {
	var tables []*CrackVersionUser
	err := db.GetEngine().Where("status = ?", library.Yes).In("uid", uidList).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetByDeviceIDList 根据uid 获取信息
func (c *crackVersionUser) GetByDeviceIDList(deviceIDList []string) []*CrackVersionUser {
	var tables []*CrackVersionUser
	err := db.GetEngine().Where("status = ?", library.Yes).In("device_id", deviceIDList).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetItemByUIDAndDeviceID 根据uid 获取信息
func (c *crackVersionUser) GetItemByUIDAndDeviceID(uid int64, deviceID string) *CrackVersionUser {
	var table CrackVersionUser
	ok, err := db.GetEngine().Where("uid = ? AND device_id = ? AND status = ?", uid, deviceID,
		library.Yes).Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByUserInfo 根据uid 获取信息
func (c *crackVersionUser) GetItemByUserInfo(uid int64, deviceID string) *CrackVersionUser {
	var table CrackVersionUser
	ok, err := db.GetEngine().Where("uid = ? AND device_id = ?", uid, deviceID).Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByUIDOrDeviceID 根据uid deviceID获取信息
func (c *crackVersionUser) GetItemByUIDOrDeviceID(uid int64, deviceID string) *CrackVersionUser {
	var table CrackVersionUser
	ok, err := db.GetEngine().Where("(uid = ? OR device_id = ? ) AND status = ?", uid, deviceID,
		library.Yes).Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByDeviceID 根据uid 获取信息
func (c *crackVersionUser) GetItemByDeviceID(deviceID string) *CrackVersionUser {
	var table CrackVersionUser
	ok, err := db.GetEngine().Where("device_id = ? AND status = ?", deviceID,
		library.Yes).Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
