package location

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// UserGeographyPosition
type UserGeographyPosition struct {
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 用户 id
	UID int64 `xorm:"not null default '0' int(11) unsigned 'uid'"`
	// ip地址
	IP string `xorm:"not null default '''' varchar(125) 'ip'"`
	// 经度
	Lon string `xorm:"not null default '''' varchar(10) 'lon'"`
	// 纬度
	Lat string `xorm:"not null default '''' varchar(10) 'lat'"`
	// 国家编码
	CountryCode string `xorm:"not null default '''' varchar(20) 'country_code'"`
	// 国家
	Country string `xorm:"not null default '''' varchar(50) 'country'"`
	// 省
	Province string `xorm:"not null default '''' varchar(50) 'province'"`
	// 市
	City string `xorm:"not null default '''' varchar(50) 'city'"`
	// 区
	District string `xorm:"not null default '''' varchar(128) 'district'"`
	// 镇
	Township string `xorm:"not null default '''' varchar(128) 'township'"`
	// 街道
	Street string `xorm:"not null default '''' varchar(255) 'street'"`
	// 详细地址
	Address string `xorm:"not null default '''' varchar(255) 'address'"`
	// 类型： 1-ip获取 2-经纬度获取
	Type int32 `xorm:"not null default '0' tinyint(1) 'type'"`
	// 创建时间
	CreateTime int64 `xorm:"not null default '0' int(11) unsigned 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null default '0' int(11) unsigned 'update_time'"`
}

// TableName 获取表名
func (u *UserGeographyPosition) TableName() string {
	return fmt.Sprintf("user_geography_position_%d", u.UID%32)
}

// Save 插入数据
func (u *UserGeographyPosition) Save() error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime

	_, err := usercenter.GetEngineMaster().Insert(u)
	return err
}

// Update 更新数据
func (u *UserGeographyPosition) Update() error {
	u.UpdateTime = time.Now().Unix()

	_, err := usercenter.GetEngineMaster().ID(u.ID).Update(u)
	return err
}

type userGeographyPosition struct {
}

var TbUserGeoggraphyPosition userGeographyPosition

// GetLastItem 获取用户最近位置
func (u *userGeographyPosition) GetLastItem(uid int64) (*UserGeographyPosition, error) {
	var table UserGeographyPosition

	table.UID = uid
	ok, err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ?", uid).Desc("id").Limit(1, 0).Get(&table)
	if err != nil {
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &table, nil
}

// GetLastItemByType 根据类型获取用户最后一次位置
func (u *userGeographyPosition) GetLastItemByType(uid int64, locationType int32) (*UserGeographyPosition, error) {
	var table UserGeographyPosition

	table.UID = uid
	ok, err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ? AND type = ?", uid, locationType).
		Desc("id").Limit(1, 0).Get(&table)
	if err != nil {
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &table, nil
}

// GetList 获取用户位置信息列表
func (u *userGeographyPosition) GetList(uid int64) ([]UserGeographyPosition, error) {
	var tables []UserGeographyPosition
	var table UserGeographyPosition

	table.UID = uid
	err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Warnf("查询用户位置失败, err: %s", err)
		return nil, err
	}

	return tables, nil
}

// GetItemByIP 根据IP地址获取位置信息
func (u *userGeographyPosition) GetItemByIP(uid int64, ip string) (*UserGeographyPosition, error) {
	var table UserGeographyPosition

	table.UID = uid
	ok, err := usercenter.GetEngine().Table(table.TableName()).Where("ip = ?", ip).Desc("id").Limit(1, 0).Get(&table)
	if err != nil {
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &table, nil
}
