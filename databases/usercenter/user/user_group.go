package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

type Group struct {
	ID         int32  `xorm:"not null int(11) pk autoincr 'id'"`
	Title      string `xorm:"not null varchar(300) 'title'"`
	Type       int32  `xorm:"not null int(5) unsigned 'type'"`
	TotalCount int32  `xorm:"not null int(5) unsigned 'total_count'"`
	Status     int32  `xorm:"not null tinyint(1) unsigned 'status'"`
	CreateTime int64  `xorm:"not null int(10) 'create_time'"`
	UpdateTime int64  `xorm:"not null int(10) 'update_time'"`
}

func (Group) TableName() string {
	return "user_group"
}

type _UserGroup struct{}

// TbUserGroup 静态变量外部调用
var TbUserGroup _UserGroup

func (*_UserGroup) IncrTotalCount(groupID int64) {
	var table Group
	table.UpdateTime = time.Now().Unix()
	_, err := db.GetEngineMaster().Where("id = ?", groupID).Incr("total_count").Update(&table)
	if err != nil {
		logger.Errorf("[IncrUserGroupTotalCountErr]err:%v", err)
	}
}
