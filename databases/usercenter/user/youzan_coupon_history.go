package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// Task 后台返送有赞优惠券记录
type ThirdKdtYouZanCouponSendHistory struct {
	ID         int64  `xorm:"not null autoincr pk unsigned int(11) 'id'"`
	UID        int64  `xorm:"not null int(11) 'uid'"`         // 用户ID
	CouponID   int64  `xorm:"not null int(11) 'coupon_id'"`   // 优惠吗ID
	RecordID   int64  `xorm:"not null int(11) 'record_id'"`   // 记录表id
	Reason     string `xorm:"not null varchar(255) 'reason'"` // 失败原因
	Status     int32  `xorm:"not null tinyint(1) 'status'"`   // 状态，1：成功，0：失败
	CreateTime int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64  `xorm:"not null int(11) 'update_time'"`
}

// TableName 表名
func (ThirdKdtYouZanCouponSendHistory) TableName() string {
	return "third_kdt_youzan_coupon_send_history"
}

func (t *ThirdKdtYouZanCouponSendHistory) Insert() error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime

	_, err := usercenter.GetEngineMaster().Insert(t)
	return err
}
