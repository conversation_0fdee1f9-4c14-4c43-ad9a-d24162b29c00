package user

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// AccountInfoLevelInfo 用户成长信息
type AccountInfoLevelInfo struct {
	ID         int `xorm:"int(11) notnull pk autoincr 'id'"`
	UID        int `xorm:"'uid'"`
	Level      int // 等级
	Grow       int // 成长
	CreateTime int
	UpdateTime int
}

// TableName Get table name
func (*AccountInfoLevelInfo) TableName() string {
	return "accountinfo_level_info"
}

type accountInfoLevelInfo struct {
}

// TbAccountInfoLevelInfo 静态变量外部调用
var TbAccountInfoLevelInfo accountInfoLevelInfo

func (*accountInfoLevelInfo) GetByUID(uid int64) *AccountInfoLevelInfo {
	var table AccountInfoLevelInfo
	ok, err := db.GetEngine().Where("uid=?", uid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetByUIDBatch 批量获取用户等级信息
func (*accountInfoLevelInfo) GetByUIDBatch(uidList []int64) []AccountInfoLevelInfo {
	var tables []AccountInfoLevelInfo

	err := db.GetEngine().In("uid", uidList).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}

	return tables
}
