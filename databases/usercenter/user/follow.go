package user

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// Follow 用户邀请记录
// 2018-02-27 17:37:45 庞晓楠 创建
type Follow struct {
	ID     int64 `xorm:"'follow_id'"`
	UID    int64 `xorm:"'uid'"` // 用户id
	AID    int64 `xorm:"'aid'"` // 被关注用户id
	Status int   // 用户id
}

type _Follow struct{}

// TbFollow 外部调用静态变量
var TbFollow _Follow

// GetByUID 获取记录
// 2018-02-27 17:55:34 庞晓楠 创建
func (*_Follow) GetByUID(uid int64) []Follow {
	var tables []Follow
	err := db.GetEngine().Where("uid=?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// HasRecordByUID 获取记录
// 2018-02-27 17:55:34 朱家琪 创建
func (*_Follow) HasRecordByUID(uid, aid int64) bool {
	ok, err := db.GetEngine().Where("uid=? AND aid=?", uid, aid).Exist(new(Follow))
	if err != nil {
		logger.Error(err)
	}
	return ok
}

// GetFollowUIDs 获取关注用户列表
func (*_Follow) GetFollowUIDs(uid int64, include bool) []int64 {
	var tables []Follow
	var uidList []int64

	err := db.GetEngine().Where("uid=? and status = 1", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if len(tables) > 0 {
		for _, v := range tables {
			uidList = append(uidList, v.AID)
		}
	}
	if include {
		uidList = append(uidList, uid)
	}
	return uidList
}

// JudgeUserFollowUIDList 获取那些用户关注此人
func (*_Follow) JudgeUserFollowUIDList(uid int64, uidList []int64) []int64 {
	var tables []Follow
	var followUIDList []int64

	err := db.GetEngine().Where("uid = ?", uid).In("aid", uidList).Where("status = 1").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}

	for _, item := range tables {
		followUIDList = append(followUIDList, item.AID)
	}
	return followUIDList
}
