package user

import (
	"context"
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// AccountInfo 用户基本信息表
type AccountInfo struct {
	AccountID      int64               `xorm:"'AccountId'"`      //
	Email          string              `xorm:"'Email'"`          //
	AreaCode       int                 `xorm:"'areaCode'"`       // 地区区号， 大陆：86 , 香港：852 , 澳门：853 ,台湾：886
	Mobile         string              `xorm:"'Mobile'"`         // 用户手机号
	FirstName      string              `xorm:"'FirstName'"`      //
	LastName       string              `xorm:"'LastName'"`       //
	Password       string              `xorm:"'Password'"`       //
	Password2      string              `xorm:"'Password2'"`      // MD5 后的用户密码
	AccountType    int                 `xorm:"'AccountType'"`    //
	StartTime      time.Time           `xorm:"'StartTime'"`      // 会员起始时间
	EndTime        time.Time           `xorm:"'EndTime'"`        // 会员总到期时间 所有会员类型的总和
	Level          string              `xorm:"'Level'"`          // 等级 当前生效会员等级
	Goals          string              `xorm:"'Goals'"`          //
	Intensity      string              `xorm:"'Intensity'"`      // 强度
	Privacy        string              `xorm:"'Privacy'"`        //
	TotalScore     int                 `xorm:"'TotalScore'"`     // 总积分
	TotalWorkouts  int                 `xorm:"'TotalWorkouts'"`  // 总课程数
	Thumbnail      string              `xorm:"'Thumbnail'"`      // 头像路径
	DeviceInfo     string              `xorm:"'DeviceInfo'"`     // 设备信息
	DeviceID       string              `xorm:"'DeviceId'"`       // 终端设备唯一标示
	Country        string              `xorm:"'Country'"`        // 国家
	Alternate2     string              `xorm:"'Alternate2'"`     //
	Alternate3     string              `xorm:"'Alternate3'"`     //
	Province       string              `xorm:"'province'"`       // 省
	City           string              `xorm:"'city'"`           // 市
	Gender         int                 `xorm:"'gender'"`         // 性别：-1保密，0女，1男
	Birthday       int64               `xorm:"'birthday'"`       // 生日
	Authentication string              `xorm:"'Authentication'"` // 娱乐徽章
	CreateTime     int                 `xorm:"'createTime'"`     // 创建时间
	UserType       library.MemberLevel `xorm:"'userType'"`       // 会员登记
	Follows        int                 `xorm:"'follows'"`        // 关注数量
	Fans           int                 `xorm:"'fans'"`           // 粉丝数量
	// 登录类型：0：游客 1：手机 （默认） 2：邮箱 3：QQ 4：新浪微博 5：微信
	LoginType        int `xorm:"'loginType'"`
	SigninCount      int `xorm:"'signinCount'"`        // 签到天数
	CreateTime2      int `xorm:"'create_time'"`        // 创建时间
	UpdateTime       int `xorm:"'update_time'"`        // 修改时间
	EnterpriseInfoID int `xorm:"'enterprise_info_id'"` // 是否是企业课：1=》是，2=》不是
}

// TableName Get table name
func (*AccountInfo) TableName() string {
	return "accountinfo"
}

// UpdateUserType 更新用户等级
// 2018-03-01 17:18:55 庞晓楠 创建
func (a *AccountInfo) UpdateUserType(userType library.MemberLevel) {
	a.UserType = userType
	_, err := usercenter.GetEngineMaster().Where("AccountId=?", a.AccountID).Cols("userType").Update(a)
	if err != nil {
		logger.Error(err, a)
	}
}

// SaveUserType 更新用户等级相关
// 包括 等级、开始时间、结束时间
// 2018-08-16 16:36:24 庞晓楠 创建
func (a *AccountInfo) SaveUserType() error {
	_, err := usercenter.GetEngineMaster().Where("AccountId=?", a.AccountID).
		Cols("userType", "StartTime", "EndTime").Update(a)
	return err
}

// SaveTransferUser 强制注销
func (a *AccountInfo) SaveTransferUser() error {
	_, err := usercenter.GetEngineMaster().Where("AccountId=?", a.AccountID).
		Cols("DeviceId", "Alternate3").Update(a)
	return err
}

// Save 保存
// 2018-07-17 17:21:20 庞晓楠 创建
func (a *AccountInfo) Save() error {
	if a == nil || a.AccountID <= 0 {
		logger.Error(a)
		return fmt.Errorf("用户accountinfo保存失败")
	}
	// 当消耗到0时也需要更新
	_, err := usercenter.GetEngineMaster().Where("AccountId=?", a.AccountID).MustCols("TotalScore").Update(a)
	if err != nil {
		logger.Error(err, a)
		return fmt.Errorf("用户accountinfo保存失败")
	}
	return nil
}

type accountInfo struct{}

// TbAccountInfo 外部调用静态变量
var TbAccountInfo accountInfo

// GetByUID 根据UID获取数据
// 2018-06-08 13:55:47 庞晓楠 创建
func (*accountInfo) GetByUID(uid int64) *AccountInfo {
	var table AccountInfo
	ok, err := usercenter.GetEngine().Where("AccountId = ?", uid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*accountInfo) GetByUIDs(uids []int64) []AccountInfo {
	var tables []AccountInfo
	err := usercenter.GetEngine().In("AccountId", uids).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetByMobile 根据手机号查找
// 2018-03-07 16:03:48 庞晓楠 创建
func (*accountInfo) GetByMobile(mobile string) *AccountInfo {
	var table AccountInfo
	ok, err := usercenter.GetEngine().In("Mobile", mobile).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetNamesByUIDs 根据id列表获取名字列表
// 2018-03-01 10:05:52 庞晓楠 创建
func (a *accountInfo) GetNamesByUIDs(uids []int64) []string {
	tables := a.GetByUIDs(uids)
	var names []string
	for i := range tables {
		names = append(names, tables[i].FirstName)
	}
	return names
}

// GetAccountInfo 获取用户基本表
// 2018-02-26 13:57:06 庞晓楠 创建
func GetAccountInfo(ctx context.Context, id int64) *AccountInfo {
	var table AccountInfo
	ok, err := usercenter.GetEngine().Where("AccountId=?", id).Get(&table)
	if err != nil {
		logger.CError(ctx, err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// UpdateUserInfo @desc: 体脂秤主账号修改信息的时候，需要同步修改用户信息
// @version:750 @date:2018-12-06 @author:Sachin.song
func (a *AccountInfo) UpdateUserInfo(name string, gender int, thumbnail string, birthDay int64) {
	a.FirstName = name      // 用户名
	a.Gender = gender       // 性别
	a.Thumbnail = thumbnail // 头像
	a.Birthday = birthDay   // 生日
	_, err := usercenter.GetEngineMaster().Where("AccountId=?", a.AccountID).
		Cols("FirstName", "gender", "Thumbnail", "birthday").Update(a)
	if err != nil {
		logger.Error(err, a)
	}
}

// GetByName @desc: 检查用户名是否已经存在
// @version:750 @date:2018-12-06 @author:Sachin.song
func (*accountInfo) GetByName(name string) *AccountInfo {
	var table AccountInfo
	ok, err := usercenter.GetEngine().Where("FirstName = ?", name).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (a *accountInfo) GetUserInfoBatch(uidList []int64) ([]AccountInfo, error) {
	var tables []AccountInfo
	err := usercenter.GetEngine().In("AccountID", uidList).
		Cols("AccountID", "Mobile", "FirstName", "LastName", "gender", "UserType", "TotalScore").Find(&tables)
	if err != nil {
		logger.Error("批量查询用户信息失败")
		return nil, err
	}

	return tables, nil
}
