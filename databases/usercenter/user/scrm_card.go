package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// Task 挑战赛任务表
type ThirdKdtScrmCardList struct {
	ID          int64  `xorm:"not null autoincr pk unsigned int(11) 'id'"`
	UID         int64  `xorm:"not null int(11) 'uid'"`
	CardAlias   string `xorm:"not null varchar(64) 'card_alias'"`  // 商家会员卡的唯一标识
	CardNo      string `xorm:"not null varchar(64) 'card_no'"`     // 会员卡号
	MemberLevel int32  `xorm:"not null tinyint(2) 'member_level'"` // 会员卡类型:
	Status      int32  `xorm:"not null int(11) 'status'"`          // 会员卡状态，3：待重试，1：正常，2：删除
	CreateTime  int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime  int64  `xorm:"not null int(11) 'update_time'"`
}

var ScrmCardStatus = struct {
	Valid  int32
	Delete int32
	Retry  int32
}{
	Valid:  1,
	Delete: 2,
	Retry:  3,
}

// TableName 表名
func (ThirdKdtScrmCardList) TableName() string {
	return "third_kdt_scrm_card_list"
}

func (t *ThirdKdtScrmCardList) Insert() error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime

	_, err := usercenter.GetEngineMaster().Insert(t)
	return err
}

// UpdateRecentPost 更改会话记录为已读
func (t *ThirdKdtScrmCardList) DeleteScrmCard() error {
	t.Status = ScrmCardStatus.Delete
	t.UpdateTime = time.Now().Unix()

	_, err := usercenter.GetEngineMaster().ID(t.ID).Update(t)
	return err
}

type thirdKdtScrmCardList struct{}

// TbThirdKdtScrmCardList 外部引用对象
var TbThirdKdtScrmCardList thirdKdtScrmCardList

// GetUserValidScrmCard 获取用户目前有效的有赞会员卡
func (t *thirdKdtScrmCardList) GetUserValidScrmCard(uid int64) *ThirdKdtScrmCardList {
	var table ThirdKdtScrmCardList

	ok, err := usercenter.GetEngine().Where("uid = ?", uid).And("status = ?", ScrmCardStatus.Valid).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
