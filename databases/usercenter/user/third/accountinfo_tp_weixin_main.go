package third

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// AccountInfoTpWechatMain 	用户微信信息表
type AccountInfoTpWechatMain struct {
	ID           int    `xorm:"'id'"`
	UID          int64  `xorm:"'uid'"`         // 用户id
	UnionID      string `xorm:"'unionid'"`     // 微信针对开放平台所有应用的唯一识别码
	Nickname     string `xorm:"'nickname'"`    // 第三方昵称
	Sex          int    `xorm:"'sex'"`         // 性别：0女,1男
	Country      string `xorm:"'country'"`     // 国家
	Province     string `xorm:"'province'"`    // 省
	City         string `xorm:"'city'"`        // '市
	HeadImageURL string `xorm:"'headimgurl'"`  // 头像
	BindStatus   int    `xorm:"'bind_status'"` // 绑定状态1整除2解除
	CreateTime   int    `xorm:"'create_time'"` // 创建时间
	UpdateTime   int    `xorm:"'update_time'"` // 修改时间
}

var WechatBindStatus = struct {
	Bind   int
	Remove int
}{
	Bind:   1,
	Remove: 2,
}

// TableName Get table name
func (*AccountInfoTpWechatMain) TableName() string {
	return "accountinfo_tp_weixin_main"
}

type tpWechatMain struct{}

var TbAccountInfoTpWechatMain tpWechatMain

// 查询用户微信主表信息
func (a *tpWechatMain) GetMainInfo(uid int64) *AccountInfoTpWechatMain {
	var table AccountInfoTpWechatMain
	ok, err := db.GetEngine().Where("uid = ? AND bind_status = ?", uid, WechatBindStatus.Bind).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

//
// GetMainInfoByUnionID
//  @Description: 通过 unionid 查询
//  @receiver a
//  @param unionID
//  @return *AccountInfoTpWechatMain
//
func (a *tpWechatMain) GetMainInfoByUnionID(unionID string) *AccountInfoTpWechatMain {
	var table AccountInfoTpWechatMain
	ok, err := db.GetEngineMaster().Where("unionid = ?", unionID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

//
// Save
//  @Description: 保存
//  @receiver a
//  @return error
//
func (a *AccountInfoTpWechatMain) Save() error {
	a.CreateTime = int(time.Now().Unix())
	a.UpdateTime = a.CreateTime

	_, err := db.GetEngineMaster().Insert(a)
	return err
}

//
// Update
//  @Description: 更新
//  @receiver a
//  @return error
//
func (a *AccountInfoTpWechatMain) Update() error {
	_, err := db.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

//
// UpdateUIDByUnionID
//  @Description: 通过 unionid 更新
//  @receiver a
//  @param unionID
//  @return error
//
func (a *AccountInfoTpWechatMain) UpdateUIDByUnionID(unionID string) error {
	_, err := db.GetEngineMaster().
		Table(a.TableName()).
		Where("unionid = ?", unionID).
		Update(map[string]interface{}{"UID": a.UID})
	return err
}
