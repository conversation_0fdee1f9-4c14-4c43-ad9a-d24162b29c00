package third

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type AccountinfoTpQq struct {
	ID         int64  `xorm:"pk autoincr BIGINT(20) 'id'" json:"id"`
	UID        int64  `xorm:"not null BIGINT(20) 'uid'" json:"uid"`
	OpenID     string `xorm:"not null VARCHAR(255) 'open_id'" json:"open_id"`
	BindStatus int    `xorm:"not null TINYINT(2) 'bind_status'" json:"bind_status"`
	CreateTime int    `xorm:"not null INT(11) 'create_time'" json:"create_time"`
	UpdateTime int    `xorm:"not null INT(11) 'update_time'" json:"update_time"`
	Nickname   string `xorm:"not null VARCHAR(255) 'nickname'" json:"nickname"`
	UnionID    string `xorm:"not null VARCHAR(255) 'unionid'" json:"unionid"`
	SourceType int    `xorm:"default 1 TINYINT(1) 'source_type'" json:"source_type"`
}

func (*AccountinfoTpQq) TableName() string {
	return "accountinfo_tp_qq"
}

type aTpQq struct{}

// TpAQq 外部调用静态变量
var TpAQq aTpQq

func (*aTpQq) GetByOpenIDBind(uid int64) *AccountinfoTpQq {
	var table AccountinfoTpQq
	ok, err := usercenter.GetEngine().Where("uid = ? AND bind_status=?",
		uid, library.DataStatusEnum.Valid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
