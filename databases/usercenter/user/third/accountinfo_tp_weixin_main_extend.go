package third

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// AccountInfoTpWechatMainExtend 用户微信信息表
type AccountInfoTpWechatMainExtend struct {
	ID           int    `xorm:"'id'"`
	UnionID      string `xorm:"'unionid'"`       // 微信针对开放平台所有应用的唯一识别码
	OpenID       string `xorm:"'openid'"`        // 微信应用ID
	RemindStatus int    `xorm:"'remind_status'"` // 推送开关:0默认1开2关
	Alias        int    `xorm:"'alias'"`         // 推送开关:0默认1开2关
	SourceType   int    `xorm:"'source_type'"`   // 来源类型：1-APP,2-web微信wx24779a6640253a14,3-每日瑜伽打卡（小程序）,4-训练营（小程序）
	CreateTime   int    `xorm:"'create_time'"`   // 创建时间
	UpdateTime   int    `xorm:"'update_time'"`   // 修改时间
}

func (*AccountInfoTpWechatMainExtend) TableName() string {
	return "accountinfo_tp_weixin_main_extend"
}

//
// Save
//  @Description:
//  @receiver a
//  @return error
//
func (a *AccountInfoTpWechatMainExtend) Save() error {
	a.CreateTime = int(time.Now().Unix())
	a.UpdateTime = a.CreateTime

	_, err := db.GetEngineMaster().Insert(a)
	return err
}

type tpWechatMainExtend struct{}

var TbAccountInfoTpWechatMainExtend tpWechatMainExtend

type GetMainInfoExtendRequest struct {
	UnionID    string // 微信针对开放平台所有应用的唯一识别码
	OpenID     string // 微信应用ID
	SourceType int    // 来源类型：1-APP,2-web微信wx24779a6640253a14,3-每日瑜伽打卡（小程序）,4-训练营（小程序）
}

// GetMainInfoExtend 查询用户微信信息
func (a *tpWechatMainExtend) GetMainInfoExtend(req *GetMainInfoExtendRequest) *AccountInfoTpWechatMainExtend {
	var table AccountInfoTpWechatMainExtend
	ok, err := db.GetEngine().
		Where("unionid = ?", req.UnionID).
		Where("openid = ?", req.OpenID).
		Where("source_type = ?", req.SourceType).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
