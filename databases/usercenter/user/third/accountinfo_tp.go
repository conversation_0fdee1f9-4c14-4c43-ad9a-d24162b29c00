package third

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

var AccountInfoTpTableNameMap = map[library.UserLoginType]interface{}{
	library.UserLoginTypeEnum.QQ:          &AccountinfoTpQq{},
	library.UserLoginTypeEnum.Sina:        &AccountinfoTpSinaweibo{},
	library.UserLoginTypeEnum.WeiXin:      &AccountinfoTpWeixin{},
	library.UserLoginTypeEnum.Mama:        &AccountinfoTpMama{},
	library.UserLoginTypeEnum.Huawei:      &AccountinfoTpHuawei{},
	library.UserLoginTypeEnum.Ios:         &AccountinfoTpIos{},
	library.UserLoginTypeEnum.Xiaomi:      &AccountinfoTpXiaomi{},
	library.UserLoginTypeEnum.SensorsData: &AccountinfoTpSensorData{},
}

type AccountInfoTp struct {
	ID         int64
	UID        int64
	OpenID     string
	BindStatus int
	CreateTime int
	UpdateTime int
	Nickname   string
	UnionID    string
	SourceType int
}

type aTp struct{}

// Tp 外部调用静态变量
var Tp aTp

func (a *aTp) GetAccountInfoTpByTableName(openID string, loginType, bindStatus int) map[string]string {
	valuesMap := make(map[string]string)

	db := usercenter.GetEngine().
		Table(AccountInfoTpTableNameMap[library.UserLoginType(loginType)]).
		Where("open_id = ?", openID).
		Where("bind_status = ?", bindStatus)

	flag := library.CheckLoginType(library.UserLoginType(loginType), []library.UserLoginType{
		library.UserLoginTypeEnum.QQ,
		library.UserLoginTypeEnum.WeiXin,
		library.UserLoginTypeEnum.Huawei,
	})
	if flag {
		db = db.Cols("id", "bind_status", "unionid")
	} else {
		db = db.Cols("id", "bind_status")
	}

	ok, err := db.Get(&valuesMap)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}

	return valuesMap
}

func (a *aTp) UpdateAccountInfoTp(table interface{}, id int64) error {
	_, err := usercenter.GetEngineMaster().ID(id).Update(table)
	if err != nil {
		logger.Error(err)
		return err
	}

	return err
}

func (a *aTp) SaveAccountInfoTp(table interface{}) error {
	_, err := usercenter.GetEngineMaster().Insert(table)
	if err != nil {
		logger.Error(err)
		return err
	}

	return err
}

//nolint
func (accountInfoTp *AccountInfoTp) FormatAccountInfoTp(loginType int) interface{} {
	modelInstance := AccountInfoTpTableNameMap[library.UserLoginType(loginType)]

	if modelInstance == nil {
		logger.Warnf("FormatAccountInfoTp: Invalid or unhandled loginType %d,"+
			"not found in AccountInfoTpTableNameMap. accountInfoTp: %+v", loginType, accountInfoTp)
		return nil // 明确返回 nil，因为 loginType 无效
	}

	// 使用 type switch 来正确处理不同类型的模型
	switch m := modelInstance.(type) {
	case *AccountinfoTpQq:
		return &AccountinfoTpQq{
			ID:         accountInfoTp.ID,
			UID:        accountInfoTp.UID,
			OpenID:     accountInfoTp.OpenID,
			BindStatus: accountInfoTp.BindStatus,
			CreateTime: accountInfoTp.CreateTime,
			UpdateTime: accountInfoTp.UpdateTime,
			Nickname:   accountInfoTp.Nickname,
			UnionID:    accountInfoTp.UnionID,
			SourceType: accountInfoTp.SourceType,
		}
	case *AccountinfoTpSinaweibo:
		return &AccountinfoTpSinaweibo{
			ID:         accountInfoTp.ID,
			UID:        accountInfoTp.UID,
			OpenID:     accountInfoTp.OpenID,
			BindStatus: accountInfoTp.BindStatus,
			CreateTime: accountInfoTp.CreateTime,
			UpdateTime: accountInfoTp.UpdateTime,
			Nickname:   accountInfoTp.Nickname,
			// Sina Weibo 类型可能没有 UnionID 和 SourceType，根据实际结构体定义
		}
	case *AccountinfoTpWeixin:
		return &AccountinfoTpWeixin{
			ID:         accountInfoTp.ID,
			UID:        accountInfoTp.UID,
			OpenID:     accountInfoTp.OpenID,
			BindStatus: accountInfoTp.BindStatus,
			CreateTime: accountInfoTp.CreateTime,
			UpdateTime: accountInfoTp.UpdateTime,
			Nickname:   accountInfoTp.Nickname,
			UnionID:    accountInfoTp.UnionID,
			SourceType: accountInfoTp.SourceType,
			// RemindStatus: accountInfoTp.RemindStatus, // 如果 AccountInfoTp 有此字段
		}
	case *AccountinfoTpMama:
		return &AccountinfoTpMama{
			ID:         accountInfoTp.ID,
			UID:        accountInfoTp.UID,
			OpenID:     accountInfoTp.OpenID,
			BindStatus: accountInfoTp.BindStatus,
			CreateTime: accountInfoTp.CreateTime,
			UpdateTime: accountInfoTp.UpdateTime,
			Nickname:   accountInfoTp.Nickname,
			UnionID:    accountInfoTp.UnionID,
			SourceType: accountInfoTp.SourceType,
		}
	case *AccountinfoTpHuawei:
		return &AccountinfoTpHuawei{
			ID:         accountInfoTp.ID,
			UID:        accountInfoTp.UID,
			OpenID:     accountInfoTp.OpenID,
			BindStatus: accountInfoTp.BindStatus,
			CreateTime: accountInfoTp.CreateTime,
			UpdateTime: accountInfoTp.UpdateTime,
			Nickname:   accountInfoTp.Nickname,
			UnionID:    accountInfoTp.UnionID,
			SourceType: accountInfoTp.SourceType,
		}
	case *AccountinfoTpIos:
		return &AccountinfoTpIos{
			ID:         accountInfoTp.ID,
			UID:        accountInfoTp.UID,
			OpenID:     accountInfoTp.OpenID,
			BindStatus: accountInfoTp.BindStatus,
			CreateTime: accountInfoTp.CreateTime,
			UpdateTime: accountInfoTp.UpdateTime,
			Nickname:   accountInfoTp.Nickname,
		}
	case *AccountinfoTpXiaomi:
		return &AccountinfoTpXiaomi{
			ID:         accountInfoTp.ID,
			UID:        accountInfoTp.UID,
			OpenID:     accountInfoTp.OpenID,
			BindStatus: accountInfoTp.BindStatus,
			CreateTime: accountInfoTp.CreateTime,
			UpdateTime: accountInfoTp.UpdateTime,
			Nickname:   accountInfoTp.Nickname,
			SourceType: accountInfoTp.SourceType,
		}
	case *AccountinfoTpSensorData:
		return &AccountinfoTpSensorData{
			ID:         accountInfoTp.ID,
			UID:        accountInfoTp.UID,
			OpenID:     accountInfoTp.OpenID,
			BindStatus: int32(accountInfoTp.BindStatus), // 注意类型转换
			CreateTime: int64(accountInfoTp.CreateTime), // 注意类型转换
			UpdateTime: int64(accountInfoTp.UpdateTime), // 注意类型转换
			Nickname:   accountInfoTp.Nickname,
		}
	default:
		// 理论上，如果 modelInstance 不为 nil，且 AccountInfoTpTableNameMap 中的值都是预期的指针类型，
		// 那么不应该执行到这里。但为了保险起见，保留这个 default。
		logger.Errorf("FormatAccountInfoTp: Unhandled model type "+
			"for loginType %d. Model type: %T, accountInfoTp: %+v", loginType, m, accountInfoTp)
		return nil
	}
}
