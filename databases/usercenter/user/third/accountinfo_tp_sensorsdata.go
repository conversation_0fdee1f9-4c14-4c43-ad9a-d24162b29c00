package third

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type AccountinfoTpSensorData struct {
	ID         int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	UID        int64  `xorm:"not null BIGINT(20) 'uid'" json:"uid"`
	OpenID     string `xorm:"not null VARCHAR(255) 'open_id'" json:"open_id"`
	Nickname   string `xorm:"VARCHAR(255) 'nickname'" json:"nickname"`
	BindStatus int32  `xorm:"not null default 1 TINYINT(2) 'bind_status'" json:"bind_status"`
	CreateTime int64  `xorm:"not null INT(11) 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"not null INT(11) 'update_time'" json:"update_time"`
}

// TableName Get table name
func (*AccountinfoTpSensorData) TableName() string {
	return "accountinfo_tp_sensorsdata"
}
func (a *AccountinfoTpSensorData) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := usercenter.GetEngineMaster().Insert(a)
	return err
}

// Update 更新绑定状态
func (a *AccountinfoTpSensorData) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := usercenter.GetEngineMaster().ID(a.ID).Update(a)
	if err != nil {
		logger.Error(err, a)
	}
	return err
}

type aTpSensorsData struct{}

// TbATpSensorsData 外部调用静态变量
var TbATpSensorsData aTpSensorsData

func (*aTpSensorsData) GetItemByUID(uid int64) *AccountinfoTpSensorData {
	var table AccountinfoTpSensorData
	ok, err := usercenter.GetEngine().Where("uid = ? AND bind_status = ?",
		uid, library.DataStatusEnum.Valid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*aTpSensorsData) GetItemByOpenID(openID string) *AccountinfoTpSensorData {
	var table AccountinfoTpSensorData
	ok, err := usercenter.GetEngine().Where("open_id = ?", openID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*aTpSensorsData) GetByOpenIDBind(openID string) *AccountinfoTpSensorData {
	var table AccountinfoTpSensorData
	ok, err := usercenter.GetEngine().Where("open_id = ? AND bind_status=?",
		openID, library.DataStatusEnum.Valid).OrderBy("update_time desc").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*aTpSensorsData) GetByOpenIDS(openID string) []*AccountinfoTpSensorData {
	var table []*AccountinfoTpSensorData
	err := usercenter.GetEngine().Where("open_id = ?", openID).Find(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}

	return table
}
