package third

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type AccountinfoTpIos struct {
	ID         int64  `xorm:"pk autoincr BIGINT(20) 'id'" json:"id"`
	UID        int64  `xorm:"not null BIGINT(20) 'uid'" json:"uid"`
	OpenID     string `xorm:"not null VARCHAR(255) 'open_id'" json:"open_id"`
	Nickname   string `xorm:"VARCHAR(255) 'nickname'" json:"nickname"`
	BindStatus int    `xorm:"not null default 1 TINYINT(2) 'bind_status'" json:"bind_status"`
	CreateTime int    `xorm:"not null INT(11) 'create_time'" json:"create_time"`
	UpdateTime int    `xorm:"not null INT(11) 'update_time'" json:"update_time"`
}

func (*AccountinfoTpIos) TableName() string {
	return "accountinfo_tp_ios"
}

type aTpIos struct{}

// TpAaTpIos 外部调用静态变量
var TpAaTpIos aTpIos

func (*aTpIos) GetByOpenIDBind(uid int64) *AccountinfoTpIos {
	var table AccountinfoTpIos
	ok, err := usercenter.GetEngine().Where("uid = ? AND bind_status=?",
		uid, library.DataStatusEnum.Valid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
