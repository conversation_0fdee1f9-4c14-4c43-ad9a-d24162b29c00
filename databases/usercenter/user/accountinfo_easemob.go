package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// AccountInfoAuth 	用户微信信息表
type AccountEasemob struct {
	ID         int64  `xorm:"'id'"`
	UID        int64  `xorm:"'uid'"`
	Password   string `xorm:"'password'"`    // md5(uid)
	HxType     int64  `xorm:"'type'"`        // 1-环信
	CreateTime int64  `xorm:"'create_time'"` // 创建时间
	UpdateTime int64  `xorm:"'update_time'"` // 修改时间
}

type accountEasemob struct{}

var TbAccountEasemobd accountEasemob

// TableName
func (*AccountEasemob) TableName() string {
	return "accountinfo_easemob"
}

// 获取用户注册信息
func (*accountEasemob) GetUserHXInfo(uid, hxType int64) (*AccountEasemob, error) {
	var table AccountEasemob
	ok, err := usercenter.GetEngine().Where("uid =? AND type=?", uid, hxType).Get(&table)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &table, nil
}

// 记录用户注册信息
func (*accountEasemob) SetUserHXInfo(uid int64, password string, hxType int64) (int64, error) {
	var table AccountEasemob
	table.UID = uid
	table.Password = password
	table.HxType = hxType
	table.CreateTime = time.Now().Unix()
	table.UpdateTime = table.CreateTime
	insertID, err := usercenter.GetEngineMaster().Insert(&table)
	return insertID, err
}
