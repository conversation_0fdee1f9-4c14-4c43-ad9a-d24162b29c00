package birthdaywish

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// LikeRecord 生日点赞记录表
type LikeRecord struct {
	// 许愿ID
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 点赞人uid
	UID int64 `xorm:"not null int(11) unsigned 'uid'"`
	// 被点赞人uid
	Tid int64 `xorm:"not null int(11) unsigned 'tid'"`
	// 帖子id
	PostID int64 `xorm:"not null int(11) unsigned 'post_id'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) unsigned 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) unsigned 'update_time'"`
}

// TableName 获取表名
func (LikeRecord) TableName() string {
	return "activity_birthday_like_record"
}

type likeRecord struct {
}

var TbLikeRecord likeRecord

// buildSql 拼接查询sql
func (l *likeRecord) buildSQL(beginTime, endTime int32) string {
	condSQL := ""

	if beginTime > 0 {
		condSQL += fmt.Sprintf("create_time >= %d", beginTime)
	}

	if endTime > 0 {
		condSQL += fmt.Sprintf(" And create_time < %d", endTime)
	}

	return condSQL
}

func (l *likeRecord) GetCount(uid int64, beginTime, endTime int32) int64 {
	condSQL := l.buildSQL(beginTime, endTime)

	total, err := usercenter.GetEngine().Where("uid = ?", uid).Where(condSQL).Count(&LikeRecord{})
	if err != nil {
		logger.Warnf("查询用户生日点赞次数失败, err: %s", err)
		return 0
	}
	return total
}
