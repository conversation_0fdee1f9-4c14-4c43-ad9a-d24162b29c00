package note

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// List 用户记事本内容
type List struct {
	// 自增id
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 用户uid
	UID int64 `xorm:"not null int(11) unsigned 'uid'"`
	// 记录本内容类型 1-帖子 2-动态
	Type int32 `xorm:"not null tinyint(4) unsigned 'type'"`
	// 记录本关联类型内容id
	ObjID string `xorm:"not null varchar(64) 'obj_id'"`
	// 记录本关联内容
	Content string `xorm:"not null default '{}' varchar(256) 'content'"`
	// 状态 1-正常 2-删除 3-隐藏 4-设私
	Status int `xorm:"not null default '1' tinyint(3) unsigned 'status'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) unsigned 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) unsigned 'update_time'"`
}

// TableName 获取表名
func (l *List) TableName() string {
	return fmt.Sprintf("user_notebook_list_%d", l.UID%32)
}

// Save 新增记录
func (l *List) Save() error {
	_, err := usercenter.GetEngineMaster().Insert(l)
	return err
}

// Update 更新记录
func (l *List) Update() error {
	l.UpdateTime = time.Now().Unix()

	_, err := usercenter.GetEngineMaster().ID(l.ID).Update(l)
	return err
}

type list struct {
}

var TbList list

// GetItem 获取记录本内容详情
func (l *list) GetItem(uid int64, objID string, objType int32) (*List, error) {
	var table List

	table.UID = uid
	ok, err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ?", uid).
		Where("obj_id = ?", objID).Where("type = ?", objType).Get(&table)
	if err != nil {
		logger.Warnf("获取记录本内容信息失败, err: %s", err)
		return nil, err
	}
	if !ok {
		return nil, nil
	}

	return &table, nil
}

// GetCount 获取用户记录本总数
func (l *list) GetCount(uid int64, status []int) int64 {
	var table List

	table.UID = uid
	count, err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ? AND type = ?", uid, 1).
		In("status", status).Count(&table)
	if err != nil {
		logger.Warnf("获取记录本总数失败, err: %s", err)
		return 0
	}
	return count
}

// GetListBatch 批量获取用户记录本资源信息
func (l *list) GetListBatch(uid int64, objIDList []string, objType int32) []List {
	var tables []List
	var table List

	table.UID = uid
	err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ?", uid).In("obj_id", objIDList).Find(&tables)
	if err != nil {
		logger.Warnf("获取用户记录本列表失败, err: %s", err)
		return nil
	}

	return tables
}

// GetList 分页获取用户记录本列表
func (l *list) GetList(uid int64, page, pageSize int) []List {
	var tables []List
	var table List

	table.UID = uid
	err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Errorf("获取用户记录本列表失败, err: %s", err)
		return nil
	}

	return tables
}

// GetListByTimeRange 根据时间区间获取记录本内容列表
func (l *list) GetListByTimeRange(uid, beginTime, endTime int64, status []int) []List {
	var tables []List
	var table List

	table.UID = uid
	err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ? AND type = ?", uid, 1).
		Where("create_time >= ? AND create_time < ?", beginTime, endTime).
		In("status", status).Find(&tables)
	if err != nil {
		logger.Warnf("获取用户记录本内容列表失败, err: %s", err)
		return nil
	}

	return tables
}

func (l *list) GetLastestItem(uid int64, status []int) (*List, error) {
	var table List

	table.UID = uid
	ok, err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ? AND type = ?", uid, 1).
		In("status", status).Asc("create_time").Limit(1, 0).Get(&table)
	if err != nil {
		logger.Warnf("获取用户最早记录失败, err: %s", err)
		return nil, err
	}
	if !ok {
		return nil, nil
	}

	return &table, nil
}
