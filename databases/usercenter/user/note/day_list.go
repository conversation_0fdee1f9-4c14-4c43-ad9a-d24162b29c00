package note

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// DayList 用户记事本日历表
type DayList struct {
	// 自增id
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 用户uid
	UID int64 `xorm:"not null int(11) unsigned 'uid'"`
	// 用户记录时间 格式19700101
	DateIndex string `xorm:"not null default '' char(8) 'date_index'"`
	// 记录月份 格式197001
	MonthIndex string `xorm:"not null default '' char(6) '' 'month_index'"`
	// 状态 1-有记录 2-无记录
	Status int `xorm:"not null default '1' tinyint(3) unsigned 'status'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) unsigned 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) unsigned 'update_time'"`
}

// TableName 获取表名
func (d DayList) TableName() string {
	return fmt.Sprintf("user_notebook_day_list_%d", d.UID%32)
}

// Save 新增记录
func (d DayList) Save() error {
	_, err := usercenter.GetEngineMaster().Insert(d)
	return err
}

// Update 更新记录
func (d DayList) Update() error {
	d.UpdateTime = time.Now().Unix()

	_, err := usercenter.GetEngineMaster().ID(d.ID).Update(d)
	return err
}

type dayList struct {
}

var TbDayList dayList

// GetItem 获取用户记录本打卡记录
func (d *dayList) GetItem(uid int64, dateIndex string) (*DayList, error) {
	var table DayList

	table.UID = uid
	ok, err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ? AND date_index = ?", uid, dateIndex).
		Get(&table)
	if err != nil {
		logger.Warnf("获取用户记录本打卡记录失败, err: %s", err)
		return nil, err
	}

	if !ok {
		return nil, nil
	}

	return &table, nil
}

// GetListByPage 按照月份区间获取
func (d *dayList) GetListByMonthRange(uid int64, beginMonthIndex, endMonthIndex string) ([]DayList, error) {
	var tables []DayList
	var table DayList

	table.UID = uid
	err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ? AND month_index >= ? AND month_index <= ?",
		uid, beginMonthIndex, endMonthIndex).Desc("date_index").Find(&tables)
	if err != nil {
		logger.Errorf("获取用户记录本日期失败, err: %s", err)
		return nil, err
	}

	return tables, nil
}

// GetListByPage 分页获取
func (d *dayList) GetListByPage(uid int64, monthIndex string, page, pageSize int) ([]DayList, error) {
	var tables []DayList
	var table DayList

	table.UID = uid
	offSize := (page - 1) * pageSize
	err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ? AND month_index = ? AND status = ?",
		uid, monthIndex, 1).Desc("date_index").Limit(pageSize, offSize).Find(&tables)
	if err != nil {
		logger.Errorf("获取用户记录本日期失败, err: %s", err)
		return nil, err
	}

	return tables, nil
}

// GetCount 获取用户记录总天数
func (d *dayList) GetCount(uid int64) int32 {
	var table DayList

	table.UID = uid
	count, err := usercenter.GetEngine().Table(table.TableName()).Where("uid = ? AND status = ?", uid, 1).Count(&table)
	if err != nil {
		return 0
	}

	return int32(count)
}

// UpdateRecordStatus 更新用户记录本打卡状态
func (d *dayList) UpdateRecordStatus(uid int64, dateIndex string, status int) error {
	var table DayList

	table.UID = uid
	sql := fmt.Sprintf("UPDATE %s SET status = %d, update_time = %d WHERE uid = %d AND date_index = %s",
		table.TableName(), status, time.Now().Unix(), uid, dateIndex)
	_, err := usercenter.GetEngineMaster().Exec(sql)
	return err
}
