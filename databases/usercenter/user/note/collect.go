package note

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
)

// Collect 用户记事本
type Collect struct {
	// 自增id
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 用户uid
	UID int64 `xorm:"not null int(11) unsigned 'uid'"`
	// 总打卡天数
	TotalRecordDay int32 `xorm:"not null int(11) unsigned 'total_record_day'"`
	// 总记录数
	TotalRecordCount int32 `xorm:"not null int(11) unsigned 'total_record_count'"`
	// 开启状态 1-开启 2-未开启
	JoinStatus int32 `xorm:"default '2' not null tinyint(3) unsigned 'join_status'"`
	// 隐私状态 1-公开 2-隐私
	PrivacyStatus int32 `xorm:"default '1' not null tinyint(3) unsigned 'privacy_status'"`
	// 用户最近发帖图片
	ImageList string `xorm:"default '{}' varchar(512) 'image_list'"`
	// 开启时间
	JoinTime int64 `xorm:"default '0' not null int(11) unsigned 'join_time'"`
	// 是否可以同步到达人记录本
	SyncRankList int32 `xorm:"default '0' not null tinyint(1) unsigned 'sync_rank_list'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) unsigned 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) unsigned 'update_time'"`
}

// TableName 获取表名
func (c *Collect) TableName() string {
	return fmt.Sprintf("user_notebook_collect_%d", c.UID%32)
}

// Save 插入记录
func (c *Collect) Save() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime

	_, err := usercenter.GetEngineMaster().Insert(c)
	return err
}

// Update 更新记录
func (c *Collect) Update() error {
	c.UpdateTime = time.Now().Unix()

	_, err := usercenter.GetEngineMaster().ID(c.ID).
		MustCols("total_record_day", "total_record_count", "sync_rank_list").Update(c)
	return err
}

type collect struct {
}

var TbCollect collect

// GetItem 获取用户记录本信息
func (c *collect) GetItem(uid int64) (*Collect, error) {
	var table Collect

	table.UID = uid
	ok, err := usercenter.GetEngine().Where("uid = ?", uid).Get(&table)
	if err != nil {
		logger.Errorf("获取用户记录本记录失败, err: %s", err)
		return nil, err
	}
	if !ok {
		return nil, nil
	}

	return &table, nil
}

// GetListByPage 分页获取记录
func (c *collect) GetTopList(tableIndex int64, pageSize int) ([]Collect, error) {
	var tables []Collect
	var table Collect

	table.UID = tableIndex
	err := usercenter.GetEngine().Table(table.TableName()).Where("sync_rank_list = ?", 1).
		Desc("total_record_day").Limit(pageSize).Find(&table)
	if err != nil {
		logger.Warnf("查询记录本信息失败, err: %s", err)
		return nil, err
	}

	return tables, nil
}
