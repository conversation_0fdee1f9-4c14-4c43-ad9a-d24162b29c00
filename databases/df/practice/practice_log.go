package practice

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// Log 用户练习记录
type Log struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid'"`
	// 程序ID
	ProgramID int64 `xorm:"not null int(11) 'program_id'"`
	// 课程ID
	SessionID int64 `xorm:"not null int(11) 'session_id'"`
	// 第几天
	OrderDay int `xorm:"not null tinyint(4) 'order_day'"`
	// 卡路里
	Calories int `xorm:"not null int(11) 'calories'"`
	// 练习时长(min)
	Minutes int `xorm:"not null int(11) 'minutes'"`
	// 是否中途退出：1-是，0-否
	IsExit int `xorm:"not null tinyint(1) 'is_exit'"`
	// 是否内部课程：1-是，2-否
	IsInternal int `xorm:"not null tinyint(1) 'is_internal'"`
	// 上报类型
	ReportType int `xorm:"not null int(11) 'report_type'"`
	// 练习时长(s)
	PlayTime int `xorm:"not null int(11) 'play_time'"`
	// 练习开始时间
	PracticeStartTime int64 `xorm:"not null int(11) 'practice_start_time'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (Log) TableName() string {
	return "user_practice_log"
}

// Save 保存数据
func (p *Log) Save() error {
	p.CreateTime = time.Now().Unix()
	p.UpdateTime = p.CreateTime

	_, err := databases.GetDanceFit().GetEngineMaster().Insert(p)
	return err
}

// Update 更新数据
func (p *Log) Update() error {
	p.UpdateTime = time.Now().Unix()

	_, err := databases.GetDanceFit().GetEngineMaster().ID(p.ID).Update(p)
	return err
}

type practiceLog struct{}

// TbPracticeLog 外部引用对象
var TbPracticeLog practiceLog

// GetRecentPracticeList 获取用户最近练习记录
func (p *practiceLog) GetRecentPracticeList(uid int64, limit int) []Log {
	var tables []Log
	err := databases.GetDanceFit().GetEngine().
		Select("distinct(session_id) as session_id,id").
		Where("uid = ?", uid).
		Where("is_internal = ?", library.Yes).
		Desc("id").
		Limit(limit, 0).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetLogByRange 根据时间区间获取
func (p *practiceLog) GetLogByRange(uid, start, end, page, pageSize int64) []*Log {
	var tables []*Log
	offset := (page - 1) * pageSize
	err := databases.GetDanceFit().GetEngine().
		Where("uid = ?", uid).
		Where("practice_start_time > ? and practice_start_time < ?", start, end).
		Desc("practice_start_time").
		Limit(int(pageSize), int(offset)).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (p *practiceLog) GetItem(uid, practiceStartTime int64) *Log {
	var table Log
	ok, err := databases.GetDanceFit().GetEngine().
		Where("uid = ? AND practice_start_time = ?", uid, practiceStartTime).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

type SearchQuery struct {
	UID               int64
	PracticeStartTime int64
	ProgramID         int64
	SessionID         int64
	IsInternal        int
}

func (p *practiceLog) GetItemByCond(r *SearchQuery) *Log {
	var table Log
	session := databases.GetDanceFit().GetEngine().NewSession()
	defer session.Close()
	session = session.Table(table.TableName()).Where("uid = ?", r.UID)
	if r.PracticeStartTime > 0 {
		session = session.And("practice_start_time = ?", r.PracticeStartTime)
	}
	if r.ProgramID > 0 {
		session = session.And("program_id = ?", r.ProgramID)
	}
	if r.SessionID > 0 {
		session = session.And("session_id = ?", r.SessionID)
	}
	if r.IsInternal > 0 {
		session = session.And("is_internal = ?", r.IsInternal)
	}
	ok, err := session.Get(&table)
	if err != nil {
		logger.Error(err)
	}
	if !ok {
		return nil
	}
	return &table
}
