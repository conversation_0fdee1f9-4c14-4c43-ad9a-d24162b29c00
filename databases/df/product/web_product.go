package product

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type WebProduct struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'" json:"id"`
	// 商品名称
	Name string `xorm:"not null varchar(255) 'name'" json:"name"`
	// 产品主类型 1 会员 2 精选会员卡
	ProductMainType int `xorm:"not null tinyint(4) 'product_main_type'" json:"product_main_type"`
	// 产品类型
	ProductType int `xorm:"not null tinyint(4) 'product_type'" json:"product_type"`
	// 支付方式
	PayType string `xorm:"not null varchar(24) 'pay_type'" json:"pay_type"`
	// 终端类型
	TerminalType int32 `xorm:"not null tinyint(4) 'terminal_type'" json:"terminal_type"`
	// 会员时长类型 1天2月3年
	DurationType int `xorm:"not null tinyint(4) 'duration_type'" json:"duration_type"`
	// 会员时长
	DurationValue int `xorm:"not null int(6) 'duration_value'" json:"duration_value"`
	// 价格
	Price float64 `xorm:"not null decimal(10,2) 'price'" json:"price"`
	// 价格
	OriginPrice float64 `xorm:"not null decimal(10,2) 'origin_price'" json:"origin_price"`
	// 是否订阅 1 订阅产品 2非订阅产品
	IsSubscribe int `xorm:"not null tinyint(1) 'is_subscribe'" json:"is_subscribe"`
	// 订阅说明
	SubscribeDesc string `xorm:"not null varchar(512) 'subscribe_desc'" json:"subscribe_desc"`
	// IOS产品
	IOSProductID string `xorm:"not null varchar(64) 'ios_product_id'" json:"ios_product_id"`
	// 默认非订阅产品ID(老版本不支持订阅产品时替换)
	DefaultSkuID int `xorm:"not null int(10) 'default_sku_id'" json:"default_sku_id"`
	// 是否删除 1 删除 2删除
	IsDel int `xorm:"not null tinyint(1) 'is_del'" json:"is_del"`
	// 优惠类型：1-无 2-首购 3-试用 4-试用+首购
	OfferType int32 `xorm:"not null default 1 TINYINT(1) 'offer_type'" json:"offer_type"`
	// 首购优惠价
	OfferFirstBuyPrice float64 `xorm:"default 0.00 DECIMAL(10,2) 'offer_first_buy_price'" json:"offer_first_buy_price"`
	// 首购优惠周期
	OfferFirstBuyCycle int32 `xorm:"not null default 0 TINYINT(1) 'offer_first_buy_cycle'" json:"offer_first_buy_cycle"`
	// 试用价格
	OfferTrialPrice float64 `xorm:"default 0.00 DECIMAL(10,2) 'offer_trial_price'" json:"offer_trial_price"`
	// 试用天数
	OfferTrialDay int32 `xorm:"not null default 0 INT(5) 'offer_trial_day'" json:"offer_trial_day"`
	// 是否增值 1 是 2否
	IsIncValue int `xorm:"not null tinyint(1) 'is_inc_value'" json:"is_inc_value"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'" json:"update_time"`
	// 是否有赠品 1是 2否
	HasGift int32 `xorm:"not null int(11) 'has_gift'" json:"has_gift"`
	// 赠品信息
	GiftInfo string `xorm:"not null varchar(4096) 'gift_info'" json:"gift_info"`
	// 抖音APPID
	TiktokAppID string `xorm:"not null varchar(512) 'tiktok_appid'" json:"tiktok_appid"`
	// 抖音自动订阅模版ID
	TiktokSubID string `xorm:"not null varchar(512) 'tiktok_sub_id'" json:"tiktok_sub_id"`
	// hms产品
	HMSProductID string `xorm:"not null varchar(64) 'hms_product_id'" json:"hms_product_id"`
}

// TableName 获取表名
func (WebProduct) TableName() string {
	return "web_product"
}

// Save 保存数据
func (a *WebProduct) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetDanceFit().GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *WebProduct) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetDanceFit().GetEngineMaster().ID(a.ID).Update(a)
	return err
}

type webProduct struct{}

// TbWebProduct 外部引用对象
var TbWebProduct webProduct

// GetItemByID 通过ID获取详情
func (a *webProduct) GetItemByID(id int64) *WebProduct {
	var table WebProduct

	ok, err := databases.GetDanceFit().GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByID 通过ID获取详情
func (a *webProduct) GetItemByIOSID(iosProductID string) *WebProduct {
	var table WebProduct

	ok, err := databases.GetDanceFit().GetEngine().Where("ios_product_id = ?", iosProductID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByHMSID 通过ID获取详情
func (a *webProduct) GetItemByHMSID(hmsProductID string) *WebProduct {
	var table WebProduct

	ok, err := databases.GetDanceFit().GetEngine().Where("hms_product_id = ?", hmsProductID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetList 获取商品列表
func (a *webProduct) GetList() []WebProduct {
	var tables []WebProduct
	err := databases.GetDanceFit().GetEngine().Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetList 获取商品列表
func (a *webProduct) GetListBySubscribe(isSubscribe int) []WebProduct {
	var tables []WebProduct
	err := databases.GetDanceFit().GetEngine().Where("is_subscribe=?", isSubscribe).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetAllProductMap 获取所有的商品列表
func (a *webProduct) GetAllProductMap() map[int64]*WebProduct {
	var tables []*WebProduct
	err := databases.GetDanceFit().GetEngine().Where("is_del=?", library.No).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if len(tables) == 0 {
		return nil
	}
	res := make(map[int64]*WebProduct)
	for _, v := range tables {
		res[v.ID] = v
	}
	return res
}
