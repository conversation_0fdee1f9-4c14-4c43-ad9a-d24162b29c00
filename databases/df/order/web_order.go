package order

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
)

type WebOrder struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户UID
	UID int64 `xorm:"not null int(11) 'uid'"`
	// 订单ID
	OrderID string `xorm:"not null varchar(64) 'order_id'"`
	// 商品ID
	ProductID int64 `xorm:"not null int(10) 'product_id'"`
	// 订单状态 0 待支付 1 已支付
	OrderStatus int `xorm:"not null tinyint(1) 'order_status'"`
	// 订单金额
	OrderAmount float64 `xorm:"not null decimal(10,2) 'order_amount'"`
	// 支付方式
	PayType int `xorm:"not null tinyint(4) 'pay_type'"`
	// 支付商户号
	MchID string `xorm:"not null varchar(128) 'mch_id'"`
	// 支付时间
	PayTime int64 `xorm:"not null int(11) 'pay_time'"`
	// 是否续订 1 是 2 否
	IsRenew int `xorm:"not null tinyint(1) 'is_renew'"`
	// 订单来源
	Source        int    `xorm:"not null int(11) 'source'"`
	SourceID      string `xorm:"not null varchar(128) 'source_id'"`
	SourceRefer   int    `xorm:"not null int(11) 'source_refer'"`
	SourceReferID string `xorm:"not null varchar(128) 'source_refer_id'"`
	// 客户端版本
	Version string `xorm:"not null varchar(32) 'app_version'"`
	// 客户端渠道号
	Channel int `xorm:"not null int(11) 'app_channel'"`
	// 订单类型
	PaymentOrderType int `xorm:"not null int(11) 'payment_order_type'"`
	// 子产品
	ProductSubID int64 `xorm:"not null int(11) 'product_sub_id'"`
	// 关联挑战赛产品ID
	ChallengeID int64 `xorm:"not null int(11) 'challenge_id'"`
	// 是否有赠品 1是 2否
	HasGift int32 `xorm:"not null int(11) 'has_gift'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (WebOrder) TableName() string {
	return "web_order"
}

type tbwebOrder struct{}

var TbWebOrder tbwebOrder

func (tbwebOrder) GetItemByOrderID(orderID string) *WebOrder {
	var table WebOrder

	ok, err := databases.GetDanceFit().GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
