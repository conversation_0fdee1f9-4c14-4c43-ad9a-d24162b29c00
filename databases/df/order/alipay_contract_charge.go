package order

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type AlipayContractCharge struct {
	ID      int64  `xorm:"not null pk autoincr INT(11) 'id'"`
	OrderID string `xorm:"not null varchar(128) 'order_id'"`
	// 商品ID
	ProductID int64 `xorm:"not null int(10) 'product_id'"`
	// 用户UID
	UID int64 `xorm:"not null bigint(20) 'uid'"`
	// 商户签约号
	ContractCode string `xorm:"not null varchar(50) 'contract_code'"`
	// 用户签约成功后协议号
	ContractID string `xorm:"not null varchar(100) 'contract_id'"`
	// 扣款时间
	ChargeDate string `xorm:"not null varchar(10) 'charge_date'"`
	// 状态，0, 未支付，1：支付成功
	Status int `xorm:"not null tinyint(3) 'status'"`
	// 订单金额
	OrderAmount float64 `xorm:"not null decimal(10,2) 'order_amount'"`
	// 原始订单金额
	OriginalOrderAmount float64 `xorm:"not null decimal(10,2) 'original_order_amount'"`
	// 阶梯金额
	StepAmount float64 `xorm:"not null decimal(10,2) 'step_amount'"`
	// 是否阶梯价格, 1 是 2 否
	IsStepPrice int `xorm:"not null tinyint(1) 'is_step_price'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (AlipayContractCharge) TableName() string {
	return "web_order_alipay_contract_charge"
}

// Save 保存数据
func (a *AlipayContractCharge) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetDanceFit().GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *AlipayContractCharge) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetDanceFit().GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *AlipayContractCharge) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

type alipayCharge struct{}

var TbAlipayCharge alipayCharge

func (*alipayCharge) GetLastChargeByCode(contractCode string) *AlipayContractCharge {
	var table AlipayContractCharge
	ok, err := databases.GetDanceFit().GetEngine().Where("contract_code = ?", contractCode).OrderBy("id desc").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*alipayCharge) GetItemByOrderID(orderID string) *AlipayContractCharge {
	var table AlipayContractCharge
	ok, err := databases.GetDanceFit().GetEngine().Where("order_id = ?", orderID).OrderBy("id desc").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetChargeListByRange 获取用户扣款记录
func (*alipayCharge) GetChargeListByRange(start, end int64, contractCode string) []*AlipayContractCharge {
	tables := make([]*AlipayContractCharge, 0)
	err := databases.GetDanceFit().GetEngine().Where("contract_code = ?", contractCode).
		And("create_time >= ? and create_time <= ?", start, end).OrderBy("id asc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetValidChargeByCode 根据签约号获取所有的扣款记录
func (*alipayCharge) GetValidChargeByCode(contractCode string, maxID int64) []*AlipayContractCharge {
	tables := make([]*AlipayContractCharge, 0)
	err := databases.GetDanceFit().GetEngine().Where("contract_code = ? and status = ? and id < ?",
		contractCode, library.Yes, maxID).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*alipayCharge) GetLastSuccessCharge(contractCode string, maxID int64) *AlipayContractCharge {
	var table AlipayContractCharge
	ok, err := databases.GetDanceFit().GetEngine().Where("contract_code = ? and id < ?",
		contractCode, maxID).And("status = ?", library.Yes).OrderBy("id desc").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetListByIDRange 根据id区间获取扣款记录
func (*alipayCharge) GetListByIDRange(contractCode string, minID, maxID int64) []*AlipayContractCharge {
	tables := make([]*AlipayContractCharge, 0)
	err := databases.GetDanceFit().GetEngine().Where("contract_code = ?", contractCode).
		And("id > ? and id < ?", minID, maxID).OrderBy("id asc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetListByUID 根据UID获取扣款记录
func (*alipayCharge) GetListByUID(uid int64) []*AlipayContractCharge {
	tables := make([]*AlipayContractCharge, 0)
	err := databases.GetDanceFit().GetEngine().Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*alipayCharge) GetSuccessOrderByOrderID(orderID string) *AlipayContractCharge {
	var table AlipayContractCharge
	ok, err := databases.GetDanceFit().GetEngine().Where("order_id = ? AND status = ?", orderID, library.Yes).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*alipayCharge) GetOtherSuccessOrder(orderID, contractCode string) *AlipayContractCharge {
	var table AlipayContractCharge
	ok, err := databases.GetDanceFit().GetEngine().Where("order_id != ? AND contract_code = ? AND status = ?",
		orderID, contractCode, library.Yes).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
