package cron

import (
	"context"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/user"
)

const CalcExpireUserLableLock = "Lock:Calc:Expire:User:Lable"

func CalcExpireUserLable() {
	rdl := cache.GetLockRedis()
	ctx := context.Background()
	if lock, err := rdl.SetNX(ctx, CalcExpireUserLableLock, 1, 48*time.Hour).Result(); err != nil {
		logger.Error(err)
		return
	} else if !lock {
		logger.Info("重新计算标签过期用户任务已被锁定")
		return
	}
	defer func() {
		if err := rdl.Del(ctx, CalcExpireUserLableLock).Err(); err != nil {
			logger.Error(err)
		}
	}()
	logger.Info("重新计算标签过期用户任务")
	// 处理昨天的数据
	keyYesterday := UserLableExpireUserKey + time.Unix(time.Now().Unix()-86400, 0).Format("20060102")
	calcExpireByKey(keyYesterday)
}

func calcExpireByKey(key string) {
	rd := cache.GetYogaRedis()
	ctx := context.Background()
	exist, err := rd.Exists(ctx, key).Result()
	if err != nil {
		return
	}
	if exist <= 0 {
		return
	}
	var count int
	defer func(int) {
		logger.Info("计算过期标签用户数量", key, count)
	}(count)
	for {
		uidStr, err := rd.SPop(ctx, key).Result()
		if err != nil {
			logger.Warn(err, key)
			return
		}
		uid, err := strconv.ParseInt(uidStr, 10, 64)
		if err != nil {
			logger.Warn(err, key)
			return
		}
		if uid <= 0 {
			logger.Warn("uid解析错误", uidStr, key)
			return
		}
		_ = user.ServiceUserLabel.CalcUserLabel(uid, int32(0))
		count++
		time.Sleep(time.Millisecond * 50)
	}
}
