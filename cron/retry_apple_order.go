package cron

import (
	"context"
	"encoding/json"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/h2-artifact/delayqueue"

	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/order"
)

func RetryAppleOrder() {
	// try consume
	ctx, cancel := context.WithCancel(context.Background())
	resCh, errCh := delayqueue.GetFromQueue(ctx, cache.GetYogaRedis(), library.Order4IosIapQueueKey)
	isStop := false
	for {
		select {
		case msg, ok := <-resCh:
			if !ok {
				isStop = true
				break
			}
			var payload order.CreateOrder4IosIapPayload
			err := json.Unmarshal([]byte(msg), &payload)
			if err != nil {
				logger.Info("RetryAppleOrder 解析出错了", msg, err.Error())
				continue
			}
			order.ServiceIosOrder.CreateOrder4IosIap(&payload)
			if err != nil {
				logger.Warn("RetryAppleOrder 延迟队列报错", err.Error())
			}
		case <-errCh:
		}
		if isStop {
			cancel()
			break
		}
	}
}
