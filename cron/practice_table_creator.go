package cron

import (
	"fmt"
	"strconv"
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/playtime"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/sessionplay"
	lib "gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/practice"
)

// PreparePracticeTable 创建练习数据相关表
func PreparePracticeTable() {
	dataSource := config.Get().DBStatRDS.DDL.String()
	engine, err := xorm.NewEngine("mysql", dataSource)
	if err != nil {
		logger.Error(err)
		return
	}
	defer func() {
		err := engine.Close()
		if err != nil {
			logger.Error(err)
		}
	}()

	curDay := time.Now().Format("2006-01-02")
	curT, _ := time.ParseInLocation("2006-01-02", curDay, time.Local)
	timeStart := curT.Unix()
	week := practice.GetWeekCount(timeStart)

	c := creator{}

	// 创建st_session_play_xx
	c.createSessionPlay(engine, timeStart)
	// 创建st_play_time_week_xx
	c.createPlayTimeWeek(engine, week)
	// 创建st_play_time_month_xx
	c.createPlayTimeMonth(engine, timeStart)
	// 创建st_play_time_year_xx
	c.createPlayTimeYear(engine, timeStart)
	// 创建st_practice_days_week_xx
	c.createPracticeDaysWeek(engine, week)
	// 创建st_practice_days_month_xx
	c.createPracticeDaysMonth(engine, timeStart)
	// 创建st_practice_days_year_xx
	c.createPracticeDaysYear(engine, timeStart)
	// 创建st_user_practice_xx
	c.createUserPractice(engine, timeStart)
	// 创建st_user_practice_feedback_xx
	c.createUserPracticeFeedback(engine, timeStart)
	logger.Debug("prepare create db-stat-new table success")
}

type creator struct{}

func (c *creator) createSessionPlay(engine *xorm.Engine, timeStart int64) {
	for i := 0; i < 3; i++ {
		t := &sessionplay.Detail{
			ReportTime: timeStart + int64(86400*i),
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			day := time.Unix(timeStart+int64(86400*i), 0).Format(lib.DateFormatDay)
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '课程播放日志表%s'", t.TableName(), day)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}
func (c *creator) createPlayTimeWeek(engine *xorm.Engine, week int64) {
	for i := 0; i < 3; i++ {
		t := &playtime.Week{
			Week: strconv.Itoa(i + int(week)),
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '用户练习时长按周统计表第%s周'", t.TableName(), t.Week)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}
func (c *creator) createPlayTimeMonth(engine *xorm.Engine, timeStart int64) {
	for i := 0; i < 100; i += 30 {
		dateIndex := time.Unix(timeStart+int64(i*86400), 0).Format(lib.DateFormatMonth)
		t := &playtime.Month{
			DateIndex: dateIndex,
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '用户练习时长按月统计表%s'", t.TableName(), dateIndex)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}
func (c *creator) createPlayTimeYear(engine *xorm.Engine, timeStart int64) {
	for i := 0; i < 100; i += 30 {
		dateIndex := time.Unix(timeStart+int64(i*86400), 0).Format(lib.DateFormatYear)
		t := &playtime.Year{
			DateIndex: dateIndex,
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '用户练习时长按年统计表%s'", t.TableName(), dateIndex)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}
func (c *creator) createPracticeDaysWeek(engine *xorm.Engine, week int64) {
	for i := 0; i < 3; i++ {
		t := &db.Week{
			Week: strconv.Itoa(i + int(week)),
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '用户练习天数按周统计表第%s周'", t.TableName(), t.Week)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}
func (c *creator) createPracticeDaysMonth(engine *xorm.Engine, timeStart int64) {
	for i := 0; i < 100; i += 30 {
		dateIndex := time.Unix(timeStart+int64(i*86400), 0).Format(lib.DateFormatMonth)
		t := &db.Month{
			DateIndex: dateIndex,
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '用户练习天数按月统计表%s'", t.TableName(), dateIndex)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}
func (c *creator) createPracticeDaysYear(engine *xorm.Engine, timeStart int64) {
	for i := 0; i < 100; i += 30 {
		dateIndex := time.Unix(timeStart+int64(i*86400), 0).Format(lib.DateFormatYear)
		t := &db.Year{
			DateIndex: dateIndex,
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '用户练习天数按年统计表%s'", t.TableName(), dateIndex)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}
func (c *creator) createUserPractice(engine *xorm.Engine, timeStart int64) {
	for i := 0; i < 100; i += 30 {
		t := &db.UserPractice{
			ReportTime: timeStart + int64(i*86400),
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			month := time.Unix(timeStart+int64(i*86400), 0).Format(lib.DateFormatMonth)
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '用户练习日志按月统计表%s'", t.TableName(), month)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}
func (c *creator) createUserPracticeFeedback(engine *xorm.Engine, timeStart int64) {
	for i := 0; i < 100; i += 30 {
		dateIndex := time.Unix(timeStart+int64(i*86400), 0).Format(lib.DateFormatMonth)
		t := &db.UserPracticeFeedbackV2{
			DateIndex: dateIndex,
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '练习记录反馈表%s'", t.TableName(), dateIndex)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}
