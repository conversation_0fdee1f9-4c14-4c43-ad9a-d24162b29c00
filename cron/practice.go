package cron

import (
	"context"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/practice"
)

const CollectPracticeLockKey = "locking:db:collect"

// CollectPracticeData 习练历程数据定时汇总
func CollectPracticeData() {
	rd := cache.GetLockRedis()
	v := strconv.FormatInt(time.Now().Unix(), 10)
	ctx := context.Background()
	// 设置一个10分钟的锁，每个任务处理之前一直续期
	var expireMinutes time.Duration = 10
	if lock, err := rd.SetNX(ctx, CollectPracticeLockKey, v, expireMinutes*time.Minute).Result(); err != nil {
		logger.Error(err)
		return
	} else if !lock {
		return
	}
	defer func() {
		if err := rd.Del(ctx, CollectPracticeLockKey).Err(); err != nil {
			logger.Error(err)
		}
	}()
	itemList := make([]db.UserPractice, 0)
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	todayTime := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, loc)
	todayMonth := todayTime.Format("2006-01")
	yesterdayMonth := time.Unix(todayTime.Unix()-86400, 0).Format("2006-01")

	items := db.TbUserPractice.GetNotCollectList(todayTime.Unix() - 86400)
	itemList = append(itemList, items...)
	if todayMonth != yesterdayMonth {
		todayItems := db.TbUserPractice.GetNotCollectList(todayTime.Unix())
		itemList = append(itemList, todayItems...)
	}
	for index := range itemList {
		ok, err := rd.Expire(ctx, CollectPracticeLockKey, expireMinutes*time.Minute).Result()
		if !ok || err != nil {
			logger.Error("习练历程数据定时汇总锁续期失败，脚本退出", ok, err)
			return
		}
		item := itemList[index]
		practice.Collect.UpdateUserPractice(&item)
	}
}
