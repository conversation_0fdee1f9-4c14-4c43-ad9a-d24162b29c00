package cron

import (
	"fmt"
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/stat/top"
)

func PrePareDBStatTable() {
	timeNow := time.Now().Unix()
	dataSource := config.Get().DBStat.DDL.String()
	engine, err := xorm.NewEngine("mysql", dataSource)
	if err != nil {
		logger.Error(err)
		return
	}
	defer func() {
		err := engine.Close()
		if err != nil {
			logger.Error(err)
		}
	}()

	c := &dbStatTableCreator{}
	c.createTopClickDetail(engine, timeNow)
	c.createTopGetDetail(engine, timeNow)
	c.createTopHrefDetail(engine, timeNow)
	c.createTopShowDetail(engine, timeNow)
}

type dbStatTableCreator struct {
}

func (d *dbStatTableCreator) createTopClickDetail(engine *xorm.Engine, timeStart int64) {
	for i := 0; i < 100; i += 30 {
		t := &top.Click{
			CreateTime: timeStart + int64(i*86400),
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			month := time.Unix(timeStart+int64(i*86400), 0).Format("200601")
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '广告点击统计表%s'", t.TableName(), month)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}

func (d *dbStatTableCreator) createTopGetDetail(engine *xorm.Engine, timeStart int64) {
	for i := 0; i < 100; i += 30 {
		t := &top.Get{
			CreateTime: timeStart + int64(i*86400),
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			month := time.Unix(timeStart+int64(i*86400), 0).Format("200601")
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '广告拉取统计表%s'", t.TableName(), month)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}

func (d *dbStatTableCreator) createTopHrefDetail(engine *xorm.Engine, timeStart int64) {
	for i := 0; i < 100; i += 30 {
		t := &top.Href{
			CreateTime: timeStart + int64(i*86400),
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			month := time.Unix(timeStart+int64(i*86400), 0).Format("200601")
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '广告跳转展示统计表%s'", t.TableName(), month)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}

func (d *dbStatTableCreator) createTopShowDetail(engine *xorm.Engine, timeStart int64) {
	for i := 0; i < 100; i += 30 {
		t := &top.Show{
			CreateTime: timeStart + int64(i*86400),
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			month := time.Unix(timeStart+int64(i*86400), 0).Format("200601")
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '广告显示统计表%s'", t.TableName(), month)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}
