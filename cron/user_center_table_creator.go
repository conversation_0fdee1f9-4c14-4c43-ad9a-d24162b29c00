package cron

import (
	"fmt"
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/assess"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/push"
)

func PrepareUserCenterTable() {
	timeNow := time.Now().Unix()
	dataSource := config.Get().DBUserCenter.DDL.String()
	engine, err := xorm.NewEngine("mysql", dataSource)
	if err != nil {
		logger.Error(err)
		return
	}
	defer func() {
		err := engine.Close()
		if err != nil {
			logger.Error(err)
		}
	}()

	c := userCenterTableCreator{}

	c.createNoticePushDetail(engine, timeNow)
	c.createNoticePushTarget(engine, timeNow)
	c.createAssessLog(engine, timeNow)
}

type userCenterTableCreator struct {
}

func (u *userCenterTableCreator) createAssessLog(engine *xorm.Engine, timeStart int64) {
	for i := 0; i < 100; i += 30 {
		t := &assess.Log{
			CreateTime: timeStart + int64(i*86400),
		}

		if exist, _ := engine.IsTableExist(t); !exist {
			month := time.Unix(timeStart+int64(i*86400), 0).Format("200601")
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '测评问题选项表%s'", t.TableName(), month)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
			uniqueSQL := fmt.Sprintf("ALTER TABLE `%s` ADD UNIQUE KEY `UNIQ_AID_MID_DID_UID_TUID`"+
				" (`assess_id`,`module_id`,`detail_id`,`uid`,`to_user_id`)", t.TableName())
			if _, err := engine.Exec(uniqueSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}

func (u *userCenterTableCreator) createNoticePushDetail(engine *xorm.Engine, timeStart int64) {
	for i := 0; i < 100; i += 30 {
		t := &push.Detail{
			CreateTime: timeStart + int64(i*86400),
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			month := time.Unix(timeStart+int64(i*86400), 0).Format("200601")
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '消息记录按月表%s'", t.TableName(), month)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}

func (u *userCenterTableCreator) createNoticePushTarget(engine *xorm.Engine, timeStart int64) {
	for i := 0; i < 100; i += 30 {
		t := &push.Target{
			CreateTime: timeStart + int64(i*86400),
		}
		if exist, _ := engine.IsTableExist(t); !exist {
			month := time.Unix(timeStart+int64(i*86400), 0).Format("200601")
			if err := engine.CreateTables(t); err != nil {
				logger.Error(err)
				continue
			}
			if err := engine.CreateIndexes(t); err != nil {
				logger.Error(err)
				continue
			}
			commentSQL := fmt.Sprintf("ALTER TABLE `%s` COMMENT = '消息发送记录%s'", t.TableName(), month)
			if _, err := engine.Exec(commentSQL); err != nil {
				logger.Error(err)
				continue
			}
		}
	}
}
