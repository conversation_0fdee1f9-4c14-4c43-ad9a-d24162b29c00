package cron

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/group"
)

const UserLableLockKey = "Lock:Cron:User:Lable"
const UserLableExpireUserKey = "User:Lable:Expire:User"

func DeleteExpireLabelData() {
	rdl := cache.GetLockRedis()
	ctx := context.Background()
	if lock, err := rdl.SetNX(ctx, UserLableLockKey, 1, 10*time.Minute).Result(); err != nil {
		logger.Error(err)
		return
	} else if !lock {
		return
	}
	defer func() {
		if err := rdl.Del(ctx, UserLableLockKey).Err(); err != nil {
			logger.Error(err)
		}
	}()
	ids := make([]string, 0)
	expiredTime := time.Now().Unix() - 30*24*3600
	lastUserLabel := group.LastUserLabelData{}

	dataSource := config.Get().DBUserCenter.DML.String()
	engine, err := xorm.NewEngine("mysql", dataSource)
	if err != nil {
		logger.Error(err)
		return
	}

	defer func() {
		if err = engine.Close(); err != nil {
			logger.Error(err)
		}
	}()

	expiredDataList, err := group.TbLastUserLabelDataList.GetNeedDeleteDataList(expiredTime, 100)
	if err != nil || len(expiredDataList) == 0 {
		return
	}

	for _, item := range expiredDataList {
		ids = append(ids, strconv.FormatInt(item.ID, 10))
	}
	deleteExpiredDataSQL := fmt.Sprintf("DELETE FROM %s WHERE id in (%s)",
		lastUserLabel.TableName(), strings.Join(ids, ","))
	if _, err := engine.Exec(deleteExpiredDataSQL); err != nil {
		logger.Error(err)
		return
	}

	// 将uid插入当天的数据中
	key := UserLableExpireUserKey + time.Now().Format("20060102")
	rd := cache.GetYogaRedis()
	uids := make([]interface{}, 0)
	for _, item := range expiredDataList {
		if len(uids) <= 100 {
			uids = append(uids, item.UID)
		} else {
			rd.SAdd(ctx, key, uids...)
			uids = uids[:0]
		}
	}
	if len(uids) > 0 {
		rd.SAdd(ctx, key, uids...)
	}
	rd.Expire(ctx, key, 48*time.Hour)
}
