package partner

type SendHXPartnerSystemMsgPayload struct {
	GroupID         int64  `json:"group_id"`
	UID             int64  `json:"uid"`
	Content         string `json:"content"`
	TeamID          int64  `json:"team_id"`
	TeamName        string `json:"team_name"`
	TeamImage       string `json:"team_image"`
	IsSystemMessage int64  `json:"is_system_message"`
}

var HxAccountType = struct {
	HxType int64
}{
	HxType: 1,
}

var HXResponse = struct {
	ResourceNotFound     string
	UniquePropertyExists string
	ForbiddenOp          string
}{
	// 获取用户信息时：表示该用户不存在
	ResourceNotFound: "service_resource_not_found",
	// 注册环信时：表示用户已存在、用户名或密码为空、用户名不合法
	UniquePropertyExists: "duplicate_unique_property_exists",
	// 注册加入群组时：表示用户已在群组
	ForbiddenOp: "forbidden_op",
}

var HxSendRequest = struct {
	HxSendTargetType string
	HxFrom           string
	HxType           string
	HxExtPushTitle   string
}{
	HxSendTargetType: "chatgroups",
	HxFrom:           "service",
	HxType:           "txt",
	HxExtPushTitle:   "每日瑜伽提醒你收到新的消息",
}
