package filter

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/filter"
)

type tag struct {
}

var Tag tag

// GetTagListBySourceAndPID 获取指定类型的标签列表
func (tag) GetTagListBySourceAndPID(sourceType, sourceID int32, pid int64) []*filter.Tag {
	rd := cache.GetYogaRedis()
	cacheKey := fmt.Sprintf("GetTagList:sourceType:%d:sourceID:%d:pid:%d", sourceType, sourceID, pid)
	cacheValue, _ := rd.Get(context.Background(), cacheKey).Result()
	if cacheValue != "" {
		tagList := make([]*filter.Tag, 0)
		err := json.Unmarshal([]byte(cacheValue), &tagList)
		if err != nil {
			logger.Error(err)
		} else {
			return tagList
		}
	}

	list := filter.TbTagResource.GetTagResourceItemList([]int32{sourceID}, sourceType)
	if list == nil {
		return nil
	}
	tagList := make([]*filter.Tag, 0)
	for _, item := range list {
		if item.Pid != pid {
			continue
		}
		tagList = append(tagList, &filter.Tag{
			ID:       int32(item.ID),
			Pid:      int32(item.Pid),
			Name:     item.Title,
			SubTitle: item.SubTitle,
		})
	}

	if len(tagList) > 0 {
		bytes, _ := json.Marshal(tagList)
		rd.Set(context.Background(), cacheKey, bytes, 24*time.Hour)
	}
	return tagList
}
