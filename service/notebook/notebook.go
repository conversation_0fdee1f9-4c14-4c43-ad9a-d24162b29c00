package notebook

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	dbnote "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/note"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/yogaparadise"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type ObjInfo struct {
	ImageList  []string `json:"image_list"`
	CreateTime int64    `json:"create_time"`
}

type notebook struct {
}

type DynamicRecordInfo struct {
	Title     string   `json:"title"`
	Content   string   `json:"content"`
	ImageList []string `json:"image_list"`
	Type      int32    `json:"type"`
}

var ServiceNoteBook notebook

// getRankListSize 获取排行榜大小
func (n *notebook) getRankListSize() int {
	ctx := context.Background()
	noteBookRankListSize, err := cache.GetYogaRedis().ZCard(ctx, library.NoteBookRankListKey).Result()
	if err != nil {
		return 0
	}

	return int(noteBookRankListSize)
}

// updateRankListSize 更新排行行大小
func (n *notebook) updateRankListSize() {
	noteBookRankListSize := n.getRankListSize()
	needDeleteSize := noteBookRankListSize - library.NoteBookRankListSize
	if needDeleteSize <= 0 {
		return
	}
	ctx := context.Background()
	if err := cache.GetYogaRedis().ZRemRangeByRank(ctx,
		library.NoteBookRankListKey, 0, int64(needDeleteSize)-1).Err(); err != nil {
		logger.Warnf("更新达人记录本排行榜数量失败, err: %s", err)
	}
}

// getObjInfo 获取资源信息
func (n *notebook) GetObjInfo(objID string, objType int32) *ObjInfo {
	objInfo := &ObjInfo{}

	if objType == int32(library.NoteBookContentTypeEnum.Posts) {
		postID, err := strconv.ParseInt(objID, 10, 64)
		if err != nil {
			return nil
		}

		postInfo, err := yogaparadise.TbPosts.GetItem(postID)
		if err != nil {
			logger.Warnf("get obj info error: %s", err)
			return nil
		}
		if postInfo == nil {
			return nil
		}

		objInfo.ImageList = strings.Split(postInfo.Images, ",")
		objInfo.ImageList = objInfo.ImageList[:len(objInfo.ImageList)-1]
		objInfo.CreateTime = postInfo.Created
	}

	return objInfo
}

// UpdateNoteBookInfo 更新记录本内容信息
func (n *notebook) UpdateNoteBookInfo(noteBookInfo *dbnote.List,
	uid int64, objType int32, objID string, status int, createTime int64) (bool, error) {
	needUpdateNoteBookDay := true

	if noteBookInfo != nil && noteBookInfo.ID > 0 {
		if (noteBookInfo.Status == int(library.NoteBookContentStatusEnum.Normal) ||
			noteBookInfo.Status == int(library.NoteBookContentStatusEnum.Privacy)) &&
			(status == int(library.NoteBookContentStatusEnum.Normal) ||
				status == int(library.NoteBookContentStatusEnum.Privacy)) {
			needUpdateNoteBookDay = false
		}

		noteBookInfo.Status = status
		if err := noteBookInfo.Update(); err != nil {
			logger.Warnf("更新记录本状态失败 err: %s", err)
			return false, err
		}
	} else {
		if status == int(library.NoteBookContentStatusEnum.Normal) ||
			status == int(library.NoteBookContentStatusEnum.Privacy) {
			needUpdateNoteBookDay = true
		}
		noteBookInfo = &dbnote.List{
			UID:        uid,
			Type:       objType,
			ObjID:      objID,
			Content:    "{}",
			Status:     status,
			CreateTime: createTime,
			UpdateTime: time.Now().Unix(),
		}
		if err := noteBookInfo.Save(); err != nil {
			logger.Warnf("新增记录本状态失败, err: %s", err)
			return false, err
		}
	}

	return needUpdateNoteBookDay, nil
}

// updateUserNoteBookDay 更新用户记录本打卡记录
func (n *notebook) updateUserNoteBookDay(uid, createTime int64, status library.DayStatus) {
	dateIndex := time.Unix(createTime, 0).Format("20060102")
	monthIndex := time.Unix(createTime, 0).Format("200601")
	userNoteBookDayInfo, err := dbnote.TbDayList.GetItem(uid, dateIndex)
	if err != nil {
		return
	}

	if userNoteBookDayInfo != nil && userNoteBookDayInfo.ID > 0 {
		userNoteBookDayInfo.Status = int(status)
		if err := userNoteBookDayInfo.Update(); err != nil {
			logger.Warnf("更新用户记录本打卡记录失败 err: %s", err)
		}
	} else {
		userNoteBookDayInfo = &dbnote.DayList{
			UID:        uid,
			DateIndex:  dateIndex,
			MonthIndex: monthIndex,
			Status:     int(status),
			CreateTime: createTime,
			UpdateTime: createTime,
		}
		if err := userNoteBookDayInfo.Save(); err != nil {
			logger.Warnf("新增用户记录本打卡记录失败 err: %s", err)
		}
	}
}

// getPostImageList 获取记录本排行榜图片
func (n *notebook) getPostImageList(userPostList []yogaparadise.Posts,
	mpUserNoteBookList map[string]dbnote.List) []string {
	var allImageList, imageList []string

	var baseCreatedTime int64 = 1577808000
	for _, item := range userPostList {
		strObjID := strconv.FormatInt(item.ID, 10)
		if item.Images == "" {
			continue
		}
		if _, ok := mpUserNoteBookList[strObjID]; !ok ||
			(ok && mpUserNoteBookList[strObjID].Status != int(library.NoteBookContentStatusEnum.Normal)) {
			continue
		}

		imageList = strings.Split(item.Images, ",")
		imageList = imageList[:len(imageList)-1]
		if len(imageList) == 0 {
			continue
		}
		allImageList = append(allImageList, imageList[0])
		if len(allImageList) >= library.NoteBookRankImageSizeMax {
			allImageList = allImageList[:library.NoteBookRankImageSizeMax]
			break
		}

		if item.Created < baseCreatedTime {
			break
		}
	}

	return allImageList
}

// getRankListImageList 获取用户最近N张帖子图片
func (n *notebook) getRankListImageList(uid int64) []string {
	page := 1
	var allImageList, postIDList []string
	mpUserNoteBookList := make(map[string]dbnote.List)

	pageSize := 20
	for {
		userPostList := yogaparadise.TbPosts.GetValidListByPage(uid, page, pageSize)
		for _, item := range userPostList {
			postIDList = append(postIDList, strconv.FormatInt(item.ID, 10))
		}

		if len(postIDList) == 0 {
			break
		}

		userNoteBookList := dbnote.TbList.GetListBatch(uid, postIDList, int32(library.NoteBookContentTypeEnum.Posts))
		if userNoteBookList == nil {
			break
		}

		for _, item := range userNoteBookList {
			mpUserNoteBookList[item.ObjID] = item
		}

		imageList := n.getPostImageList(userPostList, mpUserNoteBookList)
		allImageList = append(allImageList, imageList...)
		if len(allImageList) >= library.NoteBookRankImageSizeMax {
			allImageList = allImageList[:library.NoteBookRankImageSizeMax]
			break
		}

		if len(userPostList) < pageSize {
			break
		}
		page++
	}

	return allImageList
}

// createUserNoteBookCollect 新增用户记录本
func (n *notebook) CreateUserNoteBook(uid int64, objID string, objType int32, status int) {
	objInfo := n.GetObjInfo(objID, objType)
	if objInfo == nil {
		logger.Warnf("无法获取记录本资源")
		return
	}

	if len(objInfo.ImageList) > 1 {
		objInfo.ImageList = objInfo.ImageList[:1]
	}
	images, err := json.Marshal(objInfo.ImageList)
	if err != nil {
		logger.Warnf("格式化资源图片信息失败 err: %s", err)
		return
	}

	dayList := &dbnote.DayList{
		UID:        uid,
		DateIndex:  time.Unix(objInfo.CreateTime, 0).Format("20060102"),
		MonthIndex: time.Unix(objInfo.CreateTime, 0).Format("200601"),
		Status:     int(library.NoteBookDayStatusEnum.NoRecord),
		CreateTime: objInfo.CreateTime,
		UpdateTime: time.Now().Unix(),
	}
	if status == int(library.NoteBookContentStatusEnum.Normal) ||
		status == int(library.NoteBookContentStatusEnum.Privacy) {
		dayList.Status = int(library.NoteBookDayStatusEnum.HasRecord)
	}
	if err := dayList.Save(); err != nil {
		logger.Warnf("保存记录本打卡记录 err: %s", err)
	}

	collect := &dbnote.Collect{
		UID:              uid,
		TotalRecordDay:   0,
		TotalRecordCount: 0,
		ImageList:        string(images),
		JoinStatus:       int32(library.NoteBookJoinStatusEnum.UnJoint),
		PrivacyStatus:    int32(library.NoteBookPrivacyStatuEnum.Public),
	}
	if status == int(library.NoteBookContentStatusEnum.Normal) ||
		status == int(library.NoteBookContentStatusEnum.Privacy) {
		collect.TotalRecordDay = 1
		collect.TotalRecordCount = 1
	}

	if err := collect.Save(); err != nil {
		logger.Warnf("保存记录本失败 err: %s", err)
	}
}

const EmptyText = "{}"

// DeleteUserNoteBook 记录本中删除资源
func (n *notebook) DeleteUserNoteBook(noteBookCollectInfo *dbnote.Collect,
	objID string, objType int32, status int, needUpdateNoteBookDay bool) {
	ctx := context.Background()
	needUpdateRankList := false
	var imageListOld []string

	err := json.Unmarshal([]byte(noteBookCollectInfo.ImageList), &imageListOld)
	if noteBookCollectInfo.ImageList != "" && err != nil {
		return
	}

	// 获取资源信息
	objInfo := n.GetObjInfo(objID, objType)
	if objInfo == nil {
		logger.Warnf("无法获取记录本资源")
		return
	}

	t := time.Unix(objInfo.CreateTime, 0)
	objCreateTime := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local).Unix()

	// 如果当天没有资源，在新增资源是判断是否需要更新记录天数据信息
	noteBookList := dbnote.TbList.GetListByTimeRange(noteBookCollectInfo.UID,
		objCreateTime, objCreateTime+86400, []int{int(library.NoteBookContentStatusEnum.Normal),
			int(library.NoteBookContentStatusEnum.Privacy)})
	noteBookCollectInfo.TotalRecordCount = int32(dbnote.TbList.GetCount(noteBookCollectInfo.UID,
		[]int{int(library.NoteBookContentStatusEnum.Normal), int(library.NoteBookContentStatusEnum.Privacy)}))
	if needUpdateNoteBookDay && len(noteBookList) == 0 {
		needUpdateRankList = true
		n.updateUserNoteBookDay(noteBookCollectInfo.UID, objInfo.CreateTime, library.NoteBookDayStatusEnum.NoRecord)
	}

	imageList := n.getRankListImageList(noteBookCollectInfo.UID)
	uidStr := fmt.Sprintf("%d", noteBookCollectInfo.UID)
	// 当天没有更新过 并且 图片数量超过达人记录本最低限制则将用户添加到排行榜中
	noteBookCollectInfo.TotalRecordDay = dbnote.TbDayList.GetCount(noteBookCollectInfo.UID)
	if len(imageListOld) >= library.NoteBookRankImageSizeMin && needUpdateRankList &&
		len(imageList) >= library.NoteBookRankImageSizeMin &&
		noteBookCollectInfo.JoinStatus == int32(library.NoteBookJoinStatusEnum.Jointed) {
		noteBookCollectInfo.SyncRankList = 1
		z := &redis.Z{
			Score:  float64(noteBookCollectInfo.TotalRecordDay),
			Member: uidStr,
		}
		if err := cache.GetYogaRedis().ZAdd(ctx, library.NoteBookRankListKey, z).Err(); err != nil {
			logger.Error(err)
		}
		n.updateRankListSize()
	}
	// 排行榜中存在 并且 图片达不到最低限度
	if noteBookCollectInfo.TotalRecordDay == 0 ||
		(len(imageListOld) >= library.NoteBookRankImageSizeMin && len(imageList) < library.NoteBookRankImageSizeMin) {
		noteBookCollectInfo.SyncRankList = 0
		if err := cache.GetYogaRedis().ZRem(ctx, library.NoteBookRankListKey, uidStr).Err(); err != nil {
			logger.Error(err)
		}
	}

	// 保存最近N张记录本图片
	images, err := json.Marshal(imageList)
	if err == nil {
		noteBookCollectInfo.ImageList = string(images)
	} else {
		noteBookCollectInfo.ImageList = EmptyText
	}
	if err := noteBookCollectInfo.Update(); err != nil {
		logger.Warnf("更新用户记录本失败 err: %s", err)
	}
}

// UpdateUserNoteBookCollect 记录本中资源状态更新
func (n *notebook) UpdateUserNoteBook(noteBookCollectInfo *dbnote.Collect,
	objID string, objType int32, status int, objCreateTime int64, needUpdateNoteBookDay bool) {
	ctx := context.Background()
	needUpdateRankList := false
	var imageListOld []string

	err := json.Unmarshal([]byte(noteBookCollectInfo.ImageList), &imageListOld)
	if noteBookCollectInfo.ImageList != "" && err != nil {
		logger.Debugf("note book: %s, err: %s", noteBookCollectInfo.ImageList, err)
		return
	}

	t := time.Unix(objCreateTime, 0)
	objCreateBeginTime := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local).Unix()

	// 如果当天没有资源，在新增资源是判断是否需要更新记录天数据信息
	noteBookList := dbnote.TbList.GetListByTimeRange(noteBookCollectInfo.UID,
		objCreateBeginTime, objCreateBeginTime+86400,
		[]int{int(library.NoteBookContentStatusEnum.Normal), int(library.NoteBookContentStatusEnum.Privacy)})
	noteBookCollectInfo.TotalRecordCount = int32(dbnote.TbList.GetCount(noteBookCollectInfo.UID,
		[]int{int(library.NoteBookContentStatusEnum.Normal), int(library.NoteBookContentStatusEnum.Privacy)}))

	if needUpdateNoteBookDay && len(noteBookList) == 1 {
		needUpdateRankList = true
		n.updateUserNoteBookDay(noteBookCollectInfo.UID, objCreateTime, library.NoteBookDayStatusEnum.HasRecord)
	}

	imageList := n.getRankListImageList(noteBookCollectInfo.UID)
	uidStr := fmt.Sprintf("%d", noteBookCollectInfo.UID)
	// 排行榜中存在 并且 当天没有更新过 并且 图片数量超过达人记录本最低限制则将用户添加到排行榜中
	// 或者  排行榜中不存在 并且 图片数量超过达人记录本最低限制则将用户添加到排行榜中
	noteBookCollectInfo.TotalRecordDay = dbnote.TbDayList.GetCount(noteBookCollectInfo.UID)

	if ((len(imageListOld) >= library.NoteBookRankImageSizeMin && needUpdateRankList &&
		len(imageList) >= library.NoteBookRankImageSizeMin) ||
		(len(imageListOld) < library.NoteBookRankImageSizeMin && len(imageList) >= library.NoteBookRankImageSizeMin)) &&
		noteBookCollectInfo.JoinStatus == int32(library.NoteBookJoinStatusEnum.Jointed) {
		noteBookCollectInfo.SyncRankList = 1
		z := &redis.Z{
			Score:  float64(noteBookCollectInfo.TotalRecordDay),
			Member: uidStr,
		}
		if err := cache.GetYogaRedis().ZAdd(ctx, library.NoteBookRankListKey, z).Err(); err != nil {
			logger.Error(err)
		}
		n.updateRankListSize()
	}

	// 排行榜中存在 并且 图片达不到最低限度
	if len(imageListOld) >= library.NoteBookRankImageSizeMin && len(imageList) < library.NoteBookRankImageSizeMin {
		noteBookCollectInfo.SyncRankList = 0
		if err := cache.GetYogaRedis().ZRem(ctx, library.NoteBookRankListKey, uidStr).Err(); err != nil {
			logger.Error(err)
		}
	}

	// 保存最近N张记录本图片
	images, err := json.Marshal(imageList)
	if err == nil {
		noteBookCollectInfo.ImageList = string(images)
	} else {
		noteBookCollectInfo.ImageList = EmptyText
	}
	if err := noteBookCollectInfo.Update(); err != nil {
		logger.Warnf("更新用户记录本失败 err: %s", err)
	}
}

// JudgeNoteBookBadgeDynamicRecordStatus 判断用户记录本徽章是否达到条件
func (n *notebook) JudgeNoteBookBadgeDynamicRecordStatus(uid int64,
	monthBeginDate, monthEndDate, monthIndex string, monthDayCount int) library.StatusType {
	noteBookDayCount := 0
	practiceDaysList := practice.TbDay.GetListByRangeTime(uid, monthBeginDate, monthEndDate)
	noteBookDayList, err := dbnote.TbDayList.GetListByPage(uid, monthIndex, 1, 40)
	if err != nil {
		return library.NoteBookBadgeDynamicStatusEnum.UnFinished
	}

	// 计算用户练习+记录天数
	mpPracticeDay := make(map[string]bool)
	for _, item := range practiceDaysList {
		mpPracticeDay[item.DateIndex] = true
	}
	for _, item := range noteBookDayList {
		if ok := mpPracticeDay[item.DateIndex]; ok {
			noteBookDayCount++
		}
	}

	// 记录数等于月天数 并且 有练习&&有记录天数大于指定天数
	if len(noteBookDayList) == monthDayCount && noteBookDayCount >= library.NoteBookDynamicRecordDayCount {
		return library.NoteBookBadgeDynamicStatusEnum.PracticeRecordAllFinished
	}

	if len(noteBookDayList) >= library.NoteBookBadgePracticeDayCount &&
		noteBookDayCount >= library.NoteBookDynamicRecordDayCount {
		return library.NoteBookBadgeDynamicStatusEnum.RecordFinished
	}

	if len(noteBookDayList) == monthDayCount && noteBookDayCount > 0 {
		return library.NoteBookBadgeDynamicStatusEnum.PracticeFinished
	}

	return library.NoteBookBadgeDynamicStatusEnum.UnFinished
}

// GetNoteBookBadgeDynamicRecordContent 获取用户记录本徽章动态内容文案
func (n *notebook) GetNoteBookBadgeDynamicRecordContent(badgeStatus library.StatusType) string {
	content := ""
	switch badgeStatus {
	case library.NoteBookBadgeDynamicStatusEnum.PracticeRecordAllFinished:
		content = library.NoteBookPracticeFinishedRecordContent
	case library.NoteBookBadgeDynamicStatusEnum.RecordFinished:
		content = library.NoteBookDynamicFinishedContent
	case library.NoteBookBadgeDynamicStatusEnum.PracticeFinished:
		content = library.NoteBookPracticeFinishContent
	default:
		content = ""
	}

	return content
}

// InsertNoteBookDynamicRecord 插入记录本动态
func (n *notebook) InsertNoteBookDynamicRecord(uid int64,
	title, content, image string, dynamicRecordType int32, createTime int64) {
	dynamicRecordInfo := &DynamicRecordInfo{
		Title:   title,
		Content: content,
		Type:    dynamicRecordType,
	}

	dynamicRecordInfo.ImageList = append(dynamicRecordInfo.ImageList, image)
	contentInfo, err := json.Marshal(dynamicRecordInfo)
	if err != nil {
		logger.Warnf("记录本动态格式化失败 err: %s", err)
		return
	}

	list := &dbnote.List{
		UID:        uid,
		Type:       int32(library.NoteBookContentTypeEnum.Record),
		Content:    string(contentInfo),
		ObjID:      "",
		Status:     int(library.NoteBookContentStatusTypeEnum.Normal),
		CreateTime: createTime,
		UpdateTime: time.Now().Unix(),
	}

	if err := list.Save(); err != nil {
		logger.Warnf("保存记录本动态失败 err: %s", err)
	}
}
