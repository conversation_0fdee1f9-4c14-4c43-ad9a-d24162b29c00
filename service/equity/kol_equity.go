package equity

import (
	"sort"
	"time"

	dbequity "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/equity"
	dbuser "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	dbvip "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/vip"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type CalculationEffectiveDuration struct {
	StartTime int64
	EndTime   int64
}

type AddEffectiveDuration struct {
	StartTime int64
	EndTime   int64
}

type ServiceKolEquity struct {
	TbAccountInfo *dbuser.AccountInfo
}

type TbServiceKolEquity ServiceKolEquity

// GetAccountInfo 获取用户表中的信息
func GetAccountInfo(uid int64) *ServiceKolEquity {
	user := ServiceKolEquity{}
	if user.TbAccountInfo == nil {
		user.TbAccountInfo = dbuser.TbAccountInfo.GetByUID(uid)
	}
	return &user
}

// getUserKolEquityInfo 是否做过清理
func (i *ServiceKolEquity) GetUserKolEquityInfo(equityType int32) *dbequity.UserEquityDuration {
	return dbequity.TbUserEquityDuration.GetItem(i.TbAccountInfo.AccountID, equityType)
}

// IsVIPPause 是否会员暂停状态
func (i *ServiceKolEquity) IsVIPPause() bool {
	record := dbvip.TbVIPPauseRecord.GetUserRecords(i.TbAccountInfo.AccountID)
	if len(record) == 0 {
		return false
	}
	if record[len(record)-1].ContinueTime > 0 {
		return false
	}
	return true
}

// 获取会员等级是4、5的时长
func (i *ServiceKolEquity) CalculationInitUserKolDuration() *CalculationEffectiveDuration {
	res := &CalculationEffectiveDuration{}
	list := dbuser.TbUserMemberDuration.GetEffectiveList(i.TbAccountInfo.AccountID, library.EffectiveUserMember)
	if len(list) > 0 {
		data := make(map[int]*dbuser.MemberDuration)
		sortSlice := make([]int, 0)
		for i, v := range list {
			data[int(v.MemberLevel)] = &list[i]
			sortSlice = append(sortSlice, int(v.MemberLevel))
		}
		sort.Sort(sort.Reverse(sort.IntSlice(sortSlice)))
		for _, v := range sortSlice {
			if res.StartTime == 0 && data[v].StartTime > 0 {
				res.StartTime = i.formatStartTime(data[v].StartTime)
			}
			if data[v].EndTime > 0 {
				res.EndTime = i.formatEndTime(data[v].EndTime)
			}
		}
	}
	return res
}

func (i *ServiceKolEquity) formatEndTime(cTime int64) int64 {
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	timeStamp := time.Unix(cTime, 0)
	return time.Date(timeStamp.Year(), timeStamp.Month(), timeStamp.Day(), 23, 59, 59, 0, loc).Unix()
}

func (i *ServiceKolEquity) formatStartTime(cTime int64) int64 {
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	timeStamp := time.Unix(cTime, 0)
	return time.Date(timeStamp.Year(), timeStamp.Month(), timeStamp.Day(), 00, 00, 00, 0, loc).Unix()
}

// 计算用户对应的时长
func (i *ServiceKolEquity) CalculationUserKolDuration(durationType, operation int32,
	durationValue, startTime, endTime int64) *AddEffectiveDuration {
	effectiveDuration := &AddEffectiveDuration{}
	now := time.Now().Unix()
	if endTime > now {
		effectiveDuration.StartTime = startTime
		effectiveDuration.EndTime = i.formatUserKolEDurationTimeStamp(durationType, operation, durationValue, endTime)
	} else {
		effectiveDuration.StartTime = i.formatStartTime(now)
		effectiveDuration.EndTime = i.formatUserKolEDurationTimeStamp(durationType, operation, durationValue,
			effectiveDuration.StartTime)
	}
	return effectiveDuration
}

// 计算截止时间
func (i *ServiceKolEquity) formatUserKolEDurationTimeStamp(durationType, operation int32,
	durationValue, endTime int64) int64 {
	var dateTimeStamp int64
	if endTime <= 0 {
		endTime = time.Now().Unix()
	}
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	timeStamp := time.Unix(endTime, 0)
	switch durationType {
	case library.EquityDurationTypeEnum.Day.ToInt32():
		dateTimeStamp = time.Date(timeStamp.Year(), timeStamp.Month(), i.symbolDuration(timeStamp.Day(),
			int(durationValue), operation), 23, 59, 59, 0, loc).Unix()
	case library.EquityDurationTypeEnum.Month.ToInt32():
		dateTimeStamp = time.Date(timeStamp.Year(), time.Month(i.symbolDuration(int(timeStamp.Month()),
			int(durationValue), operation)), timeStamp.Day(), 23, 59, 59, 0, loc).Unix()
	case library.EquityDurationTypeEnum.Year.ToInt32():
		dateTimeStamp = time.Date(i.symbolDuration(timeStamp.Year(), int(durationValue), operation), timeStamp.Month(),
			timeStamp.Day(), 23, 59, 59, 0, loc).Unix()
	}
	return dateTimeStamp
}

func (i *ServiceKolEquity) symbolDuration(a, b int, operation int32) int {
	var val int
	val = a + b
	if operation == library.OperationEnum.Sub.ToInt32() {
		val = a - b
	}
	return val
}
