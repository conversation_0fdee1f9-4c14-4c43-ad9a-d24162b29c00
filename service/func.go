package service

import (
	"context"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/artifact"
)

func GetIntervalDays(start, end int64) int {
	if start == 0 && end == 0 {
		return 0
	}
	if end < start {
		return 0
	}
	t1, _ := time.ParseInLocation("2006-01-02", time.Unix(start, 0).Format("2006-01-02"), time.Local)
	t2, _ := time.ParseInLocation("2006-01-02", time.Unix(end, 0).Format("2006-01-02"), time.Local)
	return int((t2.Unix()-t1.Unix())/86400 + 1)
}

func GetDayFirstAndLastUnixTimestamp(t time.Time) (first, last int64) {
	firstTime := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)
	last = firstTime.Unix() + 86400 - 1
	return firstTime.Unix(), last
}

type httpProxy struct{}

var HTTP httpProxy

func (h *httpProxy) PostForm(ctx context.Context, addr string, data url.Values) (resp *http.Response, err error) {
	return h.do(ctx, http.MethodPost, addr, "application/x-www-form-urlencoded", strings.NewReader(data.Encode()))
}

func (h *httpProxy) Get(ctx context.Context, addr string) (resp *http.Response, err error) {
	return h.do(ctx, http.MethodGet, addr, "", nil)
}

func (h *httpProxy) do(ctx context.Context, method, addr, contentType string,
	body io.Reader) (resp *http.Response, err error) {
	req, err := http.NewRequestWithContext(ctx, method, addr, body)
	if err != nil {
		return nil, err
	}
	if contentType != "" {
		req.Header.Set("Content-Type", contentType)
	}

	c := &http.Client{}
	return c.Do(req)
}

// ReplacePayURLScheme 修改支付网关的scheme
func ReplacePayURLScheme(outURL string) string {
	var httpsPrefix string = "https"
	if outURL == "" || !artifact.IsURL(outURL) || len(outURL) <= 5 {
		return outURL
	}
	scheme := os.Getenv("YOGA_PAY_SCHEME")
	if scheme == "" {
		scheme = httpsPrefix
	}
	domainTop := outURL[:5]
	if domainTop == httpsPrefix {
		outURL = scheme + outURL[5:]
	}
	return outURL
}
