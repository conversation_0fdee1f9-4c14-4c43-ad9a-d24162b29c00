package user

import (
	"time"

	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/order"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type VIPStatusAttr struct {
}

// GetVIPStatusAttrByUID 通过 UID 获取用户 VIP 状态属性。属性分别是:
// 未知、会员有效期内、注册15天内未开通会员、注册15天已上未开通会员、会员已过期15日内
// 会员已过期15日已上
func (v *VIPStatusAttr) GetVIPStatusAttrByUID(uid int64) (library.UserVIPStatusAttr, error) {
	if uid == 0 {
		return library.UserVIPStatusAttrEnum.UnKnow, nil
	}
	// 获取一条用户会员的到期时间
	duration := new(user.MemberDuration)
	_, err := db.GetEngine().Where("uid = ?", uid).
		OrderBy("end_time desc").Cols("end_time").Get(duration)
	if err != nil {
		return library.UserVIPStatusAttrEnum.UnKnow, err
	}
	// 取一条用户创建时间
	account := new(user.AccountInfo)
	_, err = db.GetEngine().Where("AccountId = ?", uid).Cols("createTime").Get(account)
	if err != nil {
		return library.UserVIPStatusAttrEnum.UnKnow, err
	}
	now := time.Now().Unix()
	// 查看用户是否有历史订单记录
	hasHistoryMember := order.TbWebOrder.HasHistoryMember(uid)
	// 如果没买过会员
	if !hasHistoryMember {
		isBeyond15 := account.CreateTime+(86400*15) < int(now)
		if isBeyond15 {
			return library.UserVIPStatusAttrEnum.VIPNotOpenBeyondRegister15, nil
		}
		return library.UserVIPStatusAttrEnum.VIPNotOpenWithInRegister15, nil
	}
	// 有效期内
	if hasHistoryMember && duration.EndTime > now {
		return library.UserVIPStatusAttrEnum.VIPIsValid, nil
	}
	// 会员过期了
	if hasHistoryMember && duration.EndTime < now {
		isBeyond15 := duration.EndTime+(86400*15) < now
		if isBeyond15 {
			return library.UserVIPStatusAttrEnum.VIPOverdueBeyond15, nil
		}
		return library.UserVIPStatusAttrEnum.VIPOverdueWithIn15, nil
	}
	return library.UserVIPStatusAttrEnum.UnKnow, nil
}
