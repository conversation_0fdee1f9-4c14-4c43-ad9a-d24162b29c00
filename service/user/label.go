package user

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/client"

	"gitlab.dailyyoga.com.cn/protogen/srv-usercenter-go/packagepay"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	srvcache "gitlab.dailyyoga.com.cn/server/srv-task/cache"
	db_stat "gitlab.dailyyoga.com.cn/server/srv-task/databases/stat/member"
	dbpractice "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	rdsu "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/challenge"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/course"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/equity"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/filter"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/intelligence"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/order"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/group"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/third"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/usersetting"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/yogao2school"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/comm"
)

type label struct {
}

type Utm struct {
	AnonymousID string `json:"anonymous_id"`
	UtmSource   string `json:"utm_source"`
	UtmMedium   string `json:"utm_medium"`
}

type Questions struct {
	PostureProblem  string `json:"bodyPosture"`  // 体态问题
	PostureProblem2 string `json:"bodyPosture2"` // 体态问题
	SportsImpetus   string `json:"impetus"`      // 运动动力
	SportsType      int32  `json:"SportsType"`   // 运动类型
}

const (
	Posture = iota + 1 // 体态
	Face
)

var SelfUtmMediumMap = map[string]int{
	"【DY_体态】":   Posture,
	"【DY_面部瑜伽】": Face,
}

type FilterTag struct {
	ID   int32  `json:"id"`
	PID  int32  `json:"pid"`
	Name string `json:"name"`
}

type ResourceFilterTagList struct {
	DifficultyFilterTagList     []FilterTag `json:"difficulty_filter_tag_list"`
	BodyPositionFilterTagList   []FilterTag `json:"body_position_filter_tag_list"`
	ClassificationFilterTagList []FilterTag `json:"classification_filter_tag_list"`
}

type PracticeTimeRangeInfo struct {
	Min int32 `json:"min"`
	Max int32 `json:"max"`
}

type Efficacy struct {
	CyclePeriod   int32  `json:"cycle_period"` // 生理期周期
	KeepDay       int32  `json:"keep_day"`     // 生理期天数
	LastStartDate string `json:"start_date"`   // 上次生理期第一天日期
	LastEndDate   string `json:"end_date"`     // 上次生理期第后一天日期
}

type LabelList struct {
	Birthday                   int64                     `json:"birthday"`
	Gender                     int32                     `json:"gender"`
	RegisterTime               int64                     `json:"register_time"`
	BodyPosition               []int32                   `json:"body_position"`
	PracticeTime               []int32                   `json:"practice_time"`
	Classification             []int32                   `json:"classification"`
	DifficultyLevel            []int32                   `json:"difficulty_level"`
	PracticeGoal               []int64                   `json:"practice_goal"`
	FlexibilityLevel           int32                     `json:"flexibility_level"`
	BalanceLevel               int32                     `json:"balance_level"`
	StrengthLevel              int32                     `json:"strength_level"`
	PracticeTimeRange          PracticeTimeRangeInfo     `json:"practice_time_range"`
	ComprehensiveLevel         int32                     `json:"comprehensive_level"`
	VipBuyRecord               int32                     `json:"vip_buy_record"`
	O2BuyRecord                int32                     `json:"o2_buy_record"`
	VipStatus                  int32                     `json:"vip_status"`
	VipExpiredDays             int                       `json:"vip_expired_days"`
	VipLastDays                int                       `json:"vip_last_days"`
	NewUserChallenge           int32                     `json:"new_user_challenge"`
	UserVIPStatusAttr          library.UserVIPStatusAttr `json:"user_vip_status_attr"`
	CampIsValid                int64                     `json:"camp_is_valid"`
	BusinessVIPPurchaseTimes   int                       `json:"business_vip_purchase_times"`
	BusinessVIPPurchaseAmount  int                       `json:"business_vip_purchase_amount"`
	LatestVIPTradeIsSub        int                       `json:"latest_vip_trade_is_sub"`
	BusinessORPurchaseTimes    int                       `json:"business_or_purchase_times"`
	IsCampNewUser              int                       `json:"is_camp_new_user"`
	IsOpenKolEquity            int                       `json:"is_open_kol_equity"`
	Last7DayPlayTimes          int                       `json:"last_7day_play_times"`
	Last7DayPlayDays           int                       `json:"last_7day_play_days"`
	Last30DayPlayTimes         int                       `json:"last_30day_play_times"`
	Last30DayPlayDays          int                       `json:"last_30day_play_days"`
	Last7DayLoginDays          int                       `json:"last_7day_login_days"`
	Last30DayLoginDays         int                       `json:"last_30day_login_days"`
	LastLoginDateTime          int                       `json:"last_login_date_time"`
	SubMemberSelect            int                       `json:"sub_member_select"`
	SubKolSelect               int                       `json:"sub_kol_select"`
	UserEquityType             []int32                   `json:"user_equity_type"`
	Last1DayPlayTimes          int                       `json:"last_1day_play_times"`
	UserGoalsNew               []int32                   `json:"user_goals_new"`            // 用户目标（新）
	StageOfYoga                []int32                   `json:"stage_of_yoga"`             // 所处瑜伽阶段
	DurationOfEachPractice     []int32                   `json:"duration_of_each_practice"` // 每次练习时长
	ThePartWantPoPractice      []int32                   `json:"the_part_want_po_practice"` // 想练习的部位
	TimeSinceLastLogin         int                       `json:"time_since_last_start"`     // 想练习的部位
	PhysiologyPeriod           Efficacy                  `json:"physiology_period"`         // 生理期信息
	ObAge                      int32                     `json:"ob_age"`                    // 年龄
	BMI                        int32                     `json:"bmi"`                       // bmi指数
	AppWelfareChallenge        int32                     `json:"app_welfare_challenge"`     // 参加国内会员福利赛
	ObTypeAttributes           []ObTypeAttribute         `json:"ob_type_attributes"`        // 进入OB次数
	ChannelSource              int                       `json:"channel_source"`            // 渠道素材
	PostureProblem             []int32                   `json:"posture_problem"`           // 体态问题
	SportsImpetus              []int32                   `json:"sports_impetus"`            // 运动动力
	UserChallengeIng           int32                     `json:"user_challenge_ing"`        // 正在参加挑战赛
	SportsType                 int32                     `json:"sports_type"`               // 运动类型
	VipDiscountType            int64                     `json:"vip_discount_type"`         // 会员产品的优惠类型
	PackageDiscountType        int64                     `json:"package_discount_type"`     // 会员产品的优惠类型
	RegisterToReinstall        int64                     `json:"register_to_reinstall"`
	ReinstallTime              int64                     `json:"reinstall_time"`
	PredictProbabilityBuy      int64                     `json:"predict_probability_buy"`
	PredictProbabilityHigh     int64                     `json:"predict_probability_high"`
	AdsChannel                 int                       `json:"ads_channel"`
	OldUserPredictProbability1 int                       `json:"old_user_predict_probability_1"`
	IsMiniAPP                  int                       `json:"is_mini_app"` // 是不是小程序
	RefundProb                 int                       `json:"refund_prob"`
	MiniappNewUserProbability1 int                       `json:"miniapp_new_user_probability_1"`
}

// 不同OB类型对应的OB属性 ObTypeAttribute
type ObTypeAttribute struct {
	ObProcessType int64       `json:"ob_process_type"` // 用户上次进入的OB类型
	ObAttribute   ObAttribute `json:"ob_attribute"`
}

// 对应的OB属性 ObAttribute
type ObAttribute struct {
	LastIntoObTime int64 `json:"last_into_ob_time"` // 用户上次进入OB的时间
	IntoObTimes    int64 `json:"into_ob_times"`     // 用户30天内进入OB的次数
}

var ServiceUserLabel label

// getResourceType 获取练习资源类型
func (l *label) GetResourceType(labelData *group.LastUserLabelData) library.ResourceType {
	// Prince 2021-03-26 判断是否直播课程
	if labelData.SourceType == library.LiveSessionSourceType {
		return library.ResourceTypeEnum.LiveSession
	}
	if labelData.UserActionTable == library.LeaderUserScheduleUnit {
		return library.ResourceTypeEnum.UserSchedule
	}

	if labelData.UserActionTable == library.PracticeFinish && labelData.SessionIndex != 0 {
		return library.ResourceTypeEnum.IntelligenceSchedule
	}

	if labelData.UserActionTable == library.PracticeFinish {
		return library.ResourceTypeEnum.Session
	}

	programInfo := course.TbProgram.GetProgramByID(labelData.ProgramID)
	if programInfo != nil {
		if v, ok := library.SeriesTypeToPracticeType[library.SeriesType(programInfo.SeriesType)]; ok {
			return v
		}
	}

	return library.ResourceTypeEnum.UnKnown
}

// getTimeRangeType 获取练习时间区间类型
func (l *label) getTimeRangeType(reportTime int64) library.PracticeTimeRangeType {
	hour := time.Unix(reportTime, 0).Hour()

	if hour >= 0 && hour < 6 {
		return library.PracticeTimeRangeTypeEnum.Night
	}
	if hour >= 6 && hour <= 9 {
		return library.PracticeTimeRangeTypeEnum.Morning
	}
	if hour >= 11 && hour <= 14 {
		return library.PracticeTimeRangeTypeEnum.Noon
	}
	if hour >= 21 && hour < 24 {
		return library.PracticeTimeRangeTypeEnum.Evening
	}

	return library.PracticeTimeRangeTypeEnum.Unknown
}

// getResourceLabelList 获取难度、练习目的、练习部位、流派标签
func (l *label) getResourceLabelList() *ResourceFilterTagList {
	resourceFilterTagList := &ResourceFilterTagList{}
	filterTagList, err := filter.TbTag.GetTagList(context.Background())
	if err != nil {
		return nil
	}

	for _, item := range filterTagList {
		filterTag := FilterTag{
			ID:   item.ID,
			PID:  item.Pid,
			Name: item.Name,
		}

		switch item.Pid {
		case library.DifficultyFilterTagID:
			resourceFilterTagList.DifficultyFilterTagList =
				append(resourceFilterTagList.DifficultyFilterTagList, filterTag)
		case library.BodyPositionFilterTagID:
			resourceFilterTagList.BodyPositionFilterTagList =
				append(resourceFilterTagList.BodyPositionFilterTagList, filterTag)
		case library.ClassificationFilterTagID:
			resourceFilterTagList.ClassificationFilterTagList =
				append(resourceFilterTagList.ClassificationFilterTagList, filterTag)
		}
	}

	return resourceFilterTagList
}

// getUserTrainTagList 获取用户训练目的标签id列表
func (l *label) getUserTrainTagList(uid int64) []int64 {
	trainTagIDList := make([]int64, 0)
	trainTagIDStrList := make([]string, 0)

	// 获取用户训练目的标签
	userTrainTagConfig, err := usersetting.TbUserSetting.GetItem(uid, library.UserTrainTagList)
	if err != nil {
		return trainTagIDList
	}
	if userTrainTagConfig != nil {
		if err := json.Unmarshal([]byte(userTrainTagConfig.Value), &trainTagIDList); err != nil {
			trainTagIDList = make([]int64, 0)
			if err := json.Unmarshal([]byte(userTrainTagConfig.Value), &trainTagIDStrList); err != nil {
				logger.Warnf("解析用户训练目标列表失败 %s", err)
				return trainTagIDList
			}
		}
	}
	for _, v := range trainTagIDStrList {
		if val, _ := strconv.ParseInt(v, 10, 64); val != 0 {
			trainTagIDList = append(trainTagIDList, val)
		}
	}
	return trainTagIDList
}

// getUserLabelConfig 获取用户标签
func (l *label) getUserLabelConfig(uid int64) (*usersetting.UserSetting, *LabelList, error) {
	userLabelList := &LabelList{
		BodyPosition:     make([]int32, 0),
		PracticeTime:     make([]int32, 0),
		Classification:   make([]int32, 0),
		DifficultyLevel:  make([]int32, 0),
		PracticeGoal:     make([]int64, 0),
		FlexibilityLevel: 0,
		StrengthLevel:    0,
		PracticeTimeRange: PracticeTimeRangeInfo{
			Min: 0,
			Max: 0,
		},
		ComprehensiveLevel:     0,
		VipBuyRecord:           0,
		O2BuyRecord:            0,
		VipStatus:              0,
		VipExpiredDays:         0,
		VipLastDays:            0,
		NewUserChallenge:       0,
		Last7DayPlayDays:       0,
		Last7DayPlayTimes:      0,
		Last30DayPlayDays:      0,
		Last30DayPlayTimes:     0,
		Last7DayLoginDays:      0,
		Last30DayLoginDays:     0,
		SubMemberSelect:        0,
		SubKolSelect:           0,
		UserEquityType:         make([]int32, 0),
		Last1DayPlayTimes:      0,
		StageOfYoga:            make([]int32, 0),
		DurationOfEachPractice: make([]int32, 0),
		ThePartWantPoPractice:  make([]int32, 0),
	}

	// 获取用户标签
	userLabelConfig, err := usersetting.TbUserSetting.GetItem(uid, library.UserLabel)
	if err != nil {
		return nil, userLabelList, err
	}
	if userLabelConfig != nil {
		if err := json.Unmarshal([]byte(userLabelConfig.Value), &userLabelList); err != nil {
			logger.Warnf("解析用户标签列表失败, err: %s", err)
			return userLabelConfig, userLabelList, nil
		}
	}
	accountInfo := user.TbAccountInfo.GetByUID(uid)
	if accountInfo != nil {
		userLabelList.RegisterTime = int64(accountInfo.CreateTime)
		userLabelList.Birthday = accountInfo.Birthday
		userLabelList.Gender = int32(accountInfo.Gender)
	}

	// 获取用户训练目的标签
	userLabelList.PracticeGoal = l.getUserTrainTagList(uid)

	return userLabelConfig, userLabelList, nil
}

// getUserLastLabelDataList 获取用户最近一月练习、浏览、搜索元数据
func (l *label) getUserLastLabelDataList(uid int64) (practice, browse, search []group.LastUserLabelData) {
	practiceLabelDataList := make([]group.LastUserLabelData, 0)
	browseLabelDataList := make([]group.LastUserLabelData, 0)
	searchLabelDataList := make([]group.LastUserLabelData, 0)

	userLabelDataList, err := group.TbLastUserLabelDataList.GetList(uid)
	if err != nil {
		logger.Warnf("查询用户标签元数据失败:%s", err)
		return nil, nil, nil
	}

	// 标签元数据按照来源分类
	for _, item := range userLabelDataList {
		switch library.SourceType(item.SourceType) {
		case library.SourceTypeEum.Practice:
			practiceLabelDataList = append(practiceLabelDataList, item)
		case library.SourceTypeEum.Browse:
			browseLabelDataList = append(browseLabelDataList, item)
		case library.SourceTypeEum.Search:
			searchLabelDataList = append(searchLabelDataList, item)
		}
	}

	return practiceLabelDataList, browseLabelDataList, searchLabelDataList
}

// getFilterTagListByType 获取资源标签列表
func (l *label) getFilterTagListByType(resourceIDList []int32, mpResourceIDList map[int64]int32,
	objType int32, mpFilterTagList map[int64]int32) map[int64]int32 {
	if len(resourceIDList) == 0 {
		return mpFilterTagList
	}

	o2SessionFilterTagList := filter.TbTagResource.GetTagResourceItemList(resourceIDList, objType)
	for _, item := range o2SessionFilterTagList {
		if _, ok := mpResourceIDList[item.ObjID]; !ok {
			continue
		}

		mpFilterTagList[item.ID] += mpResourceIDList[item.ObjID]
	}

	return mpFilterTagList
}

// getResourceFilterTagList 获取资源标签
func (l *label) getResourceFilterTagList(labelDataList []group.LastUserLabelData) map[int64]int32 {
	programIDList := make([]int32, 0)
	mpProgramIDList := make(map[int64]int32)
	sessionIDList := make([]int32, 0)
	mpSessionIDList := make(map[int64]int32)
	o2SessionIDList := make([]int32, 0)
	mpO2SessionIDList := make(map[int64]int32)
	rytSessionIDList := make([]int32, 0)
	mpRytSessionIDLIst := make(map[int64]int32)
	kolSessionIDList := make([]int32, 0)
	mpKolSessionIDList := make(map[int64]int32)
	userScheduleIDList := make([]int64, 0)
	mpFilterTagList := make(map[int64]int32)

	for _, item := range labelDataList {
		if item.UserActionTable == library.BrowseO2Session ||
			library.ResourceType(item.ResourceType) == library.ResourceTypeEnum.O2 {
			o2SessionIDList = append(o2SessionIDList, int32(item.O2SessionID))
			mpO2SessionIDList[item.O2SessionID]++
			continue
		}

		if item.UserActionTable == library.BrowseKOLEx ||
			library.ResourceType(item.ResourceType) == library.ResourceTypeEnum.Kol {
			kolSessionIDList = append(kolSessionIDList, int32(item.ProgramID))
			mpKolSessionIDList[item.ProgramID]++
			continue
		}

		if item.UserActionTable == library.BrowseRYT {
			rytSessionIDList = append(rytSessionIDList, int32(item.ProgramID))
			mpRytSessionIDLIst[item.ProgramID]++
			continue
		}

		if library.ResourceType(item.ResourceType) == library.ResourceTypeEnum.UserSchedule {
			userScheduleIDList = append(userScheduleIDList, item.SessionID)
			continue
		}

		if item.SessionID != 0 {
			sessionIDList = append(sessionIDList, int32(item.SessionID))
			mpSessionIDList[item.SessionID]++
			continue
		}

		if item.ProgramID != 0 {
			programIDList = append(programIDList, int32(item.ProgramID))
			mpProgramIDList[item.ProgramID]++
			continue
		}
	}

	// 查询自定义课程表对应的课程id
	if len(userScheduleIDList) > 0 {
		userScheduleList := user.TbScheduleUnit.GetBatch(userScheduleIDList)
		for _, item := range userScheduleList {
			sessionIDList = append(sessionIDList, int32(item.SessionID))
			mpSessionIDList[item.SessionID]++
		}
	}

	resources := []struct {
		objType     int32
		resources   []int32
		mpResources map[int64]int32
	}{
		{objType: library.ObjTypeEnum.O2, resources: o2SessionIDList, mpResources: mpO2SessionIDList},
		{objType: library.ObjTypeEnum.Ryt, resources: rytSessionIDList, mpResources: mpRytSessionIDLIst},
		{objType: library.ObjTypeEnum.Kol, resources: kolSessionIDList, mpResources: mpKolSessionIDList},
		{objType: library.ObjTypeEnum.Session, resources: sessionIDList, mpResources: mpSessionIDList},
		{objType: library.ObjTypeEnum.Program, resources: programIDList, mpResources: mpProgramIDList},
	}

	for _, v := range resources {
		if len(v.resources) == 0 {
			continue
		}
		mpFilterTagList = l.getFilterTagListByType(v.resources, v.mpResources, v.objType, mpFilterTagList)
	}

	return mpFilterTagList
}

// calcResourceLabel 计算与资源相关标签
func (l *label) calcResourceLabel(sourceType library.SourceType, labelDataList []group.LastUserLabelData,
	resourceFilterTagList *ResourceFilterTagList, userLabelList *LabelList) *LabelList {
	mpFilterLabel := l.getResourceFilterTagList(labelDataList)

	// 身体部位偏好
	for _, item := range resourceFilterTagList.BodyPositionFilterTagList {
		if _, ok := mpFilterLabel[int64(item.ID)]; !ok ||
			(ok && mpFilterLabel[int64(item.ID)] < library.GetBodyPositionThreshold(sourceType)) {
			continue
		}
		userLabelList.BodyPosition = append(userLabelList.BodyPosition, item.ID)
	}

	// 流派
	for _, item := range resourceFilterTagList.ClassificationFilterTagList {
		if _, ok := mpFilterLabel[int64(item.ID)]; !ok ||
			(ok && mpFilterLabel[int64(item.ID)] < library.GetClassificationThreshold(sourceType)) {
			continue
		}
		userLabelList.Classification = append(userLabelList.Classification, item.ID)
	}

	// 难度等级
	for _, item := range resourceFilterTagList.DifficultyFilterTagList {
		if _, ok := mpFilterLabel[int64(item.ID)]; !ok ||
			(ok && mpFilterLabel[int64(item.ID)] < library.GetDifficultyThreshold(sourceType)) {
			continue
		}
		userLabelList.DifficultyLevel = append(userLabelList.DifficultyLevel, item.ID)
	}

	return userLabelList
}

// calcUserPracticeLabel 匹配用户练习数据对应的标签
// nolint
func (l *label) calcUserPracticeLabel(uid int64, practiceLabelDataList []group.LastUserLabelData,
	resourceFilterTagList *ResourceFilterTagList, userLabelList *LabelList) *LabelList {
	mpPracticeTimeRangeFilterLabel := make(map[int32]int32)
	last7DayTime := comm.FormatStartTime(comm.PastTime(time.Now().Unix(), 7))
	last30DayTime := comm.FormatStartTime(comm.PastTime(time.Now().Unix(), 30))
	last1DayTime := comm.FormatStartTime(comm.PastTime(time.Now().Unix(), 1))
	if userLabelList.LastLoginDateTime < int(comm.FormatStartTime(time.Now().Unix())) {
		userLabelList.Last7DayLoginDays = 0
		userLabelList.Last30DayLoginDays = 0
		loginList := rdsu.TbUserLoginLog.GetListByRangeTime(uid,
			time.Unix(last30DayTime, 0).Format("20060102"), time.Now().Format("20060102"))
		stepDayIndex := ""
		for _, v := range loginList {
			if v.OriginalDate > int64(userLabelList.LastLoginDateTime) {
				userLabelList.LastLoginDateTime = int(v.OriginalDate)
			}
			if v.DayIndex == stepDayIndex {
				continue
			}
			stepDayIndex = v.DayIndex
			if v.DayIndex >= time.Unix(last30DayTime, 0).Format("20060102") {
				userLabelList.Last30DayLoginDays++
			}
			if v.DayIndex >= time.Unix(last7DayTime, 0).Format("20060102") {
				userLabelList.Last7DayLoginDays++
			}
		}
	}
	if len(practiceLabelDataList) == 0 {
		return userLabelList
	}

	// 资源相关类标签
	userLabelList = l.calcResourceLabel(library.SourceTypeEum.Practice,
		practiceLabelDataList, resourceFilterTagList, userLabelList)

	// 练习时间偏好
	for _, item := range practiceLabelDataList {
		practiceTimeRangeType := l.getTimeRangeType(item.ReportTime)
		mpPracticeTimeRangeFilterLabel[int32(practiceTimeRangeType)]++
	}
	for practiceTimeRangeType, count := range mpPracticeTimeRangeFilterLabel {
		if count < library.PracticeTimeRangeLabelPracticeThreshold {
			continue
		}

		userLabelList.PracticeTime = append(userLabelList.PracticeTime, practiceTimeRangeType)
	}
	logger.Debugf("user practice label: %#v", userLabelList)
	// 练习标签从小到大
	sort.Slice(practiceLabelDataList, func(i, j int) bool {
		return practiceLabelDataList[i].ReportTime < practiceLabelDataList[j].ReportTime
	})
	lastDate := ""
	userLabelList.Last7DayPlayTimes = 0
	userLabelList.Last7DayPlayDays = 0
	userLabelList.Last30DayPlayDays = 0
	userLabelList.Last30DayPlayTimes = 0
	userLabelList.Last1DayPlayTimes = 0
	for _, item := range practiceLabelDataList {
		// 先自增练习次数
		if item.ReportTime > last7DayTime {
			userLabelList.Last7DayPlayTimes++
		}
		if item.ReportTime > last30DayTime {
			userLabelList.Last30DayPlayTimes++
		}
		if item.ReportTime > last1DayTime {
			userLabelList.Last1DayPlayTimes++
		}
		// 日期变化 自增练习天数
		if lastDate == "" || time.Unix(item.ReportTime, 0).Format("2006-01-02") > lastDate {
			lastDate = time.Unix(item.ReportTime, 0).Format("2006-01-02")
			if item.ReportTime > last7DayTime {
				userLabelList.Last7DayPlayDays++
			}
			if item.ReportTime > last30DayTime {
				userLabelList.Last30DayPlayDays++
			}
		}
	}

	return userLabelList
}

// calcUserBrowseLabel 匹配用户浏览数据对应的标签
func (l *label) calcUserBrowseLabel(browseLabelList []group.LastUserLabelData,
	resourceFilterTagList *ResourceFilterTagList, userLabelList *LabelList) *LabelList {
	if len(browseLabelList) == 0 {
		return userLabelList
	}

	// 资源相关类标签
	return l.calcResourceLabel(library.SourceTypeEum.Browse, browseLabelList, resourceFilterTagList, userLabelList)
}

// calcUserSearchLabel 匹配用户搜索数据对应的标签
func (l *label) calcUserSearchLabel(searchLabelDataList []group.LastUserLabelData,
	resourceFilterTagList *ResourceFilterTagList, userLabelList *LabelList) *LabelList {
	mpBodyPositionFilterLabel := make(map[int32]int32)
	mpClassificationFilterLabel := make(map[int32]int32)

	if len(searchLabelDataList) == 0 {
		return userLabelList
	}

	for _, labelData := range searchLabelDataList {
		for _, filterTag := range resourceFilterTagList.BodyPositionFilterTagList {
			if strings.Contains(labelData.Keyword, filterTag.Name) {
				mpBodyPositionFilterLabel[filterTag.ID]++
				break
			}
		}

		for _, filterTag := range resourceFilterTagList.ClassificationFilterTagList {
			if strings.Contains(labelData.Keyword, filterTag.Name) {
				mpClassificationFilterLabel[filterTag.ID]++
			}
		}
	}

	// 身体部位偏好
	for filterTagID, count := range mpBodyPositionFilterLabel {
		if count < library.BodyPositionLabelSearchThreshold {
			continue
		}

		userLabelList.BodyPosition = append(userLabelList.BodyPosition, filterTagID)
	}

	// 流派
	for filterTagID, count := range mpClassificationFilterLabel {
		if count < library.ClassificationLabelSearchThreshold {
			continue
		}

		userLabelList.Classification = append(userLabelList.Classification, filterTagID)
	}

	return userLabelList
}

// updateUserLabel 更新用户标签
func (l *label) updateUserLabel(uid int64, userLabelConfig *usersetting.UserSetting, userLabelList *LabelList) error {
	if userLabelList.IsMiniAPP == library.Yes {
		return nil
	}
	keySuffix := fmt.Sprintf("%s_%d", library.UserLabel, uid)
	userLabelList.BodyPosition = library.RemoveRepeatedElement(userLabelList.BodyPosition)
	userLabelList.PracticeTime = library.RemoveRepeatedElement(userLabelList.PracticeTime)
	userLabelList.Classification = library.RemoveRepeatedElement(userLabelList.Classification)
	userLabelList.DifficultyLevel = library.RemoveRepeatedElement(userLabelList.DifficultyLevel)
	// 获取渠道，根据uid查询device_id->anonymous_id
	accountInfo := user.TbAccountInfo.GetByUID(uid)
	if accountInfo != nil && accountInfo.DeviceID != "" {
		item := client.TbAdSensorsData.GetItemByDeviceID(accountInfo.DeviceID)
		if item != nil && item.AnonymousID != "" {
			if channel, ok := SelfUtmMediumMap[item.UtmMedium]; ok {
				userLabelList.ChannelSource = channel
			}
		}
	}
	value, err := json.Marshal(userLabelList)
	if err != nil {
		logger.Warnf("用户标签格式化失败, err: %s", err)
		return err
	}
	if userLabelConfig != nil && userLabelConfig.ID > 0 {
		userLabelConfig.Value = string(value)
		if err := userLabelConfig.Update(); err != nil {
			logger.Errorf("更新用户配置失败, err: %s", err)
			return err
		}
	} else {
		userLabelConfig = &usersetting.UserSetting{
			UID:          uid,
			Key:          library.UserLabel,
			Value:        string(value),
			DeleteStatus: int32(library.DataStatusEnum.Valid),
		}
		if err := userLabelConfig.Save(); err != nil {
			logger.Warnf("插入用户配置失败 %s", err)
			return err
		}
	}
	if err := cache.Set(library.UserSetting, keySuffix, string(value), library.UserLabelCacheExpireTime); err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

// 更新小程序用户标签
func (l *label) updateMiniUserLabel(uid int64, userLabelConfig *usersetting.UserSetting,
	userLabelList *LabelList) error {
	keySuffix := fmt.Sprintf("%s_%d", library.UserLabel, uid)
	value, err := json.Marshal(userLabelList)
	if err != nil {
		logger.Warnf("用户标签格式化失败, err: %s", err)
		return err
	}
	if userLabelConfig != nil && userLabelConfig.ID > 0 {
		userLabelConfig.Value = string(value)
		if err := userLabelConfig.Update(); err != nil {
			logger.Errorf("更新用户配置失败, err: %s", err)
			return err
		}
	} else {
		userLabelConfig := &usersetting.UserSetting{
			UID:          uid,
			Key:          library.UserLabel,
			Value:        string(value),
			DeleteStatus: int32(library.DataStatusEnum.Valid),
		}
		if err := userLabelConfig.Save(); err != nil {
			logger.Warnf("插入用户配置失败 %s", err)
			return err
		}
	}
	if err := cache.Set(library.UserSetting, keySuffix, string(value), library.UserLabelCacheExpireTime); err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

// CalcUserLabel 计算用户标签
func (l *label) CalcUserLabel(uid int64, resourceType int32) error {
	// 查询标签资源相关标签
	resourceFilterTagList := l.getResourceLabelList()
	// 获取用户标签
	userLabelConfig, userLabelList, err := l.getUserLabelConfig(uid)
	if err != nil {
		return nil
	}

	// 查询用户最近一月标签元数据
	practiceLabelDataList, browseLabelDataList, searchLabelDataList := l.getUserLastLabelDataList(uid)
	if practiceLabelDataList == nil || browseLabelDataList == nil || searchLabelDataList == nil {
		return nil
	}

	// 首先清空标签
	userLabelList.BodyPosition = userLabelList.BodyPosition[0:0]
	userLabelList.Classification = userLabelList.Classification[0:0]
	userLabelList.PracticeTime = userLabelList.PracticeTime[0:0]
	userLabelList.DifficultyLevel = userLabelList.DifficultyLevel[0:0]
	userLabelList.UserEquityType = userLabelList.UserEquityType[0:0]
	userLabelList.UserGoalsNew = userLabelList.UserGoalsNew[0:0]
	userLabelList.StageOfYoga = userLabelList.StageOfYoga[0:0]
	userLabelList.DurationOfEachPractice = userLabelList.DurationOfEachPractice[0:0]
	userLabelList.ThePartWantPoPractice = userLabelList.ThePartWantPoPractice[0:0]
	// 判断用户是可以生成此类型标签
	userLabelList = l.calcUserPracticeLabel(uid, practiceLabelDataList, resourceFilterTagList, userLabelList)
	userLabelList = l.calcUserBrowseLabel(browseLabelDataList, resourceFilterTagList, userLabelList)
	userLabelList = l.calcUserSearchLabel(searchLabelDataList, resourceFilterTagList, userLabelList)
	// Prince 2020-08-13 计算用户会员相关标签
	userLabelList = l.calcUserVipLabel(uid, userLabelList)
	// 计算电商相关业务标签
	userLabelList = l.calcUserORLabel(uid, userLabelList)
	// 2021/09/03 许弘毅将训练营相关业务归类
	userLabelList = l.calcUserO2Label(uid, userLabelList)
	// 2021/10/08 chris增加判断用户是否开通名师会员权益
	userLabelList = l.calcUserKolOpenEquity(uid, userLabelList)
	// 2022-08-09 增加判断用户是否开通冥想会员权益
	userLabelList = l.calcUserNowOpenEquity(uid, userLabelList)
	// 2022-08-09 增加判断用户是否开通面部会员权益
	userLabelList = l.calcUserFaceEquity(uid, userLabelList)
	// 2022-08-12 增加判断用户智能课程表问卷上报结果
	userLabelList = l.calcIntelligenceScheduleQuestionnaireReportEquity(uid, userLabelList)
	// 2023-03-01 增加上次启动距今时间
	userLabelList = l.calcTimeSinceLastStart(uid, userLabelList)
	// 2023-06-29 增加上次进入OB距今天数的更新
	userLabelList = l.calcDaysSinceLastIntoOb(uid, userLabelList)

	// 需求 9.34.0 新增生理期信息
	userLabelList = l.calcPhysiologyPeriod(uid, userLabelList)
	// 更新注册时间等
	userLabelList = l.calcUserIntervalTime(uid, userLabelList)
	// 付费模型预测
	userLabelList = l.calcPredictResult(uid, userLabelList)
	// 用户来源
	userLabelList = l.calcUserSource(uid, userLabelList)
	// 老用户付费模型
	userLabelList = l.calcOldUserPredictResult(uid, userLabelList)
	// 用户退款意愿模型
	userLabelList = l.calcUserRefundPredictResult(uid, userLabelList)
	userLabelList.IsMiniAPP = 0 // 这里特殊处理，兼容app和小程序打通的情况
	if err := l.updateUserLabel(uid, userLabelConfig, userLabelList); err != nil {
		logger.Warnf("更新用户标签失败 err: %s", err)
	}
	return nil
}

func (l *label) calcUserRefundPredictResult(uid int64, userLabelList *LabelList) *LabelList {
	predictResult, err := srvcache.GetYogaRedis().Get(context.Background(),
		fmt.Sprintf("%d:%s", uid, "userRefund:predict:result")).Result()
	if err != nil && err.Error() != RedisErrNil {
		return userLabelList
	}
	if predictResult == "" {
		return userLabelList
	}
	predictResultInt, err := strconv.Atoi(predictResult)
	if err != nil {
		logger.Error(err)
		return userLabelList
	}
	userLabelList.RefundProb = predictResultInt
	return userLabelList
}

func (l *label) calcMiniappNewUserPredictResult(uid int64, userLabelList *LabelList) *LabelList {
	predictResult, err := srvcache.GetYogaRedis().Get(context.Background(),
		fmt.Sprintf("%s%d", "miniapp:predict:result:", uid)).Result()
	if err != nil && err.Error() != RedisErrNil {
		return userLabelList
	}
	if predictResult == "" {
		return userLabelList
	}
	predictResultInt, err := strconv.Atoi(predictResult)
	if err != nil {
		logger.Error(err)
		return userLabelList
	}
	userLabelList.MiniappNewUserProbability1 = predictResultInt
	return userLabelList
}

func (l *label) calcOldUserPredictResult(uid int64, userLabelList *LabelList) *LabelList {
	predictResult, err := srvcache.GetYogaRedis().Get(context.Background(),
		fmt.Sprintf("%d:%s", uid, "oldUser:predict:result")).Result()
	if err != nil && err.Error() != RedisErrNil {
		return userLabelList
	}
	if predictResult == "" {
		return userLabelList
	}
	predictResultInt, err := strconv.Atoi(predictResult)
	if err != nil {
		logger.Error(err)
		return userLabelList
	}
	userLabelList.OldUserPredictProbability1 = predictResultInt
	return userLabelList
}

func (l *label) calcUserSource(uid int64, userLabelList *LabelList) *LabelList {
	userChannel := GetChannel(uid)
	userLabelList.AdsChannel = userChannel
	return userLabelList
}

func GetChannel(uid int64) int {
	chFun := func(utmSource string) int {
		for channel, value := range library.ChannelMap {
			if strings.Replace(value, " ", "", 1) ==
				strings.Replace(utmSource, " ", "", 1) {
				return channel
			}
		}
		return library.AdChanneDef
	}
	adUIDChannel := client.TbAdUIDChannel.GetItemByUID(uid)
	if adUIDChannel != nil && adUIDChannel.UtmSource != "" {
		return chFun(adUIDChannel.UtmSource)
	}
	return library.AdChanneDef
}

const RedisErrNil = "redis: nil"

type PredictModelResp struct {
	PredictionBuy  []float64 `json:"prediction_buy"`
	PredictionHigh []float64 `json:"prediction_high"`
}

func (l *label) calcPredictResult(uid int64, userLabelList *LabelList) *LabelList {
	predictResult, err := srvcache.GetYogaRedis().Get(context.Background(),
		fmt.Sprintf("%d:%s", uid, "predict:result")).Result()
	if err != nil && err.Error() != RedisErrNil {
		return userLabelList
	}
	if predictResult != "" {
		predictModel := PredictModelResp{}
		err := json.Unmarshal([]byte(predictResult), &predictModel)
		if err != nil {
			logger.Error(err)
			return userLabelList
		}
		if len(predictModel.PredictionBuy) > 0 {
			userLabelList.PredictProbabilityBuy = int64(predictModel.PredictionBuy[0] * 10000)
		}
		if len(predictModel.PredictionHigh) > 0 {
			userLabelList.PredictProbabilityHigh = int64(predictModel.PredictionHigh[0] * 10000)
		}
		logger.Info("缓存预测模型值:", predictModel.PredictionBuy, predictModel.PredictionHigh,
			userLabelList.PredictProbabilityBuy, userLabelList.PredictProbabilityHigh)
	}
	return userLabelList
}

const NoReInstall = "-1"

func (l *label) calcUserIntervalTime(uid int64, userLabelList *LabelList) *LabelList {
	var numTime int64
	var err error
	cacheKey := fmt.Sprintf("%s:%d", library.UserNewInstallTimeKey, uid)
	installTime, _ := srvcache.GetYogaRedis().Get(context.Background(), cacheKey).Result()
	if installTime != "" {
		if installTime == NoReInstall {
			if userLabelList.ReinstallTime == 0 {
				userLabelList.RegisterToReinstall = -1
				return userLabelList
			}
			numTime = userLabelList.ReinstallTime
		}
		if numTime == 0 {
			numTime, err = strconv.ParseInt(installTime, 10, 32)
			if err != nil {
				logger.Error("strconv.ParseInt err", err)
				userLabelList.RegisterToReinstall = -1
				return userLabelList
			}
		}
		// 获取该设备第一次注册时间
		firstTime := GetUserFirstTime(uid)
		if firstTime < 0 {
			userLabelList.RegisterToReinstall = -1
			return userLabelList
		}
		// 计算间隔时间
		var intervalDays int64
		differenceTime := numTime - firstTime
		if differenceTime > 86400 {
			intervalDays = int64(math.Ceil(float64(differenceTime) / 86400))
		}
		userLabelList.RegisterToReinstall = intervalDays
		userLabelList.ReinstallTime = numTime
	}
	return userLabelList
}

func GetUserFirstTime(uid int64) int64 {
	sensorsAccount := third.TbATpSensorsData.GetItemByUID(uid)
	if sensorsAccount == nil {
		return -1
	}
	thirdUniqID := sensorsAccount.OpenID
	if thirdUniqID == "" {
		return -1
	}
	thirdAuthOld := third.TbATpSensorsData.GetItemByOpenID(thirdUniqID)
	accountInfoOld := user.TbAccountInfo.GetByUID(thirdAuthOld.UID)
	if accountInfoOld == nil {
		return -1
	}
	return int64(accountInfoOld.CreateTime)
}

func (l *label) ProcessUserLabel(payload *group.LastUserLabelData) error {
	// 保存标签元数据
	payload.ResourceType = int32(l.GetResourceType(payload))
	if err := payload.Save(); err != nil {
		return nil
	}
	go func(uid int64, resourceType int32) {
		if err := l.CalcUserLabel(uid, resourceType); err != nil {
			logger.Error("用户分群标签更新错误", err)
		}
	}(payload.UID, payload.ResourceType)
	return nil
}

// CalcMiniAppUserLabel 更新小程序用户标签
func (l *label) CalcMiniAppUserLabel(data *library.MiniAppUserLabel) error {
	// 获取用户标签
	userLabelConfig, userLabelList, err := l.getUserLabelConfig(data.UID)
	if err != nil {
		return nil
	}
	// OB年龄分群
	userLabelList.ObAge = data.Age
	// bmi
	userLabelList.BMI = data.BMI
	// 标记小程序来源
	userLabelList.IsMiniAPP = data.IsMiniAPP
	// 更新小程序新用户付费标签
	l.calcMiniappNewUserPredictResult(data.UID, userLabelList)
	// 更新用户标签
	if err = l.updateMiniUserLabel(data.UID, userLabelConfig, userLabelList); err != nil {
		logger.Warnf("更新瑜伽小程序用户标签失败 err: %s", err)
	}
	return nil
}

// calcUserVipLabel 计算用户会员相关维度标签
func (l *label) calcUserVipLabel(uid int64, userLabelList *LabelList) *LabelList {
	logger.Debug("ProcessUserLabel:", uid, userLabelList)
	// calcUserVipLabel
	if userLabelList.VipBuyRecord == 0 {
		if vipBuyRecordStatus := order.TbWebOrder.HasHistoryMember(uid); vipBuyRecordStatus {
			userLabelList.VipBuyRecord = 1
		} else {
			challengeBuyStatus := challenge.TbUser.HasJoinedUserChallenge(uid,
				int(library.ChallengeTypeEnum.NoviceChallenge),
				int(library.ChallengeTypeEnum.MemberChallenge),
				int(library.ChallengeTypeEnum.WelfareChallenge))
			// 整理自控基金表ID
			selfControlFundIDs := make([]int64, 0)
			for i := range challengeBuyStatus {
				selfControlFundIDs = append(selfControlFundIDs, challengeBuyStatus[i].ProductID)
			}
			// 查询“支付环节”为“不需要”的挑战赛
			total := challenge.TbProduct.GetFreeChallengeCountByIds(selfControlFundIDs)
			// 参加过新手、会员挑战赛 则为已购会员产品
			if total != int64(len(selfControlFundIDs)) {
				userLabelList.VipBuyRecord = 1
			}
		}
	}

	// 判断用户是否参加过新手挑战赛
	if userLabelList.NewUserChallenge == 0 {
		if newUserChallengeStatus := challenge.TbUser.CheckJoinedNewUserChallenge(uid); newUserChallengeStatus {
			userLabelList.NewUserChallenge = 1
		}
	}

	if userLabelList.AppWelfareChallenge == 0 {
		challengeList := challenge.TbUser.HasJoinedUserChallengeList(uid,
			int(library.ChallengeTypeEnum.MemberWelfare))
		for i := range challengeList {
			if challengeList[i].BusinessLine == library.SelfControlProductBusinessLineApp {
				userLabelList.AppWelfareChallenge = 1
				break
			}
		}
	}
	// 正在进行中的挑战赛
	userLabelList.UserChallengeIng = 0
	challengeList := challenge.TbUser.HasUserIngChallengeList(uid)
	if len(challengeList) > 0 {
		userLabelList.UserChallengeIng = 1
	}
	// 2021/07/09 许弘毅从 620 项目迁移 5 个专属 VIP 状态的分群属性
	vipStatusAttr, err := new(VIPStatusAttr).GetVIPStatusAttrByUID(uid)
	if err != nil {
		logger.Errorf("更新用户 VIP 状态失败,用户 UID 为:%d 错误为:%s", uid, err.Error())
	}
	userLabelList.UserVIPStatusAttr = vipStatusAttr
	// 2021/09/03 许弘毅统计用户累计在会员业务线交易的总次数与总金额,以及最后一次会员相关的业务订单是否是自动订阅
	memberTradeTotal := db_stat.TbUserMemberTradeTotal.GetByUID(uid)
	if memberTradeTotal != nil && memberTradeTotal.TradeTimes != userLabelList.BusinessVIPPurchaseTimes {
		// 更新总次数
		userLabelList.BusinessVIPPurchaseTimes = memberTradeTotal.TradeTimes
		// 更新交易总金额
		amount := GetUserMemberTradeAmount(uid)
		if amount != 0 {
			userLabelList.BusinessVIPPurchaseAmount = amount
		}
		userLabelList.LatestVIPTradeIsSub = 0
		// 判断最后一次是否是会员业务的自动订阅
		if JudgeUserLastPurchaseTypeIsSub(uid) {
			userLabelList.LatestVIPTradeIsSub = 1
		}
	}
	userLabelList.VipStatus = 0
	// 当用户没有权益的时候 未开通和当天过期时间默认是-1  过期时间第一天0开始的问题
	userLabelList.VipExpiredDays = -1
	userLabelList.VipLastDays = 0
	l.webMemberEquity(uid, userLabelList)
	// 获取用户 user_member_duration 记录进行计算
	userMemberDuration := user.TbUserMemberDuration.GeUserLastedDuration(uid)
	if userMemberDuration == nil {
		return userLabelList
	}
	currUnixTime := time.Now().Unix()
	if userMemberDuration.EndTime >= currUnixTime {
		userLabelList.VipStatus = 1
		VipLastDays := service.GetIntervalDays(currUnixTime, userMemberDuration.EndTime)
		if VipLastDays >= 1 {
			// Prince 2020-10-09 会员剩余时间从 0 开始
			userLabelList.VipLastDays = VipLastDays
		}
		userLabelList.UserEquityType = append(userLabelList.UserEquityType, library.UserEquityTypeEnum.Member.Int32())
	} else {
		vipExpiredDays := service.GetIntervalDays(userMemberDuration.EndTime, currUnixTime)
		if vipExpiredDays >= 1 {
			// Prince 2020-08-24 过期时间从 0 开始
			userLabelList.VipExpiredDays = vipExpiredDays - 2
		}
	}
	return userLabelList
}

// 9.20 新增用户属性
func (l *label) webMemberEquity(uid int64, userLabelList *LabelList) {
	// 增加会员权益
	userLabelList.SubMemberSelect = 0
	if wSub := order.TbWPSubU.GetWebSubscribeUser(uid); wSub != nil {
		if wp := order.TbWebProduct.GetByID(int64(wSub.ProductID)); wp != nil {
			if wp.DurationType == library.EquityDurationTypeEnum.Month.ToInt() {
				switch wp.DurationValue {
				case library.DurTypeEnum.One.ToInt():
					userLabelList.SubMemberSelect = library.SubDurTypeEnum.Month.ToInt()
				case library.DurTypeEnum.Three.ToInt():
					userLabelList.SubMemberSelect = library.SubDurTypeEnum.Quarter.ToInt()
				case library.DurTypeEnum.Six.ToInt():
					userLabelList.SubMemberSelect = library.SubDurTypeEnum.HalfYear.ToInt()
				case library.DurTypeEnum.Twelve.ToInt():
					userLabelList.SubMemberSelect = library.SubDurTypeEnum.Year.ToInt()
				}
			} else if wp.DurationType == library.EquityDurationTypeEnum.Year.ToInt() {
				userLabelList.SubMemberSelect = library.SubDurTypeEnum.Year.ToInt()
			}
			// 增加会员产品优惠类型
			userLabelList.VipDiscountType = wp.OfferType
		}
	}
}

// calcUserORLabel 计算电商业务相关的维度标签
func (l *label) calcUserORLabel(uid int64, userLabelList *LabelList) *LabelList {
	// 2021/09/03 许弘毅统计用户累计电商业务交易次数
	userLabelList.BusinessORPurchaseTimes = order.TbThirdKdtSubOrder.GetOrderTotalCountByUIDAndShopType(int(uid),
		order.KdtShopTypeEnum.OR)
	return userLabelList
}

// calcUserO2Label 计算训练营相关的维度标签
func (l *label) calcUserO2Label(uid int64, userLabelList *LabelList) *LabelList {
	// 判断是否已购训练营产品
	// 2021-08-21 产品@王凯确定这里判断逻辑和训练营详情页逻辑 yoga 保持一致
	if userLabelList.O2BuyRecord == 0 {
		// 以下分类购买后不算是训练营老学员, 老逻辑保持不变
		var categoryIds = []int32{148, 149, 150, 151}
		if o2BuyRecordStatus := yogao2school.TbSessionMember.CheckUserCampSessionsRecord(uid,
			[]int32{2, 3, 7}, categoryIds); o2BuyRecordStatus {
			userLabelList.O2BuyRecord = 1
		}
	}
	// 判断是否在训练营 有效期
	if yogao2school.TbSessionMember.CheckCampIsValid(uid) {
		userLabelList.CampIsValid = 1
	} else {
		userLabelList.CampIsValid = 0
	}

	// 2021/09/06 许弘毅判断是否是训练营新用户
	// 注:是否为新老用户取决于用户是否有成交的订单,由于可能存在第一笔成交的订单就有退款的行为因此这种情况还是新用户
	userLabelList.IsCampNewUser = 1 // 新用户
	if yogao2school.TbSessionMember.HavePurchasedO2ByUID(uid) {
		userLabelList.IsCampNewUser = 0 // 老用户
	}

	return userLabelList
}

// calcUserKolOpenEquity 判断用户是否开通名师课堂会员
func (l *label) calcUserKolOpenEquity(uid int64, userLabelList *LabelList) *LabelList {
	userLabelList.IsOpenKolEquity = 0 // 未开通名师课堂权益
	if equity.TbUserEquityDuration.ValidUserIsOpenKolEquityDuration(uid, library.EquityTypeEnum.Kol.ToInt32()) {
		userLabelList.IsOpenKolEquity = 1 // 已开通名师课堂权益
		userLabelList.UserEquityType = append(userLabelList.UserEquityType, library.UserEquityTypeEnum.Kol.Int32())
	}
	l.kolMemberEquity(uid, userLabelList)
	return userLabelList
}

// 9.20 新增用户属性
func (l *label) kolMemberEquity(uid int64, userLabelList *LabelList) {
	// 增加会员权益
	userLabelList.SubKolSelect = 0
	if wSub := order.TbPPSubU.GetWebSubscribeUser(uid); wSub != nil {
		if wp := equity.TbWebPreferentialPackage.GetItem(int64(wSub.ProductID)); wp != nil {
			if wp.PurchaseType != 2 {
				return
			}
			list, err := equity.TbWebPreferentialPackageDetail.GetList(int64(wSub.ProductID))
			if err != nil || len(list) < 1 {
				return
			}
			product := list[0]
			var durationType, durationValue int
			if product.EquityType == library.EquityTypeEnum.None.ToInt32() {
				if wp := order.TbWebProduct.GetByID(int64(wSub.ProductID)); wp != nil {
					durationType = wp.DurationType
					durationValue = wp.DurationValue
				}
			} else {
				if ep := equity.TbWekEquityProduct.GetItem(int64(wSub.ProductID)); ep != nil {
					durationType = int(ep.EquityDurationType)
					durationValue = int(ep.EquityDurationValue)
				}
			}
			if durationType == library.EquityDurationTypeEnum.Month.ToInt() {
				switch durationValue {
				case library.DurTypeEnum.One.ToInt():
					userLabelList.SubKolSelect = library.SubDurTypeEnum.Month.ToInt()
				case library.DurTypeEnum.Three.ToInt():
					userLabelList.SubKolSelect = library.SubDurTypeEnum.Quarter.ToInt()
				case library.DurTypeEnum.Six.ToInt():
					userLabelList.SubKolSelect = library.SubDurTypeEnum.HalfYear.ToInt()
				case library.DurTypeEnum.Twelve.ToInt():
					userLabelList.SubKolSelect = library.SubDurTypeEnum.Year.ToInt()
				}
			} else if durationType == library.EquityDurationTypeEnum.Year.ToInt() {
				userLabelList.SubKolSelect = library.SubDurTypeEnum.Year.ToInt()
			}
			// 增加特惠套餐产品优惠类型
			userLabelList.PackageDiscountType = wp.OfferType
		}
	}
}

func (l *label) calcUserNowOpenEquity(uid int64, userLabelList *LabelList) *LabelList {
	if equity.TbUserEquityDuration.ValidUserIsOpenKolEquityDuration(uid, library.EquityTypeEnum.Now.ToInt32()) {
		userLabelList.UserEquityType = append(userLabelList.UserEquityType, library.UserEquityTypeEnum.Now.Int32())
	}
	return userLabelList
}

func (l *label) calcUserFaceEquity(uid int64, userLabelList *LabelList) *LabelList {
	if equity.TbUserEquityDuration.ValidUserIsOpenKolEquityDuration(uid, library.EquityTypeEnum.Face.ToInt32()) {
		userLabelList.UserEquityType = append(userLabelList.UserEquityType, library.UserEquityTypeEnum.Face.Int32())
	}
	return userLabelList
}

// nolint
func (l *label) calcIntelligenceScheduleQuestionnaireReportEquity(uid int64, userLabelList *LabelList) *LabelList {
	reports, err := intelligence.TbIntelligenceScheduleQuestionnaireReport.GetIntelligenceByUID(uid)
	if err == nil && len(reports) > 0 {
		for i := range reports {
			goalTypeArr := formatScheduleQuestionnaireReportStr(reports[i].GoalType)
			yogaStageArr := formatScheduleQuestionnaireReportStr(reports[i].YogaStage)
			durationArr := formatScheduleQuestionnaireReportStr(reports[i].Duration)
			sitePreferenceArr := formatScheduleQuestionnaireReportStr(reports[i].SitePreference)
			ObAge := formatScheduleQuestionnaireReportStr(reports[i].Age)
			if reports[i].GoalType != "" {
				// 用户目标（新）：减脂塑形、身体调理、体态改善、入门到进阶
				for j := range goalTypeArr {
					goalType, _ := strconv.ParseInt(strings.Trim(goalTypeArr[j], "\""), 10, 32)
					userLabelList.UserGoalsNew = append(userLabelList.UserGoalsNew, int32(goalType))
				}
			}
			if reports[i].YogaStage != "" {
				// 所处瑜伽阶段：瑜伽新手、初级爱好者、中级爱好者、瑜伽大师
				for j := range yogaStageArr {
					yogaStage, _ := strconv.ParseInt(strings.Trim(yogaStageArr[j], "\""), 10, 32)
					userLabelList.StageOfYoga = append(userLabelList.StageOfYoga, int32(yogaStage))
				}
			}
			if reports[i].Duration != "" {
				// 每次练习时长：20分钟以内、20分钟以上
				for j := range durationArr {
					duration, _ := strconv.ParseInt(strings.Trim(durationArr[j], "\""), 10, 32)
					userLabelList.DurationOfEachPractice = append(userLabelList.DurationOfEachPractice, int32(duration))
				}
			}
			if reports[i].SitePreference != "" {
				// 想练习的部位：胸部、上肢、腰腹、臀腿、全身
				for j := range sitePreferenceArr {
					site, _ := strconv.ParseInt(strings.Trim(sitePreferenceArr[j], "\""), 10, 32)
					userLabelList.ThePartWantPoPractice = append(userLabelList.ThePartWantPoPractice, int32(site))
				}
			}

			if reports[i].Age != "" {
				// OB年龄分群
				for j := range ObAge {
					site, _ := strconv.ParseInt(strings.Trim(ObAge[j], "\""), 10, 32)
					userLabelList.ObAge = int32(site)
				}
			}
			// 扩展问题
			if reports[i].ExtQuestions != "" && reports[i].ExtQuestions != "{}" && reports[i].ExtQuestions != "[]" {
				var question Questions
				err := json.Unmarshal([]byte(reports[i].ExtQuestions), &question)
				if err == nil {
					userLabelList.PostureProblem = make([]int32, 0)
					userLabelList.SportsImpetus = make([]int32, 0)
					var result []string
					if question.PostureProblem != "" {
						_ = json.Unmarshal([]byte(question.PostureProblem), &result)
						for _, v := range result {
							i, _ := strconv.ParseInt(v, 10, 32)
							userLabelList.PostureProblem = append(userLabelList.PostureProblem, int32(i))
						}
					}
					if question.PostureProblem2 != "" {
						_ = json.Unmarshal([]byte(question.PostureProblem2), &result)
						for _, v := range result {
							i, _ := strconv.ParseInt(v, 10, 32)
							userLabelList.PostureProblem = append(userLabelList.PostureProblem, int32(i))
						}
					}
					if question.SportsImpetus != "" {
						_ = json.Unmarshal([]byte(question.SportsImpetus), &result)
						for _, v := range result {
							i, _ := strconv.ParseInt(v, 10, 32)
							userLabelList.SportsImpetus = append(userLabelList.SportsImpetus, int32(i))
						}
					}
					if question.SportsType != 0 {
						userLabelList.SportsType = question.SportsType
					}
				}
			}
		}
	}
	logger.Infof("用户：%d, userLabelList更新后为 %+v", uid, userLabelList)
	return userLabelList
}

func (l *label) calcTimeSinceLastStart(uid int64, userLabelList *LabelList) *LabelList {
	collect, err := rdsu.TbUserCollect.FindOrCreate(uid)
	if err != nil || collect == nil {
		logger.Error("UserCollectPoint 查询报错", err)
		return userLabelList
	}
	now := time.Now()
	if collect.BeforeLastLoginTime == 0 || comm.FormatStartTime(collect.LastLoginTime) < comm.FormatStartTime(now.Unix()) {
		if collect.LastLoginTime == 0 {
			collect.LastLoginTime = now.Unix()
		}
		collect.BeforeLastLoginTime = collect.LastLoginTime
	}
	userLabelList.TimeSinceLastLogin = int((now.Unix() - collect.BeforeLastLoginTime) / 60 / 60 / 24)
	return userLabelList
}

func (l *label) calcPhysiologyPeriod(uid int64, userLabelList *LabelList) *LabelList {
	period := dbpractice.TbPeriod.GetPeriodByUID(uid)
	if period == nil {
		return userLabelList
	}
	if period.KeepDay == 0 {
		period.KeepDay = calculateKeepDay(period.StartDate, period.EndDate)
	}
	last := dbpractice.TbPeriodRecord.GetLastRecord(uid)
	if last == nil {
		userLabelList.PhysiologyPeriod = Efficacy{

			CyclePeriod:   period.CyclePeriod,
			KeepDay:       period.KeepDay,
			LastStartDate: "",
			LastEndDate:   "",
		}
		return userLabelList
	}

	userLabelList.PhysiologyPeriod = Efficacy{
		CyclePeriod:   period.CyclePeriod,
		KeepDay:       period.KeepDay,
		LastStartDate: last.StartDate,
		LastEndDate:   last.EndDate,
	}
	return userLabelList
}

func calculateKeepDay(startDate, endDate string) int32 {
	dateFormat := "20060102"
	lastEndDate, _ := time.Parse(dateFormat, endDate)
	lastStartDate, _ := time.Parse(dateFormat, startDate)
	return int32(lastStartDate.Sub(lastEndDate).Hours()/24 + 1)
}

func formatScheduleQuestionnaireReportStr(questionnaire string) []string {
	questionnaire = strings.Trim(questionnaire, "[")
	questionnaire = strings.Trim(questionnaire, "]")
	questionnaireArr := strings.Split(questionnaire, ",")
	return questionnaireArr
}

// 更新上次进入OB距今天数和进入OB次数 (查询前一天有没有进入记录)
func (l *label) calcDaysSinceLastIntoOb(uid int64, userLabelList *LabelList) *LabelList {
	// OB所有的类型次数和上次进入OB时间获取
	res, err := grpc.GetUserCenterClient().GetUserObTypeTimes(context.Background(),
		&packagepay.GetUserObTypeTimesRequest{
			UID: uid,
		})
	if err != nil {
		logger.Warnf("请求GetUserObTypeTimes错误err: %v, 返回结果：%+v", err, res)
		return userLabelList
	}

	obProcessTypes := []int64{library.OldUser, library.Appreciation, library.Common}
	for _, obProcessType := range obProcessTypes {
		if _, ok := res.ObTypeAttributes[obProcessType]; !ok {
			break
		}
		// 组装
		obTypeAttribute := ObTypeAttribute{
			ObProcessType: obProcessType,
			ObAttribute: ObAttribute{
				LastIntoObTime: res.ObTypeAttributes[obProcessType].LastIntoObTime,
				IntoObTimes:    res.ObTypeAttributes[obProcessType].IntoObTimes,
			},
		}
		// 是否存在该属性
		if len(userLabelList.ObTypeAttributes) > 0 {
			var exist = false
			for i := range userLabelList.ObTypeAttributes {
				// 是否存在该类型
				if userLabelList.ObTypeAttributes[i].ObProcessType == obProcessType {
					userLabelList.ObTypeAttributes[i] = obTypeAttribute
					exist = true
					break
				}
			}
			if !exist {
				userLabelList.ObTypeAttributes = append(userLabelList.ObTypeAttributes, obTypeAttribute)
			}
		} else {
			userLabelList.ObTypeAttributes = make([]ObTypeAttribute, 0)
			userLabelList.ObTypeAttributes = append(userLabelList.ObTypeAttributes, obTypeAttribute)
		}
	}

	return userLabelList
}
