package user

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db_stat "gitlab.dailyyoga.com.cn/server/srv-task/databases/stat/member"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/order"
)

// GetUserMemberTradeAmount 获取用户在会员业务线的交易总金额
func GetUserMemberTradeAmount(uid int64) int {
	items := db_stat.TbUserMemberTrade.GetItemsByUID(uid)
	if items == nil {
		return 0
	}
	orders := make([]string, 0, len(items))
	for _, v := range items {
		orders = append(orders, v.OrderID)
	}
	return int(order.TbOrderMain.GetAmountByOrderIDs(orders))
}

// JudgeUserLastPurchaseTypeIsSub 判断用户最后一次在会员业务线消费的类型是否是订阅类型
func JudgeUserLastPurchaseTypeIsSub(uid int64) bool {
	// 获取用户在会员业务线的最后一条订单
	item := db_stat.TbUserMemberTrade.GetLastItemByUID(uid)
	if item == nil {
		return false
	}
	// 目前会员业务线只有会员单品是有自动续订的
	vipOrder, err := order.TbWebOrder.GetByOrderID(item.OrderID)
	if err != nil {
		logger.Errorf("%s", err.Error())
		return false
	}
	if vipOrder == nil {
		return false
	}
	// 判断该产品是否是自动续订的产品
	product := order.TbWebProduct.GetByID(vipOrder.ProductID)
	if product == nil {
		return false
	}
	// 购买类型，1单次购买 2订阅 3会员卡
	return product.PurchaseType == 2
}
