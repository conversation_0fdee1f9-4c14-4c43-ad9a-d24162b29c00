package challenge

import (
	"strconv"

	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/goroutine"
	sc "gitlab.dailyyoga.com.cn/server/go-artifact/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

type WelfareDailySuccess struct {
	IsSuccess    bool   `json:"is_success"`
	GroupID      string `json:"group_id"`
	ActivityID   int64  `json:"activity_id"`
	ActivityName string `json:"activity_name"`
}

// EventName 神策数据上报
func (WelfareDailySuccess) EventName() string {
	if config.Get().Env != env.Product && config.Get().Env != env.Mirror {
		return "h2_welfare_dailysuccess"
	}
	return "welfare_dailysuccess"
}

func (WelfareDailySuccess) Prefix() string {
	return ""
}

func (o *WelfareDailySuccess) Track(uid int64) {
	userAllVipLabelEvent := *o
	uidStr := strconv.FormatInt(uid, 10)
	goroutine.GoSafely(func() {
		sc.Track(uidStr, userAllVipLabelEvent, true)
	})
}

type WelfareDailyTask struct {
	ActivityID   int64  `json:"activity_id"`   // 传入活动id；属性值类型：number类型
	ActivityName string `json:"activity_name"` // 活动名称；属性值类型：字符串类型
	TaskID       int32  `json:"task_id"`       // 传入任务的排序；属性值类型：number类型
	TaskName     string `json:"task_name"`     // 传入任务的名称；属性值类型：字符串类型
	IsSuccess    bool   `json:"is_success"`    // 成功，失败
	PeriodType   int32  `json:"period_type"`   // 传入周期类型：天、周、月；属性值类型：字符串类型
	TaskType     int32  `json:"task_type"`     // 传入任务类型：计划任务、目标任务、时长任务；属性值类型：字符串类型
}

// EventName 神策数据上报
func (WelfareDailyTask) EventName() string {
	if config.Get().Env != env.Product && config.Get().Env != env.Mirror {
		return "h2_welfare_dailytask"
	}
	return "welfare_dailytask"
}

func (WelfareDailyTask) Prefix() string {
	return ""
}

func (o *WelfareDailyTask) Track(uid int64) {
	userAllVipLabelEvent := *o
	uidStr := strconv.FormatInt(uid, 10)
	goroutine.GoSafely(func() {
		sc.Track(uidStr, userAllVipLabelEvent, true)
	})
}
