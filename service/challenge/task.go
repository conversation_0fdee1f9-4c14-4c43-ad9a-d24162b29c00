package challenge

import (
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/goroutine"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/sessionplay"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/actionlog"
	dbchallenge "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/challenge"
	courseDb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/course"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/filter"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/live"
	userDb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/yogao2school"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/comm"
	courseServ "gitlab.dailyyoga.com.cn/server/srv-task/service/course"
	filterServ "gitlab.dailyyoga.com.cn/server/srv-task/service/filter"
)

type task struct {
}

var serviceTask task

// ProcessPlayTimeTask 练习时长任务逻辑实现
func (t *task) ProcessPlayTimeTask(uid int64,
	userChallenge *dbchallenge.User, product *dbchallenge.Product, startTime time.Time) bool {
	date, _ := strconv.Atoi(startTime.Format("20060102"))
	dateIndex := int64(date)
	dateBeginTime := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, time.Local)

	// 获取挑战赛练习时长任务
	taskItem := dbchallenge.TbTask.GetItemByCompleteType(userChallenge.ProductID,
		library.ChallengeTaskTypeEnum.PlayTimeTask, library.ChallengeCompleteTypeEnum.Everyday)
	if taskItem == nil {
		return false
	}

	// 根据挑战赛限制资源类型获取练习时长
	totalTime := t.calcChallengePlayTime(uid, dateBeginTime.Unix(),
		library.ResourceType(taskItem.BindType), taskItem.BindID)
	// 获取当天的任务完成信息
	cacheKey := fmt.Sprintf("%s:%d:%d:%d", library.RedisKeyChallengeTaskComplete, uid, taskItem.ID, dateIndex)
	taskComplete := dbchallenge.TbTaskComplete.GetItemByUIDTaskDate(uid, taskItem.ID, dateIndex)
	if taskComplete == nil {
		taskComplete = &dbchallenge.TaskComplete{
			UID:          uid,
			ProductID:    product.ID,
			TaskID:       taskItem.ID,
			DateIndex:    dateIndex,
			PracticeTime: totalTime,
		}
		if taskComplete.Status == library.ChallengeBoolLibraryEnum.No && totalTime >= taskItem.PracticeTime {
			taskComplete.Status = library.ChallengeBoolLibraryEnum.Yes
			taskComplete.TimeIndex = time.Now().Unix()
		}
		err := taskComplete.SaveAsNew(cacheKey)
		if err != nil {
			logger.Error("挑战赛时长任务添加数据异常", taskComplete, err)
			return false
		}
	} else {
		taskComplete.PracticeTime = totalTime
		if taskComplete.Status == library.ChallengeBoolLibraryEnum.No && totalTime >= taskItem.PracticeTime {
			taskComplete.Status = library.ChallengeBoolLibraryEnum.Yes
			taskComplete.TimeIndex = time.Now().Unix()
		}
		err := taskComplete.UpdateDate(cacheKey)
		if err != nil {
			logger.Error("挑战赛时长任务更新数据失败", taskItem, err)
			return false
		}
	}
	return taskComplete.Status == library.ChallengeBoolLibraryEnum.Yes
}

// ProcessSignTask 处理课程打卡任务
func (t *task) ProcessSignTask(uid int64,
	userChallenge *dbchallenge.User, product *dbchallenge.Product, startTime time.Time) bool {
	endTime := startTime.Add(time.Hour * 24)
	date, _ := strconv.Atoi(startTime.Format("20060102"))
	dateIndex := int64(date)

	// 获取完成打卡任务信息
	taskItem := dbchallenge.TbTask.GetItem(userChallenge.ProductID, library.ChallengeTaskTypeEnum.SignTask)
	if taskItem == nil {
		return false
	}

	var taskComplete *dbchallenge.TaskComplete
	var cacheKey string
	if taskItem.CompleteType == library.ChallengeCompleteTypeEnum.Everyday.ToInt32() {
		cacheKey = fmt.Sprintf("%s:%d:%d:%d", library.RedisKeyChallengeTaskComplete, uid, taskItem.ID, dateIndex)
		taskComplete = dbchallenge.TbTaskComplete.GetItemByUIDTaskDate(uid, taskItem.ID, dateIndex)
	} else {
		cacheKey = fmt.Sprintf("%s:%d:%d", library.RedisKeyChallengeTaskComplete, uid, taskItem.ID)
		taskComplete = dbchallenge.TbTaskComplete.GetItemByUID(uid, taskItem.ID)
		startTime = time.Unix(userChallenge.StartTime, 0)
		endTime = time.Unix(userChallenge.EndTime, 0)
	}

	if taskComplete != nil && taskComplete.Status == library.ChallengeBoolLibraryEnum.Yes {
		return true
	}

	bean := &db.UserPractice{
		UID:             uid,
		UserActionTable: library.PracticeFinish,
		ReportTime:      startTime.Unix(),
	}
	isFinished := false
	switch library.ResourceType(taskItem.BindType) {
	case library.ChallengeResourceTypeEnum.No:
		sessionLog := db.TbUserPractice.GetItemByReportTime(bean, startTime.Unix(), endTime.Unix())
		if sessionLog != nil {
			isFinished = true
		}
	case library.ChallengeResourceTypeEnum.Program:
		bean.UserActionTable = library.FinishSessionInProgram
		bean.ProgramID = taskItem.BindID
		sessionLog := db.TbUserPractice.GetItemByReportTime(bean, startTime.Unix(), endTime.Unix())
		if sessionLog != nil {
			isFinished = true
		}
	case library.ChallengeResourceTypeEnum.Session:
		bean.SessionID = taskItem.BindID
		sessionLog := db.TbUserPractice.GetItemByReportTime(bean, startTime.Unix(), endTime.Unix())
		if sessionLog != nil {
			isFinished = true
		}
	}

	// 未完成任务则直接退出，下面逻辑不再处理
	if !isFinished {
		return false
	}

	if taskComplete == nil {
		taskComplete = &dbchallenge.TaskComplete{
			UID:       uid,
			ProductID: product.ID,
			TaskID:    taskItem.ID,
			DateIndex: dateIndex,
		}
		if isFinished {
			taskComplete.Status = library.ChallengeBoolLibraryEnum.Yes
			taskComplete.TimeIndex = time.Now().Unix()
		}
		err := taskComplete.SaveAsNew(cacheKey)
		if err != nil {
			logger.Error("挑战赛时长任务添加数据异常", taskComplete, err)
			return false
		}
	} else {
		if isFinished {
			taskComplete.Status = library.ChallengeBoolLibraryEnum.Yes
			taskComplete.TimeIndex = time.Now().Unix()
		}
		err := taskComplete.UpdateDate(cacheKey)
		if err != nil {
			logger.Error("挑战赛时长任务更新数据失败", taskItem, err)
			return false
		}
	}
	return isFinished
}

// ProcessShareTask 处理课程分享任务
func (t *task) ProcessShareTask(uid int64,
	userChallenge *dbchallenge.User, product *dbchallenge.Product, startTime time.Time) bool {
	endTime := startTime.Add(time.Hour * 24)

	// 获取完成打卡任务信息
	taskItem := dbchallenge.TbTask.GetItem(userChallenge.ProductID, library.ChallengeTaskTypeEnum.ShareTask)
	if taskItem == nil {
		return false
	}

	// 先判断今天是否已经完成
	date, _ := strconv.Atoi(startTime.Format("20060102"))
	dateIndex := int64(date)
	var taskComplete *dbchallenge.TaskComplete
	var cacheKey string
	if taskItem.CompleteType == library.ChallengeCompleteTypeEnum.Everyday.ToInt32() {
		cacheKey = fmt.Sprintf("%s:%d:%d:%d", library.RedisKeyChallengeTaskComplete, uid, taskItem.ID, dateIndex)
		taskComplete = dbchallenge.TbTaskComplete.GetItemByUIDTaskDate(uid, taskItem.ID, dateIndex)
	} else {
		cacheKey = fmt.Sprintf("%s:%d:%d", library.RedisKeyChallengeTaskComplete, uid, taskItem.ID)
		taskComplete = dbchallenge.TbTaskComplete.GetItemByUID(uid, taskItem.ID)
		startTime = time.Unix(userChallenge.StartTime, 0)
		endTime = time.Unix(userChallenge.EndTime, 0)
	}

	if taskComplete != nil && taskComplete.Status == library.ChallengeBoolLibraryEnum.Yes {
		return true
	}

	findTypes := make([]int, 0)
	switch library.ResourceType(taskItem.BindType) {
	case library.ChallengeResourceTypeEnum.No:
		findTypes = append(findTypes, library.SharePlanResult, library.ShareCourseResult)
	case library.ChallengeResourceTypeEnum.Program:
		findTypes = append(findTypes, library.SharePlanResult)
	case library.ChallengeResourceTypeEnum.Session:
		findTypes = append(findTypes, library.ShareCourseResult)
	default:
	}

	isFinished := false
	for _, v := range findTypes {
		logs := actionlog.TbShareProgramSession.GetLogByTime(v, uid, startTime.Unix(), endTime.Unix())
		if logs != nil {
			isFinished = true
			break
		}
	}

	// 未完成任务则直接退出，下面逻辑不再处理
	if !isFinished {
		return false
	}

	if taskComplete == nil {
		taskComplete = &dbchallenge.TaskComplete{
			UID:       uid,
			ProductID: product.ID,
			TaskID:    taskItem.ID,
			DateIndex: dateIndex,
		}
		if isFinished {
			taskComplete.Status = library.ChallengeBoolLibraryEnum.Yes
			taskComplete.TimeIndex = time.Now().Unix()
		}
		err := taskComplete.SaveAsNew(cacheKey)
		if err != nil {
			logger.Error("挑战赛时长任务添加数据异常", taskComplete, err)
			return false
		}
	} else {
		if isFinished {
			taskComplete.Status = library.ChallengeBoolLibraryEnum.Yes
			taskComplete.TimeIndex = time.Now().Unix()
		}
		err := taskComplete.UpdateDate(cacheKey)
		if err != nil {
			logger.Error("挑战赛时长任务更新数据失败", taskItem, err)
			return false
		}
	}
	return isFinished
}

// ProcessFinishProgramOrO2Session 处理完成计划任务
func (t *task) ProcessFinishProgramOrO2Session(finishtype int, uid int64,
	userChallenge *dbchallenge.User, product *dbchallenge.Product, startTime time.Time) bool {
	endTime := startTime.Add(time.Hour * 24)

	// 获取完成打卡任务信息
	taskItem := dbchallenge.TbTask.GetItem(userChallenge.ProductID, library.ChallengeTaskTypeEnum.FinishProgram)
	if taskItem == nil {
		return false
	}

	// 先判断今天是否已经完成
	date, _ := strconv.Atoi(startTime.Format("20060102"))
	dateIndex := int64(date)

	var taskComplete *dbchallenge.TaskComplete
	var cacheKey string
	if taskItem.CompleteType == library.ChallengeCompleteTypeEnum.Everyday.ToInt32() {
		cacheKey = fmt.Sprintf("%s:%d:%d:%d", library.RedisKeyChallengeTaskComplete, uid, taskItem.ID, dateIndex)
		taskComplete = dbchallenge.TbTaskComplete.GetItemByUIDTaskDate(uid, taskItem.ID, dateIndex)
	} else {
		cacheKey = fmt.Sprintf("%s:%d:%d", library.RedisKeyChallengeTaskComplete, uid, taskItem.ID)
		taskComplete = dbchallenge.TbTaskComplete.GetItemByUID(uid, taskItem.ID)
		startTime = time.Unix(userChallenge.StartTime, 0)
		endTime = time.Unix(userChallenge.EndTime, 0)
	}

	if taskComplete != nil && taskComplete.Status == library.ChallengeBoolLibraryEnum.Yes {
		return true
	}

	isFinished := false
	switch library.ResourceType(taskItem.BindType) {
	case library.ChallengeResourceTypeEnum.No:
		if finishtype == library.FinishO2Session {
			sessionLog := actionlog.TbFinishO2Session.GetLogByTime(uid, startTime.Unix(), endTime.Unix())
			if sessionLog != nil {
				isFinished = true
			}
		} else {
			sessionLog := actionlog.TbFinishProgram.GetLogByTime(uid, startTime.Unix(), endTime.Unix())
			if sessionLog != nil {
				isFinished = true
			}
		}
	case library.ChallengeResourceTypeEnum.Program:
		if finishtype == library.FinishO2Session {
			sessionLog := actionlog.TbFinishO2Session.GetLogOfObjByTime(uid, taskItem.BindID,
				startTime.Unix(), endTime.Unix())
			if sessionLog != nil {
				isFinished = true
			}
		} else {
			sessionLog := actionlog.TbFinishProgram.GetLogOfObjByTime(uid, taskItem.BindID,
				startTime.Unix(), endTime.Unix())
			if sessionLog != nil {
				isFinished = true
			}
		}
	}

	// 未完成任务则直接退出，下面逻辑不再处理
	if !isFinished {
		return false
	}

	if taskComplete == nil {
		taskComplete = &dbchallenge.TaskComplete{
			UID:       uid,
			ProductID: product.ID,
			TaskID:    taskItem.ID,
			DateIndex: dateIndex,
		}
		if isFinished {
			taskComplete.Status = library.ChallengeBoolLibraryEnum.Yes
			taskComplete.TimeIndex = time.Now().Unix()
		}
		err := taskComplete.SaveAsNew(cacheKey)
		if err != nil {
			return false
		}
	} else {
		if isFinished {
			taskComplete.Status = library.ChallengeBoolLibraryEnum.Yes
			taskComplete.TimeIndex = time.Now().Unix()
		}
		err := taskComplete.UpdateDate(cacheKey)
		if err != nil {
			return false
		}
	}
	return isFinished
}

// calcPracticeTime 计算某次练习时长
func (t *task) calcPracticeTime(uid, programID, sessionID, reportTime int64) int64 {
	var playTime int64
	data := sessionplay.TbSessionPlay.GetList(uid, programID, sessionID, reportTime)
	for _, v := range data {
		playTime += v.PlayTime
	}
	return playTime
}

// calcChallengePlayTime 计算用户挑战赛时长
func (t *task) calcChallengePlayTime(uid, reportTime int64, bindType library.ResourceType, bindID int64) int64 {
	var totalTime int64
	practiceList := db.TbUserPractice.GetListByTimeIndex(uid, reportTime)
	for _, info := range practiceList {
		if info.UserActionTable == library.FinishPlan {
			continue
		}
		if bindType != library.ChallengeResourceTypeEnum.No &&
			bindType != library.ChallengeResourceTypeEnum.Session &&
			bindType != library.ChallengeResourceTypeEnum.Program {
			continue
		}

		if (bindType == library.ChallengeResourceTypeEnum.Session && bindID != info.SessionID) ||
			(bindType == library.ChallengeResourceTypeEnum.Program && bindID != info.ProgramID) {
			continue
		}

		if info.PlayTime == 0 {
			info.PlayTime = t.calcPracticeTime(uid, info.ProgramID, info.SessionID, info.ReportTime)
		}

		totalTime += info.PlayTime
	}

	totalTime /= 60
	return totalTime
}

// nolint
func (t *task) calcSpecialTypePlayTime(uid, reportTime, bindID int64, styleType int32) int64 {
	var totalTime int64
	programIDs := make([]int64, 0)
	sessionIDs := make([]int64, 0)
	programMaps := make(map[int64]int64)
	sessionMaps := make(map[int64]int64)
	practiceList := db.TbUserPractice.GetListByTimeIndex(uid, reportTime)
	for _, info := range practiceList {
		if info.UserActionTable == library.FinishPlan {
			continue
		}
		if styleType == library.SubTaskTypeEnum.KolProgram && bindID == info.ProgramID {
			programIDs = append(programIDs, info.ProgramID)
		} else if styleType == library.SubTaskTypeEnum.FaceProgram && bindID == info.ProgramID {
			programIDs = append(programIDs, info.ProgramID)
		} else if styleType == library.SubTaskTypeEnum.FaceSession && bindID == info.SessionID {
			sessionIDs = append(sessionIDs, info.SessionID)
		}
	}
	if len(programIDs) != 0 {
		programs := courseDb.TbProgram.GetProgramByIDs(programIDs)
		for _, program := range programs {
			if _, ok := programMaps[program.ProgramID]; !ok {
				programMaps[program.ProgramID] = program.SeriesType
			}
		}
	}
	if len(sessionIDs) != 0 {
		sessions := courseDb.TbSession.GetSessionByIDs(sessionIDs)
		for _, session := range sessions {
			if _, ok := sessionMaps[session.SessionID]; !ok {
				sessionMaps[session.SessionID] = session.SeriesType
			}
		}
	}
	for _, info := range practiceList {
		if info.UserActionTable == library.FinishPlan {
			continue
		}
		if (styleType == library.SubTaskTypeEnum.KolProgram && bindID != info.ProgramID) ||
			(styleType == library.SubTaskTypeEnum.FaceProgram && bindID != info.ProgramID) ||
			(styleType == library.SubTaskTypeEnum.FaceSession && bindID != info.SessionID) {
			continue
		}
		// 如果是kol计划
		if styleType == library.SubTaskTypeEnum.KolProgram && bindID == info.ProgramID {
			if v, ok := programMaps[info.ProgramID]; ok {
				if v != library.SessionSeriesTypeKol {
					continue
				}
			}
		} else if styleType == library.SubTaskTypeEnum.FaceProgram && bindID == info.ProgramID {
			if v, ok := programMaps[info.ProgramID]; ok {
				if v != library.PlanSeriesTypeFace {
					continue
				}
			}
		} else if styleType == library.SubTaskTypeEnum.FaceSession && bindID == info.SessionID {
			if v, ok := sessionMaps[info.SessionID]; ok {
				if v != library.SessionSeriesTypeFace {
					continue
				}
			}
		}
		if info.PlayTime == 0 {
			info.PlayTime = t.calcPracticeTime(uid, info.ProgramID, info.SessionID, info.ReportTime)
		}

		totalTime += info.PlayTime
	}
	logger.Debug("total time:", uid, reportTime, styleType, bindID, totalTime)
	return totalTime
}

// NewProcessTask 新流程任务
// nolint
func (t *task) NewProcessTask(uid int64,
	userChallenge *dbchallenge.User, reportTime int64) bool {
	var isFinished bool
	taskItem := dbchallenge.TbTask.GetItemByTaskType(userChallenge.ProductID,
		library.ChallengeTaskTypeEnum.NewTaskProcess)
	if taskItem == nil {
		return isFinished
	}
	practiceList := db.TbUserPractice.GetListByReportTime(uid, reportTime)
	if len(practiceList) < 1 {
		logger.Warnf("NewProcessTask 未查询到 uid %d userChallenge:%#v reportTime %d", uid, userChallenge, reportTime)
		return isFinished
	}
	date, err := strconv.Atoi(time.Unix(reportTime, 0).Format("20060102"))
	if err != nil {
		logger.Warnf("NewProcessTask 时间解析失败 userChallenge:%+v 完成练习:%d", userChallenge, reportTime)
		return isFinished
	}
	// 先判断今天是否已经完成
	dateIndex := int64(date)
	var cacheKey string
	cacheKey = fmt.Sprintf("%s:%d:%d:%d", library.RedisKeyChallengeTaskComplete, uid, taskItem.ID, dateIndex)
	taskComplete := dbchallenge.TbTaskComplete.GetItemByUIDTaskDate(uid, taskItem.ID, dateIndex)
	if taskComplete != nil && taskComplete.Status == library.ChallengeBoolLibraryEnum.Yes {
		return true
	}
	userTask, subTaskID, extTaskID := t.GetUserSpecifiedTask(dateIndex, userChallenge, taskItem)
	userComplete := t.getUserSubTask(dateIndex, userChallenge)
	completeIDX := make([]int32, 0) // 完成的子任务
	var practiceTime int64
	var GroupID int
	for _, info := range practiceList {
		logger.Infof("NewProcessTask practice %#v", info)
		if info.UserActionTable == library.FinishPlan {
			continue
		}
		var todayTask bool // 或
		for group, rw := range userTask {
			var oneTask bool // 且
			for skw, srw := range rw {
				logger.Infof("NewProcessTask userTask uid %d srw:%v,skw:%v ", info.UID, srw, skw)
				uTask, ok := userComplete[int64(srw.practiceIDX)]
				if !ok {
					uTask = nil
				}
				if srw.practiceIDX == 0 || (srw.SubTaskID == 0 && srw.ExtSubTaskID == 0) {
					logger.Error("NewProcessTask err uid %d zu:%d zi:%d srw:%#v ", info.UID, group, skw, srw)
				}
				if skw == 0 {
					oneTask = t.compareCondition(srw, info, uTask, userChallenge)
					if oneTask {
						completeIDX = append(completeIDX, srw.practiceIDX)
					}
					logger.Infof("NewProcessTask zu-oneTask uid %d zu:%d oneTask:%t ", info.UID, group, oneTask)
					continue
				}
				ucp := t.compareCondition(srw, info, uTask, userChallenge)
				if ucp {
					completeIDX = append(completeIDX, srw.practiceIDX)
				}
				logger.Infof("NewProcessTask zu-ucp uid %d zu:%d ucp:%t ", info.UID, group, ucp)
				oneTask = oneTask && ucp
			}
			logger.Infof("NewProcessTask zu uid %d zu:%d %t ", info.UID, group, oneTask)
			todayTask = todayTask || oneTask
			// 多组任务 最先完成 直接跳过不做后续处理了
			if todayTask {
				GroupID = group
				break
			}
		}
		if todayTask {
			isFinished = todayTask
			practiceTime += info.PlayTime / 60
			break
		}
	}
	// 未完成任务则直接退出，下面逻辑不再处理
	if !isFinished {
		return false
	}
	// 完成任务上报神策
	goroutine.GoSafely(
		func() {
			// 打卡上报神策
			t.reportSCDaily(uid, userChallenge, GroupID)
		})
	completeIDXJson, _ := json.Marshal(completeIDX)
	if taskComplete == nil {
		taskComplete = &dbchallenge.TaskComplete{
			UID:           uid,
			ProductID:     userChallenge.ProductID,
			TaskID:        taskItem.ID,
			DateIndex:     dateIndex,
			CompleteIndex: string(completeIDXJson),
			SubTaskID:     subTaskID,
			ExtSubTaskID:  extTaskID,
			PracticeTime:  practiceTime,
		}
		if isFinished {
			taskComplete.Status = library.ChallengeBoolLibraryEnum.Yes
			taskComplete.TimeIndex = time.Now().Unix()
		}
		err := taskComplete.SaveAsNew(cacheKey)
		if err != nil {
			logger.Error("NewProcessTask 挑战赛时长任务添加数据异常", taskComplete, err)
			return false
		}
		return isFinished
	}
	if isFinished {
		taskComplete.Status = library.ChallengeBoolLibraryEnum.Yes
		taskComplete.TimeIndex = time.Now().Unix()
		taskComplete.CompleteIndex = string(completeIDXJson)
		taskComplete.PracticeTime += practiceTime
	}
	err = taskComplete.UpdateDate(cacheKey)
	if err != nil {
		logger.Error("NewProcessTask 挑战赛时长任务更新数据失败", taskItem, err)
		return false
	}
	return isFinished
}

// reportSCDaily 完成任务上报神策
func (t *task) reportSCDaily(uid int64, userChallenge *dbchallenge.User, groupID int) {
	var selfName string
	selfItem := dbchallenge.TbProduct.GetItem(userChallenge.ProductID)
	if selfItem != nil {
		selfName = selfItem.Name
	}
	sc := &WelfareDailySuccess{
		IsSuccess:    true,
		GroupID:      strconv.Itoa(groupID),
		ActivityID:   userChallenge.ProductID,
		ActivityName: selfName,
	}
	sc.Track(uid)
}

// compareCondition 根据任务判断数据
// nolint
func (t *task) compareCondition(srw *SubTask, practice db.UserPractice,
	userComplete *dbchallenge.ProductUserSubTask, userChallenge *dbchallenge.User) bool {
	var complete bool
	if userComplete == nil {
		dateStr := time.Unix(practice.ReportTime, 0).Format("20060102")
		date, err := strconv.ParseInt(dateStr, 10, 64)
		if err != nil {
			logger.Warn("NewProcessTask 解析日期出错", err)
			return complete
		}
		userComplete = &dbchallenge.ProductUserSubTask{
			UID:           practice.UID,
			ProductID:     userChallenge.ProductID,
			SubTaskID:     srw.SubTaskID,
			ExtSubTaskID:  srw.ExtSubTaskID,
			DateIndex:     date,
			PracticeIndex: int64(srw.practiceIDX),
		}
		if err := userComplete.Save(); err != nil {
			logger.Error("NewProcessTask 保存挑战赛子任务出错 ", err)
			return complete
		}
	}
	startTime := time.Unix(practice.ReportTime, 0)
	dateBeginTime := time.Date(startTime.Year(),
		startTime.Month(), startTime.Day(), 0, 0, 0, 0, time.Local)
	var totalTime int64
	switch srw.StyleType {
	case library.SubTaskTypeEnum.Tags:
		session, tagList := GetSessionAndTagListByPractice(practice,
			&courseServ.Session{}, &courseServ.Program{})
		if session == nil || tagList == nil {
			return complete
		}
		logger.Infof("NewProcessTask tage uid:%d 任务:%#v 课程练习时长:%d", practice.UID, srw, practice.PlayTime)
		for _, v := range tagList {
			logger.Infof("NewProcessTask tage-1 uid:%d tage:%#v 课程练习时长:%d", practice.UID, v, practice.PlayTime)
			if int(v.ID) == srw.Value {
				practice.PlayTime /= 60
				totalTime = userComplete.PracticeTime + practice.PlayTime
				logger.Infof("NewProcessTask tage-2 uid:%d 任务:%#v 总时长:%d", practice.UID, srw, totalTime)
				practiceList := db.TbUserPractice.GetListByReportTime(practice.UID, practice.ReportTime)
				// 处理计划最后一节时 上报计划完成和计划中课程完成两个事件叠加的逻辑
				if len(practiceList) > 1 {
					for p := range practiceList {
						if practiceList[p].UserActionTable == library.FinishPlan {
							totalTime = t.calcChallengePlayTime(practice.UID, dateBeginTime.Unix(),
								library.ResourceTypeEnum.Program, practice.ProgramID)
							logger.Infof("NewProcessTask tage-2 计划完成 uid:%d 任务:%#v 总时长:%d",
								practice.UID, srw, totalTime)
							break
						}
					}
				}
				break
			}
		}
	case library.SubTaskTypeEnum.Program:
		// 根据挑战赛限制资源类型获取练习时长
		totalTime = t.calcChallengePlayTime(practice.UID, dateBeginTime.Unix(),
			library.ResourceTypeEnum.Program, int64(srw.Value))
	case library.SubTaskTypeEnum.Duration:
		totalTime = t.calcChallengePlayTime(practice.UID, dateBeginTime.Unix(),
			library.ResourceTypeEnum.UnKnown, 0)
	case library.SubTaskTypeEnum.KolProgram, library.SubTaskTypeEnum.FaceProgram, library.SubTaskTypeEnum.FaceSession:
		totalTime = t.calcSpecialTypePlayTime(userComplete.UID, dateBeginTime.Unix(), int64(srw.Value), srw.StyleType)
		totalTime /= 60
	default:
		logger.Errorf("NewProcessTask 未知任务类型 %#v", srw)
		return complete
	}
	logger.Infof("NewProcessTask 用户任务 uid:%d %#v 任务完成时长 %d 练习数据: %#v", practice.UID, srw, totalTime, practice)
	// 防止不满足条件把时间更新为0
	if totalTime > 0 {
		if totalTime >= int64(srw.Time) {
			complete = true
			userComplete.Status = library.ChallengeBoolLibraryEnum.Yes.ToInt32()
		}
		userComplete.PracticeTime = totalTime
		if err := userComplete.Update(); err != nil {
			logger.Error("NewProcessTask 更新挑战赛子任务出错 ", err)
			return false
		}
	}
	// 因为一个资源需要匹配多组任务 有可能某个已经完成了
	if userComplete.Status == library.ChallengeBoolLibraryEnum.Yes.ToInt32() {
		complete = true
	}
	return complete
}

// getUserSubTask 获取用户任务完成情况
func (t *task) getUserSubTask(date int64, userSelfProduct *dbchallenge.User) map[int64]*dbchallenge.ProductUserSubTask {
	userSubTaskData := dbchallenge.TbUserSubTask.GetList(userSelfProduct.UID, userSelfProduct.ProductID, date)
	userSubTask := make(map[int64]*dbchallenge.ProductUserSubTask)
	for _, v := range userSubTaskData {
		userSubTask[v.PracticeIndex] = v
	}
	return userSubTask
}

// GetUserSpecifiedTask 获取挑战赛指定日期的任务
func (t *task) GetUserSpecifiedTask(date int64, userSelfProduct *dbchallenge.User,
	task *dbchallenge.Task) (dateTask [][]*SubTask, subTaskID, extSubTaskID int64) {
	taskList, _ := dayTaskProcess(userSelfProduct, task)
	if len(taskList) < 1 {
		return nil, 0, 0
	}
	var ok bool
	dateTask, ok = taskList[date]
	if !ok {
		return nil, 0, 0
	}
	if len(dateTask) > 0 && len(dateTask[0]) > 0 {
		subTaskID = dateTask[0][0].SubTaskID
		extSubTaskID = dateTask[0][0].ExtSubTaskID
	}
	return dateTask, subTaskID, extSubTaskID
}

type SubTaskItem struct {
	ID        int64            `json:"id"`
	TaskID    int64            `json:"task_id"`
	ExtTaskID int64            `json:"ext_task_id"`
	PassDays  int64            `json:"pass_days"`
	TimeType  library.TimeType `json:"time_type"`
	TaskInfo  string           `json:"task_info"`
}

// dayTaskProcess 处理每天的挑战赛的任务
// nolint
func dayTaskProcess(userSelfProduct *dbchallenge.User,
	task *dbchallenge.Task) (taskList map[int64][][]*SubTask, dayList []int64) {
	var allTaskList []*SubTaskItem
	subTaskList := dbchallenge.TbSubTask.GetList(task.ID)
	if len(subTaskList) < 1 {
		return
	}
	taskStartTime := comm.FormatStartTime(userSelfProduct.StartTime)
	taskEndTime := comm.FormatStartTime(userSelfProduct.EndTime)
	diffDay := comm.Days(taskStartTime, comm.FormatEndTime(taskEndTime), false)
	for _, subTask := range subTaskList {
		subTaskItem := &SubTaskItem{
			ID:       subTask.ID,
			TaskID:   subTask.TaskID,
			TimeType: subTask.TimeType,
			TaskInfo: subTask.TaskInfo,
		}
		allTaskList = append(allTaskList, subTaskItem)
	}
	// 判断用户是否升级，升级后添加附加任务
	var extDays int64
	extTask := dbchallenge.TbUserExtTask.GetItemByUIDToUserID(userSelfProduct.UID, userSelfProduct.ID)
	if extTask != nil {
		// 获取附加任务详情
		extSubTask := dbchallenge.TbProductExtTask.GetItemByID(extTask.ExtTaskID)
		if extSubTask != nil {
			extDays = extSubTask.ExtDays
		}
		// 填充挑战赛任务
		challengeLength := int(diffDay - extDays)
		if len(allTaskList) < challengeLength {
			lastItem := allTaskList[len(allTaskList)-1] // 获取最后一个任务项
			for len(allTaskList) < challengeLength {
				allTaskList = append(allTaskList, &SubTaskItem{
					ID:       lastItem.ID,
					TaskID:   lastItem.TaskID,
					TimeType: lastItem.TimeType,
					TaskInfo: lastItem.TaskInfo,
				})
			}
		}
		extSubTaskList := dbchallenge.TbExtSubTask.GetList(extTask.ExtTaskID)
		if len(extSubTaskList) > 0 {
			for _, subTask := range extSubTaskList {
				subTaskItem := &SubTaskItem{
					ID:        subTask.ID,
					ExtTaskID: subTask.ExtTaskID,
					TimeType:  subTask.TimeType,
					TaskInfo:  subTask.TaskInfo,
				}
				allTaskList = append(allTaskList, subTaskItem)
			}
		}
		// 填充附加任务
		allLength := int(diffDay - extDays)
		if len(allTaskList) < allLength {
			lastItem := allTaskList[len(allTaskList)-1] // 获取最后一个任务项
			for len(allTaskList) < allLength {
				allTaskList = append(allTaskList, &SubTaskItem{
					ID:       lastItem.ID,
					TaskID:   lastItem.TaskID,
					TimeType: lastItem.TimeType,
					TaskInfo: lastItem.TaskInfo,
				})
			}
		}
	}
	taskList = make(map[int64][][]*SubTask)
	dayList = make([]int64, 0)
	for _, ts := range allTaskList {
		subTask := make([][]*SubTask, 0)
		err := json.Unmarshal([]byte(ts.TaskInfo), &subTask)
		logger.Infof("NewProcessTask TaskInfo uid:%d %s", userSelfProduct.UID, ts.TaskInfo)
		if err != nil {
			logger.Warn("NewProcessTask json 未能解析", ts.TaskInfo)
			return
		}
		// 组装 pIndex 和 SubTaskID 数据
		formatIDXTaskID(subTask, ts)
		switch ts.TimeType {
		case library.TimeTypeEnum.Day:
			taskStartTime, dayList = formatSubTask(taskStartTime, taskList, dayList, subTask)
			if taskStartTime == 0 {
				return
			}
		case library.TimeTypeEnum.Week:
			for n := 0; n < 7; n++ {
				taskStartTime, dayList = formatSubTask(taskStartTime, taskList, dayList, subTask)
				if taskStartTime == 0 {
					return
				}
			}
		case library.TimeTypeEnum.Month:
			for n := 0; n < 30; n++ {
				taskStartTime, dayList = formatSubTask(taskStartTime, taskList, dayList, subTask)
				if taskStartTime == 0 {
					return
				}
			}
		}
		if taskStartTime > userSelfProduct.EndTime {
			break
		}
	}
	// 不全任务 加入只配置一个任务做任务补全
	if taskStartTime < userSelfProduct.EndTime {
		s := allTaskList[len(allTaskList)-1]
		subTask := make([][]*SubTask, 0)
		err := json.Unmarshal([]byte(s.TaskInfo), &subTask)
		if err != nil {
			return
		}
		// 组装 pIndex 和 SubTaskID 数据
		formatIDXTaskID(subTask, s)
		for n := 0; n < int(diffDay); n++ {
			taskStartTime, dayList = formatSubTask(taskStartTime, taskList, dayList, subTask)
			if taskStartTime == 0 {
				return
			}
		}
	}
	// 任务时序 正序
	sort.Slice(dayList, func(i, j int) bool {
		return dayList[i] < dayList[j]
	})
	// 格式化 删除多余天数的挑战赛数据 输出数据
	cutOut := comm.Days(comm.FormatStartTime(userSelfProduct.StartTime),
		comm.FormatEndTime(userSelfProduct.EndTime), false)
	if len(dayList) > int(cutOut) {
		dayList = dayList[:cutOut]
		taskTempList := taskList
		taskList = make(map[int64][][]*SubTask)
		for _, d := range dayList {
			if _, ok := taskTempList[d]; !ok {
				taskList[d] = make([][]*SubTask, 0)
			}
			taskList[d] = taskTempList[d]
		}
	}
	return taskList, dayList
}

// 拼接数据
func formatIDXTaskID(subTask [][]*SubTask, ts *SubTaskItem) {
	pIndex := 1
	for t := range subTask {
		for tt := range subTask[t] {
			subTask[t][tt].practiceIDX = int32(pIndex)
			subTask[t][tt].SubTaskID = ts.ID
			subTask[t][tt].ExtSubTaskID = ts.ExtTaskID
			pIndex++
		}
	}
}

type SubTask struct {
	StyleType    int32 `json:"style_type"` // 1 目标，2 计划,3 分钟
	Value        int   `json:"value"`      // 下拉框选择id
	Time         int   `json:"time"`       // 分钟数
	SubTaskID    int64 // 任务ID
	ExtSubTaskID int64 // 附加任务ID
	practiceIDX  int32 // 子任务虚拟ID
}

// formatSubTask 处理任务
// nolint
func formatSubTask(taskStartTime int64, taskList map[int64][][]*SubTask,
	dayList []int64, subTask [][]*SubTask) (int64, []int64) {
	dateStr := time.Unix(taskStartTime, 0).Format("20060102")
	date, err := strconv.ParseInt(dateStr, 10, 64)
	if err != nil {
		logger.Warn("NewProcessTask 解析日期出错", err)
		return 0, dayList
	}
	if _, ok := taskList[date]; !ok {
		taskList[date] = make([][]*SubTask, 0)
	}
	taskList[date] = subTask
	dayList = append(dayList, date)
	taskStartTime += 86400
	return taskStartTime, dayList
}

// GetSessionAndTagListByPractice 根据练习记录获取目标列表
// nolint
func GetSessionAndTagListByPractice(userPractice db.UserPractice,
	courseService *courseServ.Session, programService *courseServ.Program) (*courseDb.Session, []*filter.Tag) {
	var sourceID int64
	var sourceType int32
	var session *courseDb.Session
	switch userPractice.UserActionTable {
	case library.PlayQuitSession, library.PracticeFinish:
		session = courseService.GetSessionByID(userPractice.SessionID)
		if session == nil {
			return nil, nil
		}
		if session.SeriesType != library.SessionSeriesTypeGeneral || session.ContentType != library.SessionContentTypeAction {
			return nil, nil
		}
		sourceID = userPractice.SessionID
		sourceType = library.ObjTypeEnum.Session
	case library.PlayQuitUserSchedule, library.LeaderUserScheduleUnit:
		scheduleUnit := userDb.TbScheduleUnit.GetItem(userPractice.SessionID)
		if scheduleUnit == nil {
			return nil, nil
		}
		session = courseService.GetSessionByID(scheduleUnit.SessionID)
		if session == nil {
			return nil, nil
		}
		if session.SeriesType != library.SessionSeriesTypeGeneral || session.ContentType != library.SessionContentTypeAction {
			return nil, nil
		}
		sourceID = scheduleUnit.SessionID
		sourceType = library.ObjTypeEnum.Session
	case library.FinishSessionInProgram, library.PlayQuitSessionInProgram:
		session = courseService.GetSessionByID(userPractice.SessionID)
		if session == nil {
			return nil, nil
		}
		programInfo := programService.GetProgramByID(userPractice.ProgramID)
		if programInfo == nil {
			return nil, nil
		}
		sourceID = userPractice.ProgramID
		switch programInfo.SeriesType {
		case library.ProgramSeriesTypeGeneral:
			sourceType = library.ObjTypeEnum.Program
		case library.ProgramSeriesTypeKol:
			sourceType = library.ObjTypeEnum.Kol
		case library.ProgramSeriesTypeO2:
			sourceType = library.ObjTypeEnum.O2
			o2Session := yogao2school.TbSession.GetSessionInfoBySourceID(sourceID)
			if o2Session == nil {
				return nil, nil
			}
			sourceID = o2Session.ID
		default:
			return nil, nil
		}
	case library.LivePlayback, library.LivePlaybackComplete, library.LiveBroadcast:
		sourceType = library.ObjTypeEnum.Live
		liveSession := live.TbLiveSession.GetItem(userPractice.SessionID)
		if liveSession == nil {
			return nil, nil
		}
		sourceID = liveSession.LiveSessionMainID
		session = &courseDb.Session{}
	}
	tagList := filterServ.Tag.GetTagListBySourceAndPID(sourceType, int32(sourceID), library.TagPidPracticeTarget)
	return session, tagList
}
