package challenge

import (
	"net/url"
	"sort"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/challenge"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type TaskInfo struct {
	UID        int64                     `json:"uid"`
	TaskType   library.ChallengeTaskType `json:"task_type"`
	Date       string                    `json:"date"`
	ReportTime int64                     `json:"report_time"`
}

type challenge struct {
}

var ServiceChallenge challenge

// ProcessTask 处理挑战赛任务
func (c *challenge) ProcessTask(taskInfo TaskInfo) {
	userChallengeList := db.TbUser.GetListByUserAndStatusForApp(taskInfo.UID,
		library.ChallengeToUserStatusEnum.Underway)
	// 判断用户是否参加挑战赛
	if len(userChallengeList) == 0 {
		logger.Debugf("用户没有参加挑战赛, taskInfo: %#v", taskInfo)
		return
	}

	dateTime, err := time.ParseInLocation("2006-01-02", taskInfo.Date, time.Local)
	if err != nil {
		logger.Warn("时间解析失败", taskInfo)
		return
	}

	for _, userChallenge := range userChallengeList {
		if !userChallenge.IsChallengeOnGoing(time.Unix(taskInfo.ReportTime, 0)) {
			logger.Info("processPlayTimeTask:用户挑战赛未进行中", userChallenge)
			continue
		}

		// 获取挑战赛信息
		product := db.TbProduct.GetItem(userChallenge.ProductID)
		if product == nil {
			logger.Warn("未找到挑战赛", userChallenge)
			continue
		}
		processRet := false
		switch taskInfo.TaskType {
		case library.ChallengeTaskTypeEnum.PlayTimeTask:
			processRet = serviceTask.ProcessPlayTimeTask(taskInfo.UID, userChallenge, product, dateTime)
		case library.ChallengeTaskTypeEnum.SignTask:
			processRet = serviceTask.ProcessSignTask(taskInfo.UID, userChallenge, product, dateTime)
		case library.ChallengeTaskTypeEnum.ShareTask:
			processRet = serviceTask.ProcessShareTask(taskInfo.UID, userChallenge, product, dateTime)
		case library.ChallengeTaskTypeEnum.FinishProgram:
			processRet = serviceTask.ProcessFinishProgramOrO2Session(library.FinishPlan, taskInfo.UID, userChallenge,
				product, dateTime)
		case library.ChallengeTaskTypeEnum.FinishO2Session:
			processRet = serviceTask.ProcessFinishProgramOrO2Session(library.FinishO2Session, taskInfo.UID,
				userChallenge, product, dateTime)
		case library.ChallengeTaskTypeEnum.NewTaskProcess:
			processRet = serviceTask.NewProcessTask(taskInfo.UID, userChallenge, taskInfo.ReportTime)
		}

		// 用户完成任务，检查是否完成当天所有任务
		if processRet {
			logger.Info("用户任务练习完成，检查当天打卡状态", userChallenge)
			c.checkUserDailyTaskStatus(taskInfo.UID, userChallenge, product, dateTime, taskInfo.ReportTime)
		}
	}
}

// checkUserDailyTaskStatus 检查用户挑战赛打卡信息
func (c *challenge) checkUserDailyTaskStatus(uid int64, record *db.User, product *db.Product,
	dateTime time.Time, reportTime int64) {
	challengeEndTime := time.Unix(record.EndTime, 0)
	isLastDay := false
	if challengeEndTime.Format("2006-01-02") == dateTime.Format("2006-01-02") {
		// 当前时间是挑战赛最后一天，检查用户挑战赛完成状态
		isLastDay = true
	}

	dateIndex, _ := strconv.Atoi(dateTime.Format("20060102"))
	if todayRecord := db.TbPracticeHistory.GetHistory(record.ID, int64(dateIndex)); todayRecord != nil {
		logger.Info("用户已完成今日打卡", record)
		c.checkResultDetermination(isLastDay, record, product)
		return
	}
	taskList := db.TbTask.GetList(product.ID)
	if taskList == nil {
		logger.Warn("未找到挑战赛任务", record)
		return
	}

	result := true
	for _, task := range taskList {
		if task.CompleteType == library.ChallengeCompleteTypeEnum.Once.ToInt32() {
			continue
		}
		taskComplete := db.TbTaskComplete.GetItemByUIDTaskDate(uid, task.ID, int64(dateIndex))
		if taskComplete == nil {
			result = false
			logger.Info("用户今日任务未完成", uid, task.ID, dateIndex)
			break
		}
		if taskComplete.Status == library.ChallengeBoolLibraryEnum.No {
			result = false
			logger.Info("用户今日任务未完成", taskComplete)
			break
		}
	}

	if !result {
		logger.Debug("用户当天任务未完成", record)
		return
	}
	logger.Debug("用户当天任务已完成", record)

	newHistory := db.PracticeHistory{
		UID:        uid,
		UserID:     record.ID,
		DateIndex:  int64(dateIndex),
		CreateTime: reportTime,
	}
	if err := newHistory.SaveAsNew(); err != nil {
		logger.Warn(err)
	}
	c.checkResultDetermination(isLastDay, record, product)
}

func (c *challenge) checkResultDetermination(lastDay bool, record *db.User, product *db.Product) {
	// 先检测是否可以提前结束，如果可以提前结束则执行一次checkResult
	if c.isNeedCheckResult(record, product) || lastDay {
		logger.Debugf("调用yoga服务checkResult")
		go c.checkUserChallengeResult(record.ID)
	}
}

// checkUserChallengeResult 调用yoga接口检查挑战赛
func (c *challenge) checkUserChallengeResult(toUserID int64) {
	values := url.Values{}
	values.Add("record_id", strconv.FormatInt(toUserID, 10))
	if resp, err := requests.NewYogaClient(config.Get().PhpYogaAddress).
		PostForm("/usercenter/Selfcontrolfund/checkResult", values); err != nil {
		logger.Error(err)
	} else {
		logger.Debugf("resp url: %s; status_code: %d; body: %s", resp.RequestURL, resp.StatusCode, resp.Body)
	}
}

// isNeedCheckResult 是否需要调用checkResult发放挑战赛奖励
func (c *challenge) isNeedCheckResult(record *db.User, product *db.Product) bool {
	if record.Status != library.ChallengeToUserStatusEnum.Underway.ToInt32() { // 是进行中
		logger.Warnf("用户状态异常; uid: %d; status: %d;", record.UID, record.Status)
		return false
	}

	var cnt int64
	// 获取count
	if product.PassType == library.ChallengePassTypeEnum.Total.ToInt32() {
		cnt = db.TbPracticeHistory.GetHistoryCount(record.ID)
	} else if product.PassType == library.ChallengePassTypeEnum.Keep.ToInt32() {
		cnt = c.getKeepDays(record.ID)
	} else {
		logger.Warnf("挑战赛成功方式配置异常; product_id: %d", product.ID)
		return false
	}
	logger.Debugf("挑战赛计数; cnt: %d; pass_days: %d", cnt, record.PassDays)
	return cnt >= int64(record.PassDays)
}

// getKeepDays 获取连续天数
func (c *challenge) getKeepDays(toUserID int64) int64 {
	timeList := db.TbPracticeHistory.GetAllHistoryCreateTime(toUserID)
	if len(timeList) == 0 {
		return 0
	}

	// 排序
	sort.Slice(timeList, func(i, j int) bool {
		return timeList[i] < timeList[j]
	})

	interruptIndex := make([]int, 0)
	keepDays := 0

	// 先找出中断的点，存入interruptIndex
	// 然后将所有连续的天数计算出来，与keepDays比较
	for i := 0; i < len(timeList)-1; i++ {
		// 全部转换为零点数据
		nextDate, _ := time.Parse("2006-01-02 00:00:00", time.Unix(timeList[i+1], 0).Format("2006-01-02 00:00:00"))
		curDate, _ := time.Parse("2006-01-02 00:00:00", time.Unix(timeList[i], 0).Format("2006-01-02 00:00:00"))
		if nextDate.Sub(curDate).Hours()/24 != 1 {
			interruptIndex = append(interruptIndex, i)
		}
	}

	if len(interruptIndex) == 0 {
		return int64(len(timeList))
	}

	maker := 0
	for _, index := range interruptIndex {
		if cnt := len(timeList[maker : index+1]); keepDays < cnt {
			keepDays = cnt
		}
		maker = index + 1
	}
	// 如果maker小于timeList的长度，则表示后面还有一组连续时间，需要判断这个连续时间与keepDays的大小
	if maker < len(timeList) {
		if cnt := len(timeList[maker:]); keepDays < cnt {
			keepDays = cnt
		}
	}
	return int64(keepDays)
}
