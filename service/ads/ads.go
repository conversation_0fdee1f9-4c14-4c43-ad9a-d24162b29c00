package ads

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/stat/top"
	dbtop "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/top"
)

type ads struct{}

var ServiceAds ads

type ReportRequest struct {
	DeviceID   string         `json:"device_id"`
	UID        int64          `json:"uid"`
	TopID      int64          `json:"top_id"`
	Channels   int32          `json:"channels"`
	DeviceType int32          `json:"device_type"`
	Version    int32          `json:"version"`
	CreateTime int64          `json:"create_time"`
	UpdateTime int64          `json:"update_time"`
	ReportType top.ReportType `json:"report_type"`
	AdType     int32          `json:"ad_type"`
}

// 广告展示上报
func (a *ads) AdsReport(params string) {
	// 客户端请求参数解析
	var reportRequest ReportRequest
	if err := json.Unmarshal([]byte(params), &reportRequest); err != nil {
		logger.Warn("客户端请求解析失败， log_id：", err)
		return
	}

	// 写入数据
	switch reportRequest.ReportType {
	case top.ReportTypeEnum.ReportShow:
		bean := top.Show{
			DeviceID:   reportRequest.DeviceID,
			UID:        reportRequest.UID,
			TopID:      reportRequest.TopID,
			AdType:     reportRequest.AdType,
			Channels:   reportRequest.Channels,
			Version:    reportRequest.Version,
			DeviceType: reportRequest.DeviceType,
			CreateTime: reportRequest.CreateTime,
			UpdateTime: reportRequest.UpdateTime,
		}
		if err := bean.Save(); err != nil {
			logger.Error(err)
			return
		}
	case top.ReportTypeEnum.ReportClick:
		bean := top.Click{
			DeviceID:   reportRequest.DeviceID,
			UID:        reportRequest.UID,
			TopID:      reportRequest.TopID,
			AdType:     reportRequest.AdType,
			Channels:   reportRequest.Channels,
			Version:    reportRequest.Version,
			DeviceType: reportRequest.DeviceType,
			CreateTime: reportRequest.CreateTime,
			UpdateTime: reportRequest.UpdateTime,
		}
		if err := bean.Save(); err != nil {
			logger.Error(err)
			return
		}
	case top.ReportTypeEnum.ReportGet:
		bean := top.Get{
			DeviceID:   reportRequest.DeviceID,
			UID:        reportRequest.UID,
			TopID:      reportRequest.TopID,
			AdType:     reportRequest.AdType,
			Channels:   reportRequest.Channels,
			Version:    reportRequest.Version,
			DeviceType: reportRequest.DeviceType,
			CreateTime: reportRequest.CreateTime,
			UpdateTime: reportRequest.UpdateTime,
		}
		if err := bean.Save(); err != nil {
			logger.Error(err)
			return
		}
	case top.ReportTypeEnum.ReportHref:
		bean := top.Href{
			DeviceID:   reportRequest.DeviceID,
			UID:        reportRequest.UID,
			TopID:      reportRequest.TopID,
			AdType:     reportRequest.AdType,
			Channels:   reportRequest.Channels,
			Version:    reportRequest.Version,
			DeviceType: reportRequest.DeviceType,
			CreateTime: reportRequest.CreateTime,
			UpdateTime: reportRequest.UpdateTime,
		}
		if err := bean.Save(); err != nil {
			logger.Error(err)
			return
		}
	}
	if reportRequest.ReportType != top.ReportTypeEnum.ReportShow {
		return
	}

	incrClickCnt(reportRequest)
}

func incrClickCnt(reportRequest ReportRequest) {
	if reportRequest.AdType == int32(top.TopTypeEnum.VideoType) {
		getTopItem := dbtop.TbAdsOnVideo.GetTopItem(reportRequest.TopID)
		if getTopItem != nil && getTopItem.ID > 0 {
			if err := getTopItem.IncrClickCnt(); err != nil {
				return
			}
		}
	} else {
		getTopItem := dbtop.TbTop.GetTopItem(reportRequest.TopID)
		if getTopItem != nil && getTopItem.ID > 0 {
			if err := getTopItem.IncrClickCnt(); err != nil {
				return
			}
		}
	}
}
