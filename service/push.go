package service

import (
	"fmt"
	"net/url"
	"strconv"

	"gitlab.dailyyoga.com.cn/server/srv-task/library"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

type push struct{}

var Push push

type PushData struct {
	UID         int64
	UserID      string
	Content     string
	Title       string
	LinkType    int32
	UserType    int32
	Platform    int32
	Link        string
	ObjID       int64
	Logo        string
	Image       string
	ImagePad    string
	MessageType int32
	ChannelType int32
	Tags        []string
	TargetTitle string
	SignID      library.SignIDType
	ItemType    library.ItemTypeEnum
}

func (p *push) Send(data *PushData) error {
	params := url.Values{}
	params.Set("userid", data.UserID)
	params.Set("content", data.Content)
	params.Set("title", data.Title)
	params.Set("linktype", strconv.Itoa(int(data.LinkType)))
	params.Set("usertype", strconv.Itoa(int(data.UserType)))
	params.Set("platform", strconv.Itoa(int(data.Platform)))
	params.Set("link", data.Link)
	params.Set("objid", fmt.Sprintf("%d", data.ObjID))
	params.Set("logo", data.Logo)
	params.Set("image", data.Image)
	params.Set("image_pad", data.ImagePad)
	params.Set("message_type", strconv.Itoa(int(data.MessageType)))
	params.Set("channel_type", strconv.Itoa(int(data.ChannelType)))
	params.Set("_f", "inner_api")
	params.Set("debug", "1")
	params.Set("target_title", data.TargetTitle)
	params.Set("signId", strconv.Itoa(int(data.SignID)))
	params.Set("sign_id", strconv.Itoa(int(data.SignID)))
	params.Set("item_type", strconv.Itoa(int(data.ItemType)))

	for _, tag := range data.Tags {
		params.Set("tags[]", tag)
	}

	logger.Debugf("push data:%s", params.Encode())
	resp, err := requests.New620Client(config.Get().Php620Address).PostForm("/admin/systempush", params)
	if err != nil {
		return err
	}
	if !resp.IsOK() {
		logger.Errorf("send yoga_notice_push error. status: %d, params: %s", resp.StatusCode, params.Encode())
	}
	return nil
}
