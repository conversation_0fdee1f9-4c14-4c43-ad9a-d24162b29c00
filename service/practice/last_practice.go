package practice

import (
	"encoding/json"
	"sort"
	"strconv"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	dbpractice "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/course"
	dbu "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type lastPractice struct{}

var LastPractice lastPractice

type Intensity struct {
	DisplayDuration string `json:"display_duration"`
	Duration        string `json:"duration"`
}

// LastPracticeLog 记录用户上次练习
func (p *lastPractice) LastPracticeLog(practice *dbpractice.UserPractice, practiceDate, o2SessionID int64) {
	if _, ok := library.LastPracticeLogTypes[practice.UserActionTable]; !ok {
		return
	}
	resourceType := practiceResourceType(practice)
	if resourceType == library.ResourceTypeEnum.UnKnown.ToInt32() {
		return
	}
	var session *course.Session
	if practice.UserActionTable == library.LeaderUserScheduleUnit ||
		practice.UserActionTable == library.PlayQuitUserSchedule {
		scheduleUnit := dbu.TbScheduleUnit.GetItem(practice.SessionID)
		if scheduleUnit == nil || scheduleUnit.ID < 1 {
			return
		}
		session = course.TbSession.GetSessionByID(scheduleUnit.SessionID)
	} else {
		session = course.TbSession.GetSessionByID(practice.SessionID)
	}
	if session == nil || session.ContentType == library.SessionContentTypeNow {
		return
	}
	if practice.UserActionTable == library.PlayQuitSession ||
		practice.UserActionTable == library.PlayQuitSessionInProgram ||
		practice.UserActionTable == library.PlayQuitUserSchedule {
		// 课程练习时长超过（≥）该课程总时长的50%，则该课程也进入到「最近练习」模块列表
		if session.Duration < 1 {
			if extend := course.TbSession.GetMakePageInfo(practice.SessionID); extend != nil {
				intensityList := make([]Intensity, 0)
				if err := json.Unmarshal([]byte(extend.Intensity), &intensityList); err == nil &&
					intensityList[0].Duration != "" {
					if j, err := strconv.ParseInt(intensityList[0].Duration, 10, 32); err == nil {
						session.Duration = int32(j)
					}
				}
			}
		}
		duration := library.ToFloat32(session.Duration * 60 / 2)
		if duration > 0 && library.ToFloat32(practice.PlayTime) < duration {
			return
		}
	}
	lastItem := getLastPracticeItem(practice, o2SessionID, resourceType)
	if lastItem != nil {
		lastItem.ProgramID = practice.ProgramID
		lastItem.SessionID = practice.SessionID
		lastItem.SessionIndex = practice.SessionIndex
		lastItem.SubSessionIndex = practice.SubSessionIndex
		lastItem.UserActionTable = practice.UserActionTable
		lastItem.ReportTime = practice.ReportTime
		lastItem.LinkType = practice.LinkType
		lastItem.SchedulePracticeDate = practiceDate
		lastItem.ResourceType = resourceType
		lastItem.O2SessionID = o2SessionID
		err := lastItem.UpdateMustCols()
		if err != nil {
			logger.Error(err)
		}
		return
	}
	lastItem = &dbpractice.LastPractice{
		UID:                  practice.UID,
		ProgramID:            practice.ProgramID,
		SessionID:            practice.SessionID,
		SessionIndex:         practice.SessionIndex,
		SubSessionIndex:      practice.SubSessionIndex,
		UserActionTable:      practice.UserActionTable,
		ReportTime:           practice.ReportTime,
		LinkType:             practice.LinkType,
		SchedulePracticeDate: practiceDate,
		ResourceType:         resourceType,
		O2SessionID:          o2SessionID,
	}
	err := lastItem.Save()
	if err != nil {
		logger.Error(err)
	}
}

// 获取用户练习资源类型
func practiceResourceType(practice *dbpractice.UserPractice) int32 {
	if practice.UserActionTable == library.LeaderUserScheduleUnit ||
		practice.UserActionTable == library.PlayQuitUserSchedule {
		return library.ResourceTypeEnum.UserSchedule.ToInt32()
	}
	if practice.UserActionTable == library.PracticeFinish || practice.UserActionTable == library.PlayQuitSession {
		// 2021-07-28 water LinkType 96 智能课表创建
		if practice.SessionIndex != 0 || practice.LinkType == 96 {
			if practice.UserActionTable == library.PlayQuitSession {
				return library.ResourceTypeEnum.UnKnown.ToInt32()
			}
			return library.ResourceTypeEnum.IntelligenceSchedule.ToInt32()
		}
		return library.ResourceTypeEnum.Session.ToInt32()
	}
	program := course.TbProgram.GetProgramByID(practice.ProgramID)
	if program == nil {
		return library.ResourceTypeEnum.UnKnown.ToInt32()
	}
	if v, ok := library.SeriesTypeToPracticeType[library.SeriesType(program.SeriesType)]; ok {
		return v.ToInt32()
	}
	return library.ResourceTypeEnum.UnKnown.ToInt32()
}

// getLastPracticeItem 组装数据
func getLastPracticeItem(practice *dbpractice.UserPractice, o2SessionID int64,
	resourceType int32) *dbpractice.LastPractice {
	lastList := dbpractice.TbLastPractice.GetListByUID(practice.UID)
	if lastList == nil {
		return nil
	}
	userPractice := make(map[int32][]*dbpractice.LastPractice)
	for _, v := range lastList {
		if _, ok := userPractice[v.ResourceType]; !ok {
			userPractice[v.ResourceType] = make([]*dbpractice.LastPractice, 0)
		}
		userPractice[v.ResourceType] = append(userPractice[v.ResourceType], v)
	}
	if u, ok := userPractice[resourceType]; ok {
		if s := sameData(u, practice, o2SessionID); s != nil {
			return s
		}
	}
	if len(lastList) < library.LastPracticeItemNum {
		return nil
	}
	sort.Slice(lastList, func(i, j int) bool {
		return lastList[i].UpdateTime < lastList[j].UpdateTime
	})
	return lastList[0]
}

// sameData 智能课表在该列表中有内容展示时仅展示1个内容，练习多个智能课表课程时，更新该内容
func sameData(u []*dbpractice.LastPractice, p *dbpractice.UserPractice, o2SessionID int64) *dbpractice.LastPractice {
	for _, v := range u {
		switch v.ResourceType {
		case library.ResourceTypeEnum.IntelligenceSchedule.ToInt32(), library.ResourceTypeEnum.UserSchedule.ToInt32():
			return v
		case library.ResourceTypeEnum.Program.ToInt32(),
			library.ResourceTypeEnum.Kol.ToInt32(), library.ResourceTypeEnum.FaceProgram.ToInt32():
			if v.ProgramID == p.ProgramID {
				return v
			}
		case library.ResourceTypeEnum.Session.ToInt32(), library.ResourceTypeEnum.FaceSession.ToInt32():
			if v.SessionID == p.SessionID {
				return v
			}
		case library.ResourceTypeEnum.O2.ToInt32():
			if v.O2SessionID == o2SessionID {
				return v
			}
		}
	}
	return nil
}
