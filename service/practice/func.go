package practice

import (
	"math"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"
)

// GetDisplayPlayTime 获取对外显示的练习时长（60s向下取整）
func GetDisplayPlayTime(playTime int64) int64 {
	var displayPlayTime int64

	if playTime > 0 {
		t := playTime % 60
		displayPlayTime = playTime - t
	}
	return displayPlayTime
}

// GetWeekCount 计算周
func GetWeekCount(endTime int64) int64 {
	var WeekCount int64
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)

	// 计算周开始时间
	weekStart := "2018-03-05"
	ts, _ := time.ParseInLocation("2006-01-02", weekStart, loc)
	WeekStart := ts.Unix()
	// 获取总天数
	TotalDays := service.GetIntervalDays(WeekStart, endTime)
	stringTotalDays := strconv.FormatInt(int64(TotalDays), 10)
	floatTotalDays64, err := strconv.ParseFloat(stringTotalDays, 32)
	if err != nil {
		logger.Error("数据异常", floatTotalDays64)
		return WeekCount
	}
	floatTotalDays := float32(floatTotalDays64)
	WeekDays := float32(7)

	WeekCountFloat64 := math.Ceil(float64(floatTotalDays / WeekDays))
	WeekCount = int64(WeekCountFloat64)
	return WeekCount
}

// GetTimeRangeByWeek 根据周计算日期范围
func GetTimeRangeByWeek(week int64) (start, end int64) {
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	base, _ := time.ParseInLocation("2006-01-02", "2018-03-05", loc)

	weekStart := base.Unix() + 7*(week-1)*86400
	weekEnd := base.Unix() + 7*week*86400 - 1
	return weekStart, weekEnd
}

// 获取指定天的开始时间
func GetDayStartTime(in time.Time) time.Time {
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	out, _ := time.ParseInLocation(library.DateFormatDay, in.Format(library.DateFormatDay), loc)
	return out
}

// 计算卡路里
func CalcCalorie(calorieRatio, playTime int64) int64 {
	return calorieRatio * playTime / 60 * library.StandBodyWeight / 1000
}
