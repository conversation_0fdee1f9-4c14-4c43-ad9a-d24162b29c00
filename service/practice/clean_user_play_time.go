package practice

import (
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/playtime"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/sessionplay"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type cleanPlayTime struct {
}

var CleanPlayTime cleanPlayTime

// CleanUserPractice 计算用户总的练习数
func (s *cleanPlayTime) CleanUserPractice(uid int64) error {
	// 计算4月每一天的练习时长以及消耗的卡路里
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	startDate, err := time.ParseInLocation("20060102", "20210401", loc)
	if err != nil {
		return err
	}
	endDate := startDate.AddDate(0, 1, -1)
	for dateIndex := startDate; dateIndex.Unix() <= endDate.Unix(); dateIndex = dateIndex.AddDate(0, 0, 1) {
		if playTime, calorie := s.calcDayPlayTimeAndCalorie(uid, dateIndex.Format("20060102")); playTime > 0 &&
			calorie > 0 {
			if table := playtime.TbDay.GetItem(uid, dateIndex.Format("20060102")); table != nil {
				table.PlayTime = playTime
				table.DisplayPlayTime = GetDisplayPlayTime(playTime)
				table.Calorie = calorie
			}
			err := (&playtime.Day{
				PlayTime:        playTime,
				DisplayPlayTime: GetDisplayPlayTime(playTime),
				Calorie:         calorie,
			}).UpdateByCond(uid, dateIndex.Format("20060102"))
			if err != nil {
				return err
			}
		}
	}

	// 计算4月每一周的练习时长
	startDate, err = time.ParseInLocation("20060102", "20210401", loc)
	if err != nil {
		return err
	}
	endDate = startDate.AddDate(0, 1, -1)
	startWeek := GetWeekCount(startDate.Unix())
	endWeek := GetWeekCount(endDate.Unix())
	for week := startWeek; week <= endWeek; week++ {
		playTime := s.calcWeekPlayTime(uid, week)
		err = (&playtime.Week{
			PlayTime:        playTime,
			DisplayPlayTime: GetDisplayPlayTime(playTime),
		}).UpdateByCond(uid, strconv.Itoa(int(week)))
		if err != nil {
			return err
		}
	}

	// 计算4月的练习时长
	month := "202104"
	playTime := s.calcMonthPlayTime(uid, month)
	err = (&playtime.Month{
		PlayTime:        playTime,
		DisplayPlayTime: GetDisplayPlayTime(playTime),
	}).UpdateByCond(uid, month)
	if err != nil {
		return err
	}

	// 计算2021年的练习时长
	year := "2021"
	playTime = s.calcYearPlayTime(uid, year)
	err = (&playtime.Year{
		PlayTime:        playTime,
		DisplayPlayTime: GetDisplayPlayTime(playTime),
	}).UpdateByCond(uid, year)
	if err != nil {
		return err
	}

	// 计算总的练习时长以及消耗的卡路里
	var calorie int32
	playTime, calorie = s.calcTotalPlayTimeAndCalorie(uid)
	err = (&playtime.Collect{
		PlayTime:        playTime,
		DisplayPlayTime: GetDisplayPlayTime(playTime),
		Calorie:         calorie,
	}).UpdateByCond(uid)
	if err != nil {
		return err
	}

	// 汇总
	PlayTime.setUserCollect(&sessionplay.Detail{
		UID: uid,
	})
	return nil
}

// CleanUserRepeatPlayTime 客户端bug 清除用户重复数据
// nolint
func (s *cleanPlayTime) CleanUserRepeatPlayTime(uid int64, timeDate string) error {
	var diffPlayTime, diffCalorie int64
	// 要清理的日期
	cleanDate, err := time.ParseInLocation("20060102", timeDate, time.Local)
	if err != nil {
		return err
	}
	// 获取当天的所有练习数据
	userPracticeList := db.TbUserPractice.GetListByTimeIndex(uid, cleanDate.Unix())
	if len(userPracticeList) < 1 {
		return nil
	}
	// 找出重复数据
	countMap := make(map[int64]int64)
	var duplicates []db.UserPractice
	for _, v := range userPracticeList {
		countMap[v.ReportTime]++
	}
	for _, practice := range userPracticeList {
		// 统计重复数据里userActionTable为78和79的数据
		if countMap[practice.ReportTime] > 1 && (practice.UserActionTable == library.PlayQuitSession ||
			practice.UserActionTable == library.PlayQuitSessionInProgram) {
			duplicates = append(duplicates, practice)
		}
	}
	if len(duplicates) == 0 {
		return nil
	}
	// 累加重复数据
	for _, repeatData := range duplicates {
		diffPlayTime += repeatData.PlayTime
		diffCalorie += repeatData.Calorie
	}
	// 开始清理
	// 获取用户当天练习总时长
	table := playtime.TbDay.GetItem(uid, cleanDate.Format("20060102"))
	// 修改当天总时长、展示时长、卡路里
	temp := table.PlayTime - diffPlayTime
	table.PlayTime = temp
	table.DisplayPlayTime = GetDisplayPlayTime(temp)
	table.Calorie -= int32(diffCalorie)
	err = table.Update()
	if err != nil {
		return err
	}
	// 处理周数据
	repeatWeek := GetWeekCount(cleanDate.Unix())
	// 根据 st_play_time_day_ 表 计算一周的练习时长
	weekPlayTime := s.calcWeekPlayTime(uid, repeatWeek)
	err = (&playtime.Week{
		PlayTime:        weekPlayTime,
		DisplayPlayTime: GetDisplayPlayTime(weekPlayTime),
	}).UpdateByCond(uid, strconv.Itoa(int(repeatWeek)))
	if err != nil {
		return err
	}
	// 处理月数据
	month := time.Unix(cleanDate.Unix(), 0).Format("200601")
	monthPlayTime := s.calcMonthPlayTime(uid, month)
	err = (&playtime.Month{
		PlayTime:        monthPlayTime,
		DisplayPlayTime: GetDisplayPlayTime(monthPlayTime),
	}).UpdateByCond(uid, month)
	if err != nil {
		return err
	}
	// 处理年数据
	year := time.Unix(cleanDate.Unix(), 0).Format("2006")
	oYPlayTime := playtime.TbYear.GetItem(uid, year)
	if oYPlayTime != nil {
		temp = oYPlayTime.PlayTime - diffPlayTime
		oYPlayTime.PlayTime = temp
		oYPlayTime.DisplayPlayTime = GetDisplayPlayTime(temp)
		err = oYPlayTime.Update()
		if err != nil {
			return err
		}
	}
	// 处理总的练习时长以及消耗的卡路里
	collect := playtime.TbCollect.GetItem(uid)
	if collect != nil {
		temp = collect.PlayTime - diffPlayTime
		collect.PlayTime = temp
		collect.DisplayPlayTime = GetDisplayPlayTime(temp)
		collect.Calorie -= int32(diffCalorie)
		err = collect.Update()
		if err != nil {
			return err
		}
	}
	// 删除用户重复数据(软删除)
	for _, repeatData := range duplicates {
		repeatData.UID = 666 // 把这些重复的数据uid随便改个就行
		err = repeatData.Update()
		if err != nil {
			return err
		}
	}
	return nil
}

// CleanUserPlayTime 清理用户的练习时长 202109 water
func (s *cleanPlayTime) CleanUserPlayTime(uid int64) error {
	// 新修改的 数据特征为 用户report_time相同时间出现2条一模一样记录，且其中一条记录user_action_table为78
	// 清理用户练习时长
	startDate, err := time.ParseInLocation("20060102", "20210801", time.Local)
	if err != nil {
		return err
	}
	endDate := startDate.AddDate(0, 1, -1)
	var diffPlayTime, diffDPlayTime int64
	var diffCalorie int32
	// 从开始时间到结束时间 循环每一天
	for dateIndex := startDate; dateIndex.Unix() <= endDate.Unix(); dateIndex = dateIndex.AddDate(0, 0, 1) {
		// 根据当天练习记录计算当天的练习时长及卡路里 st_user_practice_表
		playTime, calorie := s.calcDayPlayTimeAndCalorie(uid, dateIndex.Format("20060102"))
		// 根据用户练习时长按天统计表获取当天练习总时长 st_play_time_day_
		table := playtime.TbDay.GetItem(uid, dateIndex.Format("20060102"))
		if table != nil && (playTime > 0 || calorie > 0) {
			// 练习时长
			diffPlayTime += (table.PlayTime - playTime)
			// 展示时长
			diffDPlayTime += (table.DisplayPlayTime - GetDisplayPlayTime(playTime))
			// 卡路里
			diffCalorie += (table.Calorie - calorie)

			table.PlayTime = playTime
			table.DisplayPlayTime = GetDisplayPlayTime(playTime)
			table.Calorie = calorie
			err = table.Update()
			if err != nil {
				return err
			}
		}
	}

	// 计算每一周的练习时长
	startWeek := GetWeekCount(startDate.Unix())
	endWeek := GetWeekCount(endDate.Unix())
	for week := startWeek; week <= endWeek; week++ {
		// 根据 st_play_time_day_ 表 计算一周的练习时长
		playTime := s.calcWeekPlayTime(uid, week)
		err = (&playtime.Week{
			PlayTime:        playTime,
			DisplayPlayTime: GetDisplayPlayTime(playTime),
		}).UpdateByCond(uid, strconv.Itoa(int(week)))
		if err != nil {
			return err
		}
	}

	// 计算月的练习时长
	month := "202108"
	// 根据 st_play_time_day_ 表 计算一个月的练习时长
	playTime := s.calcMonthPlayTime(uid, month)
	err = (&playtime.Month{
		PlayTime:        playTime,
		DisplayPlayTime: GetDisplayPlayTime(playTime),
	}).UpdateByCond(uid, month)
	if err != nil {
		return err
	}

	// 计算2021年的练习时长
	year := "2021"
	oYPlayTime := playtime.TbYear.GetItem(uid, year)
	if oYPlayTime != nil {
		oYPlayTime.PlayTime -= diffPlayTime
		oYPlayTime.DisplayPlayTime -= diffDPlayTime
		err = oYPlayTime.Update()
		if err != nil {
			return err
		}
	}

	// 计算总的练习时长以及消耗的卡路里
	collect := playtime.TbCollect.GetItem(uid)
	if collect != nil {
		collect.PlayTime -= diffPlayTime
		collect.DisplayPlayTime = diffDPlayTime
		collect.Calorie -= diffCalorie
		err = collect.Update()
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *cleanPlayTime) calcDayPlayTimeAndCalorie(uid int64, dateIndex string) (playTime int64, calorie int32) {
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	startTime, err := time.ParseInLocation("20060102", dateIndex, loc)
	if err != nil {
		logger.Error(err)
		return 0, 0
	}
	userPractices := db.TbUserPractice.GetListByTimeIndex(uid, startTime.Unix())
	if len(userPractices) < 1 {
		return 0, 0
	}

	for _, v := range userPractices {
		playTime += v.PlayTime
		calorie += int32(v.Calorie)
	}
	return playTime, calorie
}

func (s *cleanPlayTime) calcWeekPlayTime(uid, week int64) int64 {
	weekStart, weekEnd := GetTimeRangeByWeek(week)
	startDate := time.Unix(weekStart, 0).Format("20060102")
	endDate := time.Unix(weekEnd, 0).Format("20060102")
	days := playtime.TbDay.GetListByTimeRange(uid, startDate, endDate)

	var playTime int64
	for _, day := range days {
		playTime += day.PlayTime
	}
	return playTime
}

func (s *cleanPlayTime) calcMonthPlayTime(uid int64, month string) int64 {
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	startDate, err := time.ParseInLocation("200601", month, loc)
	if err != nil {
		logger.Error(err)
		return 0
	}
	endDate := startDate.AddDate(0, 1, -1)
	days := playtime.TbDay.GetListByTimeRange(uid, startDate.Format("20060102"), endDate.Format("20060102"))

	var playTime int64
	for _, day := range days {
		playTime += day.PlayTime
	}
	return playTime
}

func (s *cleanPlayTime) calcYearPlayTime(uid int64, year string) int64 {
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	startDate, err := time.ParseInLocation("2006", year, loc)
	if err != nil {
		logger.Error(err)
		return 0
	}
	endDate := startDate.AddDate(0, 12, -1)
	days := playtime.TbDay.GetListByTimeRange(uid, startDate.Format("20060102"), endDate.Format("20060102"))

	var playTime int64
	for _, day := range days {
		playTime += day.PlayTime
	}
	return playTime
}

func (s *cleanPlayTime) calcTotalPlayTimeAndCalorie(uid int64) (playTime int64, calorie int32) {
	days := playtime.TbDay.GetList(uid)

	for _, day := range days {
		playTime += day.PlayTime
		calorie += day.Calorie
	}
	return playTime, calorie
}
