package practice

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	practiceDb "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/user"
	clientDb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/client"
	courseDb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/course"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/filter"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/live"
	userDb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/yogao2school"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	courseServ "gitlab.dailyyoga.com.cn/server/srv-task/service/course"
	filterServ "gitlab.dailyyoga.com.cn/server/srv-task/service/filter"
)

type userGoalPlayTime struct {
}

var UserGoalPlayTime userGoalPlayTime

// UpdateGoalPlayTimeByPracticeLog 根据练习记录更新目标时长
func (u *userGoalPlayTime) UpdateGoalPlayTimeByPracticeLog(payload *LoggingPracticePayload) {
	if !u.isOpen() {
		return
	}

	now := time.Now()
	rd := cache.GetLockRedis()
	lockKey := fmt.Sprintf("UpdateGoalPlayTimeByPracticeLog:uid:%d", payload.UID)
	if lock, err := rd.SetNX(context.Background(), lockKey, now.Unix(), 5*time.Second).Result(); err != nil {
		logger.Error(err)
		return
	} else if !lock {
		return
	}
	defer func() {
		if err := rd.Del(context.Background(), lockKey).Err(); err != nil {
			logger.Error(err)
		}
	}()

	if payload == nil {
		return
	}

	list := db.TbGoalPlayTime.GetList(payload.UID, nil)
	if len(list) == 0 {
		startTime := time.Now().Unix()
		u.initUserGoalPlayTime(payload.UID)
		logger.Infof("initUserGoalPlayTime耗时:%d,UID:%d", time.Now().Unix()-startTime, payload.UID)
		return
	}

	formatList := make(map[int32]*db.GoalPlayTime)
	for _, item := range list {
		formatList[item.GoalID] = item
	}

	session, tagList := GetSessionAndTagListByPractice(&practiceDb.UserPractice{
		ProgramID:       payload.ProgramID,
		SessionID:       payload.SessionID,
		UserActionTable: payload.ActionLogType,
	}, &courseServ.Session{}, &courseServ.Program{})

	if session == nil || tagList == nil {
		return
	}
	tagIds := make([]int32, 0)
	for _, tag := range tagList {
		tagIds = append(tagIds, tag.ID)
	}

	for _, tagID := range tagIds {
		if item, ok := formatList[tagID]; ok {
			item.PlayTime += payload.PlayTime
			item.DisplayPlayTime = GetDisplayPlayTime(item.PlayTime)
			item.Calorie += CalcCalorie(int64(session.CalorieRatio), payload.PlayTime)
			if !item.Update() {
				logger.Error("用户目标时长更新失败", item)
			}
		} else {
			item := &db.GoalPlayTime{
				UID:             payload.UID,
				GoalID:          tagID,
				PlayTime:        payload.PlayTime,
				DisplayPlayTime: GetDisplayPlayTime(payload.PlayTime),
				Calorie:         CalcCalorie(int64(session.CalorieRatio), payload.PlayTime),
			}
			if !item.Save() {
				logger.Error("用户目标时长保存失败", item)
			}
		}
	}
}

// initUserGoalPlayTime 初始化用户目标练习时长
func (u *userGoalPlayTime) initUserGoalPlayTime(uid int64) {
	sessionService := &courseServ.Session{}
	programService := &courseServ.Program{}

	now := time.Now().Unix()
	var startTime int64 = 1577808000 // 2020-01-01
	userGoalPlayTimeMap := make(map[int32]*db.GoalPlayTime)
	for startTime <= now {
		endTime := time.Unix(startTime, 0).AddDate(0, 6, 0).Unix()
		list := practiceDb.TbUserPractice.GetListByRangeTime(&practiceDb.UserPractice{
			UID: uid,
		}, startTime, endTime)
		for i := range list {
			userPractice := &list[i]
			session, tagList := GetSessionAndTagListByPractice(userPractice, sessionService, programService)
			if session == nil || tagList == nil {
				continue
			}

			var userGoalPlayTime *db.GoalPlayTime
			for _, tag := range tagList {
				ok := false
				userGoalPlayTime = nil
				if userGoalPlayTime, ok = userGoalPlayTimeMap[tag.ID]; !ok {
					userGoalPlayTime = &db.GoalPlayTime{
						UID:    uid,
						GoalID: tag.ID,
					}
					userGoalPlayTimeMap[tag.ID] = userGoalPlayTime
				}
				userGoalPlayTime.PlayTime += userPractice.PlayTime
				userGoalPlayTime.DisplayPlayTime = GetDisplayPlayTime(userGoalPlayTime.PlayTime)
				userGoalPlayTime.Calorie += CalcCalorie(int64(session.CalorieRatio), userPractice.PlayTime)
			}
		}
		startTime = endTime + 1
	}

	// 存储
	for _, userGoalPlayTime := range userGoalPlayTimeMap {
		if !userGoalPlayTime.Save() {
			logger.Error("save error", userGoalPlayTime)
		}
	}
}

// GetSessionAndTagListByPractice 根据练习记录获取目标列表
// nolint
func GetSessionAndTagListByPractice(userPractice *practiceDb.UserPractice,
	courseService *courseServ.Session, programService *courseServ.Program) (*courseDb.Session, []*filter.Tag) {
	var sourceID int64
	var sourceType int32
	var session *courseDb.Session
	switch userPractice.UserActionTable {
	case library.PlayQuitSession, library.PracticeFinish:
		session = courseService.GetSessionByID(userPractice.SessionID)
		if session == nil {
			return nil, nil
		}
		if session.SeriesType != library.SessionSeriesTypeGeneral || session.ContentType != library.SessionContentTypeAction {
			return nil, nil
		}
		sourceID = userPractice.SessionID
		sourceType = library.ObjTypeEnum.Session
	case library.PlayQuitUserSchedule, library.LeaderUserScheduleUnit:
		scheduleUnit := userDb.TbScheduleUnit.GetItem(userPractice.SessionID)
		if scheduleUnit == nil {
			return nil, nil
		}
		session = courseService.GetSessionByID(scheduleUnit.SessionID)
		if session == nil {
			return nil, nil
		}
		if session.SeriesType != library.SessionSeriesTypeGeneral || session.ContentType != library.SessionContentTypeAction {
			return nil, nil
		}
		sourceID = scheduleUnit.SessionID
		sourceType = library.ObjTypeEnum.Session
	case library.FinishSessionInProgram, library.PlayQuitSessionInProgram:
		session = courseService.GetSessionByID(userPractice.SessionID)
		if session == nil {
			return nil, nil
		}
		programInfo := programService.GetProgramByID(userPractice.ProgramID)
		if programInfo == nil {
			return nil, nil
		}
		sourceID = userPractice.ProgramID
		if programInfo.ContentType != library.SessionContentTypeAction {
			return nil, nil
		}
		switch programInfo.SeriesType {
		case library.ProgramSeriesTypeGeneral:
			sourceType = library.ObjTypeEnum.Program
		case library.ProgramSeriesTypeKol:
			sourceType = library.ObjTypeEnum.Kol
		case library.ProgramSeriesTypeO2:
			sourceType = library.ObjTypeEnum.O2
			o2Session := yogao2school.TbSession.GetSessionInfoBySourceID(sourceID)
			if o2Session == nil {
				return nil, nil
			}
			sourceID = o2Session.ID
		default:
			return nil, nil
		}
	case library.LivePlayback, library.LivePlaybackComplete, library.LiveBroadcast:
		sourceType = library.ObjTypeEnum.Live
		liveSession := live.TbLiveSession.GetItem(userPractice.SessionID)
		if liveSession == nil {
			return nil, nil
		}
		sourceID = liveSession.LiveSessionMainID
		session = &courseDb.Session{}
	}

	tagList := filterServ.Tag.GetTagListBySourceAndPID(sourceType, int32(sourceID), library.TagPidPracticeTarget)
	return session, tagList
}

// 开关，避免线上出问题 默认返回true
func (u *userGoalPlayTime) isOpen() bool {
	clientConfig := clientDb.TbBaseClientConfig.GetItem("user_goal_play_time_switch")
	if clientConfig == nil {
		return true
	}

	var config = struct {
		IsOpen bool `json:"is_open"`
	}{}
	err := json.Unmarshal([]byte(clientConfig.Value), &config)
	if err != nil {
		logger.Error("json.Unmarshal失败", err)
		return true
	}
	return config.IsOpen
}
