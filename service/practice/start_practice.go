package practice

import (
	"context"
	"net/url"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	practiceDb "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/course"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"
)

type StartPracticePayload struct {
	UID                 int64 `json:"uid"`
	ActionLogType       int32 `json:"action_log_type"`
	ProgramID           int64 `json:"program_id"`
	SessionID           int64 `json:"session_id"`
	SessionIndex        int32 `json:"session_index"`
	PracticeCurrentTime int64 `json:"practice_current_time"` // 当时练习时间

}
type StartPractice struct{}

var StPractice StartPractice

// 合并620内的 ActionLog 播放课程 播放计划中的课程
func (s *StartPractice) StartPractice(data *StartPracticePayload) {
	if !s.verifyPracticeTimeLegal(data.PracticeCurrentTime) {
		return
	}
	if data.ActionLogType == library.PracticeSession {
		s.setSessionScheduleLastPracticeTime(data)
	} else if data.ActionLogType == library.PracticeSessionOfProgram {
		s.setProgramScheduleLastPracticeTime(data)
	} else {
		logger.Error("处理到了错误数据")
	}
}

// 修改用户课程进度最后播放时间
func (s *StartPractice) setSessionScheduleLastPracticeTime(data *StartPracticePayload) {
	s.completeUserMissions(data)
	item := db.TbSessionSchedule.GetItemByUID(data.UID, data.SessionID)
	if item != nil {
		item.LastPracticeTime = time.Now().Unix()
		if err := item.Update(); err != nil {
			logger.Error("修改用户课程进度最后播放时间出错", data)
		}
	}
}

func (s *StartPractice) setProgramScheduleLastPracticeTime(data *StartPracticePayload) {
	s.completeUserMissions(data)
	item := db.TbProgramSchedule.GetItemByUID(data.UID, data.ProgramID)
	if item != nil {
		item.LastPracticeTime = time.Now().Unix()
		if err := item.Update(); err != nil {
			logger.Error("修改用户计划进度最后播放时间出错", data)
		}
	}
	programItem := db.TbProgram.GetProgramByID(data.ProgramID)
	// 1：普通练习 , 2：KOL练习
	if programItem != nil && programItem.SeriesType == 2 {
		go s.dayResourcePracticeCountFromH2(data.ProgramID, 0)
	}
}

func (s *StartPractice) completeUserMissions(data *StartPracticePayload) {
	logger.Info("jyg---------------------practice.setProgramScheduleLastPracticeTime", data)
	now := time.Now()
	sc := practiceDb.TbCollect.GetItem(data.UID)
	var totalPracticeDay int64
	var lastPracticeDate int64
	if sc != nil && sc.ID > 0 {
		// 用户总练习天数有误，重新统计
		totalPracticeDay = practiceDb.TbDay.GetTotal(data.UID)
		lastPracticeDate = sc.LastPracticeDate
	} else {
		totalPracticeDay = 1
		lastPracticeDate = now.Unix()
	}
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local)
	if lastPracticeDate < today.Unix() {
		totalPracticeDay++
	}
	if err := Mission.CompleteUserMissions(data.UID, totalPracticeDay, DayTarget); err != nil {
		logger.Error(err)
	}
}

// 开始练习统计（每开始练习一次请求一次）只统计名师课堂
func (s *StartPractice) dayResourcePracticeCountFromH2(programID int64, i int) {
	postURL := library.GetKolShareAddress() + "/coachprofit/dayResourcePracticeCountFromH2"
	params := url.Values{}
	params.Set("resource_id", strconv.FormatInt(programID, 10))
	params.Set("resource_type", "3") // 资源类型（2-课程，3-计划）

	resp, err := service.HTTP.PostForm(context.Background(), postURL, params)
	if err != nil && i < 3 {
		logger.Warn("发送数据给H2O开始练习统计 http报错重试第" + strconv.Itoa(i) + "次")
		s.dayResourcePracticeCountFromH2(programID, i+1)
		return
	} else if err != nil {
		logger.Warnf("发送数据给H2O开始练习统计 重试3次 失败 请求的参数 %+v error：%s", params, err.Error())
		return
	}

	defer func() {
		_ = resp.Body.Close()
	}()

	if resp.StatusCode != 200 {
		logger.Error("h2o URL 请求状态", resp.StatusCode)
	}
}

// 判断客户端上报时间是否合法
func (s *StartPractice) verifyPracticeTimeLegal(practiceCurrentTime int64) bool {
	timeStr := time.Now().Format("2006-01-02")
	t, _ := time.Parse("2006-01-02", timeStr)
	todayBeginTime := t.Unix()
	if practiceCurrentTime < (todayBeginTime-86400) || practiceCurrentTime > (time.Now().Unix()+30) {
		return false
	}
	return true
}
