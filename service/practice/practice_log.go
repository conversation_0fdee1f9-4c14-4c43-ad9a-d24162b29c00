package practice

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/protogen/srv-usercenter-go/activity"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/playtime"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
	comm "gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/badge"
)

type DayPayload struct {
	UID        int64 `json:"uid"`
	CreateTime int64 `json:"create_time"`
}

type LoggingPracticePayload struct {
	UID                 int64 `json:"uid"`
	ActionLogType       int32 `json:"action_log_type"`
	ProgramID           int64 `json:"program_id"`
	SessionID           int64 `json:"session_id"`
	SessionIndex        int32 `json:"session_index"`
	SubSessionIndex     int32 `json:"sub_session_index"`
	PracticeCurrentTime int64 `json:"practice_current_time"`
	CreateTime          int64 `json:"create_time"`
	PlayTime            int64 `json:"play_time"`
	Actions             int32 `json:"actions"`
	Screen              int32 `json:"screen"`
	LinkType            int32 `json:"link_type"`
	PracticeDate        int64 `json:"practice_date"`
	O2SessionID         int64 `json:"o2_session_id"`
	ScreenType          int64 `json:"screen_type,omitempty"`
	PeriodPracticeDate  int64 `json:"period_practice_date"`
}

type FeelPracticePayload struct {
	UID                 int64  `json:"uid"`
	ActionLogType       int32  `json:"action_log_type"`
	ProgramID           int64  `json:"program_id"`
	SessionID           int64  `json:"session_id"`
	SessionIndex        int32  `json:"session_index"`
	SubSessionIndex     int32  `json:"sub_session_index"`
	PracticeCurrentTime int64  `json:"practice_current_time"`
	Feel                int32  `json:"feel"`
	Labels              string `json:"labels"`
	Feedback            string `json:"feedback"`
	Version             string `json:"version"`
	SourceType          int32  `json:"source_type"`
	SourceID            int64  `json:"source_id"`
}

type CleanUserPracticePayload struct {
	UID           int64  `json:"uid"`
	CleanTimeDate string `json:"clean_time_date"`
}

type LoginPayload struct {
	UID           int64  `json:"uid"`
	ActionLogType string `json:"action_log_type"`
}

type CheckData struct {
	PracticeCurrentTime int64
}

type practiceLog struct{}

var Log practiceLog

const ActionLogTypeUserLogin = "user_login"

func (pl *practiceLog) StatPracticeLog(params []byte) error {
	var bean LoggingPracticePayload
	if err := json.Unmarshal(params, &bean); err != nil {
		return err
	}
	if bean.UID < 1 {
		return errors.New("invalid params: uid")
	}
	// 实时上报合法时间为正负一天，不合法时间只记录不统计
	// water 20220612 对练习时间大于当前时间 不合法时间只记录不统计
	if !ValidationData(&CheckData{
		PracticeCurrentTime: bean.PracticeCurrentTime,
	}) {
		pe := &db.Expire{
			UID:             bean.UID,
			ProgramID:       bean.ProgramID,
			SessionID:       bean.SessionID,
			SessionIndex:    bean.SessionIndex,
			SubSessionIndex: bean.SubSessionIndex,
			UserActionTable: bean.ActionLogType,
			ReportTime:      bean.PracticeCurrentTime,
			CreateTime:      bean.CreateTime,
		}
		if err := pe.Save(); err != nil {
			logger.Error(err)
		}
		return nil
	}
	if comm.IsLiveUsrActionTable(bean.ActionLogType) {
		SaveUserLivePractice(&bean)
		return nil
	}
	SaveUserPractice(&bean)
	// @text 调用处理练习任务数据
	go statPracticeCollect.sendPracticeTask(bean.UID)
	// 促活任务更新，当天开始练习的才算
	go pl.UpdateActivityProgress(&bean)
	// 上报完成后更新课程练习次数缓存
	go processCompleteTimes(bean.UID)
	return nil
}

// 促活活动进度更新
func (pl *practiceLog) UpdateActivityProgress(bean *LoggingPracticePayload) bool {
	if bean.PracticeCurrentTime <= GetDayStartTime(time.Now()).Unix() {
		logger.Info("UpdateActivityProgress时间不合法", bean.PracticeCurrentTime, GetDayStartTime(time.Now()).Unix())
		return false
	}
	rsp, err := grpc.GetUserCenterClient().UpdateProgress(context.Background(), &activity.UpdateProgressRequest{
		Uid: bean.UID,
	})
	logger.Debug("UpdateActivityProgress", bean.UID, rsp, err)
	if err != nil {
		logger.Error("UpdateProgress用户促活任务进度更新失败", err, rsp)
		return false
	}
	return true
}

// @text 根据用户连续天数，发放连续练习的奖励
func (p *practiceCollect) sendPracticeTask(uid int64) {
	uri := "/activity/aggregation/timedTask?uid=" + strconv.FormatInt(uid, 10)
	resp, err := requests.New620Client(config.Get().Php620Address).Get(uri)
	if err != nil {
		logger.Error(err)
		return
	}
	if !resp.IsOK() {
		logger.Errorf("record task failed; status: %d", resp.StatusCode)
	} else {
		logger.Debugf("record task success; status: %d", resp.StatusCode)
	}
}

func (pl *practiceLog) verifyPracticeTimeLegal(uid, createTime int64) bool {
	t := time.Now()
	todayTime := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)

	// step1:如果非正负+1时间内上报的数据则不更新
	if createTime < (todayTime.Unix()-86400) || createTime > time.Now().Unix() {
		return false
	}

	// step2:判断当天是否练习过
	item := db.TbCollect.GetItem(uid)
	practiceDays := db.TbDay.GetTotal(uid)
	practiceRecord := db.TbDay.GetItem(uid, time.Unix(createTime, 0).Format("20060102"))
	if (item != nil && item.ID > 0 && item.TotalPracticeDay == practiceDays) &&
		(practiceRecord != nil && practiceRecord.ID > 0) {
		logger.Debugf("%d-%d-%d - 离线上报用户练习天数忽略处理", uid, item.TotalPracticeDay, practiceDays)
		return false
	}

	return true
}

func (pl *practiceLog) StatPracticeDay(payload *DayPayload) {
	isToday, hasPracticed := false, false
	t := time.Now()
	todayTime := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)
	// 2018年3月之前的数据不处理
	usefulStartTime := time.Date(2018, 03, 01, 0, 0, 0, 0, time.Local)
	if payload.CreateTime < usefulStartTime.Unix() {
		return
	}

	if !pl.verifyPracticeTimeLegal(payload.UID, payload.CreateTime) {
		return
	}

	// 判断此上报是否为今天练习
	if payload.CreateTime >= todayTime.Unix() {
		isToday = true
	}

	// 判断用户当天是否练习过
	practiceRecord := db.TbDay.GetItem(payload.UID, time.Now().Format("20060102"))
	if practiceRecord != nil {
		hasPracticed = true
	}

	// 判断是否当天已经更新过 更新st_practice_days_week
	statPracticeCollect.setWeek(payload, isToday, hasPracticed, todayTime)
	// 判断是否周全勤，更新st_practice_week_collect_
	statPracticeCollect.setWeekCollect(payload, isToday, hasPracticed, todayTime)
	// 更新st_practice_days_month
	statPracticeCollect.setMonth(payload, isToday, hasPracticed, todayTime)
	// 更新st_practice_days_year
	statPracticeCollect.setYear(payload, isToday, hasPracticed, todayTime)
	// 更新st_practice_days_collect
	statPracticeCollect.setCollect(payload)
	// 更新用户统计st_user_collect
	statPracticeCollect.setUserCollect(payload)
	// @text 调用php连续发放接口进行奖励发放(一个用户一天只会触发一次)
	statPracticeCollect.sendContinueDayAward(payload.UID, payload.CreateTime)
}

// 2019-12-6 water 练习感受反馈
// 2020年1月6日 18:25:30 water 增加校验机制与习练历程保持一致
func (pl *practiceLog) StatPracticeFeel(data *FeelPracticePayload) {
	if data.UID < 1 {
		logger.Warn("练习感受反馈数据出错", data)
		return
	}
	bean := &db.UserPractice{
		UID:             data.UID,
		UserActionTable: data.ActionLogType,
		ReportTime:      data.PracticeCurrentTime,
		ProgramID:       data.ProgramID,
		SessionID:       data.SessionID,
		SessionIndex:    data.SessionIndex,
	}
	if validationFeelData(&CheckData{
		PracticeCurrentTime: data.PracticeCurrentTime,
	}) {
		Item := db.TbUserPractice.GetItem(bean)
		if Item != nil && Item.ID > 0 {
			Item.Feel = data.Feel
			if err := Item.Update(); err != nil {
				logger.Error(err)
			}
			// 直播无需存储练习感受
			if comm.IsLiveUsrActionTable(bean.UserActionTable) {
				return
			}

			dateIndex := time.Unix(Item.ReportTime, 0).Format("200601")
			feedback := db.UserPracticeFeedback{
				UserPracticeID: Item.ID,
				DateIndex:      dateIndex,
				UID:            data.UID,
				SourceType:     data.SourceType,
				SourceID:       data.SourceID,
				Version:        data.Version,
				Feel:           data.Feel,
				Labels:         data.Labels,
				Feedback:       data.Feedback,
			}
			feedback.SaveOrUpdate()

			// 双写到分表
			feedbackV2 := db.UserPracticeFeedbackV2{
				UserPracticeID: Item.ID,
				DateIndex:      dateIndex,
				UID:            data.UID,
				SourceType:     data.SourceType,
				SourceID:       data.SourceID,
				Version:        data.Version,
				Feel:           data.Feel,
				Labels:         data.Labels,
				Feedback:       data.Feedback,
			}
			feedbackV2.SaveOrUpdate()
		} else {
			logger.Warn("更新练习反馈时数据未查到请检查", data)
		}
	}
}

func (pl *practiceLog) StatUserLogin(data *LoginPayload) {
	todayTime := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Local)

	userCollect, err := user.TbUserCollect.FindByUID(data.UID)

	if err != nil {
		logger.Error(err)
	}

	dayIndex := todayTime.Format("20060102")
	loginLog := user.LoginLog{
		OriginalID:   0,
		OriginalType: 1,
		MonthIndex:   dayIndex[:6],
		DayIndex:     dayIndex,
		UID:          data.UID,
		OriginalDate: time.Now().Unix(),
		CreateTime:   time.Now().Unix(),
	}

	if userCollect == nil { // 如果用户登录数据为空的，就启动脚本统计
		err = LoginLog(data.UID, data.ActionLogType)
		if err != nil {
			logger.Error(err)
		}

		if data.ActionLogType == ActionLogTypeUserLogin {
			// 如果今天有登录日志，就不保存
			todayLoginLog, err := user.TbUserLoginLog.FindOne(data.UID, dayIndex)

			if err != nil {
				logger.Error(err)
				return
			}

			if todayLoginLog == nil {
				err = loginLog.Save()
				if err != nil {
					logger.Error(err)
				}
			}
		}

		// 首次异步计算用户登录历史数据完成后通知计算登录类徽章
		_ = badge.ServiceUserOldBadge.ProcessUserBadge(data.UID, []int32{int32(comm.BadgeTaskTypeEnum.LogIn)}, []int64{})
		return
	}

	if userCollect.LastLoginTime == 0 { // 有其他时间导致初始化了统计，但是没有统计用户登录历史
		err = LoginLog(data.UID, data.ActionLogType)
		if err != nil {
			logger.Error(err)
		}

		if data.ActionLogType == ActionLogTypeUserLogin {
			// 如果今天有登录日志，就不保存
			todayLoginLog, err := user.TbUserLoginLog.FindOne(data.UID, dayIndex)

			if err != nil {
				logger.Error(err)
				return
			}

			if todayLoginLog == nil {
				err = loginLog.Save()
				if err != nil {
					logger.Error(err)
				}
				userCollect.LastLoginTime = time.Now().Unix()
				userCollect.BeforeLastLoginTime = userCollect.LastLoginTime
				err = userCollect.Update()
				if err != nil {
					logger.Error(err)
				}
			}
		}

		// 首次异步计算用户登录历史数据完成后通知计算登录类徽章
		_ = badge.ServiceUserOldBadge.ProcessUserBadge(data.UID, []int32{int32(comm.BadgeTaskTypeEnum.LogIn)}, []int64{})
		return
	}

	if todayTime.Unix() > userCollect.LastLoginTime {
		err = loginLog.Save()
		if err != nil {
			logger.Error(err)
		}
		if err := userCollect.IncLoginDay(time.Now().Unix()); err != nil {
			logger.Error(err)
		}
	}
}

// ValidationData 2020年1月6日 18:21:17 water 验证数据
// 2020-07-06 water gcb 之前处理的时候把逻辑限制取消了，还原逻辑
func ValidationData(c *CheckData) bool {
	t := time.Now()
	todayTime := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)
	if c.PracticeCurrentTime < todayTime.Unix()-86400 || !validationFeelData(c) {
		return false
	}
	return true
}

// 练习感受  2020.4.8 去掉前一天限制，可以补历史的练习感受 gcb
func validationFeelData(c *CheckData) bool {
	return c.PracticeCurrentTime <= time.Now().Unix()+86400
}

type practiceCollect struct{}

var statPracticeCollect practiceCollect

// needSupplyData 是否补昨天的练习天数
func (p *practiceCollect) needSupplyData(isToday, hasPracticed bool) bool {
	return !isToday && !hasPracticed
}

// getUserWeekCollectData 获取用户周全勤数据
func (p *practiceCollect) getUserWeekCollectData(uid, reportTime int64) (total, last, current int32) {
	dateWeek := int32(GetWeekCount(reportTime))
	var totalPracticeWeek, lastPracticeWeek, currentWeekPracticeDay int32

	// 利用历史数据
	for i := 1; i <= int(dateWeek); i++ {
		userPracticeWeekInfo := db.TbWeek.GetItem(uid, strconv.Itoa(i))
		if userPracticeWeekInfo == nil {
			continue
		}

		// 计算周全勤次数
		if userPracticeWeekInfo.PracticeDays >= 7 {
			totalPracticeWeek++
		}

		lastPracticeWeek = int32(i)
		currentWeekPracticeDay = userPracticeWeekInfo.PracticeDays
		// 如果是本周，则更新最近周和周练习天数；如果本周已全勤，则重置本周进度
		if dateWeek == int32(i) && userPracticeWeekInfo.PracticeDays >= 7 {
			currentWeekPracticeDay = 0
		}
	}

	return totalPracticeWeek, lastPracticeWeek, currentWeekPracticeDay
}

func (p *practiceCollect) setWeek(payload *DayPayload, isToday, hasPracticed bool, todayTime time.Time) {
	timeNow := time.Now().Unix()
	createTime, updateTime := timeNow, timeNow
	week := GetWeekCount(payload.CreateTime)
	if week < 1 || payload.CreateTime > timeNow {
		return
	}
	dateWeek := strconv.Itoa(int(GetWeekCount(payload.CreateTime)))
	sw := db.TbWeek.GetItem(payload.UID, dateWeek)
	if sw != nil && sw.UpdateTime > todayTime.Unix() {
		return
	}

	if p.needSupplyData(isToday, hasPracticed) {
		createTime = payload.CreateTime
		updateTime = payload.CreateTime
	}

	if sw != nil && sw.ID > 0 {
		sw.PracticeDays++
		sw.UpdateTime = updateTime
		if err := sw.Update(); err != nil {
			logger.Error(err)
		}
	} else {
		dateMonth := time.Unix(payload.CreateTime, 0).Format("200601")
		sw = &db.Week{
			UID:          payload.UID,
			PracticeDays: 1,
			DateIndex:    dateMonth,
			Week:         dateWeek,
			CreateTime:   createTime,
			UpdateTime:   updateTime,
		}
		if err := sw.Save(); err != nil {
			logger.Error(err)
		}
	}
}

// setWeekCollect 更新用户周全勤数据
func (p *practiceCollect) setWeekCollect(payload *DayPayload, isToday, hasPracticed bool, todayTime time.Time) {
	timeNow := time.Now().Unix()
	createTime, updateTime := timeNow, timeNow
	dateWeek := int32(GetWeekCount(payload.CreateTime))
	if dateWeek < 1 || payload.CreateTime > timeNow {
		return
	}

	weekCollect, err := db.TbWeekCollect.GetItem(payload.UID)
	if err != nil {
		logger.Errorf("获取用户周全勤次数失败, err: %s", err)
		return
	}

	// 如果用户今天已经练习过，则周练习天数数据已经更新，无需再更新
	if weekCollect != nil && weekCollect.UpdateTime > todayTime.Unix() {
		return
	}

	// 判断是否是离线上报数据
	if p.needSupplyData(isToday, hasPracticed) {
		createTime = payload.CreateTime
		updateTime = payload.CreateTime
	}

	// 如果已经有周全勤统计数据；则在统计数据基础上进行更新；否则根据明细计算
	if weekCollect != nil && weekCollect.ID > 0 {
		weekCollect.LastPracticeWeek = dateWeek
		weekCollect.CurrentWeekPracticeDay++
		// 如果本周全勤，则更新周全勤次数，重置本周进度
		if weekCollect.CurrentWeekPracticeDay >= 7 {
			weekCollect.TotalPracticeWeek++
			weekCollect.CurrentWeekPracticeDay = 0
		}

		weekCollect.UpdateTime = updateTime
		if err := weekCollect.Update(); err != nil {
			logger.Error(err)
		}
	} else {
		weekCollect = &db.WeekCollect{
			UID:        payload.UID,
			CreateTime: createTime,
			UpdateTime: updateTime,
		}

		weekCollect.TotalPracticeWeek, weekCollect.LastPracticeWeek, weekCollect.CurrentWeekPracticeDay =
			p.getUserWeekCollectData(payload.UID, payload.CreateTime)
		if err := weekCollect.Save(); err != nil {
			logger.Error(err)
		}
	}
}

func (p *practiceCollect) setMonth(payload *DayPayload, isToday, hasPracticed bool, todayTime time.Time) {
	timeNow := time.Now().Unix()
	createTime, updateTime := timeNow, timeNow
	dateMonth := time.Unix(payload.CreateTime, 0).Format("200601")
	sm := db.TbMonth.GetItem(payload.UID, dateMonth)

	if sm != nil && sm.UpdateTime > todayTime.Unix() {
		return
	}

	if p.needSupplyData(isToday, hasPracticed) {
		createTime = payload.CreateTime
		updateTime = payload.CreateTime
	}

	if sm != nil && sm.ID > 0 {
		sm.PracticeDays++
		sm.UpdateTime = updateTime
		if err := sm.Update(); err != nil {
			return
		}
	} else {
		sm = &db.Month{
			UID:          payload.UID,
			PracticeDays: 1,
			DateIndex:    dateMonth,
			CreateTime:   createTime,
			UpdateTime:   updateTime,
		}
		if err := sm.Save(); err != nil {
			logger.Error(err)
		}
	}
}

func (p *practiceCollect) setYear(payload *DayPayload, isToday, hasPracticed bool, todayTime time.Time) {
	timeNow := time.Now().Unix()
	createTime, updateTime := timeNow, timeNow
	dateYear := time.Unix(payload.CreateTime, 0).Format("2006")
	sy := db.TbYear.GetItem(payload.UID, dateYear)

	if sy != nil && sy.UpdateTime > todayTime.Unix() {
		return
	}

	if p.needSupplyData(isToday, hasPracticed) {
		createTime = payload.CreateTime
		updateTime = payload.CreateTime
	}

	if sy != nil && sy.ID > 0 {
		sy.PracticeDays++
		sy.UpdateTime = updateTime
		if err := sy.Update(); err != nil {
			logger.Error(err)
		}
	} else {
		sy = &db.Year{
			UID:          payload.UID,
			PracticeDays: 1,
			DateIndex:    dateYear,
			CreateTime:   createTime,
			UpdateTime:   updateTime,
		}
		if err := sy.Save(); err != nil {
			logger.Error(err)
		}
	}
}

// @text 根据用户连续天数，发放连续练习的奖励
func (p *practiceCollect) sendContinueDayAward(uid, createTime int64) {
	uri := "/User/Gift/sendContinueDaysAward?uid=" +
		strconv.FormatInt(uid, 10) + "&create_time=" + strconv.FormatInt(createTime, 10)
	resp, err := requests.New620Client(config.Get().Php620Address).Get(uri)
	if err != nil {
		logger.Error(err)
		return
	}
	if resp == nil || !resp.IsOK() {
		logger.Errorf("send continue days award failed; status: %d", resp.StatusCode)
	} else {
		logger.Debugf("send continue days award success; status: %d", resp.StatusCode)
	}
}

func (p *practiceCollect) setCollect(payload *DayPayload) {
	sc := db.TbCollect.GetItem(payload.UID)
	if sc != nil && sc.ID > 0 {
		// 用户总练习天数有误，重新统计
		sc.TotalPracticeDay = db.TbDay.GetTotal(payload.UID)

		if payload.CreateTime > sc.LastPracticeDate {
			sc.LastPracticeDate = payload.CreateTime
		}

		sc.ContinuePracticeDay = p.getContinuePracticeDay(payload.UID)
		if err := sc.Update(); err != nil {
			logger.Error(err)
		}
	} else {
		sc = &db.Collect{
			UID:                 payload.UID,
			LastPracticeDate:    payload.CreateTime,
			TotalPracticeDay:    1,
			ContinuePracticeDay: 1,
		}
		if err := sc.Save(); err != nil {
			return
		}
	}
}

func (p *practiceCollect) setUserCollect(payload *DayPayload) {
	userCollect, err := user.TbUserCollect.FindOrCreate(payload.UID)
	if err != nil {
		logger.Error(err)
	}

	// 获取周的练习统计数据
	dateWeek := strconv.Itoa(int(GetWeekCount(payload.CreateTime)))
	sw := db.TbWeek.GetItem(payload.UID, dateWeek)
	weekNum, _ := strconv.ParseInt(dateWeek, 10, 32)
	userCollect.LastPracticeWeek = int32(weekNum)
	if sw != nil {
		userCollect.CurrentWeekPracticeDay = sw.PracticeDays
		if sw.PracticeDays >= 7 {
			userCollect.TotalPracticeWeek++
			userCollect.CurrentWeekPracticeDay = 0 // 当前练习周数重置
		}
	}

	sc := db.TbCollect.GetItem(payload.UID)
	if sc != nil && sc.TotalPracticeDay > 0 {
		userCollect.TotalPracticeDay = sc.TotalPracticeDay
		userCollect.LastPracticeTime = sc.LastPracticeDate
	}
	tc := playtime.TbCollect.GetItem(payload.UID)
	if tc != nil && tc.PlayTime > 0 {
		userCollect.TotalPlayTime = tc.PlayTime
		userCollect.LastPlayTime = tc.UpdateTime
	}
	if err := userCollect.UpdateByUID(); err != nil {
		logger.Error(err)
		return
	}
}

func (p *practiceCollect) getContinuePracticeDay(uid int64) int64 {
	var keepDay int64 = 1
	var lastDateIndex string

	list := db.TbDay.GetListAscDateIndex(uid)
	if len(list) < 1 {
		return keepDay
	}
	for _, v := range list {
		if lastDateIndex == "" {
			lastDateIndex = v.DateIndex
		} else {
			lastDateIndexUnix := p.convertTimeUnix(lastDateIndex)
			curDateIndexUnix := p.convertTimeUnix(v.DateIndex)
			if curDateIndexUnix-lastDateIndexUnix == 86400 {
				keepDay++
			} else {
				keepDay = 1
			}
			lastDateIndex = v.DateIndex
		}
	}
	return keepDay
}

func (p *practiceCollect) convertTimeUnix(dateIndex string) int64 {
	tUnix, _ := time.Parse("20060102", dateIndex)
	return tUnix.Unix()
}

// SaveUserLivePractice 处理直播上报
func SaveUserLivePractice(r *LoggingPracticePayload) {
	// 如果上报时间大于当天时间或者小于20160101则忽略处理
	if r.PracticeCurrentTime > time.Now().Unix() {
		return
	}
	bean := &db.UserPractice{
		UID:             r.UID,
		UserActionTable: r.ActionLogType,
		ReportTime:      r.PracticeCurrentTime,
		ProgramID:       r.ProgramID,
		SessionID:       r.SessionID,
		SessionIndex:    r.SessionIndex,
		SubSessionIndex: r.SubSessionIndex,
		CreateTime:      r.CreateTime,
		PlayTime:        r.PlayTime,
		Actions:         r.Actions,
		Screen:          r.Screen,
		LinkType:        r.LinkType,
	}
	// 针对直播回放的上报防止 重复上报
	var isUpData bool
	switch bean.UserActionTable {
	case comm.LivePlayback, comm.LivePlaybackComplete:
		if item := db.TbUserPractice.GetItem(bean); item != nil && item.ID > 0 {
			logger.Warn("重复上报userActionLog", bean)
			return
		}
	case comm.LiveBroadcast:
		if item := db.TbUserPractice.GetLiveBroadcastItem(bean); item != nil {
			isUpData = true
			item.PlayTime += bean.PlayTime
			if err := item.Update(); err != nil {
				logger.Error(err)
			}
		}
	default:
		logger.Error("错误直播播放上报类型", bean.UserActionTable)
		return
	}
	bean.CompleteTimes = TotalPractice.UserPracticeCompleteTimes(bean, !isUpData)
	if !isUpData {
		saveUserPracticeLog(bean)
		// 保存数据
		if err := bean.Save(); err != nil {
			logger.Error(err)
		}
	}
	if bean.PlayTime > 0 {
		Collect.UpdateLiveUserPractice(bean)
	}
}

func SaveUserPractice(r *LoggingPracticePayload) {
	// 如果上报时间大于当天时间或者小于20160101则忽略处理
	if r.PracticeCurrentTime > time.Now().Unix() || r.PracticeCurrentTime < 1451577600 {
		return
	}
	// 构造写入数据
	bean := &db.UserPractice{
		UID:             r.UID,
		UserActionTable: r.ActionLogType,
		ReportTime:      r.PracticeCurrentTime,
		ProgramID:       r.ProgramID,
		SessionID:       r.SessionID,
		SessionIndex:    r.SessionIndex,
		SubSessionIndex: r.SubSessionIndex,
		CreateTime:      r.CreateTime,
		PlayTime:        r.PlayTime,
		Actions:         r.Actions,
		Screen:          r.Screen,
		LinkType:        r.LinkType,
	}
	// 2020年1月6日 15:35:36 water 处理重复上报
	item := db.TbUserPractice.GetItem(bean)
	if item != nil && item.ID > 0 {
		logger.Warn("重复上报userActionLog", bean)
		return
	}
	if _, ok := comm.ExcludeTypes[bean.UserActionTable]; !ok {
		bean.CompleteTimes = TotalPractice.CleanUserPracticeCompleteTimes(bean)
		saveUserPracticeLog(bean)
	}
	// 保存数据
	if err := bean.Save(); err != nil {
		logger.Error(err)
	}
	// 记录上次练习
	go LastPractice.LastPracticeLog(bean, r.PracticeDate, r.O2SessionID)
	if r.ScreenType == comm.ScreenTypeTv {
		go TvIntelligence.ProcessIntelligenceTask(r)
	} else {
		go ServiceIntelligence.ProcessIntelligenceTask(r)
		// 练习日历
		cpr := copyLogPracticePayload(r)
		go SvrCalendar.PracticeCalendar(cpr)
	}

	// 2019-01-18 更新练习时长(保底逻辑如果user)
	if bean.PlayTime > 0 {
		Collect.UpdateUserPractice(bean)
	}
	// 用户目标练习时长更新
	go UserGoalPlayTime.UpdateGoalPlayTimeByPracticeLog(r)
}

// saveUserPracticeLog 保存用户数据
func saveUserPracticeLog(bean *db.UserPractice) {
	// 处理离线上报的数据
	list := db.TbUserPractice.GetList(bean, bean.ReportTime)
	if len(list) > 0 {
		// 找出最小的 completeTimes，设置排序数据
		var completeTimes int32
		data := make(map[int]*db.UserPractice)
		sortSlice := make([]int, 0)
		sortCompleteTimes := make([]int, 0)
		for i, v := range list {
			uploadTime := int(v.ReportTime)
			data[uploadTime] = &list[i]
			sortSlice = append(sortSlice, uploadTime)
			sortCompleteTimes = append(sortCompleteTimes, int(v.CompleteTimes))
		}
		sort.Ints(sortCompleteTimes)
		// 处理序号
		completeTimes = int32(sortCompleteTimes[0])
		bean.CompleteTimes = completeTimes
		// 将查出来的这些数据按上报时间进行排序，然后将数据中的completeTimes统一加1
		sort.Ints(sortSlice)
		for i, v := range sortSlice {
			data[v].CompleteTimes = completeTimes + int32(i+1)
			err := data[v].Update()
			if err != nil {
				logger.Error(err)
			}
		}
	}
}

func copyLogPracticePayload(p *LoggingPracticePayload) *LoggingPracticePayload {
	return &LoggingPracticePayload{
		UID:                 p.UID,
		ActionLogType:       p.ActionLogType,
		ProgramID:           p.ProgramID,
		SessionID:           p.SessionID,
		SessionIndex:        p.SessionIndex,
		SubSessionIndex:     p.SessionIndex,
		PracticeCurrentTime: p.PracticeCurrentTime,
		CreateTime:          p.CreateTime,
		PlayTime:            p.PlayTime,
		Actions:             p.Actions,
		Screen:              p.Screen,
		LinkType:            p.LinkType,
		PracticeDate:        p.PracticeDate,
		O2SessionID:         p.O2SessionID,
		PeriodPracticeDate:  p.PeriodPracticeDate,
	}
}
func (pl *practiceLog) CleanUserPracticeLog(params []byte) error {
	var bean CleanUserPracticePayload
	if err := json.Unmarshal(params, &bean); err != nil {
		return err
	}
	go bean.clean()
	return nil
}

func (bean *CleanUserPracticePayload) clean() {
	// 不用清理st_practice_session_total_count_表的数据(CleanUserPractice)
	// 因为total表记录的是练习完成的数据，而bug导致重复的数据都是78即中退
	err := CleanPlayTime.CleanUserRepeatPlayTime(bean.UID, bean.CleanTimeDate)
	if err != nil {
		logger.Error(err)
		return
	}
}

const ExpireTime = time.Hour * 24 * 30

// 2020-05-26 tlqiao 设置用户课程练习次数缓存
func processCompleteTimes(uid int64) {
	ctx := context.Background()
	rd := cache.GetYogaRedis()
	bean := &db.UserPractice{
		UID: uid,
	}
	redisSessionKey := fmt.Sprintf("620:complete_times:%d", uid)
	completeTimesSession, completeTimesProgram := db.TbUserPractice.GetCompleteTimes(bean)
	var argsSession []interface{}
	if len(completeTimesSession) == 0 {
		argsSession = append(argsSession, 0, 1)
	} else {
		for key, value := range completeTimesSession {
			argsSession = append(argsSession, key, value)
		}
	}
	err := rd.HMSet(ctx, redisSessionKey, argsSession...).Err()
	if err != nil {
		logger.Warn(err)
	}
	rd.Expire(ctx, redisSessionKey, ExpireTime)

	redisProgramKey := fmt.Sprintf("620:Program_complete_times:%d", uid)
	var argsProgram []interface{}
	if len(completeTimesProgram) == 0 {
		argsProgram = append(argsProgram, 0, 1)
	} else {
		for key, value := range completeTimesProgram {
			argsProgram = append(argsProgram, key, value)
		}
	}
	err = rd.HMSet(ctx, redisProgramKey, argsProgram...).Err()
	if err != nil {
		logger.Warn(err)
	}
	rd.Expire(ctx, redisProgramKey, ExpireTime)
}
