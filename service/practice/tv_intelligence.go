package practice

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	tdb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/tv"
	lib "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type _tvIntelligence struct{}

var TvIntelligence _tvIntelligence

func (i *_tvIntelligence) ProcessIntelligenceTask(taskInfo *LoggingPracticePayload) {
	logger.Infof("TV智能课表 %+v", taskInfo)
	if taskInfo.PracticeDate <= 0 {
		return
	}
	now := time.Now().Unix()
	uSchedule := tdb.TbTvIntelligenceSUser.GetUserSchedule(taskInfo.UID, 2)
	if uSchedule == nil {
		return
	}
	// 判断课表在不在有效期内
	if uSchedule.StartTime > now || uSchedule.EndTime < now {
		return
	}
	var rollbackErr error
	// 事务删除
	session := db.GetEngineMaster().NewSession()
	defer session.Close()
	defer func() {
		if rollbackErr != nil {
			logger.Error("TV智能课表错误", rollbackErr)
			if err := session.Rollback(); err != nil {
				logger.Warn("TV智能课表异步任务回滚出错", err)
			}
		}
	}()
	timeObj := time.Unix(taskInfo.PracticeDate, 0)
	unitSchedule := tdb.TbTvIntelligenceSUserSession.GetByParams(uSchedule.ID, timeObj.Format("20060102"),
		taskInfo.SessionIndex)
	if unitSchedule == nil {
		logger.Error("TV 智能课表未找到响应课程 %+v", taskInfo)
		return
	}
	if unitSchedule.SessionID != taskInfo.SessionID {
		logger.Error("TV 智能课表未找到响应,响应的 课程 %+v", taskInfo)
		return
	}
	unitSchedule.IsDone = lib.TNotDone
	if taskInfo.ActionLogType == lib.PracticeFinish {
		unitSchedule.IsDone = lib.TDone
	}
	unitSchedule.UpdateTime = now
	if rollbackErr = unitSchedule.UpdateByTransaction(session); rollbackErr != nil {
		return
	}
	if err := session.Commit(); err != nil {
		logger.Warn("TV 智能课表异步任务事务提交出错", err)
	}
}
