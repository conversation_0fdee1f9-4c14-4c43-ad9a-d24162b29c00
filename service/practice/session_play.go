package practice

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/playtime"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/sessionplay"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/user"
	lib "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type sessionPlay struct{}

var SessionPlay sessionPlay

// StatSessionPlay 练习行为统计
func (st *sessionPlay) Stat(data *sessionplay.Detail) {
	// 更新st_session_play
	if data.DoneStatus == lib.SessionPlayDoneStatus.Default {
		data.DoneStatus = lib.SessionPlayDoneStatus.Undone
	}

	// 如果上报时间大于当天时间或者小于20160101则忽略处理
	if data.ReportTime > time.Now().Unix() || data.ReportTime < 1451577600 {
		return
	}
	if err := data.Save(); err != nil {
		logger.Error(err)
	}

	// 更新st_session_play_day、st_session_play_month、st_session_play_year
	st.setStat(data)
	// 更新st_session_play_collect
	st.setCollect(data)
	// 更新st_user_collect
	st.setUserCollect(data)
}

func (st *sessionPlay) setStat(sp *sessionplay.Detail) {
	sts := []string{
		sessionplay.StatDay, sessionplay.StatMonth, sessionplay.StatYear,
	}

	stDateIndexFormatType := map[string]string{
		sessionplay.StatDay:   lib.DateFormatDay,
		sessionplay.StatMonth: lib.DateFormatMonth,
		sessionplay.StatYear:  lib.DateFormatYear,
	}

	for _, st := range sts {
		dateIndex := time.Unix(sp.ReportTime, 0).Format(stDateIndexFormatType[st])
		spi := sessionplay.TbStat.GetItem(st, sp.SessionID, dateIndex)
		if spi != nil && spi.ID > 0 {
			spi.PlayTime += sp.PlayTime
			if err := spi.Update(); err != nil {
				logger.Error(err)
			}
		} else {
			spi := &sessionplay.Stat{
				StType: st, SessionID: sp.SessionID, PlayTime: sp.PlayTime, DateIndex: dateIndex,
			}
			if err := spi.Save(); err != nil {
				logger.Error(err)
			}
		}
	}
}

func (st *sessionPlay) setCollect(sp *sessionplay.Detail) {
	spc := sessionplay.TbCollect.GetItem(sp.SessionID)
	if spc != nil && spc.ID > 0 {
		spc.PlayTime += sp.PlayTime
		if err := spc.Update(); err != nil {
			logger.Error(err)
		}
	} else {
		spcItem := &sessionplay.Collect{
			SessionID: sp.SessionID,
			PlayTime:  sp.PlayTime,
		}
		if err := spcItem.Save(); err != nil {
			logger.Error(err)
		}
	}
}

func (st *sessionPlay) setUserCollect(sp *sessionplay.Detail) {
	// 更新 userCollect 用户练习时间
	userCollect, err := user.TbUserCollect.FindOrCreate(sp.UID)
	if err != nil {
		logger.Error(err)
		return
	}
	tc := playtime.TbCollect.GetItem(sp.UID)
	if tc != nil && tc.PlayTime > 0 {
		userCollect.TotalPlayTime = tc.PlayTime
		userCollect.LastPlayTime = tc.UpdateTime
	}
	sc := db.TbCollect.GetItem(sp.UID)
	if sc != nil && sc.TotalPracticeDay > 0 {
		userCollect.TotalPracticeDay = sc.TotalPracticeDay
		userCollect.LastPracticeTime = sc.LastPracticeDate
	}

	userCollect.UpdateTime = time.Now().Unix()

	if err := userCollect.UpdateByUID(); err != nil {
		logger.Error(err)
		return
	}
}
