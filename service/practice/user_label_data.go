package practice

import (
	dbstatnew "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/group"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/user"
)

type userLabelDataList struct {
}

var UserLabelDataList userLabelDataList

// SaveUserPracticeLabelData 获取用户练习标签元数据
func (u *userLabelDataList) SaveUserPracticeLabelData(item *dbstatnew.UserPractice) {
	labelData := &group.LastUserLabelData{
		UID:             item.UID,
		ProgramID:       item.ProgramID,
		SessionID:       item.SessionID,
		SessionIndex:    item.SessionIndex,
		UserActionTable: item.UserActionTable,
		Keyword:         "",
		LinkType:        item.LinkType,
		ReportTime:      item.ReportTime,
		SourceType:      int32(library.SourceTypeEum.Practice),
	}

	_ = user.ServiceUserLabel.ProcessUserLabel(labelData)
}
