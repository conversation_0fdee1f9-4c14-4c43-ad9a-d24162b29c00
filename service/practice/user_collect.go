package practice

import (
	"strconv"
	"time"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/actionlog"
)

type UserLogin struct {
	UID int64 `json:"uid"`
}

func LoginLog(uid int64, actionType string) error {
	t := time.Now()
	todayTime := time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, time.Local)
	todayIndex := todayTime.Format("20060102")
	data := UserLogin{UID: uid}
	if data.UID < 1 {
		return errors.New("invalid params: uid")
	}

	userCollect, err := user.TbUserCollect.FindOrCreate(data.UID)
	if err != nil {
		logger.Error(err)
		return err
	}
	// 遍历用户签到数据
	signInData := actionlog.TbUserSignIn.GetUserSignInByUID(data.UID, 0, time.Now().Unix())
	for _, v := range signInData {
		dayIndex := time.Unix(v.CreateTime, 0).Format("20060102")

		if todayIndex == dayIndex { // 异常情况当天的签到不算
			continue
		}
		userLog, err := user.TbUserLoginLog.FindOne(data.UID, dayIndex)
		if err != nil || userLog != nil {
			continue
		}

		err = userCollect.IncLoginDay(v.CreateTime)
		if err != nil {
			continue
		}

		loginLog := user.LoginLog{
			UID:        v.UID,
			OriginalID: v.ID, OriginalType: 3,
			MonthIndex: dayIndex[:6], DayIndex: dayIndex,
			OriginalDate: v.CreateTime, CreateTime: v.CreateTime,
		}
		if err = loginLog.Save(); err != nil {
			logger.Error(err)
		}
	}

	// 遍历用户练习数据补充登录数据
	days := practice.TbDay.GetList(data.UID, "desc")
	for _, v := range days {
		userLog, err := user.TbUserLoginLog.FindOne(data.UID, v.DateIndex)
		if err != nil {
			logger.Error(err)
			continue
		}
		if todayIndex == v.DateIndex || userLog != nil { // 异常情况当天的练习不算
			continue
		}

		loginLog := user.LoginLog{
			UID:        v.UID,
			OriginalID: v.ID, OriginalType: 2,
			OriginalDate: v.CreateTime, CreateTime: v.CreateTime,
			MonthIndex: v.DateIndex[:6], DayIndex: v.DateIndex,
		}
		if err = loginLog.Save(); err != nil {
			logger.Error(err)
			continue
		}
		if err = userCollect.IncLoginDay(v.CreateTime); err != nil {
			logger.Error(err)
		}
	}

	if actionType == "user_login" {
		if err := userCollect.IncLoginDay(time.Now().Unix()); err != nil {
			return err
		}
	}

	for i := 1; i <= int(GetWeekCount(time.Now().Unix())); i++ {
		if count, err := practice.TbWeek.GetAll(data.UID, strconv.Itoa(i)); err != nil {
			logger.Error(err)
			continue
		} else if count > 0 {
			if err = user.TbUserCollect.IncPracticeWeek(data.UID, count); err != nil {
				logger.Error(err)
			}
		}
	}

	return nil
}
