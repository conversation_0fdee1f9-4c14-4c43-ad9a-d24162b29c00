package practice

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/course"
	is "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/intelligence"
)

type intelligence struct{}

var ServiceIntelligence intelligence

// 智能课表任务处理逻辑
func (i *intelligence) ProcessIntelligenceTask(taskInfo *LoggingPracticePayload) {
	// 只有上报practice_date时才需要处理智能课表
	if taskInfo.PracticeDate <= 0 {
		return
	}
	currentTime := time.Now().Unix()
	var rollbackErr error
	// 事务删除
	session := db.GetEngineMaster().NewSession()
	defer session.Close()
	defer func() {
		if rollbackErr != nil {
			logger.Error("智能课表错误", rollbackErr)
			if err := session.Rollback(); err != nil {
				logger.Warn("智能课表异步任务回滚出错", err)
			}
		}
	}()
	// 查询当前用户课程表基础信息
	userSchedule := is.TbUserSchedule.GetDetail(&is.UserScheduleQuery{UID: taskInfo.UID})
	if userSchedule == nil {
		return
	}
	// 用户当前加入的智能课表课程信息
	userSessionList := is.TbUserSession.ScheduleSessionList(taskInfo.UID)
	if len(userSessionList) == 0 {
		return
	}
	// 查询课程基础信息,为了实时获取课程数据
	sessionItem := course.TbSession.GetSessionByID(taskInfo.SessionID)
	// 神策上报数据
	practiceHistory := &is.UserPracticeHistory{
		UserID:       taskInfo.UID,
		SessionID:    taskInfo.SessionID,
		PracticeDate: taskInfo.PracticeDate,
		UploadTime:   taskInfo.PracticeCurrentTime,
		CreateTime:   currentTime,
		UpdateTime:   currentTime,
	}
	if rollbackErr = practiceHistory.SaveByTransaction(session); rollbackErr != nil {
		return
	}

	calories := int64(sessionItem.CalorieRatio) * taskInfo.PlayTime / 60 * library.StandBodyWeight / 1000
	userSession := &is.UserSession{
		UserID:           taskInfo.UID,
		SessionId:        taskInfo.SessionID,
		SessionIndex:     taskInfo.SessionIndex,
		PracticeDate:     taskInfo.PracticeDate,
		Calories:         int32(calories),
		PracticeDuration: int32(taskInfo.PlayTime) / 60,
		PracticeCount:    1,
		UpdateTime:       currentTime,
	}
	if rollbackErr = userSession.UpdateByTransaction(session); rollbackErr != nil {
		return
	}

	var lastDaySessionProcess = false
	todayFirstTime, _ := service.GetDayFirstAndLastUnixTimestamp(time.Unix(taskInfo.PracticeCurrentTime, 0))

	// 判断是否最后一天的课程，报告未生成
	if userSchedule.ReportStatus == 1 && todayFirstTime < userSchedule.EndTime &&
		taskInfo.PracticeDate == userSchedule.EndTime {
		// 只要是当天的任务当去检测
		practiceDateSession := is.TbUserSession.SessionByPracticeDate(taskInfo.UID, taskInfo.PracticeDate)
		for _, item := range practiceDateSession {
			lastDaySessionProcess = item.PracticeCount != 0
			if lastDaySessionProcess {
				break
			}
		}
		if lastDaySessionProcess {
			// 基础信息值
			collectSessionData := is.TbUserSession.CollectUserSession(taskInfo.UID, userSchedule.ID)
			userReport := &is.UserReport{
				UserID: taskInfo.UID, UserScheduleID: userSchedule.ID, UpdateTime: currentTime,
				FinishedSessionCount: collectSessionData["practice_count"],
				PracticeDuration:     collectSessionData["practice_duration"],
				Calories:             collectSessionData["calories"],
			}
			if rollbackErr = userReport.UpdateByTransaction(session); rollbackErr != nil {
				return
			}

			userSchedule := &is.UserSchedule{
				Status: 2, UserID: taskInfo.UID, ReportStatus: 2,
				ID:         userSchedule.ID,
				UpdateTime: currentTime, CompleteTime: currentTime, // 完成时间次日可展示报告
			}
			if rollbackErr = userSchedule.UpdateByTransaction(session); rollbackErr != nil {
				return
			}
		}
	}

	if err := session.Commit(); err != nil {
		logger.Warn("智能课表异步任务事务提交出错", err)
	}
}
