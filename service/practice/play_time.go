package practice

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/crypto"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/playtime"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/sessionplay"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/user"
	lib "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type playTime struct{}

var PlayTime playTime

type userCollectCacheInfo struct {
	UID             int64 `json:"uid"`
	PlayTime        int64 `json:"play_time"` // 练习总时长
	Calorie         int32 `json:"calorie"`   // 练习总卡路里
	DisplayPlayTime int64 `json:"display_play_time"`
	CreateTime      int64 `json:"create_time"`
	UpdateTime      int64 `json:"update_time"`
}

// StatPlayTime 练习时长统计
func (s *playTime) Stat(data *sessionplay.Detail) {
	// 如果上报时间大于当天时间或者小于20160101则忽略处理
	if data.ReportTime > time.Now().Unix() || data.ReportTime < 1451577600 {
		return
	}

	// 更新st_play_time_day
	s.setDay(data)
	// 更新st_play_time_week
	s.setWeek(data)
	// 更新st_play_time_month
	s.setMonth(data)
	// 更新st_play_time_year
	s.setYear(data)
	// 更新st_play_time_collect
	s.setCollect(data)
	// 更新st_user_collect
	s.setUserCollect(data)
}

func (s *playTime) StatClean(data *sessionplay.Detail) {
	// 如果上报时间大于当天时间或者小于20160101则忽略处理
	if data.ReportTime > time.Now().Unix() || data.ReportTime < 1451577600 {
		return
	}
	// 更新st_play_time_day
	s.setCleanDay(data)
	// 更新st_play_time_week
	s.setCleanWeek(data)
	// 更新st_play_time_month
	s.setCleanMonth(data)
	// 更新st_play_time_year
	s.setCleanYear(data)
	// 更新st_play_time_collect
	s.setCleanCollect(data)
}

func (s *playTime) setDay(data *sessionplay.Detail) {
	dateIndex := time.Unix(data.ReportTime, 0).Format(lib.DateFormatDay)
	sd := playtime.TbDay.GetItem(data.UID, dateIndex)
	if sd != nil && sd.ID > 0 {
		sd.PlayTime += data.PlayTime
		sd.DisplayPlayTime = GetDisplayPlayTime(sd.PlayTime)
		if err := sd.Update(); err != nil {
			return
		}
	} else {
		sdItem := &playtime.Day{
			UID:             data.UID,
			PlayTime:        data.PlayTime,
			DisplayPlayTime: GetDisplayPlayTime(data.PlayTime),
			DateIndex:       dateIndex,
		}
		if err := sdItem.Save(); err != nil {
			logger.Error(err)
		}
	}
}

func (s *playTime) setCleanDay(data *sessionplay.Detail) {
	dateIndex := time.Unix(data.ReportTime, 0).Format(lib.DateFormatDay)
	sd := playtime.TbDay.GetItem(data.UID, dateIndex)
	if sd != nil && sd.ID > 0 {
		sd.PlayTime -= data.PlayTime
		sd.DisplayPlayTime = GetDisplayPlayTime(sd.PlayTime)
		if err := sd.UpdateMustColsSetCleanDay(); err != nil {
			return
		}
	}
}

func (s *playTime) setWeek(data *sessionplay.Detail) {
	dateIndex := time.Unix(data.ReportTime, 0).Format(lib.DateFormatMonth)
	week := GetWeekCount(data.ReportTime)
	dateWeek := strconv.Itoa(int(week))
	if week < 1 {
		return
	}
	sw := playtime.TbWeek.GetItem(data.UID, dateWeek)
	if sw != nil && sw.ID > 0 {
		sw.PlayTime += data.PlayTime
		sw.DisplayPlayTime = GetDisplayPlayTime(sw.PlayTime)
		if err := sw.Update(); err != nil {
			logger.Error(err)
		}
	} else {
		swItem := &playtime.Week{
			UID:             data.UID,
			PlayTime:        data.PlayTime,
			DisplayPlayTime: GetDisplayPlayTime(data.PlayTime),
			DateIndex:       dateIndex,
			Week:            dateWeek,
		}
		if err := swItem.Save(); err != nil {
			logger.Error(err)
		}
	}
}

func (s *playTime) setCleanWeek(data *sessionplay.Detail) {
	week := GetWeekCount(data.ReportTime)
	dateWeek := strconv.Itoa(int(week))
	if week < 1 {
		return
	}
	sw := playtime.TbWeek.GetItem(data.UID, dateWeek)
	if sw != nil && sw.ID > 0 {
		sw.PlayTime -= data.PlayTime
		sw.DisplayPlayTime = GetDisplayPlayTime(sw.PlayTime)
		if err := sw.UpdateMustColsSetCleanWeek(); err != nil {
			logger.Error(err)
		}
	}
}

func (s *playTime) setMonth(data *sessionplay.Detail) {
	dateMonth := time.Unix(data.ReportTime, 0).Format(lib.DateFormatMonth)
	sm := playtime.TbMonth.GetItem(data.UID, dateMonth)
	var playTime int64
	if sm != nil && sm.ID > 0 {
		playTime = sm.PlayTime + data.PlayTime
		sm.PlayTime = playTime
		sm.DisplayPlayTime = GetDisplayPlayTime(playTime)
		if err := sm.Update(); err != nil {
			logger.Error(err)
		}
	} else {
		playTime = data.PlayTime
		smItem := &playtime.Month{
			UID:             data.UID,
			PlayTime:        playTime,
			DisplayPlayTime: GetDisplayPlayTime(data.PlayTime),
			DateIndex:       dateMonth,
		}
		if err := smItem.Save(); err != nil {
			logger.Error(err)
		}
	}
	go s.cacheMonthPlayTimeRank(data.UID, playTime, dateMonth)
}

func (s *playTime) setCleanMonth(data *sessionplay.Detail) {
	dateMonth := time.Unix(data.ReportTime, 0).Format(lib.DateFormatMonth)
	sm := playtime.TbMonth.GetItem(data.UID, dateMonth)
	if sm != nil && sm.ID > 0 {
		sm.PlayTime -= data.PlayTime
		sm.DisplayPlayTime = GetDisplayPlayTime(sm.PlayTime)

		if err := sm.UpdateMustColsSetCleanMonth(); err != nil {
			logger.Error(err)
		}
	}
}

func (s *playTime) setYear(data *sessionplay.Detail) {
	dateYear := time.Unix(data.ReportTime, 0).Format(lib.DateFormatYear)
	sy := playtime.TbYear.GetItem(data.UID, dateYear)
	if sy != nil && sy.ID > 0 {
		sy.PlayTime += data.PlayTime
		sy.DisplayPlayTime = GetDisplayPlayTime(sy.PlayTime)
		if err := sy.Update(); err != nil {
			logger.Error(err)
		}
	} else {
		syItem := &playtime.Year{
			UID:             data.UID,
			PlayTime:        data.PlayTime,
			DisplayPlayTime: GetDisplayPlayTime(data.PlayTime),
			DateIndex:       dateYear,
		}
		if err := syItem.Save(); err != nil {
			logger.Error(err)
		}
	}
}

func (s *playTime) setCleanYear(data *sessionplay.Detail) {
	dateYear := time.Unix(data.ReportTime, 0).Format(lib.DateFormatYear)
	sy := playtime.TbYear.GetItem(data.UID, dateYear)
	if sy != nil && sy.ID > 0 {
		sy.PlayTime -= data.PlayTime
		sy.DisplayPlayTime = GetDisplayPlayTime(sy.PlayTime)
		if err := sy.UpdateMustColsSetCleanYear(); err != nil {
			logger.Error(err)
		}
	}
}

// setCollect 设置时长
func (s *playTime) setCollect(data *sessionplay.Detail) {
	sc := playtime.TbCollect.GetItem(data.UID)
	if sc != nil && sc.ID > 0 {
		sc.PlayTime += data.PlayTime
		sc.DisplayPlayTime = GetDisplayPlayTime(sc.PlayTime)
		if err := sc.Update(); err != nil {
			logger.Error(err)
		}
		// 2020-02-11 water 设置总时长的缓存
		if err := s.setCollectCacheData(sc); err != nil {
			logger.Error(err)
		}
	} else {
		scItem := &playtime.Collect{
			UID:             data.UID,
			PlayTime:        data.PlayTime,
			DisplayPlayTime: GetDisplayPlayTime(data.PlayTime),
		}
		if err := scItem.Save(); err != nil {
			logger.Error(err)
		}
		sc = scItem
	}
	if err := Mission.CompleteUserMissions(data.UID, sc.PlayTime, MinutesTarget); err != nil {
		logger.Error(err)
	}
}

// 更新 userCollect 用户练习时间
func (s *playTime) setUserCollect(data *sessionplay.Detail) {
	userCollect, err := user.TbUserCollect.FindOrCreate(data.UID)
	if err != nil {
		logger.Error(err)
		return
	}
	tc := playtime.TbCollect.GetItem(data.UID)
	if tc != nil && tc.PlayTime > 0 {
		userCollect.TotalPlayTime = tc.PlayTime
		userCollect.LastPlayTime = tc.UpdateTime
	}
	sc := db.TbCollect.GetItem(data.UID)
	if sc != nil && sc.TotalPracticeDay > 0 {
		userCollect.TotalPracticeDay = sc.TotalPracticeDay
		userCollect.LastPracticeTime = sc.LastPracticeDate
	}
	userCollect.UpdateTime = time.Now().Unix()
	if err := userCollect.UpdateByUID(); err != nil {
		logger.Error(err)
		return
	}
}

func (s *playTime) setCleanCollect(data *sessionplay.Detail) {
	sc := playtime.TbCollect.GetItem(data.UID)
	if sc != nil && sc.ID > 0 {
		sc.PlayTime -= data.PlayTime
		sc.DisplayPlayTime = GetDisplayPlayTime(sc.PlayTime)
		if err := sc.UpdateMustColsSetCleanCollect(); err != nil {
			logger.Error(err)
		}
	}
}

// 2020-02-11 water 设置总时长的缓存 620 Service_service_User用
func (s *playTime) setCollectCacheData(data *playtime.Collect) error {
	cKey := fmt.Sprintf("play_time_new_data_%d", data.UID)
	cacheKey := fmt.Sprintf("product_pre_memcache%s", strings.ToLower(crypto.Byte2Md5([]byte(cKey))))
	cacheData := userCollectCacheInfo{
		UID:             data.UID,
		PlayTime:        data.PlayTime,
		Calorie:         data.Calorie,
		DisplayPlayTime: data.DisplayPlayTime,
		CreateTime:      data.CreateTime,
		UpdateTime:      data.UpdateTime,
	}
	cacheValue, err := json.Marshal(cacheData)
	if err != nil {
		return err
	}
	err = cache.GetMemcached().SetEx(cacheKey, string(cacheValue), 300)
	if err != nil {
		return err
	}

	// 2021-08-02 zhangsheng
	// 9.1版本 620首页练习时长会读取【play_time_new_data_】表，
	// 读取信息以后会对读取到的数据加一个300s的缓存 这边修改数据以后
	// 620那边如果缓存未失效的情况下数据会不一致
	// 因而在这里增加更新620指定缓存key的操作
	phpCacheKey := crypto.Byte2Md5([]byte(cKey))
	// php中的语句为 select uid, play_time, display_play_time, calorie from
	// 所以只要4个字段
	cacheDataCopy := userCollectCacheInfo{
		UID:             data.UID,
		PlayTime:        data.PlayTime,
		Calorie:         data.Calorie,
		DisplayPlayTime: data.DisplayPlayTime,
	}
	phpCacheValue, err := json.Marshal(cacheDataCopy)
	phpCacheErr := cache.GetMemcached().SetEx(phpCacheKey, string(phpCacheValue), 300)
	if phpCacheErr != nil {
		return err
	}
	return nil
}

// LimitPlayTime 这个全局变量记录月度练习总时长,排名第50名的玩家的练习时长
// 或者说叫进入排行榜的门槛
var LimitPlayTime int64 = 0

// CacheMonth 缓存当前的月份 用来进行月度切换
var CacheMonth string = ""

// MonthPlayTime 月度练习数据结构
type MonthPlayTime struct {
	UID      int64 `json:"uid"`
	PlayTime int64 `json:"play_time"`
}

// cacheMonthPlayTime 缓存月练习时长排行前50
func (s *playTime) cacheMonthPlayTimeRank(uid, playTime int64, dateMonth string) {
	// 生成缓存key
	cacheKey := fmt.Sprintf("%s%s", lib.RedisKeyMonthPLayTimePayload, dateMonth)

	// 如果LimitPlayTime值为0或者CacheMonth为空
	// 那么就认为是服务重启或者数据丢失
	// 如果CacheMonth与dateMonth不一致 说明是跨月了
	// 上述两种情况都需要重新拉取数据
	if LimitPlayTime == 0 || CacheMonth == "" || CacheMonth != dateMonth {
		rankList := s.getMonthPlayTimeRank(dateMonth)
		if len(rankList) != 50 {
			logger.Error("月度练习时长排行榜<数据异常>,请检查")
			return
		}
		// 设置缓存
		if err := s.resetMonthPlayTimeRankCache(rankList, cacheKey); err != nil {
			logger.Error("设置月度练习时长排行榜<缓存异常>,请检查,error:", err)
			return
		}
		// 设置第50名玩家的练习时长为是否更新缓存的边界
		LimitPlayTime = rankList[49].PlayTime
		CacheMonth = dateMonth
	}

	// 如果有玩家的总练习时长超过更新缓存边界值 那么前50名的名次就需要调整
	// 无论这个玩家是否以及在前50
	if playTime > LimitPlayTime {
		rankList, err := s.getMonthPlayTimeRankCache(cacheKey)
		if err != nil {
			logger.Error("读取月度练习时长排行榜<缓存异常>,请检查,error:", err)
			return
		}
		newRankList := s.sortMonthPlayTimeRank(rankList, uid, playTime)
		if err := s.resetMonthPlayTimeRankCache(newRankList, cacheKey); err != nil {
			logger.Error("更新月度练习时长排行榜<更新失败>,请检查,error:", err)
		}
	}
}

// 重新设置排行数据的cache
func (s playTime) resetMonthPlayTimeRankCache(rankList []MonthPlayTime, cacheKey string) (err error) {
	data := make([]string, 0)
	for _, value := range rankList {
		jsonByte, err := json.Marshal(value)
		if err != nil {
			return err
		}
		data = append(data, string(jsonByte))
	}
	// 先把老的list删除掉 再重新设置
	redis := cache.GetYogaRedis()
	redis.Del(context.Background(), cacheKey)
	_, err = redis.RPush(context.Background(), cacheKey, data).Result()
	return err
}

// 读取cache读取排行数据
func (s playTime) getMonthPlayTimeRankCache(cacheKey string) ([]MonthPlayTime, error) {
	data := make([]MonthPlayTime, 0)
	result, err := cache.GetYogaRedis().LRange(context.Background(), cacheKey, 0, -1).Result()
	if err != nil {
		return data, err
	}
	item := MonthPlayTime{}
	for _, value := range result {
		err := json.Unmarshal([]byte(value), &item)
		if err != nil {
			return data, err
		}
		data = append(data, item)
	}
	return data, nil
}

// 从db读取排行数据
func (s *playTime) getMonthPlayTimeRank(dateMonth string) []MonthPlayTime {
	rankList := make([]MonthPlayTime, 50)
	result := playtime.TbMonth.GetPlayTimeMonthRank(dateMonth)
	for key, value := range result {
		rankList[key] = MonthPlayTime{UID: value.UID, PlayTime: value.PlayTime}
	}
	return rankList
}

// 排行榜重排序 插序
func (s *playTime) sortMonthPlayTimeRank(rankList []MonthPlayTime, uid, playTime int64) []MonthPlayTime {
	// 1.检查这个用户在不在rank里 在的话直接替换
	inRank := false
	for key, value := range rankList {
		if value.UID == uid {
			rankList[key].PlayTime = playTime
			inRank = true
			break
		}
	}
	// 冲榜用户不在现有rank里 追加到原有rank尾部
	if !inRank {
		rankList = append(rankList, MonthPlayTime{UID: uid, PlayTime: playTime})
	}
	// 2.重新排序
	for key := range rankList {
		for j := key; j > 0; j-- {
			if rankList[j].PlayTime > rankList[j-1].PlayTime {
				rankList[j], rankList[j-1] = rankList[j-1], rankList[j]
			}
		}
	}
	// 冲榜用户不在现有rank里 把第51名踢出去
	if len(rankList) > 50 {
		return rankList[:50]
	}
	return rankList
}
