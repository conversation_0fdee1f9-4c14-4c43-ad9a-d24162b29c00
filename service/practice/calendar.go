package practice

import (
	"sort"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	dbcourse "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds"
	dbp "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	lib "gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/comm"
)

type calendar struct{}

var SvrCalendar calendar

func (c *calendar) PracticeCalendar(taskInfo *LoggingPracticePayload) {
	// 生理期课表是否完成更新
	if taskInfo.ActionLogType == lib.PracticeFinish && taskInfo.PeriodPracticeDate > 0 {
		dbp.TbPeriodSession.UpdateDatePeriodSessionDone(taskInfo.UID,
			taskInfo.PeriodPracticeDate, taskInfo.SessionID)
	}
	// 防止异步上报 导致日历 自动推的数据出错
	resourceType := c.practiceResourceType(taskInfo)
	if resourceType == lib.ResourceTypeEnum.UnKnown.ToInt32() {
		return
	}

	var calendarP *dbp.CalendarP
	if resourceType == lib.ResourceTypeEnum.IntelligenceSchedule.ToInt32() {
		status := []int32{lib.CTypeEnum.Ing.ToInt32(), lib.CTypeEnum.Done.ToInt32()}
		calendarP = dbp.TbCalendarP.GetUserLastSession(taskInfo.UID, lib.ResourceTypeEnum.IntelligenceSchedule.ToInt32(),
			status)
		if calendarP == nil {
			return
		}
		pDate := dbp.TbCalendarPD.GetItemByDate(taskInfo.UID, calendarP.ID, comm.FormatStartTime(taskInfo.PracticeDate))
		if pDate == nil {
			return
		}
		uData := dbp.TbCalendarPU.GetListUnit(calendarP.ID, taskInfo.UID, calendarP.ProgramID,
			taskInfo.SessionID, pDate.DayIndex)
		if uData == nil {
			return
		}
		taskInfo.ProgramID = calendarP.ProgramID
		taskInfo.SubSessionIndex = taskInfo.SessionIndex
		taskInfo.SessionIndex = uData.SessionIndex
	} else {
		calendarP = dbp.TbCalendarP.GetCalendarByProgramIDType(taskInfo.UID,
			taskInfo.ProgramID, []int32{lib.ResourceTypeEnum.Program.ToInt32(),
				lib.ResourceTypeEnum.Kol.ToInt32()}, lib.CTypeEnum.Ing.ToInt32())
		if calendarP != nil && calendarP.ReportTime < comm.FormatStartTime(taskInfo.PracticeCurrentTime) &&
			calendarP.SessionIndex == 0 && calendarP.SessionIndex+1 != taskInfo.SessionIndex {
			c.handleProgramProgress(calendarP, taskInfo)
			return
		}
	}
	if calendarP == nil {
		return
	}
	if comm.FormatStartTime(calendarP.ReportTime) == comm.FormatStartTime(taskInfo.PracticeCurrentTime) &&
		taskInfo.SessionIndex == calendarP.SessionIndex &&
		(calendarP.ResourceType == lib.ResourceTypeEnum.Program.ToInt32() ||
			calendarP.ResourceType == lib.ResourceTypeEnum.Kol.ToInt32()) {
		c.handleProgramCurrentProgress(calendarP, taskInfo, false)
		return
	} else if calendarP.ReportTime < comm.FormatStartTime(taskInfo.PracticeCurrentTime) &&
		(calendarP.ResourceType == lib.ResourceTypeEnum.Program.ToInt32() ||
			calendarP.ResourceType == lib.ResourceTypeEnum.Kol.ToInt32()) {
		c.handleProgramCurrentProgress(calendarP, taskInfo, true)
		return
	}
	// 不更新进度 使用sessionIndex 点亮处理完成
	c.handleProgramProgress(calendarP, taskInfo)
}

// handleProgress 处理历史练习的课程状态
func (c *calendar) handleProgramProgress(calendarP *dbp.CalendarP, taskInfo *LoggingPracticePayload) {
	dListB, dayList := c.getDateListByDay(calendarP.ID, taskInfo.UID)
	if dListB == nil || len(dListB) < 1 {
		return
	}
	uData := dbp.TbCalendarPU.GetListBYCalendarPracticeID(calendarP.ID, calendarP.UID)
	if uData == nil || len(uData) < 1 {
		return
	}
	if calendarP.ResourceType == lib.ResourceTypeEnum.IntelligenceSchedule.ToInt32() &&
		calendarP.ReportTime < comm.FormatStartTime(taskInfo.PracticeCurrentTime) {
		calendarP.DayIndex++
		lastDay := dayList[len(dayList)-1]
		if calendarP.DayIndex >= lastDay {
			calendarP.DayIndex = lastDay
		}
	}
	indexLists := make(map[int32][]*dbp.CalendarPU)
	dListD := make(map[int32]*dbp.CalendarPD)
	for _, v := range uData {
		if _, ok := indexLists[v.SessionIndex]; !ok {
			indexLists[v.SessionIndex] = make([]*dbp.CalendarPU, 0)
			if _, ok := dListB[v.DayIndex]; ok {
				dListD[v.SessionIndex] = dListB[v.DayIndex]
			}
		}
		indexLists[v.SessionIndex] = append(indexLists[v.SessionIndex], v)
	}

	session := dbcourse.GetEngineMaster().NewSession()
	defer session.Close()
	err := session.Begin()
	if err != nil {
		logger.Error(err)
		return
	}

	var isDone int
	if units, ok := indexLists[taskInfo.SessionIndex]; ok {
		for _, v := range units {
			if taskInfo.SessionID == v.SessionID && v.ReportTime == 0 {
				if calendarP.ResourceType == lib.ResourceTypeEnum.IntelligenceSchedule.ToInt32() {
					calendarP.SessionID = v.SessionID
					calendarP.SessionIndex = v.SessionIndex
					calendarP.SubSessionIndex = v.SubSessionIndex
					calendarP.ReportTime = taskInfo.PracticeCurrentTime
					if err := calendarP.UpdateByTransaction(session); err != nil {
						if rollbackErr := session.Rollback(); rollbackErr != nil {
							logger.Warn("用户日历主表出错", rollbackErr)
							return
						}
					}
				}
				v.ReportTime = taskInfo.PracticeCurrentTime
				v.IsDone = lib.CTypeEnum.Done.ToInt32()
				if err := v.UpdateByTransaction(session); err != nil {
					if rollbackErr := session.Rollback(); rollbackErr != nil {
						logger.Warn("用户日历明细出错", rollbackErr)
						return
					}
				}
			}
			logger.Infof("练习日历 是否练习完成 处理handleProgramProgress 数据 Uid:%d 课程ID：%d 完成状态：%d ",
				taskInfo.UID, v.SessionID, v.IsDone)
			if v.IsDone == lib.CTypeEnum.Done.ToInt32() {
				isDone++
			}
		}
		c.updateTableStatus(calendarP, dListD, taskInfo, isDone, units, true, session)
	}
	if rollbackErr := session.Commit(); rollbackErr != nil {
		logger.Warn("练习日历上报数据出错", rollbackErr)
	}
}

// handleCurrentProgress 处理当前进度
func (c *calendar) handleProgramCurrentProgress(calendarP *dbp.CalendarP,
	taskInfo *LoggingPracticePayload, needP bool) {
	sList, dayList := c.getSessionListByDay(calendarP.ID, taskInfo.UID)
	dListD, _ := c.getDateListByDay(calendarP.ID, taskInfo.UID)
	if needP {
		calendarP.DayIndex++
		lastDay := dayList[len(dayList)-1]
		if calendarP.DayIndex >= lastDay {
			calendarP.DayIndex = lastDay
		}
	}
	session := dbcourse.GetEngineMaster().NewSession()
	defer session.Close()
	err := session.Begin()
	if err != nil {
		logger.Error(err)
		return
	}

	var isDone int
	if units, ok := sList[calendarP.DayIndex]; ok {
		for _, v := range units {
			if taskInfo.SessionID == v.SessionID && taskInfo.SessionIndex == v.SessionIndex {
				calendarP.SessionID = v.SessionID
				calendarP.SessionIndex = v.SessionIndex
				calendarP.SubSessionIndex = v.SubSessionIndex
				calendarP.ReportTime = taskInfo.PracticeCurrentTime
				if err := calendarP.UpdateByTransaction(session); err != nil {
					if rollbackErr := session.Rollback(); rollbackErr != nil {
						logger.Warn("用户日历主表出错", rollbackErr)
						return
					}
				}

				if v.IsDone != lib.CTypeEnum.Done.ToInt32() || v.ReportTime != taskInfo.PracticeCurrentTime {
					v.ReportTime = taskInfo.PracticeCurrentTime
					v.IsDone = lib.CTypeEnum.Done.ToInt32()
					if err := v.UpdateByTransaction(session); err != nil {
						if rollbackErr := session.Rollback(); rollbackErr != nil {
							logger.Warn("用户日历明细出错", rollbackErr)
							return
						}
					}
				}
			}
			logger.Infof("练习日历 是否练习完成 处理ListByDay数据 Uid:%d 课程ID：%d 完成状态：%d ",
				taskInfo.UID, v.SessionID, v.IsDone)
			if v.IsDone == lib.CTypeEnum.Done.ToInt32() {
				isDone++
			}
		}

		// 处理课程中途加入时 两天练习的时同一个课程导致 课程不会平移的问题
		sUnit := c.getSessionListByIndex(calendarP.ID, taskInfo.UID)
		if v, ok := sUnit[taskInfo.SessionIndex]; ok {
			calendarP.DayIndex = v[0].DayIndex
		}

		c.updateTableStatus(calendarP, dListD, taskInfo, isDone, units, false, session)
	}
	if rollbackErr := session.Commit(); rollbackErr != nil {
		logger.Warn("练习日历上报数据出错", rollbackErr)
	}
}

// updateTableStatus 更新uc_calendar_practice_date和uc_calendar_practice的进度
func (c *calendar) updateTableStatus(calendarP *dbp.CalendarP, dListD map[int32]*dbp.CalendarPD,
	taskInfo *LoggingPracticePayload, isDone int, units []*dbp.CalendarPU, isTask bool, session *xorm.Session) {
	var pDone, uDone int
	upd := func(d *dbp.CalendarPD) {
		d.IsDone = lib.CTypeEnum.Ing.ToInt32()
		if isDone == len(units) {
			d.IsDone = lib.CTypeEnum.Done.ToInt32()
		}
		if err := d.UpdateByTransaction(session); err != nil {
			if rollbackErr := session.Rollback(); rollbackErr != nil {
				logger.Warn("用户日历", rollbackErr)
				return
			}
		}
	}
	if isTask {
		if d, ok := dListD[taskInfo.SessionIndex]; ok {
			upd(d)
		}
	} else {
		if d, ok := dListD[calendarP.DayIndex]; ok {
			upd(d)
		}
	}
	if calendarP.Status == lib.CTypeEnum.Ing.ToInt32() {
		for _, v := range dListD {
			if v.IsDone == lib.CTypeEnum.Done.ToInt32() {
				uDone++
			}
			if v.IsDone == lib.CTypeEnum.Ing.ToInt32() || v.IsDone == lib.CTypeEnum.Done.ToInt32() {
				pDone++
			}
		}
		if calendarP.ResourceType == lib.ResourceTypeEnum.IntelligenceSchedule.ToInt32() && pDone == len(dListD) {
			calendarP.Status = lib.CTypeEnum.Done.ToInt32()
		} else if (calendarP.ResourceType == lib.ResourceTypeEnum.Program.ToInt32() ||
			calendarP.ResourceType == lib.ResourceTypeEnum.Kol.ToInt32()) && uDone == len(dListD) {
			calendarP.Status = lib.CTypeEnum.Done.ToInt32()
		}

		if err := calendarP.UpdateByTransaction(session); err != nil {
			if rollbackErr := session.Rollback(); rollbackErr != nil {
				logger.Warn("用户日历主表出错", rollbackErr)
				return
			}
		}
	}
}

func (c *calendar) practiceResourceType(taskInfo *LoggingPracticePayload) int32 {
	if taskInfo.PracticeDate > 0 {
		return lib.ResourceTypeEnum.IntelligenceSchedule.ToInt32()
	}

	if taskInfo.ActionLogType == lib.FinishSessionInProgram {
		return lib.ResourceTypeEnum.Program.ToInt32()
	}

	return lib.ResourceTypeEnum.UnKnown.ToInt32()
}

// getSessionListByDay 获取用户的日历子进度
func (c *calendar) getSessionListByDay(calendarPracticeID,
	uid int64) (sessionList map[int32][]*dbp.CalendarPU, dayIndex []int32) {
	uData := dbp.TbCalendarPU.GetListBYCalendarPracticeID(calendarPracticeID, uid)
	if uData == nil || len(uData) < 1 {
		return nil, nil
	}
	sessionList = make(map[int32][]*dbp.CalendarPU)
	dayIndex = make([]int32, 0)
	for _, v := range uData {
		if _, ok := sessionList[v.DayIndex]; !ok {
			dayIndex = append(dayIndex, v.DayIndex)
			sessionList[v.DayIndex] = make([]*dbp.CalendarPU, 0)
		}
		sessionList[v.DayIndex] = append(sessionList[v.DayIndex], v)
	}
	sort.Slice(dayIndex, func(i, j int) bool {
		return dayIndex[i] < dayIndex[j]
	})
	return sessionList, dayIndex
}

// getSessionListByIndex
func (c *calendar) getSessionListByIndex(calendarPracticeID, uid int64) map[int32][]*dbp.CalendarPU {
	uData := dbp.TbCalendarPU.GetListBYCalendarPracticeID(calendarPracticeID, uid)
	if uData == nil || len(uData) < 1 {
		return nil
	}
	sessionList := make(map[int32][]*dbp.CalendarPU)
	for _, v := range uData {
		if _, ok := sessionList[v.SessionIndex]; !ok {
			sessionList[v.SessionIndex] = make([]*dbp.CalendarPU, 0)
		}
		sessionList[v.SessionIndex] = append(sessionList[v.SessionIndex], v)
	}
	return sessionList
}

// getDateListByDay 获取用户排课日历
func (c *calendar) getDateListByDay(calendarPracticeID,
	uid int64) (sessionList map[int32]*dbp.CalendarPD, dayIndex []int32) {
	uData := dbp.TbCalendarPD.GetListBYCalendarDID(uid, calendarPracticeID)
	if uData == nil || len(uData) < 1 {
		return nil, nil
	}
	sessionList = make(map[int32]*dbp.CalendarPD)
	dayIndex = make([]int32, 0)
	for _, v := range uData {
		if _, ok := sessionList[v.DayIndex]; !ok {
			dayIndex = append(dayIndex, v.DayIndex)
		}
		sessionList[v.DayIndex] = v
	}
	sort.Slice(dayIndex, func(i, j int) bool {
		return dayIndex[i] < dayIndex[j]
	})
	return sessionList, dayIndex
}
