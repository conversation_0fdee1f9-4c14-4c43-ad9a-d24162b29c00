package practice

import (
	"sort"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	comm "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type totalPractice struct{}

var TotalPractice totalPractice

// CleanUserPracticeCompleteTimes water 计算用户练习次数使用的
func (p *totalPractice) CleanUserPracticeCompleteTimes(c *db.UserPractice) int32 {
	var completeTime int32
	if _, ok := comm.ExcludeTypes[c.UserActionTable]; !ok {
		bean := &db.Total{
			UID:             c.UID,
			ProgramID:       c.ProgramID,
			SessionID:       c.SessionID,
			SessionIndex:    c.SessionIndex,
			SubSessionIndex: c.SubSessionIndex,
			UserActionTable: c.UserActionTable,
			LastUpdateTime:  c.ReportTime,
		}
		userPracticeCompleteItem := p.getUserPracticeCompleteTotalItem(c)
		if userPracticeCompleteItem != nil && userPracticeCompleteItem.ID > 0 && userPracticeCompleteItem.CompleteTimes > 0 {
			completeTime = userPracticeCompleteItem.CompleteTimes
			bean.ID = userPracticeCompleteItem.ID
			bean.CompleteTimes = completeTime + 1
			if err := bean.UpdateMustCols(); err != nil {
				logger.Error(err)
			}
			return bean.CompleteTimes
		}
		// water 这个是处理初始化的时候 使用的
		bean.CompleteTimes = p.getUserPracticeCompletes(c)
		bean.CompleteTimes++
		// 2020-04-07 water 处理完成次数
		userPracticeCompleteTime := p.getUserPracticeCompleteTotalList(c)
		if len(userPracticeCompleteTime) > 0 {
			for i := range userPracticeCompleteTime {
				if userPracticeCompleteTime[i].UserActionTable == 0 {
					userPracticeCompleteItem = &userPracticeCompleteTime[i]
					continue
				}
			}
		}
		// water 写这个更新是为了简单的修复数据使用 当用户数据出错的时候把CompleteTimes置为0 就会在用户下次上报的时候 重新计
		if userPracticeCompleteItem != nil && userPracticeCompleteItem.ID > 0 {
			bean.ID = userPracticeCompleteItem.ID
			if err := bean.Update(); err != nil {
				logger.Error(err)
			}
			return bean.CompleteTimes
		}
		if err := bean.Save(); err != nil {
			logger.Error(err)
		}
		return bean.CompleteTimes
	}
	return completeTime
}

// UserPracticeCompleteTimes water 不需要计算历史数据时可以使用
// 20220612 新加入直播计算逻辑
// needIncrease 需不需要增加次数
func (p *totalPractice) UserPracticeCompleteTimes(c *db.UserPractice, needIncrease bool) int32 {
	var completeTime int32
	bean := &db.Total{
		UID:             c.UID,
		ProgramID:       c.ProgramID,
		SessionID:       c.SessionID,
		SessionIndex:    c.SessionIndex,
		SubSessionIndex: c.SubSessionIndex,
		UserActionTable: c.UserActionTable,
		LastUpdateTime:  c.ReportTime,
	}
	userPracticeCompleteItem := p.getUserPracticeCompleteTotalItem(c)
	if userPracticeCompleteItem != nil && userPracticeCompleteItem.ID > 0 && userPracticeCompleteItem.CompleteTimes > 0 {
		completeTime = userPracticeCompleteItem.CompleteTimes
		bean.ID = userPracticeCompleteItem.ID
		bean.CompleteTimes = completeTime
		if needIncrease {
			bean.CompleteTimes = completeTime + 1
			if err := bean.Update(); err != nil {
				logger.Error(err)
			}
		}
		return bean.CompleteTimes
	}
	bean.CompleteTimes++
	if err := bean.Save(); err != nil {
		logger.Error(err)
	}
	return bean.CompleteTimes
}

// CleanUserPracticeData 2020-01-06 water 更新用户练习卡路里以及时长
func (p *totalPractice) CleanUserPracticeData(c *db.UserPractice) {
	if _, ok := comm.ExcludeTypes[c.UserActionTable]; !ok {
		if c.UserActionTable == comm.FinishPlan {
			return
		}
		bean := &db.Total{
			UID: c.UID,
		}
		userPracticeCompleteTotal := p.getUserPracticeCompleteTotalItem(c)
		if userPracticeCompleteTotal != nil && userPracticeCompleteTotal.ID > 0 {
			bean.ID = userPracticeCompleteTotal.ID
			if userPracticeCompleteTotal.PlayTime > 0 {
				bean.Calorie = userPracticeCompleteTotal.Calorie + c.Calorie
				bean.PlayTime = userPracticeCompleteTotal.PlayTime + c.PlayTime
				if err := bean.Update(); err != nil {
					logger.Error(err)
				}
				return
			}
			// 2020-1-4 water 初始化次数
			list := db.TbUserPractice.GetAllList(c)
			if len(list) > 0 {
				var Calorie, PlayTime int64
				for _, v := range list {
					Calorie += v.Calorie
					PlayTime += v.PlayTime
				}
				bean.Calorie = Calorie
				bean.PlayTime = PlayTime
			} else {
				bean.Calorie = c.Calorie
				bean.PlayTime = c.PlayTime
			}
			// 更新数据
			if err := bean.Update(); err != nil {
				logger.Error(err)
			}
		}
	}
}

// 清理安卓错误数据上报(2020-2-12已经不使用 可以作为清理时长使用)
func (p *totalPractice) CleanUpdateUserPracticeData(c *db.UserPractice) {
	if _, ok := comm.ExcludeTypes[c.UserActionTable]; !ok {
		bean := &db.Total{
			UID:             c.UID,
			ProgramID:       c.ProgramID,
			SessionID:       c.SessionID,
			SessionIndex:    c.SessionIndex,
			SubSessionIndex: c.SubSessionIndex,
			LastUpdateTime:  c.ReportTime,
		}
		userPracticeCompleteItem := p.getUserPracticeCompleteTotalItem(c)
		if userPracticeCompleteItem != nil && userPracticeCompleteItem.ID > 0 {
			bean.ID = userPracticeCompleteItem.ID
			if userPracticeCompleteItem.Calorie > 0 && userPracticeCompleteItem.PlayTime > 0 {
				bean.Calorie = userPracticeCompleteItem.Calorie - c.Calorie
				bean.PlayTime = userPracticeCompleteItem.PlayTime - c.PlayTime
				if err := bean.UpdateMustColsCleanUpdateUserPracticeData(); err != nil {
					logger.Error(err)
				}
				return
			}
		}
	}
}

// water 2020-4-6 处理历史数据用
func (p *totalPractice) getUserPracticeCompleteTotalList(c *db.UserPractice) []db.Total {
	bean := &db.Total{
		UID:          c.UID,
		ProgramID:    c.ProgramID,
		SessionID:    c.SessionID,
		SessionIndex: c.SessionIndex,
	}
	return db.TbPracticeTotal.GetList(bean)
}

// 2020-04-06 修复之前的数据
func (p *totalPractice) getUserPracticeCompleteTotalItem(c *db.UserPractice) *db.Total {
	bean := &db.Total{
		UID:             c.UID,
		ProgramID:       c.ProgramID,
		SessionID:       c.SessionID,
		SessionIndex:    c.SessionIndex,
		UserActionTable: c.UserActionTable,
	}
	return db.TbPracticeTotal.GetItem(bean)
}

// 2020-1-4 water 初始化次数
func (p *totalPractice) getUserPracticeCompletes(c *db.UserPractice) int32 {
	var completeTime int32
	list := db.TbUserPractice.GetAllList(c)
	if len(list) > 0 {
		data := make(map[int]*db.UserPractice)
		sortSlice := make([]int, 0)
		for i, v := range list {
			uploadTime := int(v.ReportTime)
			data[uploadTime] = &list[i]
			sortSlice = append(sortSlice, uploadTime)
		}
		sort.Ints(sortSlice)
		for i, v := range sortSlice {
			data[v].CompleteTimes = int32(i + 1)
			err := data[v].Update()
			if err != nil {
				logger.Error(err)
			}
		}
		completeTime = int32(len(list))
	}
	return completeTime
}

// CleanUserPractice 清洗total表的数据
func (p *totalPractice) CleanUserPractice(uid int64) error {
	err := (&db.Total{
		PlayTime: 0,
		Calorie:  0,
	}).UpdateByCond(uid)
	if err != nil {
		return err
	}
	totalPractices := db.TbPracticeTotal.GetListByUID(uid)
	for _, v := range totalPractices {
		p.CleanUserPracticeData(&db.UserPractice{
			UID:             v.UID,
			ProgramID:       v.ProgramID,
			SessionID:       v.SessionID,
			SessionIndex:    v.SessionIndex,
			UserActionTable: v.UserActionTable,
		})
	}
	return nil
}
