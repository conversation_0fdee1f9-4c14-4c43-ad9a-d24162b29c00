package practice

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/protogen/account-go"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/course"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/partner"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/third"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/yogao2school"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/yogao2school/eval"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"golang.org/x/net/context"
)

type teamRanking struct{}

// TeamRanking 班级排行榜
var TeamRanking teamRanking

// SetTeamPlayTimeRankingPayload 设置班级排行榜数据结构
type SetTeamPlayTimeRankingPayload struct {
	TeamID           int64 `json:"team_id"`
	UID              int64 `json:"uid"`
	SessionStartTime int64 `json:"session_start_time"`
	SessionEndTime   int64 `json:"session_end_time"`
	ProgramID        int64 `json:"program_id"`
	SessionID        int64 `json:"session_id"`
	SaveDB           bool  `json:"save_db"`
}

type userPracticeInfo struct {
	SessionNum    int64  `json:"practiceSessionNum"` // 完成课时数
	TotalDuration int64  `json:"practiceTimes"`      // 练习总时长
	TodayDuration int64  `json:"practiceTodayTimes"` // 今日练习时长
	Avatar        string `json:"Thumbnail"`
	Nickname      string `json:"nickName"`
	UID           int64  `json:"uid"`
}

type yogaO2SessionInfo struct {
	SessionID  int64 `json:"session_id"`
	CategoryID int   `json:"category_id"` // 分类id
	SourceID   int64 `json:"source_id"`   // 资源type
}

// RefreshTeamPlayTimeRank 刷新训练营排行版榜
func (p *teamRanking) RefreshTeamPlayTimeRank(req *SetTeamPlayTimeRankingPayload, list []partner.O2TeamMember) {
	ctx := context.Background()
	key := fmt.Sprintf("yoga:o2:school:playTime:ranking:%d", req.TeamID)
	expireAt := req.SessionEndTime + 86400*7
	rd := cache.GetYogaRedis()
	// 删除之前的排行榜
	if err := p.deleteRanking(key, len(list)); err != nil {
		return
	}
	// 重新生成排行榜
	for _, item := range list {
		if item.TeamID != req.TeamID {
			continue
		}
		strUID := strconv.Itoa(int(item.UID))
		// 2019-11-15 处理更换计划后的排行榜
		userEvalRes := p.getUserEvalRes(item.UID, int32(req.SessionID))
		tempID := req.ProgramID
		if userEvalRes.SourceID != 0 {
			tempID = userEvalRes.SourceID
		}
		// 获取用户练习数据
		practiceInfo := p.getUserPracticeInfo(item.UID, req.SessionStartTime, req.SessionEndTime, tempID)
		z := &redis.Z{
			Score:  float64(practiceInfo.SessionNum),
			Member: strUID,
		}
		if err := rd.ZAdd(ctx, key, z).Err(); err != nil {
			logger.Warn(err)
			continue
		}

		// 获取用户信息，并设置存储数据
		userInfo, err := p.getUserInfo(ctx, item.UID)
		if err != nil {
			continue
		}
		practiceInfo.UID = item.UID
		practiceInfo.Avatar = userInfo.Avatar
		practiceInfo.Nickname = userInfo.Name

		// 设置排行榜
		if err := p.setRanking(item.TeamID, item.UID, practiceInfo, expireAt); err != nil {
			continue
		}
		// 保存至数据库
		if req.SaveDB {
			p.saveDB(
				item.UID, item.TeamID,
				practiceInfo.TodayDuration, req.SessionID, practiceInfo.SessionNum, practiceInfo.Nickname,
			)
		}
	}

	// 记录排行榜更新时间
	curTimestamp := time.Now().Unix()
	updateTimeKey := fmt.Sprintf("yoga:o2:school:playTime:ranking:Updata:%d", req.TeamID)
	exp := time.Duration(expireAt-curTimestamp) * time.Second
	if err := rd.SetEX(ctx, updateTimeKey, strconv.Itoa(int(curTimestamp)), exp).Err(); err != nil {
		logger.Warn(err)
	}
	// 设置过期时间
	_ = p.setTTL(key, expireAt)
}

// deleteRanking 删除排行榜
func (p *teamRanking) deleteRanking(key string, cnt int) error {
	rd := cache.GetYogaRedis()
	ctx := context.Background()

	rankingMemberCnt, err := rd.ZCard(ctx, key).Result()
	if err != nil {
		return err
	}
	if rankingMemberCnt > int64(cnt) {
		if err := rd.Del(ctx, key).Err(); err != nil {
			return err
		}
		time.Sleep(100 * time.Millisecond)
	}
	return nil
}

// setRanking 设置排行榜
func (p *teamRanking) setRanking(teamID, uid int64, practiceInfo userPracticeInfo, t int64) error {
	rd := cache.GetYogaRedis()
	ctx := context.Background()

	dataKey := fmt.Sprintf("yoga:o2:school:user:playTime:data:%d:%d", teamID, uid)
	data, err := json.Marshal(practiceInfo)
	if err != nil {
		logger.Error(err)
		return err
	}
	expiration := time.Now().Unix() - t
	if err = rd.Set(ctx, dataKey, string(data), time.Duration(expiration)).Err(); err != nil {
		logger.Error(err)
		return err
	}
	return nil
}

// getUserInfo 获取用户信息，rpc调用
func (p *teamRanking) getUserInfo(ctx context.Context, uid int64) (*account.UserInfoResponse, error) {
	resp, err := grpc.GetAccountClient().GetUserInfo(ctx, &account.UIDRequest{
		UID: uid,
		Attrs: []account.UserInfoAttr{
			account.UserInfoAttr_Avatar, account.UserInfoAttr_Default,
		},
	})
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	return resp, nil
}

// setTTL 设置过期时间
func (p *teamRanking) setTTL(key string, t int64) error {
	rd := cache.GetYogaRedis()
	ctx := context.Background()

	ttl, err := rd.TTL(ctx, key).Result()
	if err != nil {
		logger.Error(err)
		return err
	}
	if ttl < 0 {
		if _, err := rd.ExpireAt(ctx, key, time.Unix(t, 0)).Result(); err != nil {
			logger.Warn(err)
		}
	}
	return nil
}

// saveDb 将排行榜数据写入数据库
func (p *teamRanking) saveDB(uid, teamID, totalDuration, sessionID, sessionNum int64, nickname string) {
	var wechatNickname, mobile string
	wechatInfo := third.TbAccountInfoTpWechatMain.GetMainInfo(uid)
	if wechatInfo != nil {
		wechatNickname = wechatInfo.Nickname
	}
	logger.Debugf("uid: %d; wechatName: %s; wechatInfo: %+v", uid, wechatNickname, wechatInfo)
	memberInfo := yogao2school.TbSessionMember.GetRowByUIDAndSessionID(uid, sessionID)
	if memberInfo != nil {
		mobile = memberInfo.MemberMobile
	}

	bean := &yogao2school.TeamRanking{
		TeamID:         teamID,
		UID:            uid,
		Nickname:       nickname,
		WechatNickname: wechatNickname,
		Mobile:         mobile,
		TotalDuration:  totalDuration,
		SessionNum:     sessionNum,
	}
	if err := bean.Save(); err != nil {
		logger.Error(err)
	}
}

// getUserPracticeInfo 获取用户练习数据信息
func (p *teamRanking) getUserPracticeInfo(uid, startTime, endTime, programID int64) userPracticeInfo {
	res := userPracticeInfo{}
	list := practice.TbUserPractice.GetListByRangeTime(&practice.UserPractice{
		UID:             uid,
		UserActionTable: library.FinishSessionInProgram,
		ProgramID:       programID,
	}, startTime, endTime)

	res.SessionNum += p.countSessionNum(list, uid, programID, startTime, endTime)

	userPlayTimeList := practice.TbUserPractice.GetListByRangeTime(&practice.UserPractice{
		UID:       uid,
		ProgramID: programID,
	}, startTime, endTime)
	for _, v := range userPlayTimeList {
		res.TotalDuration += v.PlayTime
	}

	todayStartTimeStr := time.Now().Format("2006-01-02")
	todayStartTime, _ := time.ParseInLocation("2006-01-02", todayStartTimeStr, time.Local)
	userPlayTimeTodayList := practice.TbUserPractice.GetListByRangeTime(&practice.UserPractice{
		UID:       uid,
		ProgramID: programID,
	}, todayStartTime.Unix(), time.Now().Unix())

	for _, v := range userPlayTimeTodayList {
		res.TodayDuration += v.PlayTime
	}

	// 将练习时长数据转换为分钟
	res.TotalDuration /= 60
	res.TodayDuration /= 60

	return res
}

// countSessionNum 计算课程总数
func (p *teamRanking) countSessionNum(list []practice.UserPractice, uid, programID, startTime, endTime int64) int64 {
	var i int64
	tmp := make(map[int32]map[int32]bool)
	for _, v := range list {
		if _, ok := tmp[v.SessionIndex]; !ok {
			tmp[v.SessionIndex] = make(map[int32]bool)
		}
		if _, ok := tmp[v.SessionIndex][v.SubSessionIndex]; ok {
			continue
		}
		tmp[v.SessionIndex][v.SubSessionIndex] = true
		i++
	}

	sessionCnt := course.TbProgramExtend.GetSessionCount(programID)
	if i >= sessionCnt {
		return i
	}
	data := practice.TbUserPractice.GetListByRangeTime(&practice.UserPractice{
		UID:             uid,
		UserActionTable: library.FinishO2Session,
		ProgramID:       programID,
	}, startTime, endTime)
	if len(data) > 0 {
		i++
	}
	return i
}

// 获取用户的评测结果
func (p *teamRanking) getUserEvalRes(uid int64, sessionID int32) *yogaO2SessionInfo {
	res := &yogaO2SessionInfo{}
	sessionInfo := yogao2school.TbSession.GetSessionInfoByID(sessionID)
	if sessionInfo == nil {
		return res
	}
	// 目前只有减脂塑形可以做评测
	if sessionInfo.CategoryID == 8 {
		userMemberEval := eval.TbYogaO2SessionMemberEval.GetByUIDCategory(uid,
			int64(sessionInfo.CategoryID), library.DataStatusEnum.Valid.ToInt64())
		if userMemberEval == nil {
			return res
		}
		res.SessionID = sessionInfo.ID
		res.CategoryID = sessionInfo.CategoryID
		res.SourceID = userMemberEval.SourceID
	}
	return res
}
