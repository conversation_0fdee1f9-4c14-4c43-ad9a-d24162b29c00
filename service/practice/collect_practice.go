package practice

import (
	"math"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/goroutine"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/playtime"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/sessionplay"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/course"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/challenge"
)

type collectPractice struct{}

var Collect collectPractice

// updateCalorieData 更新用户练习卡路里
func (c *collectPractice) updateCalorieData(data *db.UserPractice) {
	// 更新st_play_time_day
	dateIndex := time.Unix(data.ReportTime, 0).Format("20060102")
	sd := playtime.TbDay.GetItem(data.UID, dateIndex)
	if sd != nil && sd.ID > 0 {
		sd.Calorie += int32(data.Calorie)
		if err := sd.Update(); err != nil {
			logger.Error(err)
		}
	} else {
		sdItem := &playtime.Day{
			UID:       data.UID,
			Calorie:   int32(data.Calorie),
			DateIndex: dateIndex,
		}
		if err := sdItem.Save(); err != nil {
			logger.Error(err)
		}
	}

	// 更新st_play_time_collect
	sc := playtime.TbCollect.GetItem(data.UID)
	if sc != nil && sc.ID > 0 {
		sc.Calorie += int32(data.Calorie)
		if err := sc.Update(); err != nil {
			logger.Error(err)
		}
	} else {
		scItem := &playtime.Collect{
			UID:     data.UID,
			Calorie: int32(data.Calorie),
		}
		if err := scItem.Save(); err != nil {
			logger.Error(err)
		}
	}
}

// 2020-02-11 water 清理数据
func (c *collectPractice) updateCleanCalorieData(data *db.UserPractice) {
	// 更新st_play_time_day
	dateIndex := time.Unix(data.ReportTime, 0).Format("20060102")
	sd := playtime.TbDay.GetItem(data.UID, dateIndex)
	if sd != nil && sd.ID > 0 {
		sd.Calorie -= int32(data.Calorie)
		if err := sd.UpdateMustColsCalorie(); err != nil {
			logger.Error(err)
		}
	}

	// 更新st_play_time_collect
	sc := playtime.TbCollect.GetItem(data.UID)
	if sc != nil && sc.ID > 0 {
		sc.Calorie -= int32(data.Calorie)
		if err := sc.UpdateMustColsCalorie(); err != nil {
			logger.Error(err)
		}
	}
}

// getCalorie 根据练习时长计算卡路里
func (c *collectPractice) getCalorie(sessionID, playTime int64) int64 {
	var calorie int64

	session := course.TbSession.GetSessionByID(sessionID)
	if session != nil {
		return CalcCalorie(int64(session.CalorieRatio), playTime)
	}
	return calorie
}

// getProgramIDAndSessionID 获取sessionID与programID
func (c *collectPractice) getProgramIDAndSessionID(data *db.UserPractice) (programID, sessionID int64) {
	programID = data.ProgramID
	sessionID = data.SessionID

	if data.UserActionTable == library.FinishPlan || data.UserActionTable == library.FinishO2Session {
		sessionList := course.TbProgramExtend.GetProgramSessionList(data.ProgramID)
		for _, v := range sessionList {
			sessionID = v.SessionID
		}
	}
	if data.UserActionTable == library.LeaderUserScheduleUnit || data.UserActionTable == library.PlayQuitUserSchedule {
		if item := user.TbScheduleUnit.GetItem(data.SessionID); item != nil && item.ID > 0 {
			sessionID = item.SessionID
			programID = item.UserScheduleID
		}
	}
	return
}

// calculatePlayTime 计算playtime数值
func (c *collectPractice) calculatePlayTime(uid, programID, sessionID, reportTime int64) int64 {
	var playTime int64
	data := sessionplay.TbSessionPlay.GetList(uid, programID, sessionID, reportTime)
	for _, v := range data {
		playTime += v.PlayTime
	}
	return playTime
}

// ChallengeTaskNotify 通知更新挑战赛时长统计(更新时长和练习课程打卡)
func (c *collectPractice) ChallengeTaskNotify(uid, practiceCurrentTime int64) {
	var taskInfo challenge.TaskInfo
	taskInfo.UID = uid
	taskInfo.Date = time.Unix(practiceCurrentTime, 0).Format("2006-01-02")
	taskList := []library.ChallengeTaskType{
		library.ChallengeTaskTypeEnum.PlayTimeTask,
		library.ChallengeTaskTypeEnum.SignTask,
		library.ChallengeTaskTypeEnum.NewTaskProcess,
	}
	taskInfo.ReportTime = practiceCurrentTime
	for _, taskType := range taskList {
		taskInfo.TaskType = taskType
		challenge.ServiceChallenge.ProcessTask(taskInfo)
	}
}

// UpdateUserPractice 2020-01-18 处理方法剥离定时任务 主要用户716版本的userActionLog 直接上报时长使用
func (c *collectPractice) UpdateUserPractice(item *db.UserPractice) {
	if item.ReportTime > time.Now().Unix() || item.UserActionTable == library.FinishPlan {
		return
	}
	programID, sessionID := c.getProgramIDAndSessionID(item)
	// 2020-02-12 water 汇总习练历程练习时长和卡路里 这块逻辑主要是给CollectPracticeData使用的
	if item.PlayTime < 1 {
		item.PlayTime = c.calculatePlayTime(item.UID, programID, sessionID, item.ReportTime)
	}

	if !library.IsLiveUsrActionTable(item.UserActionTable) {
		item.Calorie = c.getCalorie(sessionID, item.PlayTime)
	}

	if err := item.Update(); err != nil {
		logger.Error(err)
	}

	// 更新用户练习卡路里（完成计划和计划最后一节课都会上报，所以完成计划不更新用户卡路里）
	c.updateCalorieData(item)
	PlayTime.Stat(&sessionplay.Detail{
		UID:        item.UID,
		ReportTime: item.ReportTime,
		PlayTime:   item.PlayTime,
	})

	// 2020-01-06 water  更新聚合课程的计划的总时长
	TotalPractice.CleanUserPracticeData(item)
	// 通知更新挑战赛时长统计
	goroutine.GoSafely(func() {
		c.ChallengeTaskNotify(item.UID, item.ReportTime)
	})
}

// CleanUserPractice 2020-02-11 water 清理安卓错误数据上报(2020-2-12已经不使用 可以作为清理时长使用)
func (c *collectPractice) CleanUserPractice(item *db.UserPractice) {
	if item.UserActionTable != library.FinishPlan && c.CleanPlayTime(item) {
		// 清理
		c.updateCleanCalorieData(item)
		PlayTime.StatClean(&sessionplay.Detail{
			UID:        item.UID,
			ReportTime: item.ReportTime,
			PlayTime:   item.PlayTime,
		})
		TotalPractice.CleanUpdateUserPracticeData(item)

		// 添加
		item.PlayTime = c.calculatePlayTime(item.UID, item.ProgramID, item.SessionID, item.ReportTime)
		item.Calorie = c.getCalorie(item.SessionID, item.PlayTime)
		if err := item.Update(); err != nil {
			logger.Error(err)
		}

		c.updateCalorieData(item)
		PlayTime.Stat(&sessionplay.Detail{
			UID:        item.UID,
			ReportTime: item.ReportTime,
			PlayTime:   item.PlayTime,
		})
		// 2020-01-06 water  更新聚合课程的计划的总时长
		TotalPractice.CleanUserPracticeData(item)
	}
}

// st_session_play_* 数据清理
func (c *collectPractice) CleanPlayTime(item *db.UserPractice) bool {
	var playTime int64
	standardTime := int64(7200)
	data := sessionplay.TbSessionPlay.GetList(item.UID, item.ProgramID, item.SessionID, item.ReportTime)
	for _, v := range data {
		playTime += v.PlayTime
	}
	num := len(data)
	if num == 0 {
		return false
	}
	if num == 1 {
		bean := data[0]
		bean.Calorie = int32(c.getCalorie(item.SessionID, standardTime))
		bean.PlayTime = standardTime
		// 保存数据
		if err := bean.Update(); err != nil {
			logger.Error(err)
			return false
		}
	} else {
		averagePlayTime := int64(math.Ceil(float64(standardTime) / float64(num)))
		calorie := int32(c.getCalorie(item.SessionID, averagePlayTime))
		for _, val := range data {
			val.Calorie = calorie
			val.PlayTime = averagePlayTime
			if err := val.Update(); err != nil {
				logger.Error(err)
				return false
			}
		}
	}
	return true
}

// UpdateLiveUserPractice 2020-01-18 处理方法剥离定时任务 主要用户716版本的userActionLog 直接上报时长使用
func (c *collectPractice) UpdateLiveUserPractice(item *db.UserPractice) {
	if item.ReportTime > time.Now().Unix() {
		return
	}
	programID, sessionID := c.getProgramIDAndSessionID(item)
	// 2020-02-12 water 汇总习练历程练习时长和卡路里 这块逻辑主要是给CollectPracticeData使用的
	if item.PlayTime < 1 {
		item.PlayTime = c.calculatePlayTime(item.UID, programID, sessionID, item.ReportTime)
	}
	PlayTime.Stat(&sessionplay.Detail{
		UID:        item.UID,
		ReportTime: item.ReportTime,
		PlayTime:   item.PlayTime,
	})
	// 2020-01-06 water  更新聚合课程的计划的总时长
	TotalPractice.CleanUserPracticeData(item)
	// 通知更新挑战赛时长统计
	goroutine.GoSafely(func() {
		c.ChallengeTaskNotify(item.UID, item.ReportTime)
	})
}
