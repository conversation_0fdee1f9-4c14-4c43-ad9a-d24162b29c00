package practice

import (
	"time"

	dbMission "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/mission"
	dbUser "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
)

// 任务目标
const (
	MinutesTarget = iota + 1 // 分钟
	DayTarget                // 天
)

// 任务状态
const (
	Finish     = iota + 1 // 完成
	Unfinished            // 未完成
)

type mission struct{}

var Mission mission

//
// CompleteUserMissions
//  @Description: 完成用户任务
//  @receiver m
//  @param uid 用户uid
//  @param data 完成数据
//  @param missionTarget 任务目标
//  @return error
//
func (m mission) CompleteUserMissions(uid, data int64, missionTarget int) error {
	// 判断用户是否为新用户
	user := dbUser.TbAccountInfo.GetByUID(uid)
	now := time.Now()
	if user == nil || user.CreateTime+7*86400 < int(now.Unix()) {
		return nil
	}

	// 查询用户所有未完成任务
	userMissionStatuses, err := dbMission.TbUserMissionStatus.List(&dbMission.UserMissionStatusListRequest{
		UID:    uid,
		Status: Unfinished,
	})
	if err != nil {
		return err
	}
	if len(userMissionStatuses) == 0 {
		return nil
	}

	// 整理所有任务id
	missionIds := make([]uint32, 0)
	for i := range userMissionStatuses {
		missionIds = append(missionIds, userMissionStatuses[i].MissionID)
	}

	// 查询所有任务详情
	missions, err := dbMission.TbMission.List(&dbMission.ListRequest{
		Ids:  missionIds,
		Type: missionTarget,
	})
	if err != nil {
		return err
	}

	if missionTarget == MinutesTarget {
		data /= 60
	}

	// 整理所有已完成的任务id
	FinishMissionIds := make([]uint32, 0)
	for i := range missions {
		if int(data) >= missions[i].Target {
			FinishMissionIds = append(FinishMissionIds, missions[i].ID)
		}
	}

	// 修改用户的任务状态
	if len(FinishMissionIds) == 0 {
		return nil
	}
	_, err = dbMission.TbUserMissionStatus.Update(uid, FinishMissionIds, Finish)
	if err != nil {
		return err
	}

	return nil
}
