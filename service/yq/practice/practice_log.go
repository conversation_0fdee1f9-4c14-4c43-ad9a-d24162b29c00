package practice

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"gitlab.dailyyoga.com.cn/protogen/stretch-go/stretch"
	"gitlab.dailyyoga.com.cn/server/go-artifact/goroutine"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/yq/course"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/yq/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/yq/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/yq/usersetting"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
)

type DayPayload struct {
	UID        int64 `json:"uid"`
	CreateTime int64 `json:"create_time"`
}

type LoggingPracticePayload struct {
	SID               string `json:"sid"`
	UID               int64  `json:"uid"`
	CourseID          int64  `json:"course_id"`
	ActionNum         int    `json:"action_num"`
	PracticeStartTime int64  `json:"practice_start_time"`
	PracticeType      int    `json:"practice_type"`
	PlayTime          int    `json:"play_time"`
}

type CheckData struct {
	PracticeCurrentTime int64
}

type practiceLog struct{}

var Log practiceLog

func (pl *practiceLog) StatPracticeLog(params []byte) error {
	var bean LoggingPracticePayload
	if err := json.Unmarshal(params, &bean); err != nil {
		return err
	}
	if bean.UID < 1 {
		return errors.New("invalid params: uid")
	}
	logger.Info("YQ练习上报", string(params))
	// 更新用户练习数据
	SaveUserPractice(&bean)
	return nil
}

type FeelReportParam struct {
	UID               int64 `json:"uid"`
	CourseID          int64 `json:"course_id"`
	ProgramID         int64 `json:"ob_program_id"`
	PracticeStartTime int64 `json:"practice_start_time"`
	PracticeFeel      int64 `json:"practice_feel"`
}

func (pl *practiceLog) StatPracticeFeel(params []byte) error {
	var bean FeelReportParam
	if err := json.Unmarshal(params, &bean); err != nil {
		return err
	}
	if bean.UID < 1 {
		return errors.New("invalid params: uid")
	}
	// 更新用户练习数据
	log := db.TbPracticeLog.GetItemByCond(&db.SearchQuery{
		UID: bean.UID, CourseID: bean.CourseID, ProgramID: bean.ProgramID,
		PracticeStartTime: bean.PracticeStartTime,
	})

	if log == nil {
		logger.Errorf("FT练习感受上报:无练习记录:%+v", params)
		return nil
	}
	log.PracticeFeel = int32(bean.PracticeFeel)
	if err := log.Update(); err != nil {
		logger.Error("FT练习感受上报:更新失败:", err)
	}
	return nil
}

const (
	PracticeTypeStart = iota + 1
	PracticeTypeExit
	PracticeTypeFinish
)

const SceneTypePlanProgram = 4

const (
	Yes = 1
	No  = 2
)

func SaveUserPractice(r *LoggingPracticePayload) {
	// 如果上报时间大于当天时间或者小于20160101则忽略处理
	if r.PracticeStartTime > time.Now().Unix() || r.PracticeStartTime < 1451577600 {
		return
	}
	// 如果记录不存在，无脑添加，不区分场景（兼容没有开训上报的情况）
	log := db.TbPracticeLog.GetItemByCond(&db.SearchQuery{
		UID: r.UID, CourseID: r.CourseID,
		PracticeStartTime: r.PracticeStartTime,
	})
	if log == nil {
		// 写入练习记录
		data := db.Log{
			UID:               r.UID,
			CourseID:          r.CourseID,
			ActionNum:         r.ActionNum,
			PracticeStartTime: r.PracticeStartTime,
			PracticeType:      r.PracticeType,
			PlayTime:          r.PlayTime,
		}
		if err := data.Save(); err != nil {
			logger.Warn("YQ练习上报错误", err)
			return
		}
	} else {
		if log.PlayTime > 0 {
			logger.Warn("YQ重复上报userActionLog", r)
			return
		}
		log.PlayTime = r.PlayTime
		log.ActionNum = r.ActionNum
		log.PracticeType = r.PracticeType
		if err := log.Update(); err != nil {
			return
		}
	}
	incrPracticeCount(r.UID, r.PracticeType)
	// 练习后刷新挑战赛状态
	goroutine.GoSafely(func() { refreshChallengeState(r.UID, r.PracticeStartTime) })
	if r.PracticeType == PracticeTypeStart {
		return
	}
	// 练习后刷新
	setUserPlayDay(r) // 修改st_user_play_day
	// 更新汇总数据
	setUserCollect(r) // 修改st_user_collect
	// 练习后的处理
	err := UpdateAfterPractice(r)
	if err != nil {
		logger.Error(err)
	}
}

func refreshChallengeState(uid, practiceStartTime int64) {
	stlient := grpc.GetStretchClient()
	res, err := stlient.RefreshChallengeState(context.Background(), &stretch.RefreshChallengeStateReq{
		UID:               uid,
		PracticeStartTime: practiceStartTime,
	})
	if err != nil {
		logger.Error(err)
		return
	}
	if res.GetErrorCode() > 0 {
		logger.Warn("刷新挑战赛状态失败", res.GetErrorCode(), res.GetMsg())
	}
}

func UpdateAfterPractice(r *LoggingPracticePayload) error {
	// 更新用户练习天数
	dateIndex := time.Unix(r.PracticeStartTime, 0).Format("********")
	dayItem := db.TbPracticeDay.GetItem(r.UID, dateIndex)
	if dayItem == nil || dayItem.ID < 1 {
		dayItem = &db.Day{
			UID:       r.UID,
			DateIndex: dateIndex,
		}
		if err := dayItem.Save(); err != nil {
			return err
		}
	}
	dayList := db.TbPracticeDay.GetList(r.UID)
	totalDay := len(dayList)
	// 更新用户练习时长、卡路里、练习天数
	userDetail := user.TbAccount.GetUserByID(r.UID)
	if userDetail != nil && userDetail.ID > 0 {
		userDetail.PracticeCount++
		userDetail.ActionNum += r.ActionNum
		userDetail.Minutes += (r.PlayTime / 60)
		userDetail.PracticeDays = totalDay
		if err := userDetail.Update(); err != nil {
			return err
		}
	}
	courseItem := course.TbCourseLibrary.GetItem(r.CourseID)
	if courseItem != nil {
		SrvLastPractice.UpdateUserLastPractice(r)
	}
	return nil
}

func setUserPlayDay(r *LoggingPracticePayload) {
	// 更新st_play_time_day
	dateIndex := time.Unix(r.PracticeStartTime, 0).Format("********")
	sd := db.TbPlayDay.GetItem(r.UID, dateIndex)
	if sd != nil && sd.ID > 0 {
		sd.PlayTime += int64(r.PlayTime)
		sd.ActionNum += r.ActionNum
		sd.PracticeCount++
		if err := sd.Update(); err != nil {
			logger.Error(err)
		}
	} else {
		sdItem := &db.PlayDay{
			UID:           r.UID,
			PlayTime:      int64(r.PlayTime),
			ActionNum:     r.ActionNum,
			PracticeCount: 1,
			DateIndex:     dateIndex,
		}
		if err := sdItem.Save(); err != nil {
			logger.Error(err)
		}
	}
}

func setUserCollect(r *LoggingPracticePayload) {
	userCollect, err := db.TbUserCollect.FindOrCreate(r.UID)
	if err != nil {
		logger.Error(err)
	}
	if userCollect == nil { // userCollect没有被真正创建
		logger.Error("st_user_collect 未创建，", r.UID)
		return
	}
	userCollect.TotalActionNum += int64(r.ActionNum)
	userCollect.TotalPlayTime += int64(r.PlayTime)
	dayKeyList := db.TbPlayDay.GetList(r.UID)
	userCollect.TotalPracticeDay = int64(len(dayKeyList))
	userCollect.LastContinuePracticeDay = LastContinuePracticeDay(r.UID, dayKeyList)
	err = userCollect.Update()
	if err != nil {
		logger.Error("更新userCollect失败", err)
	}
}

func LastContinuePracticeDay(uid int64, dayKeyList []db.PlayDay) int64 {
	var keepDay int64 = 1
	var lastDateIndex string
	if len(dayKeyList) < 1 {
		return keepDay
	}
	for _, v := range dayKeyList {
		if lastDateIndex == "" {
			lastDateIndex = v.DateIndex
		} else {
			lastDateIndexUnix := convertTimeUnix(lastDateIndex)
			curDateIndexUnix := convertTimeUnix(v.DateIndex)
			if curDateIndexUnix-lastDateIndexUnix == 86400 {
				keepDay++
			} else {
				keepDay = 1
			}
			lastDateIndex = v.DateIndex
		}
	}
	return keepDay
}

func convertTimeUnix(dateIndex string) int64 {
	tUnix, _ := time.Parse("********", dateIndex)
	return tUnix.Unix()
}

type UPracticeStat struct {
	PracticeStart  int32 `json:"practice_start"`
	PracticeExit   int32 `json:"practice_exit"`
	PracticeFinish int32 `json:"practice_finish"`
}

const (
	UserPracticeStat = "user_practice_stat"
)

func incrPracticeCount(uid int64, practiceType int) {
	if practiceType == 0 {
		return
	}
	userPracticeStat, err := usersetting.TbUserSetting.GetItem(uid, UserPracticeStat)
	if err != nil {
		logger.Errorf("用户userSetting查询数据失败, uid: %d, key: %s", uid, UserPracticeStat)
		return
	}

	var practiceCount = &UPracticeStat{}
	if userPracticeStat == nil {
		practiceCount = &UPracticeStat{PracticeStart: 0, PracticeExit: 0, PracticeFinish: 0}
	} else {
		err := json.Unmarshal([]byte(userPracticeStat.Value), practiceCount)
		if err != nil {
			logger.Errorf("解析用户练习统计信息失败, err: %s, uid: %d", err, uid)
		}
	}

	switch practiceType {
	case PracticeTypeStart:
		practiceCount.PracticeStart++
	case PracticeTypeExit:
		practiceCount.PracticeExit++
	case PracticeTypeFinish:
		practiceCount.PracticeFinish++
	}
	practiceCountJSON, err := json.Marshal(practiceCount)
	if err != nil {
		logger.Errorf("用户练习统计jsonEncode失败, err: %s, uid: %d", err, uid)
	}

	if userPracticeStat == nil {
		// 处理叠加practice_type
		userPracticeStat := &usersetting.UserSetting{
			UID:      uid,
			Key:      UserPracticeStat,
			Value:    string(practiceCountJSON),
			IsDelete: 2,
		}
		if err := userPracticeStat.Save(); err != nil {
			logger.Errorf("更新用户练习统计失败, err: %s, uid:%d", err, uid)
		}
	} else {
		userPracticeStat.Value = string(practiceCountJSON)
		if err := userPracticeStat.Update(); err != nil {
			logger.Errorf("更新用户练习统计失败, err: %s, uid:%d", err, uid)
		}
	}
}
