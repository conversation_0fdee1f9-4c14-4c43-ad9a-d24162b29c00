// 苹果 ads 相关接口,由于苹果服务器不是特别稳定
// 重复数据必须自行上缓存做到复用
package apple

import (
	"context"
	"crypto/ecdsa"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

var AppServerInstance = new(Server)

type Server struct{}

type AdsToken struct {
	Value string
}

// GetToken 获取 token
func (s *Server) GetToken() *AdsToken {
	conf := config.Get().AppleAdsConfig
	const key = "apple_ads_info_token"
	rd := cache.GetYogaRedis()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		logger.Error(cmd.Err().Error())
		return nil
	}
	if cmd.Err() == nil && cmd.Val() != "" {
		return &AdsToken{Value: cmd.Val()}
	}
	secret := s.getSecret()
	if secret == "" {
		return nil
	}
	// 根据 secret 请求 token
	f := func(secret string) *AdsToken {
		address := "https://appleid.apple.com/auth/oauth2/token"
		v := url.Values{}
		v.Set("grant_type", "client_credentials")
		v.Set("client_id", conf.ClientID)
		v.Set("client_secret", secret)
		v.Set("scope", "searchadsorg")
		address = address + "?" + v.Encode()
		header := http.Header{}
		header.Set("Content-Type", "application/x-www-form-urlencoded")
		body, err := requests.HTTPRequest(address, "POST", header, nil)
		if err != nil {
			logger.Error(err.Error())
			return nil
		}
		token := new(struct {
			AccessToken string `json:"access_token"`
			TokenType   string `json:"token_type"`
			ExpiresIn   int    `json:"expires_in"`
		})
		if err := json.Unmarshal(body, token); err != nil {
			logger.Error(err.Error())
			return nil
		}
		if token.AccessToken == "" {
			logger.Error("苹果广告数据上报神策从苹果获取到的 Value 为空,响应体为" + string(body))
			return nil
		}
		return &AdsToken{Value: token.AccessToken}
	}
	// token 不为空进行缓存
	if token := f(secret); token != nil {
		err := rd.Set(context.Background(), key, token.Value, time.Minute*50).Err()
		if err != nil {
			logger.Error("苹果广告数据上报神策存入 Value 失败 ", err.Error())
			return nil
		}
		return token
	}
	return nil
}

// getSecret 根据苹果官网签发的私钥文件生成 secret 用于验签,生成的 secret 最长有效期有 180 天
func (s *Server) getSecret() string {
	const key = "apple_ads_info_secret"
	// 缓存中有则直接拿
	rd := cache.GetYogaRedis()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		logger.Error(cmd.Err().Error())
		return ""
	}
	if cmd.Err() == nil {
		return cmd.Val()
	}
	// 没有则计算
	conf := config.Get().AppleAdsConfig
	const privateKey = `**********************************************************************************************************************************************************************************************************************************`
	ecdsaKey, err := s.authKeyFromBytes([]byte(privateKey))
	if err != nil {
		logger.Error(err.Error())
		return ""
	}
	now := time.Now()
	jwtInstance := jwt.NewWithClaims(jwt.SigningMethodES256, jwt.MapClaims{
		"sub": conf.ClientID,
		"aud": conf.Audience,
		"iat": now.Unix(),
		"exp": now.Add(time.Hour * 24 * 180).Unix(),
		"iss": conf.TeamID,
	})
	jwtInstance.Header["alg"] = conf.Alg
	jwtInstance.Header["kid"] = conf.KeyID
	secret, err := jwtInstance.SignedString(ecdsaKey)
	if err != nil {
		logger.Errorf("苹果广告数据上报神策生成秘钥失败 %s", err)
		return ""
	}
	// 缓存 180 天
	if err = rd.Set(context.Background(), key, secret, time.Hour*24*180).Err(); err != nil {
		logger.Error(err.Error())
		return ""
	}
	return secret
}

func (s *Server) authKeyFromBytes(bytes []byte) (*ecdsa.PrivateKey, error) {
	block, _ := pem.Decode(bytes)
	if block == nil {
		return nil, errors.New("value: AuthKey must be a valid .p8 PEM file")
	}
	key, err := x509.ParseECPrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	return key, nil
}

type ACLResponse struct {
	Data []struct {
		OrgName      string   `json:"orgName"`
		OrgID        int      `json:"orgId"`
		Currency     string   `json:"currency"`
		TimeZone     string   `json:"timeZone"`
		PaymentModel string   `json:"paymentModel"`
		RoleNames    []string `json:"roleNames"`
		ParentOrgID  int      `json:"parentOrgId"`
	} `json:"data"`
	Pagination interface{} `json:"pagination"`
	Error      interface{} `json:"error"`
}

// GetAcl 请求用户权限范围
func (s *Server) GetACL(token *AdsToken) (*ACLResponse, error) {
	const key = "apple_ads_info_acl"
	resp := new(ACLResponse)
	rd := cache.GetYogaRedis()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		return nil, cmd.Err()
	}
	if cmd.Err() == nil && cmd.Val() != "" {
		if err := json.Unmarshal([]byte(cmd.Val()), resp); err != nil {
			return nil, err
		}
		s.formatACLOrgID(resp)
		return resp, nil
	}
	address := "https://api.searchads.apple.com/api/v5/acls"
	header := http.Header{}
	header.Set("Authorization", fmt.Sprintf("Bearer %s", token.Value))
	body, err := requests.HTTPRequest(address, "GET", header, nil)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(body, resp); err != nil {
		return nil, fmt.Errorf("获取请求apple ads 用户权限失败 %s 响应内容:%s", err, string(body))
	}
	if resp.Error != nil {
		return nil, fmt.Errorf("%s", resp.Error)
	}
	if len(resp.Data) == 0 {
		return nil, fmt.Errorf("apple acl 没有权限 %s", string(body))
	}
	if err := rd.Set(context.Background(), key, string(body), time.Minute*100).Err(); err != nil {
		return nil, err
	}
	s.formatACLOrgID(resp)
	return resp, nil
}

// formatACLOrgID 处理ACL数据
func (s *Server) formatACLOrgID(resp *ACLResponse) {
	conf := config.Get().AppleAdsConfig
	for _, v := range resp.Data {
		if strings.Contains(v.OrgName, conf.OrgName) {
			resp.Data[0] = v
			break
		}
	}
}

// GetKeywordsResponse 获取全部关键词的响应
type GetKeywordsResponse struct {
	Data []struct {
		ID        int    `json:"id"`
		AdGroupID int    `json:"adGroupId"`
		Text      string `json:"text"`
		Status    string `json:"status"`
		MatchType string `json:"matchType"`
		BidAmount struct {
			Amount   string `json:"amount"`
			Currency string `json:"currency"`
		} `json:"bidAmount"`
		ModificationTime string `json:"modificationTime"`
		Deleted          bool   `json:"deleted"`
	} `json:"data"`
	Pagination struct {
		TotalResults int `json:"totalResults"`
		StartIndex   int `json:"startIndex"`
		ItemsPerPage int `json:"itemsPerPage"`
	} `json:"pagination"`
}

// GetKeywordsByCampaignIDAdgroupID 获取关键字
func (s *Server) GetKeywordsByCampaignIDAdgroupID(campaignID, adgroupID int) (*GetKeywordsResponse, error) {
	resp := new(GetKeywordsResponse)
	key := fmt.Sprintf("apple_ads_info_keywords_%d_%d", campaignID, adgroupID)
	rd := cache.GetYogaRedis()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		return nil, cmd.Err()
	}
	if cmd.Err() == nil && cmd.Val() != "" {
		if err := json.Unmarshal([]byte(cmd.Val()), resp); err != nil {
			return nil, err
		}
		return resp, nil
	}
	token := s.GetToken()
	acl, err := s.GetACL(token)
	if err != nil {
		return nil, err
	}
	orgID := acl.Data[0].OrgID
	address := fmt.Sprintf("https://api.searchads.apple.com/api/v5/campaigns/%d/adgroups/%d/targetingkeywords",
		campaignID, adgroupID)
	header := http.Header{}
	header.Set("Authorization", fmt.Sprintf("Bearer %s", token.Value))
	header.Set("X-AP-Context", fmt.Sprintf("orgId=%d", orgID))
	body, err := requests.HTTPRequest(address, "GET", header, nil)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(body, resp); err != nil {
		return nil, err
	}
	if err := rd.Set(context.Background(), key, string(body), time.Minute*30).Err(); err != nil {
		return nil, err
	}
	return resp, nil
}

// GetKeywordsByCampaignResponse
type GetKeywordsByCampaignResponse struct {
	Data []struct {
		ID        int    `json:"id"`
		AdGroupID int    `json:"adGroupId"`
		Text      string `json:"text"`
		Status    string `json:"status"`
		MatchType string `json:"matchType"`
		BidAmount struct {
			Amount   string `json:"amount"`
			Currency string `json:"currency"`
		} `json:"bidAmount"`
		ModificationTime string `json:"modificationTime"`
		Deleted          bool   `json:"deleted"`
	} `json:"data"`
	Pagination struct {
		TotalResults int `json:"totalResults"`
		StartIndex   int `json:"startIndex"`
		ItemsPerPage int `json:"itemsPerPage"`
	} `json:"pagination"`
	Error interface{} `json:"error"`
}

// GetKeywordsByCampaignID 通过 Campaign 获取关键词 (注:这个获取的比较全)
func (s *Server) GetKeywordsByCampaignID(campaignID int) (*GetKeywordsByCampaignResponse, error) {
	resp := new(GetKeywordsByCampaignResponse)
	key := fmt.Sprintf("apple_ads_info_keywords_bycampaign_%d", campaignID)
	rd := cache.GetYogaRedis()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		return nil, cmd.Err()
	}
	if cmd.Err() == nil && cmd.Val() != "" {
		if err := json.Unmarshal([]byte(cmd.Val()), resp); err != nil {
			return nil, err
		}
		return resp, nil
	}
	token := s.GetToken()
	acl, err := s.GetACL(token)
	if err != nil {
		return nil, err
	}
	orgID := acl.Data[0].OrgID
	address := fmt.Sprintf("https://api.searchads.apple.com/api/v5/campaigns/%d/adgroups/targetingkeywords/find",
		campaignID)
	header := http.Header{}
	header.Set("Authorization", fmt.Sprintf("Bearer %s", token.Value))
	header.Set("X-AP-Context", fmt.Sprintf("orgId=%d", orgID))
	header.Set("Content-Type", "application/json")
	reader := strings.NewReader(`{"pagination":{"offset":0,"limit":99999},"orderBy":[{"field":"id","sortOrder":"ASCENDING"}],"conditions":[{"field":"deleted","operator":"EQUALS","values":["false"]}]}`) // nolint
	body, err := requests.HTTPRequest(address, "POST", header, reader)
	if err != nil {
		logger.Warn(err.Error())
		return nil, nil
	}
	if err := json.Unmarshal(body, resp); err != nil {
		return nil, err
	}
	if resp.Error != nil {
		return nil, fmt.Errorf("%s", resp.Error)
	}
	if err := rd.Set(context.Background(), key, string(body), time.Minute*20).Err(); err != nil {
		return nil, err
	}
	return resp, nil
}

// GetCreativeSetsByIDResponse GetCreativeSetsByID 的响应
type GetCreativeSetsByIDResponse struct {
	Data []struct {
		CreativeSetAssets []struct {
			ID    int `json:"id"`
			Asset struct {
				AssetGenID       string `json:"assetGenId"`
				Type             string `json:"type"`
				AppPreviewDevice string `json:"appPreviewDevice"`
				Orientation      string `json:"orientation"`
				Deleted          bool   `json:"deleted"`
			} `json:"asset"`
		} `json:"creativeSetAssets"`
		ID            int           `json:"id"`
		OrgID         int           `json:"orgId"`
		AdamID        int           `json:"adamId"`
		Name          string        `json:"name"`
		LanguageCode  string        `json:"languageCode"`
		Status        string        `json:"status"`
		StatusReasons []interface{} `json:"statusReasons"`
	} `json:"data"`
}

// GetCreativeSetsByID 获取 creative_sets
func (s *Server) GetCreativeSetsByID(creativeSetID int) (*GetCreativeSetsByIDResponse, error) {
	resp := new(GetCreativeSetsByIDResponse)
	key := fmt.Sprintf("apple_ads_info_creative_sets_%d", creativeSetID)
	rd := cache.GetYogaRedis()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		return nil, cmd.Err()
	}
	if cmd.Err() == nil && cmd.Val() != "" {
		if err := json.Unmarshal([]byte(cmd.Val()), resp); err != nil {
			return nil, err
		}
		return resp, nil
	}
	token := s.GetToken()
	acl, err := s.GetACL(token)
	if err != nil {
		return nil, err
	}
	orgID := acl.Data[0].OrgID
	address := "https://api.searchads.apple.com/api/v5/creatives/find"
	header := http.Header{}
	header.Set("Authorization", fmt.Sprintf("Bearer %s", token.Value))
	header.Set("X-AP-Context", fmt.Sprintf("orgId=%d", orgID))
	header.Set("Content-Type", "application/json")
	reader := strings.NewReader(fmt.Sprintf(`{"selector": {"conditions": [{"field": "id","operator": "EQUALS","values": ["%d"]}]}}`, creativeSetID)) // nolint
	body, err := requests.HTTPRequest(address, "POST", header, reader)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(body, resp); err != nil {
		return nil, err
	}
	if err := rd.Set(context.Background(), key, string(body), time.Minute*50).Err(); err != nil {
		return nil, err
	}
	return resp, nil
}

// GetCampaignByIDResponse GetCampaignByID 的响应
type GetCampaignByIDResponse struct {
	Data struct {
		ID           int    `json:"id"`
		OrgID        int    `json:"orgId"`
		Name         string `json:"name"`
		BudgetAmount struct {
			Amount   string `json:"amount"`
			Currency string `json:"currency"`
		} `json:"budgetAmount"`
		DailyBudgetAmount struct {
			Amount   string `json:"amount"`
			Currency string `json:"currency"`
		} `json:"dailyBudgetAmount"`
		AdamID            int    `json:"adamId"`
		PaymentModel      string `json:"paymentModel"`
		LocInvoiceDetails struct {
			ClientName          string `json:"clientName"`
			OrderNumber         string `json:"orderNumber"`
			BuyerName           string `json:"buyerName"`
			BuyerEmail          string `json:"buyerEmail"`
			BillingContactEmail string `json:"billingContactEmail"`
		} `json:"locInvoiceDetails"`
		BudgetOrders        []interface{} `json:"budgetOrders"`
		StartTime           string        `json:"startTime"`
		EndTime             interface{}   `json:"endTime"`
		Status              string        `json:"status"`
		ServingStatus       string        `json:"servingStatus"`
		ServingStateReasons interface{}   `json:"servingStateReasons"`
		ModificationTime    string        `json:"modificationTime"`
		Deleted             bool          `json:"deleted"`
		SapinLawResponse    string        `json:"sapinLawResponse"`
		CountriesOrRegions  []string      `json:"countriesOrRegions"`
		// 该字段官网也没 demo 数据,我也不知道它该返回什么
		CountryOrRegionServingStateReasons interface{} `json:"countryOrRegionServingStateReasons"`
		SupplySources                      []string    `json:"supplySources"`
		AdChannelType                      string      `json:"adChannelType"`
		BillingEvent                       string      `json:"billingEvent"`
		DisplayStatus                      string      `json:"displayStatus"`
	}
}

// GetCampaignByID 获取 Campaign
func (s *Server) GetCampaignByID(campaignID int) (*GetCampaignByIDResponse, error) {
	address := fmt.Sprintf("https://api.searchads.apple.com/api/v5/campaigns/%d", campaignID)
	resp := new(GetCampaignByIDResponse)
	key := fmt.Sprintf("apple_ads_info_campaigns_%d", campaignID)
	rd := cache.GetYogaRedis()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		return nil, cmd.Err()
	}
	if cmd.Err() == nil && cmd.Val() != "" {
		if err := json.Unmarshal([]byte(cmd.Val()), resp); err != nil {
			return nil, err
		}
		return resp, nil
	}
	token := s.GetToken()
	acl, err := s.GetACL(token)
	if err != nil {
		return nil, err
	}
	orgID := acl.Data[0].OrgID
	header := http.Header{}
	header.Set("Authorization", fmt.Sprintf("Bearer %s", token.Value))
	header.Set("X-AP-Context", fmt.Sprintf("orgId=%d", orgID))
	header.Set("Content-Type", "application/json")
	body, err := requests.HTTPRequest(address, "GET", header, nil)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(body, resp); err != nil {
		return nil, err
	}
	if err := rd.Set(context.Background(), key, string(body), time.Minute*50).Err(); err != nil {
		return nil, err
	}
	return resp, nil
}

// GetAdGroupResponse AdGroup 的响应
type GetAdGroupResponse struct {
	Data struct {
		ID         int    `json:"id"`
		CampaignID int    `json:"campaignId"`
		Name       string `json:"name"`
		CpaGoal    struct {
			Amount   string `json:"amount"`
			Currency string `json:"currency"`
		} `json:"cpaGoal"`
		StartTime              string `json:"startTime"`
		EndTime                string `json:"endTime"`
		AutomatedKeywordsOptIn bool   `json:"automatedKeywordsOptIn"`
		DefaultBidAmount       struct {
			Amount   string `json:"amount"`
			Currency string `json:"currency"`
		} `json:"defaultBidAmount"`
		PricingModel        string `json:"pricingModel"`
		TargetingDimensions struct {
			Age struct {
				Included []struct {
					MinAge int `json:"minAge"`
					MaxAge int `json:"maxAge"`
				} `json:"included"`
			} `json:"age"`
			Gender struct {
				Included []string `json:"included"`
			} `json:"gender"`
			Country     interface{} `json:"country"`
			AdminArea   interface{} `json:"adminArea"`
			Locality    interface{} `json:"locality"`
			DeviceClass struct {
				Included []string `json:"included"`
			} `json:"deviceClass"`
			Daypart struct {
				UserTime struct {
					Included []int `json:"included"`
				} `json:"userTime"`
			} `json:"daypart"`
			AppDownloaders interface{} `json:"appDownloaders"`
		} `json:"targetingDimensions"`
		OrgID               int         `json:"orgId"`
		ModificationTime    string      `json:"modificationTime"`
		Status              string      `json:"status"`
		ServingStatus       string      `json:"servingStatus"`
		ServingStateReasons interface{} `json:"servingStateReasons"`
		DisplayStatus       string      `json:"displayStatus"`
		Deleted             bool        `json:"deleted"`
	} `json:"data"`
}

// GetAdGroupByCampaignIDAdgroupID 获取 AdGroup
func (s *Server) GetAdGroupByCampaignIDAdgroupID(campaignID, adgroupID int) (*GetAdGroupResponse, error) {
	resp := new(GetAdGroupResponse)
	key := fmt.Sprintf("apple_ads_info_adgroup_%d_%d", campaignID, adgroupID)
	rd := cache.GetYogaRedis()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		return nil, cmd.Err()
	}
	if cmd.Err() == nil && cmd.Val() != "" {
		if err := json.Unmarshal([]byte(cmd.Val()), resp); err != nil {
			return nil, err
		}
		return resp, nil
	}
	token := s.GetToken()
	acl, err := s.GetACL(token)
	if err != nil {
		return nil, err
	}
	orgID := acl.Data[0].OrgID
	address := fmt.Sprintf("https://api.searchads.apple.com/api/v5/campaigns/%d/adgroups/%d", campaignID, adgroupID)
	header := http.Header{}
	header.Set("Authorization", fmt.Sprintf("Bearer %s", token.Value))
	header.Set("X-AP-Context", fmt.Sprintf("orgId=%d", orgID))
	header.Set("Content-Type", "application/json")
	body, err := requests.HTTPRequest(address, "GET", header, nil)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(body, resp); err != nil {
		return nil, err
	}
	if err := rd.Set(context.Background(), key, string(body), time.Minute*50).Err(); err != nil {
		return nil, err
	}
	return resp, nil
}
