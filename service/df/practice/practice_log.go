package practice

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/protogen/dancefit-go/dancefit"
	"gitlab.dailyyoga.com.cn/server/go-artifact/goroutine"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/df/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/df/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
)

type DayPayload struct {
	UID        int64 `json:"uid"`
	CreateTime int64 `json:"create_time"`
}

type LoggingPracticePayload struct {
	UID               int64 `json:"uid"`
	SessionID         int64 `json:"session_id"`
	Calories          int   `json:"calories"`
	Minutes           int   `json:"minutes"`
	IsExit            int   `json:"is_exit"`
	OrderDay          int   `json:"order_day"`
	ObProgramID       int64 `json:"ob_program_id"`
	ProgramID         int64 `json:"program_id"`
	PracticeStartTime int64 `json:"practice_start_time"`
	IsInternal        int   `json:"is_internal"`
	ReportType        int   `json:"report_type"`
	PlayTime          int   `json:"play_time"`
}

type CheckData struct {
	PracticeCurrentTime int64
}

type practiceLog struct{}

var Log practiceLog

func (pl *practiceLog) StatPracticeLog(params []byte) error {
	var bean LoggingPracticePayload
	if err := json.Unmarshal(params, &bean); err != nil {
		return err
	}
	if bean.UID < 1 {
		return errors.New("invalid params: uid")
	}
	// 更新用户练习数据
	SaveUserPractice(&bean)
	cache.GetDancefitRedis().Del(context.Background(), "DF:UserPracticeStat:"+strconv.FormatInt(bean.UID, 10))
	return nil
}

func SaveUserPractice(r *LoggingPracticePayload) {
	// 如果上报时间大于当天时间或者小于20160101则忽略处理
	if r.PracticeStartTime > time.Now().Unix() || r.PracticeStartTime < 1451577600 {
		return
	}
	// 构造写入数据
	bean := &db.Log{
		UID:               r.UID,
		ProgramID:         r.ProgramID,
		SessionID:         r.SessionID,
		PracticeStartTime: r.PracticeStartTime,
		OrderDay:          r.OrderDay,
		Calories:          r.Calories,
		PlayTime:          r.PlayTime,
		Minutes:           r.Minutes,
		IsExit:            r.IsExit,
		IsInternal:        r.IsInternal,
		ReportType:        r.ReportType,
	}
	item := db.TbPracticeLog.GetItemByCond(&db.SearchQuery{
		UID: r.UID, SessionID: r.SessionID, ProgramID: r.ProgramID, IsInternal: r.IsInternal,
		PracticeStartTime: r.PracticeStartTime,
	})
	if item != nil && item.ID > 0 {
		logger.Warn("DF重复上报userActionLog", bean)
		return
	}
	// 保存数据
	if err := bean.Save(); err != nil {
		logger.Error(err)
		return
	}
	// 练习后刷新挑战赛状态
	goroutine.GoSafely(func() { refreshChallengeState(bean.UID, bean.PracticeStartTime) })
	// 练习后刷新新手任务状态
	goroutine.GoSafely(func() { refreshNewUserTask(bean.UID, bean.PracticeStartTime, int64(bean.PlayTime)) })
	// 更新用户练习天数
	if err := updatePracticeDay(r); err != nil {
		logger.Error(err)
		return
	}
	// 更新用户属性
	if err := updateUserAttrs(r); err != nil {
		logger.Error(err)
		return
	}
	// 更新汇总数据
	setUserCollect(r) // 修改st_user_collect
	setUserPlayDay(r) // 修改st_user_play_day
	SrvLastPractice.UpdateUserLastPractice(r)
}

func refreshNewUserTask(uid, practiceStartTime, playTime int64) {
	dclient := grpc.GetDancefitClient()
	res, err := dclient.RefreshNewUserTask(context.Background(), &dancefit.RefreshNewUserTaskReq{
		UID:               uid,
		PracticeStartTime: practiceStartTime,
		PlayTime:          playTime,
	})
	if err != nil {
		logger.Error(err)
		return
	}
	if res.GetErrorCode() > 0 {
		logger.Warn("刷新新手任务状态失败", res.GetErrorCode(), res.GetMsg())
	}
}

func refreshChallengeState(uid, practiceStartTime int64) {
	dclient := grpc.GetDancefitClient()
	res, err := dclient.RefreshChallengeState(context.Background(), &dancefit.RefreshChallengeStateReq{
		UID:               uid,
		PracticeStartTime: practiceStartTime,
	})
	if err != nil {
		logger.Error(err)
		return
	}
	if res.GetErrorCode() > 0 {
		logger.Warn("刷新挑战赛状态失败", res.GetErrorCode(), res.GetMsg())
	}
}

func updateUserAttrs(r *LoggingPracticePayload) error {
	dayList := db.TbPracticeDay.GetList(r.UID)
	totalDay := len(dayList)
	// 更新用户练习时长、卡路里、练习天数
	userDetail := user.TbAccount.GetUserByID(r.UID)
	if userDetail != nil && userDetail.ID > 0 {
		userDetail.Calories += r.Calories
		userDetail.Minutes += r.Minutes
		userDetail.PracticeCount++
		userDetail.PracticeDays = totalDay
		if err := userDetail.Update(); err != nil {
			return err
		}
	}
	return nil
}

func updatePracticeDay(r *LoggingPracticePayload) error {
	// 更新用户练习天数
	dateIndex := time.Unix(r.PracticeStartTime, 0).Format("********")
	dayItem := db.TbPracticeDay.GetItem(r.UID, dateIndex)
	if dayItem == nil || dayItem.ID < 1 {
		dayItem = &db.Day{
			UID:       r.UID,
			DateIndex: dateIndex,
		}
		if err := dayItem.Save(); err != nil {
			logger.Error("DF更新用户练习天数失败", err)
			return err
		}
	}
	return nil
}

func setUserPlayDay(r *LoggingPracticePayload) {
	// 更新st_play_time_day
	dateIndex := time.Unix(r.PracticeStartTime, 0).Format("********")
	sd := db.TbPlayDay.GetItem(r.UID, dateIndex)
	if sd != nil && sd.ID > 0 {
		sd.PlayTime += int64(r.PlayTime)
		sd.Calorie += int32(r.Calories)
		sd.PracticeCount++
		if err := sd.Update(); err != nil {
			logger.Error(err)
		}
	} else {
		sdItem := &db.PlayDay{
			UID:           r.UID,
			PlayTime:      int64(r.PlayTime),
			Calorie:       int32(r.Calories),
			PracticeCount: 1,
			DateIndex:     dateIndex,
		}
		if err := sdItem.Save(); err != nil {
			logger.Error(err)
		}
	}
}

func setUserCollect(r *LoggingPracticePayload) {
	userCollect, err := db.TbUserCollect.FindOrCreate(r.UID)
	if err != nil {
		logger.Error(err)
	}
	if userCollect == nil { // userCollect没有被真正创建
		logger.Error("st_user_collect 未创建，", r.UID)
		return
	}

	userCollect.TotalCalorie += int64(r.Calories)
	userCollect.TotalPlayTime += int64(r.PlayTime)
	dayKeyList := db.TbPracticeDay.GetListAscDateIndex(r.UID)
	userCollect.TotalPracticeDay = int64(len(dayKeyList))
	userCollect.LastContinuePracticeDay = LastContinuePracticeDay(r.UID, dayKeyList)
	err = userCollect.Update()
	if err != nil {
		logger.Error("更新userCollect失败", err)
	}
}

func LastContinuePracticeDay(uid int64, dayKeyList []db.Day) int64 {
	var keepDay int64 = 1
	var lastDateIndex string
	if len(dayKeyList) < 1 {
		return keepDay
	}
	for _, v := range dayKeyList {
		if lastDateIndex == "" {
			lastDateIndex = v.DateIndex
		} else {
			lastDateIndexUnix := convertTimeUnix(lastDateIndex)
			curDateIndexUnix := convertTimeUnix(v.DateIndex)
			if curDateIndexUnix-lastDateIndexUnix == 86400 {
				keepDay++
			} else {
				keepDay = 1
			}
			lastDateIndex = v.DateIndex
		}
	}
	return keepDay
}

func convertTimeUnix(dateIndex string) int64 {
	tUnix, _ := time.Parse("********", dateIndex)
	return tUnix.Unix()
}
