package practice

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
)

type LastPractice struct{}

var SrvLastPractice LastPractice

type LastPracticeItem struct {
	SessionID         int64 `json:"session_id"`
	PlanID            int64 `json:"plan_id"`
	PracticeStartTime int64 `json:"practice_start_time"`
	ReportType        int32 `json:"report_type"`
	IsInternal        int   `json:"is_internal"`
	Calories          int64 `json:"calories"`
}

const MaxLastPracticeCourse = 100

// 用户最近练习
const UserLastPracticeInfo = "Df:User:LastPractice:Course"

const (
	ReportTypeSession = iota + 1
	ReportTypeOb
	ReportTypePlan
	ReportTypeChoiceSession
)

func (*LastPractice) UpdateUserLastPractice(param *LoggingPracticePayload) {
	if param.ReportType == ReportTypeOb { // 智能课表时，program_id报的是课表ID，
		param.ProgramID = 0
	}
	rc := cache.GetDancefitRedis()
	ctx := context.Background()
	cacheKey := fmt.Sprintf("%s:%d", UserLastPracticeInfo, param.UID)
	current, err := rc.ZRange(ctx, cacheKey, 0, -1).Result()
	if err != nil && err != redis.Nil {
		logger.Error(err)
	}
	logger.Info("当前最近练习数量", param.UID, current)
	exist := false
	for _, v := range current {
		item := &LastPracticeItem{}
		err := json.Unmarshal([]byte(v), item)
		if err != nil {
			logger.Error(err)
			continue
		}
		// 如果已存在，将对应数据删除，然后插入新数据，这里内部课和外部课程（精选课）要分开记录
		if item.SessionID == param.SessionID && item.IsInternal == param.IsInternal {
			// 上报数据晚于最新的数据，直接return
			if item.PracticeStartTime > param.PracticeStartTime {
				return
			}
			exist = true
			remInt, err := rc.ZRem(ctx, cacheKey, v).Result()
			if remInt <= 0 || err != nil {
				logger.Error("DF最近练习删除重复数据失败", cacheKey, v)
				continue
			}
		}
	}
	item := &LastPracticeItem{
		SessionID:         param.SessionID,
		PlanID:            param.ProgramID,
		PracticeStartTime: param.PracticeStartTime,
		ReportType:        int32(param.ReportType),
		IsInternal:        param.IsInternal,
		Calories:          int64(param.Calories),
	}
	itemByte, _ := json.Marshal(item)
	addItem := &redis.Z{
		Score:  float64(param.PracticeStartTime),
		Member: string(itemByte),
	}
	addNum, _ := rc.ZAdd(ctx, cacheKey, addItem).Result()
	if addNum <= 0 {
		return
	}
	// 有效期180天
	rc.Expire(ctx, cacheKey, 180*24*time.Hour)
	// 大于100个 删除
	if !exist && len(current) >= MaxLastPracticeCourse {
		delNum := len(current) + 1 - MaxLastPracticeCourse
		rc.ZRemRangeByRank(ctx, cacheKey, 0, int64(delNum-1))
	}
}
