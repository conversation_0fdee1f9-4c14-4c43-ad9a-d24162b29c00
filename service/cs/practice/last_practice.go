package practice

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
)

type LastPractice struct{}

var SrvLastPractice LastPractice

type LastPracticeItem struct {
	CourseID          int64    `json:"course_id"`
	PracticeStartTime int64    `json:"practice_start_time"`
	SceneType         int32    `json:"scene_type"`
	PracticeLabels    []string `json:"practice_labels,omitempty"`
	SceneTypeID       int      `json:"scene_type_id"`
	SceneTypeIDIndex  int64    `json:"scene_type_index"`
}

const MaxLastPracticeCourse = 20

// 用户最近练习
const UserLastPracticeInfo = "Cs:User:LastPractice:Course"

func (*LastPractice) UpdateUserLastPractice(param *LoggingPracticePayload) {
	rc := cache.GetChildrenRedis()
	ctx := context.Background()
	cacheKey := fmt.Sprintf("%s:%d", UserLastPracticeInfo, param.UID)
	current, err := rc.ZRange(ctx, cacheKey, 0, -1).Result()
	if err != nil && err != redis.Nil {
		logger.Error(err)
	}
	logger.Info("当前最近练习数量", param.UID, current)
	exist := false
	for _, v := range current {
		item := &LastPracticeItem{}
		err := json.Unmarshal([]byte(v), item)
		if err != nil {
			logger.Error(err)
			continue
		}
		// 如果已存在，将对应数据删除，然后插入新数据
		if item.CourseID == param.CourseID {
			// 上报数据晚于最新的数据
			if item.PracticeStartTime > param.PracticeStartTime {
				return
			}
			exist = true
			remInt, err := rc.ZRem(ctx, cacheKey, v).Result()
			if remInt <= 0 || err != nil {
				logger.Error("最近练习删除重复数据失败", cacheKey, v)
				continue
			}
		}
	}
	item := &LastPracticeItem{
		CourseID:          param.CourseID,
		PracticeStartTime: param.PracticeStartTime,
		SceneType:         int32(param.SceneType),
		PracticeLabels:    param.PracticeLabels,
		SceneTypeID:       param.SceneTypeID,
		SceneTypeIDIndex:  param.SceneTypeIDIndex,
	}
	itemByte, _ := json.Marshal(item)
	addItem := &redis.Z{
		Score:  float64(param.PracticeStartTime),
		Member: string(itemByte),
	}
	addNum, _ := rc.ZAdd(ctx, cacheKey, addItem).Result()
	if addNum <= 0 {
		return
	}
	// 有效期180天
	rc.Expire(ctx, cacheKey, 180*24*time.Hour)
	// 大于20个 删除
	if !exist && len(current) >= MaxLastPracticeCourse {
		delNum := len(current) + 1 - MaxLastPracticeCourse
		rc.ZRemRangeByRank(ctx, cacheKey, 0, int64(delNum-1))
	}
}
