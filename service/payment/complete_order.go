package payment

import (
	"net/url"
	"strconv"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

type payment struct {
}

var ServicePayment payment

type CompleteOrderPayload struct {
	LogID int64 `json:"log_id"`
}

func (s *payment) CompleteOrder(data *CompleteOrderPayload) {
	logger.Info("异步调用支付成功回调:", data)
	go requestYogaCompleteOrder(data, 0)
}

func requestYogaCompleteOrder(data *CompleteOrderPayload, i int) {
	values := make(url.Values)
	values.Add("complete_log_id", strconv.FormatInt(data.LogID, 10))
	values.Add("complete_call_type", "2")
	resp, err := requests.NewYogaClient(config.Get().PhpYogaAddress).PostForm("order/completeorder/completeorder", values)
	if err != nil && i < 3 {
		logger.Error("异步调用yoga http报错重试第" + strconv.Itoa(i) + "次")
		requestYogaCompleteOrder(data, i+1)
		logger.Error("异步调用支付成功回调报错", err)
		return
	}
	if err != nil || !resp.IsOK() || resp.Body == "" {
		logger.Error("异步调用支付成功回调失败", resp, data)
		return
	}
}
