package payment

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	pb "gitlab.dailyyoga.com.cn/protogen/srv-usercenter-go/huawei"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/comm"
)

type report struct {
}

var ServiceReport report

type ReportPaymentPayload struct {
	UID   int64 `json:"uid"`
	Price int64 `json:"price"`
}

const (
	AppPay        = 4 // 付费
	channelHuaWei = "100020"
	channelOppo   = "100011" // OPP渠道
	channelVivo   = "100017" // Vivo渠道
)

type ActionParamItem struct {
	Name  string `json:"name"`
	Value int64  `json:"value"`
}

type ocpx struct {
	UID        string `json:"uid"`
	Channels   string `json:"channels"`
	DYei       string `json:"dyei"`
	DistinctID string `json:"distinct_id"`
}

func (r *report) ReportPayment(data *ReportPaymentPayload) {
	if data.UID < 0 {
		return
	}
	ctx := context.Background()
	rd := cache.GetYogaRedis()
	uCacheKey := fmt.Sprintf("hw:ocpd:uid:%v", data.UID)
	cacheValue, err := rd.Get(ctx, uCacheKey).Result()
	if err == redis.Nil {
		logger.Debugf("缓存不存在: %s", uCacheKey)
		return
	} else if err != nil {
		logger.Error(err)
		return
	}
	if cacheValue == "" {
		return
	}
	var ocpxData ocpx
	if err := json.Unmarshal([]byte(cacheValue), &ocpxData); err != nil {
		logger.Error("ocpd 解析数据上报出错", err)
		return
	}
	if ocpxData.Channels == "" {
		logger.Error("ocpd 渠道为空", ocpxData)
		return
	}
	channel, err := strconv.ParseInt(ocpxData.Channels, 10, 64)
	if err != nil {
		logger.Error("asyncOCPD channel format fail", err)
		return
	}
	request := &pb.PushRequest{
		CustomerId: ocpxData.DYei,
		ActionType: AppPay,
		Channel:    channel,
	}
	actionParam := make([]*ActionParamItem, 0)
	actionParam = append(actionParam, &ActionParamItem{
		Name:  "付费金额",
		Value: data.Price,
	})
	parasStr, _ := json.Marshal(actionParam)
	switch ocpxData.Channels {
	case channelHuaWei:
		if accountInfo := user.TbAccountInfo.GetByUID(data.UID); accountInfo != nil {
			todayLast := comm.FormatEndTime(time.Now().Unix())

			if todayLast >= int64(accountInfo.CreateTime) {
				request.ActionParam = string(parasStr)
			}
		}
	case channelOppo, channelVivo:
		request.DistinctID = ocpxData.DistinctID
		request.UID = data.UID
		request.ActionParam = string(parasStr)
	default:
		logger.Warn("未识别渠道", ocpxData)
	}
	rsp, err := grpc.GetUserCenterClient().HuaweiPush(ctx, request)
	if err != nil {
		logger.Warnf("opcd 回传失败:%+v 返回值：%+v", r, rsp)
	}
}
