package practice

import (
	"context"
	"encoding/json"
	"errors"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/protogen/fitness-go/fitness"
	"gitlab.dailyyoga.com.cn/server/go-artifact/goroutine"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/ft/course"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/ft/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/ft/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/ft/usersetting"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/comm"
)

const (
	IsPracticeHasPractice = iota + 1 // 已练习
	IsPracticeNoPractice             // 未练习
	IsPracticeDone                   // 已完成
)

type DayPayload struct {
	UID        int64 `json:"uid"`
	CreateTime int64 `json:"create_time"`
}

type LoggingPracticePayload struct {
	UID               int64    `json:"uid"`
	CourseID          int64    `json:"course_id"`
	ProgramID         int64    `json:"ob_program_id"`
	OrderDay          int      `json:"order_day"`
	Calorie           int      `json:"calorie"`
	PlayTime          int      `json:"play_time"`
	IsExit            int      `json:"is_exit"`
	PracticeStartTime int64    `json:"practice_start_time"`
	PracticeType      int      `json:"practice_type"`
	SceneType         int      `json:"scene_type"`
	PracticeLabels    []string `json:"practice_labels,omitempty"`
	SceneTypeID       int      `json:"scene_type_id"`
	SceneTypeIDIndex  int64    `json:"scene_type_index"`
}

type CheckData struct {
	PracticeCurrentTime int64
}

type practiceLog struct{}

var Log practiceLog

func (pl *practiceLog) StatPracticeLog(params []byte) error {
	var bean LoggingPracticePayload
	if err := json.Unmarshal(params, &bean); err != nil {
		return err
	}
	if bean.UID < 1 {
		return errors.New("invalid params: uid")
	}
	logger.Info("FT练习上报", string(params))
	// 更新用户练习数据
	SaveUserPractice(&bean)
	return nil
}

type FeelReportParam struct {
	UID               int64 `json:"uid"`
	CourseID          int64 `json:"course_id"`
	ProgramID         int64 `json:"ob_program_id"`
	PracticeStartTime int64 `json:"practice_start_time"`
	PracticeFeel      int64 `json:"practice_feel"`
}

func (pl *practiceLog) StatPracticeFeel(params []byte) error {
	var bean FeelReportParam
	if err := json.Unmarshal(params, &bean); err != nil {
		return err
	}
	if bean.UID < 1 {
		return errors.New("invalid params: uid")
	}
	// 更新用户练习数据
	log := db.TbPracticeLog.GetItemByCond(&db.SearchQuery{
		UID: bean.UID, CourseID: bean.CourseID, ProgramID: bean.ProgramID,
		PracticeStartTime: bean.PracticeStartTime,
	})

	if log == nil {
		logger.Errorf("FT练习感受上报:无练习记录:%+v", params)
		return nil
	}
	log.PracticeFeel = int32(bean.PracticeFeel)
	if err := log.Update(); err != nil {
		logger.Error("FT练习感受上报:更新失败:", err)
	}
	return nil
}

const (
	PracticeTypeStart = iota + 1
	PracticeTypeExit
	PracticeTypeFinish
)

const SceneTypePlanProgram = 4

const (
	Yes = 1
	No  = 2
)

func SaveUserPractice(r *LoggingPracticePayload) {
	// 如果上报时间大于当天时间或者小于20160101则忽略处理
	if r.PracticeStartTime > time.Now().Unix() || r.PracticeStartTime < 1451577600 {
		return
	}
	// 如果记录不存在，无脑添加，不区分场景（兼容没有开训上报的情况）
	log := db.TbPracticeLog.GetItemByCond(&db.SearchQuery{
		UID: r.UID, CourseID: r.CourseID, ProgramID: r.ProgramID,
		PracticeStartTime: r.PracticeStartTime,
	})
	if log == nil {
		// 写入练习记录
		data := db.Log{
			UID:               r.UID,
			ProgramID:         r.ProgramID,
			CourseID:          r.CourseID,
			Calories:          r.Calorie,
			IsExit:            r.IsExit,
			OrderDay:          r.OrderDay,
			PracticeStartTime: r.PracticeStartTime,
			PracticeType:      r.PracticeType,
			SceneType:         r.SceneType,
			PlayTime:          r.PlayTime,
			PracticeLabels:    strings.Join(r.PracticeLabels, ","),
			SceneTypeID:       r.SceneTypeID,
			SceneTypeIDIndex:  r.SceneTypeIDIndex,
		}
		if err := data.Save(); err != nil {
			logger.Warn("FT练习上报错误", err)
			return
		}
	} else {
		if log.PlayTime > 0 {
			logger.Warn("FT重复上报userActionLog", r)
			return
		}
		log.PlayTime = r.PlayTime
		if r.PracticeType == PracticeTypeExit {
			log.IsExit = 1
		}
		log.SceneType = r.SceneType
		log.PracticeLabels = strings.Join(r.PracticeLabels, ",")
		log.OrderDay = r.OrderDay
		log.Calories = r.Calorie
		log.PracticeType = r.PracticeType
		if err := log.Update(); err != nil {
			return
		}
	}
	incrPracticeCount(r.UID, r.PracticeType)
	goroutine.GoSafely(func() { refreshChallengeState(r.UID, r.PracticeStartTime) })
	if r.PracticeType == PracticeTypeStart {
		return
	}
	// 练习后刷新挑战赛状态
	setUserPlayDay(r) // 修改st_user_play_day
	// 更新汇总数据
	setUserCollect(r) // 修改st_user_collect
	// 练习后的处理
	err := UpdateAfterPractice(r)
	if err != nil {
		logger.Error(err)
	}
}

func UpdateAfterPractice(r *LoggingPracticePayload) error {
	// 更新用户练习天数
	dateIndex := time.Unix(r.PracticeStartTime, 0).Format("********")
	dayItem := db.TbPracticeDay.GetItem(r.UID, dateIndex)
	if dayItem == nil || dayItem.ID < 1 {
		dayItem = &db.Day{
			UID:       r.UID,
			DateIndex: dateIndex,
		}
		if err := dayItem.Save(); err != nil {
			return err
		}
	}
	dayList := db.TbPracticeDay.GetList(r.UID)
	totalDay := len(dayList)
	// 更新用户练习时长、卡路里、练习天数
	userDetail := user.TbAccount.GetUserByID(r.UID)
	if userDetail != nil && userDetail.ID > 0 {
		userDetail.PracticeCount++
		userDetail.Calories += r.Calorie
		userDetail.Minutes += (r.PlayTime / 60)
		userDetail.PracticeDays = totalDay
		if err := userDetail.Update(); err != nil {
			return err
		}
	}
	courseItem := course.TbCourseLibrary.GetItem(r.CourseID)
	if courseItem != nil && r.PlayTime > 0 {
		SrvLastPractice.UpdateUserLastPractice(r)
	}
	logger.Info("练习上报", r.UID, r.ProgramID, r.CourseID, r.OrderDay)
	// 更新ob计划完成进度
	if r.IsExit != Yes && r.SceneType != SceneTypePlanProgram {
		if r.PracticeType == 0 || r.PracticeType == PracticeTypeFinish {
			err := UpdateObProgress(r.UID, r.ProgramID, r.CourseID, r.OrderDay)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// UpdateObProgress 更新OB进度
func UpdateObProgress(uid, obProgramID, sessionID int64, orderDay int) error {
	var obSessionItem *course.ObProgramCourse
	obProgram := course.TbObProgram.GetInProcessProgram(uid)
	if obProgram == nil {
		return nil
	}
	// 如果是从课表处练习，会带上相应课表对应哪一节课程的参数，如果是
	if obProgramID == 0 || orderDay == 0 {
		obProgramID = obProgram.ID
		obSessionItem = course.TbObProgramCourse.GetItemByPracticeDate(uid, obProgramID, sessionID,
			comm.FormatStartTime(time.Now().Unix()))
	} else {
		obSessionItem = course.TbObProgramCourse.GetItem(uid, obProgramID, sessionID, orderDay)
	}
	if obSessionItem == nil || obSessionItem.IsPractice == library.Yes {
		return nil
	}
	if obProgram.ID == obSessionItem.ProgramID {
		// 获取课程详情
		if obSessionItem.IsPractice != IsPracticeDone {
			// 标记为已练习
			obSessionItem.IsPractice = library.Yes
		}
	}
	obSessionItem.UID = uid
	if err := obSessionItem.Update(); err != nil {
		return err
	}
	if obProgram != nil && obProgram.ID > 0 {
		totalFinish := course.TbObProgramCourse.GetTotalFinish(uid, obProgramID)
		obProgram.FinishCount = int32(totalFinish)
		if err := obProgram.Update(); err != nil {
			return err
		}
	}
	return nil
}

func setUserPlayDay(r *LoggingPracticePayload) {
	// 更新st_play_time_day
	dateIndex := time.Unix(r.PracticeStartTime, 0).Format("********")
	sd := db.TbPlayDay.GetItem(r.UID, dateIndex)
	if sd != nil && sd.ID > 0 {
		sd.PlayTime += int64(r.PlayTime)
		sd.Calorie += int32(r.Calorie)
		sd.PracticeCount++
		if err := sd.Update(); err != nil {
			logger.Error(err)
		}
	} else {
		sdItem := &db.PlayDay{
			UID:           r.UID,
			PlayTime:      int64(r.PlayTime),
			Calorie:       int32(r.Calorie),
			PracticeCount: 1,
			DateIndex:     dateIndex,
		}
		if err := sdItem.Save(); err != nil {
			logger.Error(err)
		}
	}
}

func setUserCollect(r *LoggingPracticePayload) {
	userCollect, err := db.TbUserCollect.FindOrCreate(r.UID)
	if err != nil {
		logger.Error(err)
	}
	if userCollect == nil { // userCollect没有被真正创建
		logger.Error("st_user_collect 未创建，", r.UID)
		return
	}
	userCollect.TotalCalorie += int64(r.Calorie)
	userCollect.TotalPlayTime += int64(r.PlayTime)
	dayKeyList := db.TbPlayDay.GetList(r.UID)
	userCollect.TotalPracticeDay = int64(len(dayKeyList))
	userCollect.LastContinuePracticeDay = LastContinuePracticeDay(r.UID, dayKeyList)
	err = userCollect.Update()
	if err != nil {
		logger.Error("更新userCollect失败", err)
	}
}

func LastContinuePracticeDay(uid int64, dayKeyList []db.PlayDay) int64 {
	var keepDay int64 = 1
	var lastDateIndex string
	if len(dayKeyList) < 1 {
		return keepDay
	}
	for _, v := range dayKeyList {
		if lastDateIndex == "" {
			lastDateIndex = v.DateIndex
		} else {
			lastDateIndexUnix := convertTimeUnix(lastDateIndex)
			curDateIndexUnix := convertTimeUnix(v.DateIndex)
			if curDateIndexUnix-lastDateIndexUnix == 86400 {
				keepDay++
			} else {
				keepDay = 1
			}
			lastDateIndex = v.DateIndex
		}
	}
	return keepDay
}

func convertTimeUnix(dateIndex string) int64 {
	tUnix, _ := time.Parse("********", dateIndex)
	return tUnix.Unix()
}

type UPracticeStat struct {
	PracticeStart  int32 `json:"practice_start"`
	PracticeExit   int32 `json:"practice_exit"`
	PracticeFinish int32 `json:"practice_finish"`
}

const (
	UserPracticeStat = "user_practice_stat"
)

func incrPracticeCount(uid int64, practiceType int) {
	if practiceType == 0 {
		return
	}
	userPracticeStat, err := usersetting.TbUserSetting.GetItem(uid, UserPracticeStat)
	if err != nil {
		logger.Errorf("用户userSetting查询数据失败, uid: %d, key: %s", uid, UserPracticeStat)
		return
	}

	var practiceCount = &UPracticeStat{}
	if userPracticeStat == nil {
		practiceCount = &UPracticeStat{PracticeStart: 0, PracticeExit: 0, PracticeFinish: 0}
	} else {
		err := json.Unmarshal([]byte(userPracticeStat.Value), practiceCount)
		if err != nil {
			logger.Errorf("解析用户练习统计信息失败, err: %s, uid: %d", err, uid)
		}
	}

	switch practiceType {
	case PracticeTypeStart:
		practiceCount.PracticeStart++
	case PracticeTypeExit:
		practiceCount.PracticeExit++
	case PracticeTypeFinish:
		practiceCount.PracticeFinish++
	}
	practiceCountJSON, err := json.Marshal(practiceCount)
	if err != nil {
		logger.Errorf("用户练习统计jsonEncode失败, err: %s, uid: %d", err, uid)
	}

	if userPracticeStat == nil {
		// 处理叠加practice_type
		userPracticeStat := &usersetting.UserSetting{
			UID:      uid,
			Key:      UserPracticeStat,
			Value:    string(practiceCountJSON),
			IsDelete: 2,
		}
		if err := userPracticeStat.Save(); err != nil {
			logger.Errorf("更新用户练习统计失败, err: %s, uid:%d", err, uid)
		}
	} else {
		userPracticeStat.Value = string(practiceCountJSON)
		if err := userPracticeStat.Update(); err != nil {
			logger.Errorf("更新用户练习统计失败, err: %s, uid:%d", err, uid)
		}
	}
}

func refreshChallengeState(uid, practiceStartTime int64) {
	fclient := grpc.GetFitnessClient()
	res, err := fclient.RefreshChallengeState(context.Background(), &fitness.RefreshChallengeStateReq{
		UID:               uid,
		PracticeStartTime: practiceStartTime,
	})
	if err != nil {
		logger.Error(err)
		return
	}
	if res.GetErrorCode() > 0 {
		logger.Warn("刷新挑战赛状态失败", res.GetErrorCode(), res.GetMsg())
	}
}
