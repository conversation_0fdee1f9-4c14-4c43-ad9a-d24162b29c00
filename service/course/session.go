package course

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	courseDb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/course"
)

type Session struct {
	sessionList map[int64]*courseDb.Session
}

// GetSessionByID 根据id返回课程信息，做了内存缓存
func (s *Session) GetSessionByID(id int64) *courseDb.Session {
	if s.sessionList == nil {
		s.sessionList = make(map[int64]*courseDb.Session)
		logger.Infof("命中课程内存缓存")
	}
	if session, ok := s.sessionList[id]; ok {
		return session
	}
	session := courseDb.TbSession.GetSessionByID(id)
	if session != nil {
		s.sessionList[id] = session
	}
	return session
}
