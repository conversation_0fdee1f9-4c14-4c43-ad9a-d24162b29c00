package course

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	courseDb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/course"
)

type Program struct {
	programList map[int64]*courseDb.Program
}

// GetProgramByID 根据id返回计划信息，做了内存缓存
func (p *Program) GetProgramByID(id int64) *courseDb.Program {
	if p.programList == nil {
		p.programList = make(map[int64]*courseDb.Program)
		logger.Infof("命中计划内存缓存")
	}
	if program, ok := p.programList[id]; ok {
		return program
	}
	program := courseDb.TbProgram.GetProgramByID(id)
	if program != nil {
		p.programList[id] = program
	}
	return program
}
