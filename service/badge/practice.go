package badge

import (
	"math"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/playtime"
	dbpractice "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/badge"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/note"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"
)

type UserPracticeCollectData struct {
	TotalLoginDay          int64
	LastLoginTime          int64
	TotalPracticeDay       int64
	LastPracticeUpdateTime int64
	TotalPlayTime          int64
	LastPlayUpdateTime     int64
	LastPracticeTime       int64
	TotalPracticeWeek      int32
	CurrentWeekPracticeDay int32
	Birthday               int32
	NoteBookJoinStatus     int32
	TotalObtainPoints      int32
	TotalConsumePoints     int32
}

type userOldBadge struct {
}

var ServiceUserOldBadge userOldBadge

// GetWeekCount 计算周
func (u *userOldBadge) GetWeekCount(endTime int64) int64 {
	var WeekCount int64
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)

	// 计算周开始时间
	weekStart := "2018-03-05"
	ts, _ := time.ParseInLocation("2006-01-02", weekStart, loc)
	WeekStart := ts.Unix()
	// 获取总天数
	TotalDays := service.GetIntervalDays(WeekStart, endTime)
	stringTotalDays := strconv.FormatInt(int64(TotalDays), 10)
	floatTotalDays64, err := strconv.ParseFloat(stringTotalDays, 32)
	if err != nil {
		logger.Error("数据异常", floatTotalDays64)
		return WeekCount
	}
	floatTotalDays := float32(floatTotalDays64)
	WeekDays := float32(7)

	WeekCountFloat64 := math.Ceil(float64(floatTotalDays / WeekDays))
	WeekCount = int64(WeekCountFloat64)
	return WeekCount
}

// GetNoteBookBadgeCategoryID 获取记录本徽章id
func (u *userOldBadge) GetNoteBookBadgeCategoryID() int64 {
	cfgEnv := config.Get().GetEnv()

	var id int64 = 7
	if cfgEnv == env.Test || cfgEnv == env.Dev {
		id = 11
	}
	return id
}

// getCategoryProgressActionProgress 获取对应任务模式的进度
func (u *userOldBadge) getCategoryProgressActionProgress(progressAction, progressUnit int32,
	userPracticeCollectData *UserPracticeCollectData) int64 {
	switch library.BadgeCategoryProgressAction(progressAction) {
	case library.BadgeCategoryProgressActionEnum.Practice:
		if library.BadgeCategoryProgressUnitType(progressUnit) == library.BadgeCategoryProgressUnitEnum.Day {
			return userPracticeCollectData.TotalPracticeDay
		} else if library.BadgeCategoryProgressUnitType(progressUnit) == library.BadgeCategoryProgressUnitEnum.Week {
			return int64(userPracticeCollectData.TotalPracticeWeek)
		} else if library.BadgeCategoryProgressUnitType(progressUnit) == library.BadgeCategoryProgressUnitEnum.Minute {
			return userPracticeCollectData.TotalPlayTime
		} else {
			return 0
		}
	case library.BadgeCategoryProgressActionEnum.LogIn:
		return userPracticeCollectData.TotalLoginDay
	case library.BadgeCategoryProgressActionEnum.ObtainPoints:
		return int64(userPracticeCollectData.TotalObtainPoints)
	case library.BadgeCategoryProgressActionEnum.ConsumePoints:
		return int64(userPracticeCollectData.TotalConsumePoints)
	default:
		return 0
	}
}

// getCategoryProgressModeAccProgress 获取徽章任务类型为累计的徽章系列进度
func (u *userOldBadge) getCategoryProgressModeAccProgress(progressMode, progressAction, progressUnit int32,
	userPracticeCollectData *UserPracticeCollectData) int64 {
	switch library.BadgeCategoryProgressModeType(progressMode) {
	case library.BadgeCategoryProgressModeEnum.Acc:
		return u.getCategoryProgressActionProgress(progressAction, progressUnit, userPracticeCollectData)
	case library.BadgeCategoryProgressModeEnum.Continue:
		// 暂时没有此类型
		return 0
	default:
		return 0
	}
}

// getCategoryProgressModeCycleProgress 获取徽章任务类型为周期的徽章系列进度
func (u *userOldBadge) getCategoryProgressModeCycleProgress() int64 {
	return int64(time.Now().Year())
}

// GetCurrentProgress 获取当前徽章系列进度
func (u *userOldBadge) getCurrentProgress(userPracticeCollectData *UserPracticeCollectData,
	categoryType, progressMode, progressAction, progressUnit int32) int64 {
	switch library.BadgeCategoryType(categoryType) {
	case library.BadgeCategoryTypeEnum.Acc:
		return u.getCategoryProgressModeAccProgress(progressMode, progressAction, progressUnit, userPracticeCollectData)
	case library.BadgeCategoryTypeEnum.Cycle:
		return u.getCategoryProgressModeCycleProgress()
	case library.BadgeCategoryTypeEnum.Once:
		// 暂时没有此类型
		return 0
	default:
		return 0
	}
}

// getUserPracticeDay 获取用户练习天数据
func (u *userOldBadge) getUserPracticeDayInfo(uid int64) (practiceDay, updateTime int64) {
	userPracticeDayCollect := dbpractice.TbCollect.GetItem(uid)
	if userPracticeDayCollect != nil {
		return userPracticeDayCollect.TotalPracticeDay, userPracticeDayCollect.UpdateTime
	}

	return 0, 0
}

// getUserPlayDataInfo 获取用户练习时长
func (u *userOldBadge) getUserPlayDataInfo(uid int64) (playTime, updateTime int64) {
	userPlayTimeCollect := playtime.TbCollect.GetItem(uid)
	if userPlayTimeCollect != nil {
		return userPlayTimeCollect.PlayTime, userPlayTimeCollect.UpdateTime
	}

	return 0, 0
}

// getUserLoginDataInfo 获取用户登录天数
func getUserLoginDataInfo(uid int64) (loginDay, loginTime int64) {
	userLoginDayList := user.TbUserLoginLog.GetList(uid)
	if userLoginDayList != nil {
		return int64(len(userLoginDayList)), userLoginDayList[len(userLoginDayList)-1].OriginalDate
	}

	return 0, 0
}

func (u *userOldBadge) getUserPracticeCollectData(uid int64) *UserPracticeCollectData {
	userPracticeCollectData := &UserPracticeCollectData{
		NoteBookJoinStatus: 2,
	}
	userCollectData, err := user.TbUserCollect.FindByUID(uid)
	if err != nil {
		logger.Warnf("查询用户练习汇总信息失败 err: %s", err)
	}

	if userCollectData != nil {
		userPracticeCollectData.TotalLoginDay = userCollectData.TotalLoginDay
		userPracticeCollectData.LastLoginTime = userCollectData.LastLoginTime
		userPracticeCollectData.TotalPracticeDay = userCollectData.TotalPracticeDay
		userPracticeCollectData.TotalPlayTime = userCollectData.TotalPlayTime
		userPracticeCollectData.LastPracticeTime = userCollectData.LastPracticeTime
		userPracticeCollectData.TotalPracticeWeek = userCollectData.TotalPracticeWeek
		userPracticeCollectData.CurrentWeekPracticeDay = userCollectData.CurrentWeekPracticeDay
		userPracticeCollectData.Birthday = userCollectData.Birthday
		userPracticeCollectData.TotalObtainPoints = userCollectData.TotalObtainPoints
		userPracticeCollectData.TotalConsumePoints = userCollectData.TotalConsumePoints
	}

	if userPracticeCollectData.TotalPracticeDay == 0 {
		userPracticeCollectData.TotalPracticeDay, userPracticeCollectData.LastPracticeUpdateTime =
			u.getUserPracticeDayInfo(uid)
	}

	if userPracticeCollectData.TotalPlayTime == 0 {
		userPracticeCollectData.TotalPlayTime, userPracticeCollectData.LastPlayUpdateTime = u.getUserPlayDataInfo(uid)
	}

	if userPracticeCollectData.TotalLoginDay == 0 {
		userPracticeCollectData.TotalLoginDay, userPracticeCollectData.LastLoginTime = getUserLoginDataInfo(uid)
	}

	userNoteBookInfo, err := note.TbCollect.GetItem(uid)
	if err == nil && userNoteBookInfo != nil && userNoteBookInfo.JoinStatus == 1 {
		userPracticeCollectData.NoteBookJoinStatus = userNoteBookInfo.JoinStatus
	}

	return userPracticeCollectData
}

// getPracticeWeekDays 获取周全勤记录
func (u *userOldBadge) getPracticeWeekDays(uid int64) []dbpractice.Week {
	practiceWeekList := make([]dbpractice.Week, 0)

	dateWeek := u.GetWeekCount(time.Now().Unix())
	for i := 1; i <= int(dateWeek-1); i++ {
		week := dbpractice.TbWeek.GetItem(uid, strconv.Itoa(i))
		if week != nil && week.PracticeDays >= 7 {
			practiceWeekList = append(practiceWeekList, *week)
		}
	}

	return practiceWeekList
}

func (u *userOldBadge) calcAccPracticeDayBadgeList(uid int64, categoryInfo *badge.Category, badgeList []*badge.Badge,
	currentProgress int32) []*badge.UserBadge {
	timeNow := time.Now().Unix()
	userOldBadgeList := make([]*badge.UserBadge, 0)
	practiceDayList := dbpractice.TbDay.GetList(uid, "ASC")

	practiceDayNum := int32(len(practiceDayList))
	practiceDayDiff := currentProgress - practiceDayNum
	if practiceDayDiff < 0 {
		practiceDayDiff = 0
	}
	for _, badgeInfo := range badgeList {
		userBadgeInfo := &badge.UserBadge{
			UID:         uid,
			CategoryID:  categoryInfo.ID,
			BadgeID:     badgeInfo.ID,
			Goal:        badgeInfo.Goal,
			Status:      int32(library.BadgeStatusEnum.Obtain),
			ReceiveTime: 0,
			ReadStatus:  int32(library.BadgeReadStatusEnum.No),
			CreateTime:  timeNow,
			UpdateTime:  timeNow,
		}

		if currentProgress >= badgeInfo.Goal {
			// 汇总数据和明细不同，无法追溯到具体时间的徽章，获取时间默认为0
			if practiceDayDiff >= badgeInfo.Goal {
				userBadgeInfo.ReceiveTime = 0
			} else {
				userBadgeInfo.ReceiveTime = practiceDayList[badgeInfo.Goal-practiceDayDiff-1].CreateTime
				receiveTime, _ := time.Parse("20060102", practiceDayList[badgeInfo.Goal-practiceDayDiff-1].DateIndex)
				userBadgeInfo.ReceiveTime = receiveTime.Unix()
			}

			userOldBadgeList = append(userOldBadgeList, userBadgeInfo)
		}
	}

	return userOldBadgeList
}

func (u *userOldBadge) calcAccPlayTimeBadgeList(uid int64, categoryInfo *badge.Category, badgeList []*badge.Badge,
	currentProgress int32) []*badge.UserBadge {
	var playTime, totalPlayTime, timeDiff, index int64
	timeNow := time.Now().Unix()
	userOldBadgeList := make([]*badge.UserBadge, 0)
	playTimeDayList := playtime.TbDay.GetList(uid)
	playTime = 0
	totalPlayTime = 0
	index = 0

	// 计算明细对应的总时长
	for _, playTimeInfo := range playTimeDayList {
		totalPlayTime += playTimeInfo.PlayTime
	}
	timeDiff = int64(currentProgress) - totalPlayTime
	if timeDiff < 0 {
		timeDiff = 0
	}

	for _, badgeInfo := range badgeList {
		if timeDiff/60 >= int64(badgeInfo.Goal) {
			userOldBadgeList = append(userOldBadgeList, &badge.UserBadge{
				UID:         uid,
				CategoryID:  categoryInfo.ID,
				BadgeID:     badgeList[index].ID,
				Goal:        badgeList[index].Goal,
				Status:      int32(library.BadgeStatusEnum.Obtain),
				ReceiveTime: 0,
				ReadStatus:  int32(library.BadgeReadStatusEnum.No),
				CreateTime:  timeNow,
				UpdateTime:  timeNow,
			})
			index++
			logger.Info("user play time badge:", uid, index, timeDiff, badgeInfo.Goal)
		}
	}

	for _, playTimeInfo := range playTimeDayList {
		playTime += playTimeInfo.PlayTime
		if index < int64(len(badgeList)) && (playTime+timeDiff)/60 >= int64(badgeList[index].Goal) {
			userBadgeInfo := &badge.UserBadge{
				UID:         uid,
				CategoryID:  categoryInfo.ID,
				BadgeID:     badgeList[index].ID,
				Goal:        badgeList[index].Goal,
				Status:      int32(library.BadgeStatusEnum.Obtain),
				ReceiveTime: playTimeInfo.CreateTime,
				ReadStatus:  int32(library.BadgeReadStatusEnum.No),
				CreateTime:  timeNow,
				UpdateTime:  timeNow,
			}

			userOldBadgeList = append(userOldBadgeList, userBadgeInfo)
			index++
		}
	}

	return userOldBadgeList
}

func (u *userOldBadge) calcAccPracticeWeekBadgeList(uid int64, categoryInfo *badge.Category, badgeList []*badge.Badge,
	currentProgress int32) []*badge.UserBadge {
	timeNow := time.Now().Unix()
	userOldBadgeList := make([]*badge.UserBadge, 0)
	practiceWeekList := u.getPracticeWeekDays(uid)

	practiceWeekNum := int32(len(practiceWeekList))
	practiceWeekDiff := currentProgress - practiceWeekNum
	if practiceWeekDiff < 0 {
		practiceWeekDiff = 0
	}
	for _, badgeInfo := range badgeList {
		userBadgeInfo := &badge.UserBadge{
			UID:         uid,
			CategoryID:  categoryInfo.ID,
			BadgeID:     badgeInfo.ID,
			Goal:        badgeInfo.Goal,
			Status:      int32(library.BadgeStatusEnum.Obtain),
			ReceiveTime: 0,
			ReadStatus:  int32(library.BadgeReadStatusEnum.No),
			CreateTime:  timeNow,
			UpdateTime:  timeNow,
		}

		// 汇总数据和明细不同，无法追溯到具体时间的徽章，获取时间默认为0
		if currentProgress >= badgeInfo.Goal {
			if practiceWeekDiff >= badgeInfo.Goal {
				userBadgeInfo.ReceiveTime = 0
			} else {
				userBadgeInfo.ReceiveTime = practiceWeekList[badgeInfo.Goal-practiceWeekDiff-1].CreateTime
			}

			logger.Info("user practice week badge:", uid, currentProgress,
				practiceWeekNum, practiceWeekDiff, badgeInfo.Goal)
			userOldBadgeList = append(userOldBadgeList, userBadgeInfo)
		}
	}

	return userOldBadgeList
}

func (u *userOldBadge) calcAccPracticeBadgeList(uid int64, categoryInfo *badge.Category, badgeList []*badge.Badge,
	currentProgress int32) []*badge.UserBadge {
	userOldBadgeList := make([]*badge.UserBadge, 0)

	switch library.BadgeCategoryProgressUnitType(categoryInfo.ProgressUnit) {
	case library.BadgeCategoryProgressUnitEnum.Day:
		userOldBadgeList = u.calcAccPracticeDayBadgeList(uid, categoryInfo, badgeList, currentProgress)
	case library.BadgeCategoryProgressUnitEnum.Minute:
		userOldBadgeList = u.calcAccPlayTimeBadgeList(uid, categoryInfo, badgeList, currentProgress)
	case library.BadgeCategoryProgressUnitEnum.Week:
		userOldBadgeList = u.calcAccPracticeWeekBadgeList(uid, categoryInfo, badgeList, currentProgress)
	}

	return userOldBadgeList
}

func (u *userOldBadge) calcAccLoginBadgeList(uid int64, categoryInfo *badge.Category,
	badgeList []*badge.Badge) []*badge.UserBadge {
	timeNow := time.Now().Unix()
	userOldBadgeList := make([]*badge.UserBadge, 0)

	userLoginDayList := user.TbUserLoginLog.GetList(uid)
	userLoginDayNum := int32(len(userLoginDayList))
	for _, badgeInfo := range badgeList {
		userBadgeInfo := &badge.UserBadge{
			UID:         uid,
			CategoryID:  categoryInfo.ID,
			BadgeID:     badgeInfo.ID,
			Goal:        badgeInfo.Goal,
			Status:      int32(library.BadgeStatusEnum.NotObtain),
			ReceiveTime: 0,
			ReadStatus:  int32(library.BadgeReadStatusEnum.No),
			CreateTime:  timeNow,
			UpdateTime:  timeNow,
		}

		if userLoginDayNum >= badgeInfo.Goal {
			userBadgeInfo.Status = int32(library.BadgeStatusEnum.Obtain)
			receiveTime, _ := time.Parse("20060102", userLoginDayList[badgeInfo.Goal-1].DayIndex)
			userBadgeInfo.ReceiveTime = receiveTime.Unix()
			userOldBadgeList = append(userOldBadgeList, userBadgeInfo)
		}
	}

	return userOldBadgeList
}

func (u *userOldBadge) calcAccBadgeList(uid int64, categoryInfo *badge.Category, badgeList []*badge.Badge,
	currentProgress int32) []*badge.UserBadge {
	switch library.BadgeCategoryProgressAction(categoryInfo.ProgressAction) {
	case library.BadgeCategoryProgressActionEnum.Practice:
		return u.calcAccPracticeBadgeList(uid, categoryInfo, badgeList, currentProgress)
	case library.BadgeCategoryProgressActionEnum.LogIn:
		return u.calcAccLoginBadgeList(uid, categoryInfo, badgeList)
	case library.BadgeCategoryProgressActionEnum.ObtainPoints,
		library.BadgeCategoryProgressActionEnum.ConsumePoints,
		library.BadgeCategoryProgressActionEnum.UserLevel:
		userOldBadgeList := make([]*badge.UserBadge, 0)

		for _, item := range badgeList {
			if item.Goal <= currentProgress {
				userOldBadgeList = append(userOldBadgeList, &badge.UserBadge{
					UID:         uid,
					CategoryID:  categoryInfo.ID,
					BadgeID:     item.ID,
					Goal:        item.Goal,
					Status:      int32(library.BadgeStatusEnum.Obtain),
					ReceiveTime: time.Now().Unix(),
					ReadStatus:  int32(library.BadgeReadStatusEnum.No),
					BadgeType:   item.BadgeType,
					CreateTime:  time.Now().Unix(),
					UpdateTime:  time.Now().Unix(),
				})
			}
		}

		return userOldBadgeList
	}

	return nil
}

// needCalcBirthdayBadgeInfo 是否达到计算生日徽章条件
func (u *userOldBadge) needCalcBirthdayBadgeInfo(goal, curYear int32, monthIndex, birthdayMonthIndex string) bool {
	// 未来年的生日徽章或本年未来月的生日徽章不补全
	if curYear > goal || (goal == curYear && monthIndex >= birthdayMonthIndex) {
		return true
	}

	return false
}

// getBirthdayBadgeReceiveTime 获取生日徽章获得时间
func (u *userOldBadge) getBirthdayBadgeReceiveTime(goal, year int32,
	birthdayMonth, month time.Month, practiceDayList []user.LoginLog) int64 {
	receiveTime, _ := time.Parse("20060102", practiceDayList[0].DayIndex)
	// 如果当年解锁生日，生日徽章获得时间为解锁当天
	if goal == year && birthdayMonth == month {
		receiveTime, _ = time.Parse("20060102", practiceDayList[len(practiceDayList)-1].DayIndex)
	}

	return receiveTime.Unix()
}

func (u *userOldBadge) calcBirthdayBadgeList(uid int64, categoryInfo *badge.Category, badgeList []*badge.Badge,
	birthday int32) []*badge.UserBadge {
	userOldBadgeList := make([]*badge.UserBadge, 0)
	timeNow := time.Now().Unix()
	year := int32(time.Now().Year())
	month := time.Now().Month()
	birthdayMonth := time.Unix(int64(birthday), 0).Month()
	monthIndex := time.Now().Format("01")
	birthdayMonthIndex := time.Unix(int64(birthday), 0).Format("01")

	if birthday == 0 {
		return nil
	}

	for _, badgeInfo := range badgeList {
		// 未来年的生日徽章或本年未来月的生日徽章不补全
		if !u.needCalcBirthdayBadgeInfo(badgeInfo.Goal, year, monthIndex, birthdayMonthIndex) {
			continue
		}

		monthBeginTime := time.Date(int(badgeInfo.Goal), birthdayMonth, 1, 0, 0, 0, 0, time.Local)
		monthBeginDateIndex := monthBeginTime.Format("20060102")
		monthEndDateIndex := time.Unix(monthBeginTime.Unix(), 0).AddDate(0, 1, -1).Format("20060102")
		userBadgeInfo := &badge.UserBadge{
			UID:         uid,
			CategoryID:  categoryInfo.ID,
			BadgeID:     badgeInfo.ID,
			Goal:        badgeInfo.Goal,
			Status:      int32(library.BadgeStatusEnum.NotObtain),
			ReceiveTime: 0,
			ReadStatus:  int32(library.BadgeReadStatusEnum.No),
			CreateTime:  timeNow,
			UpdateTime:  timeNow,
		}

		practiceDayList := user.TbUserLoginLog.GetListByRangeTime(uid, monthBeginDateIndex, monthEndDateIndex)
		if len(practiceDayList) > 0 {
			userBadgeInfo.Status = int32(library.BadgeStatusEnum.Obtain)
			userBadgeInfo.ReceiveTime = u.getBirthdayBadgeReceiveTime(badgeInfo.Goal,
				year, birthdayMonth, month, practiceDayList)
		} else {
			// 如果用户在当年生日 并且 还在生日月没有登录，则暂时不生成徽章
			if badgeInfo.Goal == year && monthIndex == birthdayMonthIndex {
				continue
			} else {
				userBadgeInfo.Status = int32(library.BadgeStatusEnum.OutOfDate)
			}
		}
		if userBadgeInfo.ReceiveTime > 0 {
			userOldBadgeList = append(userOldBadgeList, userBadgeInfo)
		}
	}

	return userOldBadgeList
}

// statBadgeList 生成用户历史徽章
func (u *userOldBadge) statOldBadgeList(uid int64, categoryInfo *badge.Category,
	badgeList []*badge.Badge, currentProgress, birthday int32) {
	userOldBadgeList := make([]*badge.UserBadge, 0)

	switch library.BadgeCategoryType(categoryInfo.Type) {
	case library.BadgeCategoryTypeEnum.Acc:
		userOldBadgeList = u.calcAccBadgeList(uid, categoryInfo, badgeList, currentProgress)
	case library.BadgeCategoryTypeEnum.Cycle:
		userOldBadgeList = u.calcBirthdayBadgeList(uid, categoryInfo, badgeList, birthday)
	}

	if len(userOldBadgeList) > 0 {
		if err := badge.TbUserBadge.BatchInsert(userOldBadgeList); err != nil {
			logger.Errorf("插入用户徽章列表失败, err: %s", err)
		}
		for _, v := range userOldBadgeList {
			// 天天练系列徽章在srv-badge发push通知(练习完成后有同步计算练习天数,避免push发重复)
			if v.CategoryID == 2 {
				continue
			}
			badgeDetail, err1 := badge.TbBadge.GetBadgeInfoByID(v.BadgeID)
			if err1 != nil {
				logger.Error("获取徽章信息失败", err1)
			}
			if badgeDetail != nil {
				pushData := service.PushData{
					UserID:      strconv.FormatInt(v.UID, 10),
					Title:       "恭喜你获得" + badgeDetail.Name + "徽章",
					Content:     badgeDetail.Desc,
					LinkType:    library.PushLinkTypeBadgeCenterEX,
					Logo:        badgeDetail.HighlightImageURL,
					Image:       badgeDetail.HighlightImageURL,
					ImagePad:    badgeDetail.HighlightImageURL,
					MessageType: library.PushMessageTypeSystem,
					SignID:      library.SignIDTypeEnum.SendUserNewBadge,
					ItemType:    library.ItemTypes.PushAll,
				}
				_ = service.Push.Send(&pushData)
			}
		}
	}
}

// ProcessUserPracticeBadge 生成练习相关徽章
func (u *userOldBadge) ProcessUserPracticeBadge(uid int64, badgeCategoryList []*badge.Category,
	allBadgeList []*badge.Badge, userBadgeList []*badge.UserBadge, taskTypeList []int32) {
	mpUserBadge := make(map[int64]*badge.UserBadge)
	mpCategoryBadgeList := make(map[int64][]*badge.Badge)

	for _, item := range allBadgeList {
		mpCategoryBadgeList[item.CategoryID] = append(mpCategoryBadgeList[item.CategoryID], item)
	}

	for _, item := range userBadgeList {
		mpUserBadge[item.BadgeID] = item
	}

	// 获取用户练习相关数据
	userPracticeCollectData := u.getUserPracticeCollectData(uid)

	categoryList := serviceBadgeCommon.GetBadgeCategoryListByTaskType(badgeCategoryList, taskTypeList)
	for _, categoryInfo := range categoryList {
		currentProgress := u.getCurrentProgress(userPracticeCollectData, categoryInfo.Type, categoryInfo.ProgressMode,
			categoryInfo.ProgressAction, categoryInfo.ProgressUnit)

		if _, ok := mpCategoryBadgeList[categoryInfo.ID]; !ok {
			continue
		}

		badgeList := make([]*badge.Badge, 0)
		for _, item := range mpCategoryBadgeList[categoryInfo.ID] {
			if int64(item.Goal) > currentProgress {
				continue
			}

			if _, ok := mpUserBadge[item.ID]; ok {
				continue
			}

			badgeList = append(badgeList, item)
		}

		if len(badgeList) == 0 {
			continue
		}

		// 生成未生成的徽章
		u.statOldBadgeList(uid, categoryInfo, badgeList, int32(currentProgress), userPracticeCollectData.Birthday)
	}
}

// ProcessUserBadge 异步生成徽章
func (u *userOldBadge) ProcessUserBadge(uid int64, taskTypeList []int32, badgeIDList []int64) error {
	// 如果有指定id直接发放的徽章，则直接发放，不必走下面逻辑，目前只有大礼包徽章这样发放
	if serviceBadgeCommon.InArray(library.BadgeTaskTypeEnum.BenefitGift, taskTypeList) &&
		len(badgeIDList) > 0 {
		ServiceOnceBadge.ProcessByBadgeIDList(uid, badgeIDList)
	}

	// 获取徽章系列列表
	categoryList, err := badge.TbCategory.GetBadgeCategoryList()
	if err != nil || len(categoryList) == 0 {
		return nil
	}

	// 获取徽章列表
	allBadgeList, err := badge.TbBadge.GetBadgeList()
	if err != nil || len(allBadgeList) == 0 {
		return nil
	}

	// 获取用户已经获取的徽章列表
	userBadgeList, err := badge.TbUserBadge.GetUserBadgeList(uid)
	if err != nil {
		logger.Warnf("获取用户徽章列表失败 err: %s", err)
	}

	// 如果有练习相关徽章
	if serviceBadgeCommon.InArray(library.BadgeTaskTypeEnum.Practice, taskTypeList) ||
		serviceBadgeCommon.InArray(library.BadgeTaskTypeEnum.LogIn, taskTypeList) ||
		serviceBadgeCommon.InArray(library.BadgeTaskTypeEnum.Birthday, taskTypeList) ||
		serviceBadgeCommon.InArray(library.BadgeTaskTypeEnum.ObtainPoints, taskTypeList) ||
		serviceBadgeCommon.InArray(library.BadgeTaskTypeEnum.ConsumePoints, taskTypeList) {
		u.ProcessUserPracticeBadge(uid, categoryList, allBadgeList, userBadgeList, taskTypeList)
	}

	// 如果有记录本徽章事件
	if serviceBadgeCommon.InArray(library.BadgeTaskTypeEnum.NoteBook, taskTypeList) {
		ServiceNoteBookBadge.ProcessUserBadge(uid, categoryList, allBadgeList, userBadgeList)
	}

	// 如果有用户等级徽章事件
	if serviceBadgeCommon.InArray(library.BadgeTaskTypeEnum.UserLevel, taskTypeList) {
		ServiceUserLevelBadge.ProcessUserBadge(uid, categoryList, allBadgeList, userBadgeList)
	}

	// 如果有一次性徽章事件
	if serviceBadgeCommon.InArray(library.BadgeTaskTypeEnum.Challenge, taskTypeList) ||
		serviceBadgeCommon.InArray(library.BadgeTaskTypeEnum.BirthdayBless, taskTypeList) ||
		serviceBadgeCommon.InArray(library.BadgeTaskTypeEnum.PracticeMeditation, taskTypeList) {
		ServiceOnceBadge.ProcessUserBadge(uid, categoryList, allBadgeList, userBadgeList, taskTypeList)
	}

	return nil
}
