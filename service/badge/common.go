package badge

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/badge"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type badgeCommon struct {
}

var serviceBadgeCommon badgeCommon

// getTaskTypeByProgressAction 根据徽章触发行为获取任务类型
func (c *badgeCommon) getTaskTypeByProgressAction(progressAction int32) library.BadgeTaskType {
	var taskType library.BadgeTaskType
	switch library.BadgeCategoryProgressAction(progressAction) {
	case library.BadgeCategoryProgressActionEnum.Practice:
		taskType = library.BadgeTaskTypeEnum.Practice
	case library.BadgeCategoryProgressActionEnum.LogIn:
		taskType = library.BadgeTaskTypeEnum.LogIn
	case library.BadgeCategoryProgressActionEnum.ObtainPoints:
		taskType = library.BadgeTaskTypeEnum.ObtainPoints
	case library.BadgeCategoryProgressActionEnum.ConsumePoints:
		taskType = library.BadgeTaskTypeEnum.ConsumePoints
	case library.BadgeCategoryProgressActionEnum.UserLevel:
		taskType = library.BadgeTaskTypeEnum.UserLevel
	default:
		taskType = library.BadgeTaskTypeEnum.Unknown
	}

	return taskType
}

// getCycleTaskTypeBySourceType 根据触发类型获取周期性徽章任务类型
func (c *badgeCommon) getCycleTaskTypeBySourceType(sourceType int32) library.BadgeTaskType {
	var taskType library.BadgeTaskType
	switch library.BadgeCategorySourceType(sourceType) {
	case library.BadgeCategorySourceTypeEnum.Birthday:
		taskType = library.BadgeTaskTypeEnum.Birthday
	case library.BadgeCategorySourceTypeEnum.NoteBook:
		taskType = library.BadgeTaskTypeEnum.NoteBook
	default:
		taskType = library.BadgeTaskTypeEnum.Unknown
	}

	return taskType
}

// getOnceTaskTypeBySourceType 根据触发类型获取一次性徽章任务类型
func (c *badgeCommon) getOnceTaskTypeBySourceType(sourceType int32) library.BadgeTaskType {
	var taskType library.BadgeTaskType

	switch library.BadgeCategorySourceType(sourceType) {
	case library.BadgeCategorySourceTypeEnum.Challenge:
		taskType = library.BadgeTaskTypeEnum.Challenge
	case library.BadgeCategorySourceTypeEnum.PracticeMeditation:
		taskType = library.BadgeTaskTypeEnum.PracticeMeditation
	case library.BadgeCategorySourceTypeEnum.BirthdayBless:
		taskType = library.BadgeTaskTypeEnum.BirthdayBless
	case library.BadgeCategorySourceTypeEnum.BenefitGift:
		taskType = library.BadgeTaskTypeEnum.BenefitGift
	default:
		taskType = library.BadgeTaskTypeEnum.Unknown
	}

	return taskType
}

// getTaskTypeByCategoryTypeAndSourceType 根据徽章类型和触发规则获取任务类型
func (c *badgeCommon) getTaskTypeByCategoryTypeAndSourceType(categoryType, sourceType int32) library.BadgeTaskType {
	taskType := library.BadgeTaskTypeEnum.Unknown

	if library.BadgeCategoryType(categoryType) == library.BadgeCategoryTypeEnum.Cycle {
		taskType = c.getCycleTaskTypeBySourceType(sourceType)
	}

	// 正常情况下不会走到此处，一次性徽章与周萌沟通过，徽章系列配置触发规则使用指定id下发，具体徽章中如果触发规则不同再重新配置
	if library.BadgeCategoryType(categoryType) == library.BadgeCategoryTypeEnum.Once {
		taskType = c.getOnceTaskTypeBySourceType(sourceType)
	}

	return taskType
}

// getTaskType 获取徽章系列对应的任务类型
func (c *badgeCommon) getTaskType(progressAction, categoryType, sourceType int32) library.BadgeTaskType {
	taskType := c.getTaskTypeByCategoryTypeAndSourceType(categoryType, sourceType)
	if taskType != library.BadgeTaskTypeEnum.Unknown {
		return taskType
	}

	return c.getTaskTypeByProgressAction(progressAction)
}

func (c *badgeCommon) InArray(taskType library.BadgeTaskType, taskTypeList []int32) bool {
	for _, item := range taskTypeList {
		if library.BadgeTaskType(item) == taskType {
			return true
		}
	}
	return false
}

// CheckBadgeCategoryTimeCond 判断徽章系列时间是否合法
func (c *badgeCommon) CheckBadgeCategoryTimeCond(startTime, endTime int64) bool {
	timeNow := time.Now().Unix()

	if startTime != 0 && startTime > timeNow || endTime != 0 && endTime < timeNow {
		return false
	}

	return true
}

// GetBadgeCategoryListByTaskType 根据任务类型获取徽章系列列表(一次性徽章与周萌沟通过，徽章系列配置指定id下发，具体徽章中配置具体的类型)
func (c *badgeCommon) GetBadgeCategoryListByTaskType(badgeCategoryList []*badge.Category,
	taskTypeList []int32) []*badge.Category {
	result := make([]*badge.Category, 0)

	for _, item := range badgeCategoryList {
		if !c.CheckBadgeCategoryTimeCond(item.StartTime, item.EndTime) {
			continue
		}

		taskType := c.getTaskType(item.ProgressAction, item.Type, item.SourceType)
		if !c.InArray(taskType, taskTypeList) {
			continue
		}

		result = append(result, item)
	}

	return result
}

// GetOnceBadgeListByTaskType 根据任务类型获取徽章列表
func (c *badgeCommon) GetOnceBadgeListByTaskType(badgeList []*badge.Badge, taskTypeList []int32) []*badge.Badge {
	result := make([]*badge.Badge, 0)

	for _, item := range badgeList {
		taskType := c.getOnceTaskTypeBySourceType(item.SourceType)
		if !c.InArray(taskType, taskTypeList) {
			continue
		}

		result = append(result, item)
	}

	return result
}
