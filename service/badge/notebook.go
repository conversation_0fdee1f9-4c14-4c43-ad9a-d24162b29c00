package badge

import (
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/badge"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/notebook"
)

type noteBookBadge struct {
}

var ServiceNoteBookBadge noteBookBadge

// statNoteBookBadgeList 生成记录本历史徽章
func (n *noteBookBadge) statNoteBookBadgeList(uid int64, categoryInfo *badge.Category,
	badgeList []*badge.Badge, userBadgeList []*badge.UserBadge) {
	mpUserBadge := make(map[int64]*badge.UserBadge)
	monthStr := time.Now().AddDate(0, -1, 0).Format("200601")
	i, _ := strconv.ParseInt(monthStr, 10, 32)
	monthIndex := int32(i)

	for _, item := range userBadgeList {
		mpUserBadge[item.BadgeID] = item
	}

	for i := range badgeList {
		item := badgeList[i]
		if item.Goal > monthIndex {
			continue
		}

		badgeMonthIndex := strconv.Itoa(int(item.Goal))
		monthBeginTime, _ := time.Parse("200601", badgeMonthIndex)
		monthBeginDate := time.Date(monthBeginTime.Year(),
			monthBeginTime.Month(), 1, 0, 0, 0, 0, time.Local).Format("20060102")
		monthEndDate := monthBeginTime.AddDate(0, 1, -1).Format("20060102")
		monthEndTime, _ := time.Parse("20060102", monthEndDate)
		receiveTime := time.Date(monthEndTime.Year(), monthEndTime.Month(),
			monthEndTime.Day(), 23, 59, 59, 0, time.Local).Unix()
		monthDayCount := int(monthEndTime.Unix()-monthBeginTime.Unix())/86400 + 1

		if _, ok := mpUserBadge[item.ID]; ok {
			continue
		}

		badgeDynamicRecordStatus := notebook.ServiceNoteBook.JudgeNoteBookBadgeDynamicRecordStatus(uid, monthBeginDate,
			monthEndDate, badgeMonthIndex, monthDayCount)
		userBadge := &badge.UserBadge{
			UID:         uid,
			CategoryID:  categoryInfo.ID,
			BadgeID:     item.ID,
			Goal:        item.Goal,
			Status:      4,
			ReceiveTime: 0,
			ReadStatus:  0,
		}
		if badgeDynamicRecordStatus == library.NoteBookBadgeDynamicStatusEnum.PracticeRecordAllFinished ||
			badgeDynamicRecordStatus == library.NoteBookBadgeDynamicStatusEnum.RecordFinished {
			userBadge.ReceiveTime = receiveTime
			userBadge.ReadStatus = int32(library.BadgeReadStatusEnum.No)
			userBadge.Status = 3
		}
		if err := userBadge.Save(); err != nil {
			logger.Warnf("插入用户记录本徽章失败, err: %s", err)
		}

		content := notebook.ServiceNoteBook.GetNoteBookBadgeDynamicRecordContent(badgeDynamicRecordStatus)
		if content != "" {
			notebook.ServiceNoteBook.InsertNoteBookDynamicRecord(uid, "", content,
				library.NoteBookPracticeRecordBackgroundImage, int32(library.NoteBookContentTypeEnum.AllPractice),
				receiveTime)
		}
	}
}

// ProcessUserBadge 生成记录本徽章
func (n *noteBookBadge) ProcessUserBadge(uid int64, badgeCategoryList []*badge.Category,
	badgeList []*badge.Badge, userBadgeList []*badge.UserBadge) {
	mpCategoryBadgeList := make(map[int64][]*badge.Badge)

	for i := range badgeList {
		mpCategoryBadgeList[badgeList[i].CategoryID] = append(mpCategoryBadgeList[badgeList[i].CategoryID],
			badgeList[i])
	}

	noteBookBadgeTaskTypeList := []int32{int32(library.BadgeTaskTypeEnum.NoteBook)}
	categoryList := serviceBadgeCommon.GetBadgeCategoryListByTaskType(badgeCategoryList, noteBookBadgeTaskTypeList)
	for i := range categoryList {
		item := categoryList[i]
		if _, ok := mpCategoryBadgeList[item.ID]; !ok {
			continue
		}

		n.statNoteBookBadgeList(uid, item, mpCategoryBadgeList[item.ID], userBadgeList)
	}
}
