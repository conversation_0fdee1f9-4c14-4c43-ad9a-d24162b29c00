package badge

import (
	"encoding/json"
	"strconv"
	"time"

	"github.com/prometheus/common/log"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/badge"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/challenge"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/birthdaywish"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type onceBadge struct{}

var ServiceOnceBadge onceBadge

// checkSelfControlSuccess 检查用户挑战赛是否成功
func (o *onceBadge) checkSelfControlSuccess(uid, selfControlProductID int64) bool {
	isSuccess := false

	selfControlToUserList := challenge.TbUser.GetListByUserAndProduct(uid, selfControlProductID)
	if len(selfControlToUserList) == 0 {
		return isSuccess
	}

	for i := range selfControlToUserList {
		if library.ChallengeToUserStatus(selfControlToUserList[i].Status) == library.ChallengeToUserStatusEnum.Success {
			isSuccess = true
			break
		}
	}

	return isSuccess
}

// statChallengeBadgeList 生成挑战赛徽章
func (o *onceBadge) statChallengeBadgeList(uid int64, badgeInfo *badge.Badge) {
	isSuccess := false
	var resourceContentList []string

	if err := json.Unmarshal([]byte(badgeInfo.ResourceContentList), &resourceContentList); err != nil {
		log.Warnf("徽章触发规则关联业务资源id列表失败, err: %s", err)
		return
	}
	if len(resourceContentList) == 0 {
		log.Warn("徽章关联的挑战赛id为空")
		return
	}

	// 判断用户是否完成指定挑战赛
	for _, item := range resourceContentList {
		selfControlProductID, err := strconv.ParseInt(item, 10, 64)
		if err != nil {
			log.Warnf("挑战赛id转换错误, err: %s", err)
		}

		if o.checkSelfControlSuccess(uid, selfControlProductID) {
			isSuccess = true
			break
		}
	}

	if !isSuccess {
		return
	}

	// 生成挑战赛徽章
	userBadge := &badge.UserBadge{
		UID:         uid,
		CategoryID:  badgeInfo.CategoryID,
		BadgeID:     badgeInfo.ID,
		Goal:        badgeInfo.Goal,
		Status:      int32(library.BadgeStatusEnum.Obtain),
		ReceiveTime: time.Now().Unix(),
		ReadStatus:  int32(library.BadgeReadStatusEnum.No),
	}
	if err := userBadge.Save(); err != nil {
		log.Warnf("保存用户挑战赛徽章失败")
	}
}

// statPracticeMeditation 生成冥想徽章
func (o *onceBadge) statPracticeMeditation(uid int64, badgeInfo *badge.Badge) {
	userBadge := &badge.UserBadge{
		UID:         uid,
		CategoryID:  badgeInfo.CategoryID,
		BadgeID:     badgeInfo.ID,
		Goal:        badgeInfo.Goal,
		Status:      int32(library.BadgeStatusEnum.Obtain),
		ReceiveTime: time.Now().Unix(),
		ReadStatus:  int32(library.BadgeReadStatusEnum.No),
	}
	if err := userBadge.Save(); err != nil {
		log.Warnf("保存用户冥想徽章失败")
	}
}

// statBirthdayBlessMeditation 生成生日祝福徽章
func (o *onceBadge) statBirthdayBlessMeditation(uid int64, categoryInfo *badge.Category, badgeInfo *badge.Badge) {
	likeCount := birthdaywish.TbLikeRecord.GetCount(uid, int32(categoryInfo.StartTime), int32(categoryInfo.EndTime))

	if likeCount >= int64(badgeInfo.Goal) {
		userBadge := &badge.UserBadge{
			UID:         uid,
			CategoryID:  categoryInfo.ID,
			BadgeID:     badgeInfo.ID,
			Goal:        badgeInfo.Goal,
			Status:      int32(library.BadgeStatusEnum.Obtain),
			ReceiveTime: time.Now().Unix(),
			ReadStatus:  int32(library.BadgeReadStatusEnum.No),
		}
		if err := userBadge.Save(); err != nil {
			log.Warnf("保存用户冥想徽章失败")
		}
	}
}

// statOnceBadgeList 生成一次性徽章
func (o *onceBadge) statOnceBadgeList(uid int64, categoryInfo *badge.Category, badgeInfo *badge.Badge) {
	switch library.BadgeCategorySourceType(badgeInfo.SourceType) {
	case library.BadgeCategorySourceTypeEnum.Challenge:
		o.statChallengeBadgeList(uid, badgeInfo)
	case library.BadgeCategorySourceTypeEnum.PracticeMeditation:
		o.statPracticeMeditation(uid, badgeInfo)
	case library.BadgeCategorySourceTypeEnum.BirthdayBless:
		o.statBirthdayBlessMeditation(uid, categoryInfo, badgeInfo)
	default:
		logger.Warnf("不支持此类型")
	}
}

// ProcessUserBadge 生成一次性系列徽章
func (o *onceBadge) ProcessUserBadge(uid int64, badgeCategoryList []*badge.Category, badgeList []*badge.Badge,
	userBadgeList []*badge.UserBadge, taskTypeList []int32) {
	mpUserBadgeList := make(map[int64]*badge.UserBadge)
	mpBadgeCategoryList := make(map[int64]*badge.Category)

	for _, item := range userBadgeList {
		mpUserBadgeList[item.BadgeID] = item
	}

	for i := range badgeCategoryList {
		mpBadgeCategoryList[badgeCategoryList[i].ID] = badgeCategoryList[i]
	}

	onceBadgeList := serviceBadgeCommon.GetOnceBadgeListByTaskType(badgeList, taskTypeList)
	for _, item := range onceBadgeList {
		// 如果已经获得，则跳过
		if _, ok := mpUserBadgeList[item.ID]; ok {
			continue
		}

		if _, ok := mpBadgeCategoryList[item.CategoryID]; !ok {
			continue
		}

		if !serviceBadgeCommon.CheckBadgeCategoryTimeCond(mpBadgeCategoryList[item.CategoryID].StartTime,
			mpBadgeCategoryList[item.CategoryID].EndTime) {
			continue
		}

		// 2021/07/15 许弘毅 一次性系列的子徽章如果拥有自己的过期时间,则还需要判断自己的时间区间
		if item.EndTime != 0 && !serviceBadgeCommon.CheckBadgeCategoryTimeCond(item.StartTime, item.EndTime) {
			logger.Infof("uid:%d 获取 %s 徽章时,发现徽章过期,徽章ID:%d", uid, item.Name, item.ID)
			continue
		}
		o.statOnceBadgeList(uid, mpBadgeCategoryList[item.CategoryID], item)
	}
}

// ProcessUserBadge 生成指定id的徽章
func (o *onceBadge) ProcessByBadgeIDList(uid int64, badgeIDList []int64) {
	if len(badgeIDList) == 0 {
		return
	}
	for _, badgeID := range badgeIDList {
		badgeInfo, err := badge.TbBadge.GetBadgeInfoByID(badgeID)
		if err != nil {
			logger.Error("指定徽章ID发放徽章，获取徽章信息失败", uid, badgeID, err)
			continue
		}
		if badgeInfo == nil {
			logger.Warn("指定徽章ID发放徽章，徽章ID无效", uid, badgeID)
			continue
		}
		// 2021/07/15 许弘毅 一次性系列的子徽章如果拥有自己的过期时间,则还需要判断自己的时间区间
		if badgeInfo.EndTime != 0 && !serviceBadgeCommon.CheckBadgeCategoryTimeCond(badgeInfo.StartTime, badgeInfo.EndTime) {
			logger.Infof("uid:%d 获取 %s 徽章时,发现徽章过期,因此不发放徽章,徽章ID:%d", uid, badgeInfo.Name, badgeInfo.ID)
			continue
		}
		userBadge, err := badge.TbUserBadge.GetUserBadgeByBadgeID(uid, badgeID)
		if err != nil {
			logger.Error("指定徽章ID发放徽章，用户徽章查询失败", uid, badgeID, err)
			continue
		}
		if userBadge == nil {
			userBadge := &badge.UserBadge{
				UID:         uid,
				CategoryID:  badgeInfo.CategoryID,
				BadgeID:     badgeInfo.ID,
				Goal:        badgeInfo.Goal,
				Status:      int32(library.BadgeStatusEnum.Obtain),
				ReceiveTime: time.Now().Unix(),
				ReadStatus:  int32(library.BadgeReadStatusEnum.No),
			}
			if err := userBadge.Save(); err != nil {
				log.Warnf("保存用户指定生成徽章失败 uid: %d, badge_id: %d", uid, badgeID)
			}
		}
	}
}
