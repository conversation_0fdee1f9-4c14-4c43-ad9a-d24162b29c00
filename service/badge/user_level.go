package badge

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/badge"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type userLevelBadge struct {
}

var ServiceUserLevelBadge userLevelBadge

// statUserLevelBadgeList 生成用户等级徽章
func (u *userLevelBadge) statUserLevelBadgeList(uid int64, categoryInfo *badge.Category,
	badgeList []*badge.Badge, userBadgeList []*badge.UserBadge) {
	mpUserBadge := make(map[int64]*badge.UserBadge)
	userLevelInfo := user.TbAccountInfoLevelInfo.GetByUID(uid)

	if userLevelInfo == nil {
		return
	}

	for _, item := range userBadgeList {
		mpUserBadge[item.BadgeID] = item
	}

	for _, item := range badgeList {
		if item.Goal > int32(userLevelInfo.Level) {
			continue
		}

		if _, ok := mpUserBadge[item.ID]; ok {
			continue
		}

		// 不历史数据时创建时间需求兼容
		userBadge := &badge.UserBadge{
			UID:         uid,
			CategoryID:  categoryInfo.ID,
			BadgeID:     item.ID,
			Goal:        item.Goal,
			Status:      int32(library.BadgeStatusEnum.Obtain),
			ReceiveTime: time.Now().Unix(),
			ReadStatus:  int32(library.BadgeReadStatusEnum.No),
		}

		if item.Goal < int32(userLevelInfo.Level) {
			userBadge.ReceiveTime = 0
		}

		if err := userBadge.CheckAndSave(); err != nil {
			logger.Warnf("保存用户等级记录本失败, err: %s, userBadge:%+v", err.Error(), userBadge)
		} else {
			logger.Debugf("保存用户等级记录本成功, userBadge:%+v", userBadge)
		}
	}
}

// ProcessUserBadge 生成用户等级徽章
func (u *userLevelBadge) ProcessUserBadge(uid int64, badgeCategoryList []*badge.Category,
	badgeList []*badge.Badge, userBadgeList []*badge.UserBadge) {
	mpCategoryBadgeList := make(map[int64][]*badge.Badge)

	for _, item := range badgeList {
		mpCategoryBadgeList[item.CategoryID] = append(mpCategoryBadgeList[item.CategoryID], item)
	}

	userLevelBadgeTaskTypeList := []int32{int32(library.BadgeTaskTypeEnum.UserLevel)}
	categoryList := serviceBadgeCommon.GetBadgeCategoryListByTaskType(badgeCategoryList, userLevelBadgeTaskTypeList)
	for _, item := range categoryList {
		if _, ok := mpCategoryBadgeList[item.ID]; !ok {
			continue
		}
		u.statUserLevelBadgeList(uid, item, mpCategoryBadgeList[item.ID], userBadgeList)
	}
}
