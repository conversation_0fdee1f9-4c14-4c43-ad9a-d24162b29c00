package crack

import (
	"encoding/json"
	"net/url"
	"strconv"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	lib "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type versionCrack struct {
	UID                 int64  `json:"uid"`
	ActionLogType       int32  `json:"action_log_type"`
	ProgramID           int64  `json:"program_id"`
	SessionID           int64  `json:"session_id"`
	SessionIndex        int32  `json:"session_index"`
	SubSessionIndex     int32  `json:"sub_session_index"`
	PracticeCurrentTime int64  `json:"practice_current_time"` // 当时练习时间
	CreateTime          int64  `json:"create_time"`           // 离线上报当时练习时间(取上报失败时的时间)
	Version             string `json:"version"`
	DeviceType          int32  `json:"device_type"`
	ScreenType          int32  `json:"screen_type"`
	Channel             int64  `json:"channel"`
	DType               int32  `json:"d_type"`
	O2SessionID         int64  `json:"o2_session_id"`
	PlayTime            int64  `json:"play_time"`
	DeviceID            string `json:"device_id"`
}

type VersionCrack versionCrack

// MCrack water 2020-06-07 破解版入口文件
func MCrack(params []byte) error {
	var bean versionCrack
	if err := json.Unmarshal(params, &bean); err != nil {
		logger.Error("破解版数据解析失败", string(params))
		return err
	}

	if _, ok := lib.CheckActionLog[bean.ActionLogType]; !ok {
		return nil
	}

	// ios 不做破解验证
	// if bean.DType == 2 || bean.DType == 3 {
	//  	return nil
	// }

	switch bean.ActionLogType {
	case lib.FinishSessionInProgram, lib.PlayQuitSessionInProgram:
		bean.checkCrackProgram()
	case lib.PracticeFinish, lib.PlayQuitSession:
		bean.checkCrackSession()
	default:
		logger.Error("破解版数据类型错误")
	}

	return nil
}

func (c *versionCrack) checkCrackProgram() {
	params := url.Values{}
	params.Set("uid", strconv.FormatInt(c.UID, 10))
	params.Set("program_id", strconv.FormatInt(c.ProgramID, 10))
	params.Set("session_id", strconv.FormatInt(c.SessionID, 10))
	params.Set("session_index", strconv.FormatInt(int64(c.SessionIndex), 10))
	params.Set("practice_current_time", strconv.FormatInt(c.PracticeCurrentTime, 10))
	params.Set("create_time", strconv.FormatInt(c.CreateTime, 10))
	params.Set("version", c.Version)
	params.Set("device_type", strconv.FormatInt(int64(c.DeviceType), 10))
	params.Set("screen_type", strconv.FormatInt(int64(c.ScreenType), 10))
	params.Set("o2_session_id", strconv.FormatInt(c.O2SessionID, 10))
	params.Set("play_time", strconv.FormatInt(c.PlayTime, 10))
	params.Set("deviceId", c.DeviceID)
	client := requests.New620Client(config.Get().Php620Address)
	client.SetHeader("D-TYPE", strconv.FormatInt(int64(c.DType), 10))
	client.SetHeader("DAILYYOGA-CHANNEL", strconv.FormatInt(c.Channel, 10))
	resp, err := client.PostForm(lib.CrackProgramURL, params)
	if err != nil {
		logger.Error(err)
	}
	if resp == nil || !resp.IsOK() {
		logger.Errorf("send asyncUserInvite error. status: %d, params: %s", resp.StatusCode, params.Encode())
	}
}

func (c *versionCrack) checkCrackSession() {
	params := url.Values{}
	params.Set("uid", strconv.FormatInt(c.UID, 10))
	params.Set("session_id", strconv.FormatInt(c.SessionID, 10))
	params.Set("practice_current_time", strconv.FormatInt(c.PracticeCurrentTime, 10))
	params.Set("create_time", strconv.FormatInt(c.CreateTime, 10))
	params.Set("version", c.Version)
	params.Set("device_type", strconv.FormatInt(int64(c.DeviceType), 10))
	params.Set("screen_type", strconv.FormatInt(int64(c.ScreenType), 10))
	params.Set("play_time", strconv.FormatInt(c.PlayTime, 10))
	params.Set("deviceId", c.DeviceID)
	client := requests.New620Client(config.Get().Php620Address)
	client.SetHeader("D-TYPE", strconv.FormatInt(int64(c.DType), 10))
	client.SetHeader("DAILYYOGA-CHANNEL", strconv.FormatInt(c.Channel, 10))
	resp, err := client.PostForm(lib.CrackSessionURL, params)
	if err != nil {
		logger.Error(err)
	}
	if resp == nil || !resp.IsOK() {
		logger.Errorf("send asyncUserInvite error. status: %d, params: %s", resp.StatusCode, params.Encode())
	}
}
