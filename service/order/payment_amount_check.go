package order

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

// PaymentAmountCheckRequest 支付金额检查请求结构体
type PaymentAmountCheckRequest struct {
	ProjectName   string  `json:"project_name"`   // 项目名称
	UID           int64   `json:"uid"`            // 用户UID
	ProductID     int64   `json:"product_id"`     // 产品ID
	ProductType   string  `json:"product_type"`   // 产品类型
	ProductAmount float64 `json:"product_amount"` // 产品金额
	OrderAmount   float64 `json:"order_amount"`   // 下单金额
	OrderID       string  `json:"order_id"`       // 订单ID
	Scene         string  `json:"scene"`          // 场景类型
}

// PaymentAmountCheck 支付金额检查函数
func PaymentAmountCheck(req *PaymentAmountCheckRequest) {
	if req.Scene == "" {
		req.Scene = "巡检"
	}
	// 将请求转换为ft包需要的格式
	logger.Infof("PaymentAmountCheck 项目名称：%s 订单ID：%s 产品ID：%d 产品类型：%s 产品金额：%f 下单金额：%f 场景类型：%s",
		req.ProjectName, req.OrderID, req.ProductID, req.ProductType, req.ProductAmount, req.OrderAmount, req.Scene)
	content := fmt.Sprintf("## <font color=\"red\">🟥 支付金额异常检测</font>\n"+
		"\n**项目名称**: <font color=\"comment\">%s</font>"+
		"\n**订单ID**: <font color=\"comment\">%s</font>"+
		"\n**产品ID**: <font color=\"comment\">%d</font>"+
		"\n**产品类型**: <font color=\"comment\">%s</font>"+
		"\n**产品金额**: <font color=\"red\">%.2f</font>"+
		"\n**下单金额**: <font color=\"red\">%.2f</font>"+
		"\n**场景类型**: <font color=\"info\">%s</font>",
		req.ProjectName, req.OrderID, req.ProductID, req.ProductType, req.ProductAmount, req.OrderAmount, req.Scene)
	err := sendWechatBotMessage(config.Get().WebhookURL, content)
	if err != nil {
		logger.Errorf("PaymentAmountCheck 发送企业微信机器人消息失败: %v", err)
	}
}

type WechatBotMessage struct {
	MsgType  string `json:"msgtype"`
	Markdown struct {
		Content string `json:"content"`
	} `json:"markdown"`
}

// sendWechatBotMessage 发送企业微信机器人消息
// nolint
func sendWechatBotMessage(webhookURL, content string) error {
	message := WechatBotMessage{
		MsgType: "markdown",
	}
	message.Markdown.Content = content

	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("消息序列化失败: %v", err)
	}

	resp, err := http.Post(webhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("发送消息失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("发送消息失败，状态码: %d", resp.StatusCode)
	}
	return nil
}
