package order

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"

	"gitlab.dailyyoga.com.cn/server/go-artifact/goroutine"
	"gitlab.dailyyoga.com.cn/server/h2-artifact/delayqueue"

	pbcomm "gitlab.dailyyoga.com.cn/protogen/srv-usercenter-go/common"
	pb "gitlab.dailyyoga.com.cn/protogen/srv-usercenter-go/iap"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type iosOrder struct{}

var ServiceIosOrder iosOrder

type AppRequest struct {
	Receipt string `json:"receipt"`
	Version string `json:"version"`
}

type CreateOrder4IosIapPayload struct {
	LogID      int64  `json:"log_id"`
	Times      int64  `json:"times"`
	DeviceType int32  `json:"device_type"`
	Version    string `json:"version"`
	ScreenType int32  `json:"screen_type"`
	Channel    int32  `json:"channel"`
	OSType     int32  `json:"os_type"`
	DeviceID   string `json:"device_id"`
	IPAds      string `json:"ip_ads"`
	AppAlias   int32  `json:"app_alias"`
	AppVersion string `json:"app_version"`
}

// CreateOrder4IosIap 处理iOS IAP 支付成功后回调
func (s *iosOrder) CreateOrder4IosIap(data *CreateOrder4IosIapPayload) {
	logger.Info("CreateOrder4IosIap, log_id:", data.LogID)
	iosData := data
	goroutine.GoSafely(
		func() {
			iosData.asyncCreateOrder4IosIap()
		},
	)
}

type IosIapResp struct {
	Status int `json:"status"`
}

// asyncCreateOrder4IosIap Prince 处理iOS IAP  订单
func (i *CreateOrder4IosIapPayload) asyncCreateOrder4IosIap() {
	rpc := grpc.GetUserCenterClient()
	res, err := rpc.PayOrderApple(context.Background(), &pb.PayOrderAppleRequest{
		AppleLog: i.LogID,
		AppDevice: &pbcomm.AppDevice{
			DeviceType: i.DeviceType,
			Version:    i.Version,
			ScreenType: i.ScreenType,
			Channel:    i.Channel,
			OSType:     i.OSType,
			DeviceID:   i.DeviceID,
			IPAds:      i.IPAds,
			AppAlias:   i.AppAlias,
			AppVersion: i.AppVersion,
		},
	})
	if err != nil || res.GetResultCode() != 1001 {
		logger.Warn("CreateOrder4IosIap:客户端请求失败", i.LogID, err, res.GetResultCode(), res.GetMsg())
		i.delayQueue()
		return
	}
}

func (i *CreateOrder4IosIapPayload) IosIapRequest(iTunesDomain string,
	appRequest AppRequest) (*requests.Response, error) {
	logger.Infof("CreateOrder4IosIap 请求的URL:%s logID:%d ", iTunesDomain, i.LogID)
	c := newItunesClient(iTunesDomain)
	postData := make(map[string]string)
	postData["password"] = config.Get().AppleConfig.Password
	postData["receipt-data"] = appRequest.Receipt
	requestData, err := json.Marshal(postData)
	if err != nil {
		logger.Warn("CreateOrder4IosIap, 请求参数编码失败， log_id：", i.LogID)
		return nil, err
	}
	toSend := bytes.NewBuffer(requestData)
	resp, err := c.Post(config.Get().AppleConfig.URI, toSend)
	logger.Infof("CreateOrder4IosIap 请求的返回结果:%v", resp)
	if err != nil {
		i.delayQueue()
		logger.Warn("CreateOrder4IosIap, iTunes 请求失败， log_id:", i.LogID, err)
		return nil, err
	}
	logger.Infof("CreateOrder4IosIap 请求的返回结果 StatusCode:%d", resp.StatusCode)
	if !resp.IsOK() {
		i.delayQueue()
		logger.Warn("CreateOrder4IosIap receipt unvalid, log_id:", i.LogID)
		return nil, fmt.Errorf("CreateOrder4IosIap receipt unvalid, log_id:%d", i.LogID)
	}
	return resp, nil
}

func newItunesClient(domain string) *requests.Client {
	c := &requests.Client{}
	c.UseHTTPS()
	c.SetBaseDomain(domain)
	return c
}

func (i *CreateOrder4IosIapPayload) delayQueue() {
	i.Times++
	appleConf := config.Get().AppleConfig
	if appleConf.Retry == 0 {
		appleConf.Retry = 30
	}
	if i.Times > appleConf.Retry {
		logger.Error("asyncCreateOrder4IosIap 重试次数已超出配置次数:", appleConf.Retry)
		return
	}
	data, err := json.Marshal(i)
	if err != nil {
		logger.Error("asyncCreateOrder4IosIap 延迟队列format json err", err.Error())
		return
	}
	// 延迟30s
	if err := delayqueue.AddToQueue(context.Background(),
		cache.GetYogaRedis(),
		library.Order4IosIapQueueKey, string(data), int64(library.QueueDelaySeconds)); err != nil {
		logger.Error("asyncCreateOrder4IosIap 延迟队列错误", err.Error())
	}
	logger.Infof("asyncCreateOrder4IosIap 重试 logid:%d times:%d", i.LogID, i.Times)
}
