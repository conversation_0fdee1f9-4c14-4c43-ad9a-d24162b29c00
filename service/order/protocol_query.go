package order

import (
	"context"
	"encoding/xml"
	"fmt"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/protogen/pay-center-go/paycenter"
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/gopay"
	"gitlab.dailyyoga.com.cn/server/gopay/pkg/xhttp"
	"gitlab.dailyyoga.com.cn/server/gopay/wechat"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/order"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"
)

type PQuery struct {
	OrderID string `json:"order_id"`
}

const (
	AndroidAlipayContract = 144
	IosAlipayContract     = 147
	H5AlipayContract      = 146
	WxContract            = 17
	H5WxContract          = 145
	aliPQCacheKey         = "ali:pq:"
	NO                    = 2
	Yes                   = 1
	wxSuccess             = "SUCCESS"
)

func HandleProtocolQuery(d *PQuery) error {
	logger.Info("HandleProtocolQuery:", d.OrderID)
	if d.OrderID == "" {
		return nil
	}
	orderMain := order.TbOrderMain.GetByOrderID(d.OrderID)
	if orderMain == nil {
		logger.Warn("未查询到订单信息", d)
		return nil
	}
	rd := cache.GetYogaRedis()
	cacheKey := fmt.Sprintf("%s%s", aliPQCacheKey, d.OrderID)
	exp := 24 * time.Hour
	var agreement = NO
	switch orderMain.PaymentCode {
	case AndroidAlipayContract, IosAlipayContract, H5AlipayContract:
		if agreementQueryExist(formatContractCode(d.OrderID), d.OrderID) {
			agreement = Yes
		}
	case WxContract, H5WxContract:
		row, err := order.TbWebWxContract.GetByContractCode(formatContractCode(d.OrderID))
		logger.Info("微信签约查询", d.OrderID, row)
		if err != nil {
			logger.Warn("微信查询签约信息出错", err)
			return nil
		}
		if row == nil {
			logger.Warn("微信查询签约未查询到订单信息", d)
			return nil
		}
		if wxAgreementQueryExist(formatContractCode(d.OrderID), row.PlanID) {
			agreement = Yes
		}
	default:
		return nil
	}
	if err := rd.SetEX(context.Background(), cacheKey, agreement, exp).Err(); err != nil {
		logger.Warn("支付宝查询签约信息，设置缓存出错", err)
	}
	return nil
}

type queryContractResponse struct {
	ReturnCode              string `xml:"return_code,omitempty" json:"return_code"`
	ResultCode              string `xml:"result_code,omitempty" json:"result_code"`
	MchID                   string `xml:"mch_id,omitempty" json:"mch_id"`
	Appid                   string `xml:"appid,omitempty" json:"appid,omitempty"`
	ContractID              string `xml:"contract_id,omitempty" json:"contract_id"`
	PlanID                  string `xml:"plan_id,omitempty" json:"plan_id,omitempty"`
	Openid                  string `xml:"openid,omitempty" json:"openid,omitempty"` // 用户唯一标识
	RequestSerial           int64  `xml:"request_serial,omitempty" json:"request_serial,omitempty"`
	ContractCode            string `xml:"contract_code,omitempty" json:"contract_code,omitempty"`
	ContractDisplayAccount  string `xml:"contract_display_account,omitempty" json:"contract_display_account,omitempty"`
	ContractState           int    `xml:"contract_state,omitempty" json:"contract_state"`
	ContractSignedTime      string `xml:"contract_signed_time,omitempty" json:"contract_signed_time"`
	ContractExpiredTime     string `xml:"contract_expired_time,omitempty" json:"contract_expired_time"`
	ContractTerminatedTime  string `xml:"contract_terminated_time,omitempty" json:"contract_terminated_time"`
	ContractTerminationMode string `xml:"contract_termination_mode,omitempty" json:"contract_termination_mode"`
	ErrCode                 string `xml:"err_code,omitempty" json:"err_code,omitempty"`
	ErrCodeDes              string `xml:"err_code_des,omitempty" json:"err_code_des,omitempty"`
	Sign                    string `xml:"sign,omitempty" json:"sign,omitempty"`
}

func wxAgreementQueryExist(contractCode, planID string) bool {
	url := "https://api.mch.weixin.qq.com/papay/querycontract"
	url = service.ReplacePayURLScheme(url)
	cfg := config.Get().Wechat
	httpClient := xhttp.NewClient()
	bm := make(gopay.BodyMap)
	bm.Set("appid", cfg.AppID).
		Set("mch_id", cfg.MchID).
		Set("plan_id", planID).
		Set("contract_code", contractCode).
		Set("version", "1.0")
	sign := wechat.GetReleaseSign(cfg.AppKey, bm.GetString("sign_type"), bm)
	bm.Set("sign", sign)
	req := wechat.GenerateXml(bm)
	res, bs, err := httpClient.Type(xhttp.TypeXML).Post(url).SendString(req).EndBytes(context.Background())
	if res == nil || err != nil || bs == nil {
		return false
	}
	if res.StatusCode != 200 {
		return false
	}
	if strings.Contains(string(bs), "HTML") || strings.Contains(string(bs), "html") {
		return false
	}
	logger.Info("返回值接口微信签约查询1", string(bs))
	var wxRsp queryContractResponse
	if err := xml.Unmarshal(bs, &wxRsp); err != nil {
		return false
	}
	// contract_state 协议状态，枚举值： 0：已签约 1：未签约 9：签约进行中
	if wxRsp.ReturnCode == wxSuccess && wxRsp.ResultCode == wxSuccess && wxRsp.ContractState != 1 {
		logger.Info("存在微信签约", contractCode, planID)
		return true
	}
	return false
}

// agreementQueryExist 查询签约协议是否存在
func agreementQueryExist(contractCode, orderID string) bool {
	logger.Info("agreementQueryExist", contractCode, orderID)
	resp, err := grpc.GetPayCenterClient().UserAgreementQuery(context.Background(),
		&paycenter.AgreementQueryReq{OrderID: orderID, ContractCode: contractCode})
	logger.Info("agreementQueryExist==", contractCode, orderID, resp.GetResp())
	if err != nil {
		return true
	}
	return resp.GetResp()
}

func formatContractCode(orderID string) string {
	var data string
	switch config.Get().GetEnv() {
	case env.Product:
		data = fmt.Sprintf("%s_%s", orderID, "product")
	case env.Mirror:
		data = fmt.Sprintf("%s_%s", orderID, "mirror")
	case env.Dev, env.Test:
		data = fmt.Sprintf("%s_%s", orderID, "qa")
	default:
		data = fmt.Sprintf("%s_%s", orderID, "qa")
	}
	return data
}
