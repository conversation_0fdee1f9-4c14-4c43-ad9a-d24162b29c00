package comm

import (
	"strconv"
	"time"

	comm "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type AddEffectiveDuration struct {
	StartTime int64
	EndTime   int64
}

func FormatEndTime(cTime int64) int64 {
	loc, _ := time.LoadLocation(comm.TimeZoneBeijing)
	t := time.Unix(cTime, 0)
	return time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, 0, loc).Unix()
}

func FormatStartTime(cTime int64) int64 {
	loc, _ := time.LoadLocation(comm.TimeZoneBeijing)
	t := time.Unix(cTime, 0)
	return time.Date(t.Year(), t.Month(), t.Day(), 00, 00, 00, 0, loc).Unix()
}

// 获取未来的时间
func FutureTime(t, day int64) int64 {
	loc, _ := time.LoadLocation(comm.TimeZoneBeijing)
	ts := time.Unix(t, 0)
	return time.Date(ts.Year(), ts.Month(), ts.Day()+int(day), 23, 59, 59, 0, loc).Unix()
}

// 获取过去的时间
func PastTime(t, day int64) int64 {
	loc, _ := time.LoadLocation(comm.TimeZoneBeijing)
	ts := time.Unix(t, 0)
	return time.Date(ts.Year(), ts.Month(), ts.Day()-int(day), 23, 59, 59, 0, loc).Unix()
}

const (

	// 定义每分钟的秒数
	SecondsPerMinute = 60
	// 定义每小时的秒数
	SecondsPerHour = SecondsPerMinute * 60
	// 定义每天的秒数
	SecondsPerDay = SecondsPerHour * 24
)

/*
时间转换函数
*/
func ResolveTime(seconds int64) (day, hour, minute int64) {
	// 每分钟秒数
	minute = seconds / SecondsPerMinute
	// 每小时秒数
	hour = seconds / SecondsPerHour
	// 每天秒数
	day = seconds / SecondsPerDay
	return
}

// 获取两个时间戳相差多少天
func Days(timestampFrom, timestampTo int64, isFormat bool) int64 {
	var days int64 = 0
	if timestampFrom >= timestampTo {
		return days
	}
	from := timestampFrom
	to := timestampTo
	if isFormat {
		from = FormatStartTime(timestampFrom)
		to = FormatStartTime(timestampTo)
	}

	var midnightUnix = func(t time.Time) int64 {
		y, m, d := t.Date()
		return time.Date(y, m, d+1, 23, 59, 59, 0, time.Local).Unix()
	}

	for {
		if midnightUnix(time.Unix(from, 0).AddDate(0, 0, int(days))) > to {
			days++
			break
		}
		days++
	}
	return days
}

func GetDateFromRange(startTimestamp, endTimestamp int64) []int64 {
	start := FormatStartTime(startTimestamp)
	end := FormatEndTime(endTimestamp)
	days := (end - start) / 86400
	list := make([]int64, 0)
	for i := 0; i <= int(days); i++ {
		t := start + (86400 * int64(i))
		timeStamp := time.Unix(t, 0).Format("20060102")
		if timeInt64, err := strconv.ParseInt(timeStamp, 10, 64); err != nil {
			list = append(list, timeInt64)
		}
	}
	return list
}

func ToInt64Ymd(timeTimestamp int64) int64 {
	s := time.Unix(timeTimestamp, 0).Format("20060102")
	if i, err := strconv.ParseInt(s, 10, 64); err == nil {
		return i
	}
	return 0
}
