package point

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	ndb "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/user"
	badgedb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/badge"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/badge"
)

type CommPoint struct {
	UID           int64 `json:"uid"`
	Point         int32 `json:"point"`
	OperationType int   `json:"operation_type"`
}

// 瑜币徽章发放使用
func (u *CommPoint) UserCollectPoint() {
	if u.Point != 0 {
		// 2020-01 仔细看这个写法，很聪明，为了解决就是插入报错
		item, err := ndb.TbUserCollect.FindOrCreate(u.UID)
		if err != nil {
			logger.Error("UserCollectPoint 查询报错", err)
		} else {
			u.updateCollectPointInfo(item)
		}
	}
	taskTypeList := make([]int32, 0)
	if u.OperationType == library.OperationEnum.Sub.ToInt() {
		taskTypeList = append(taskTypeList, int32(library.BadgeTaskTypeEnum.ConsumePoints))
	} else {
		taskTypeList = append(taskTypeList, int32(library.BadgeTaskTypeEnum.ObtainPoints))
	}
	if len(taskTypeList) > 0 {
		_ = badge.ServiceUserOldBadge.ProcessUserBadge(u.UID, taskTypeList, nil)
	}
}

func (u *CommPoint) updateCollectPointInfo(item *ndb.Collect) {
	if u.OperationType == library.OperationEnum.Add.ToInt() &&
		u.getBadgeCategoryItemByID(library.GetObtainPointsBadgeCategoryID()) {
		item.TotalObtainPoints += u.Point
	}

	if u.OperationType == library.OperationEnum.Sub.ToInt() &&
		u.getBadgeCategoryItemByID(library.GetConsumePointsBadgeCategoryID()) {
		item.TotalConsumePoints += library.WithBranch(u.Point)
	}

	item.UpdateTime = time.Now().Unix()
	if err := item.Update(); err != nil {
		logger.Error("updateCollectPointInfo Err", err)
	}
}

func (u *CommPoint) getBadgeCategoryItemByID(categoryID int64) bool {
	item, _ := badgedb.TbCategory.GetBadgeCategoryItemByID(categoryID)
	if item == nil {
		return true
	}
	now := time.Now().Unix()
	if (item.StartTime != 0 && item.StartTime > now) || (item.EndTime != 0 && item.EndTime < now) {
		return false
	}

	return true
}
