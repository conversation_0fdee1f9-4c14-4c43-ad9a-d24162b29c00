package cache

import (
	"context"
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
)

type StorageType int

const Redis StorageType = 1
const Memcache StorageType = 2

func Set(keyPrefix int64, keySuffix, value string, expireTime int) error {
	var err error
	cacheType := StorageType(keyPrefix % 10)
	key := getCacheKey(keyPrefix, keySuffix)

	if cacheType == Memcache {
		err = cache.GetMemcached().SetEx(key, value, expireTime)
	} else {
		err = cache.GetYogaRedis().SetEX(context.Background(), key, value, time.Duration(expireTime)).Err()
	}
	if err != nil {
		logger.Errorf("设置缓存时出错, err: %s", err)
	}
	return err
}

func getCacheKey(keyPrefix int64, keySuffix string) string {
	if keySuffix == "" {
		return fmt.Sprintf("%d", keyPrefix)
	}
	return fmt.Sprintf("%d_%s", keyPrefix, keySuffix)
}
