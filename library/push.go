package library

const (
	PushMessageTypeNone                 = iota
	PushMessageTypeYoMi                 // 瑜小蜜通知
	PushMessageTypeComment              // 评论
	PushMessageTypeNewFans              // 新增粉丝
	PushMessageTypeLike                 // 赞帖子
	PushMessageTypeReply                // 回复帖子
	PushMessageTypeSystem               // 系统通知
	PushMessageTypePartnerApply         // 加入结伴申请通知
	PushMessageTypePartnerApplyFeedback // 加入结伴申请通知反馈
)

type SignIDType int

var SignIDTypeEnum = struct {
	AddPoints         SignIDType
	SendYouZanVoucher SignIDType
	SendAppVoucher    SignIDType
	SendUserNewBadge  SignIDType
}{
	AddPoints:         500001, // 后台加瑜币
	SendYouZanVoucher: 500002, // 后台发送有赞优惠券后发送推送
	SendAppVoucher:    500003, // 后台发送站内优惠券后发送推送
	SendUserNewBadge:  200024, // 获得新徽章发push通知
}

type SendStatus int

var SendStatusEnum = struct {
	AlreadySend SendStatus
	WaitSend    SendStatus
	OnGoingSend SendStatus
	FailSend    SendStatus
}{
	AlreadySend: 1, // 发送成功
	WaitSend:    2, // 待发送
	OnGoingSend: 3, // 正在发送
	FailSend:    4, // 发送失败
}

type ItemTypeEnum int64

// ItemTypes 推送条目类型
var ItemTypes = struct {
	PushAll    ItemTypeEnum // 推送+站内消息
	PushNotice ItemTypeEnum // 通知推送
	PushSite   ItemTypeEnum // 站内推送
}{
	PushAll:    1,
	PushNotice: 2,
	PushSite:   3,
}

const (
	PushLinkTypeBadgeCenterEX = 117 // 跳转路由:徽章墙
)
