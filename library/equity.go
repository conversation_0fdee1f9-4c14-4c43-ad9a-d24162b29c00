package library

const (
	None = iota
	Kol
	Fats
	Advanced
	Tv
	Now
	Face     = 7
	AdminVip = 999 // 特殊标记会员权益
)

type CEnumType int

var EquityTypeEnum = struct {
	None     CEnumType
	Kol      CEnumType
	Fat      CEnumType
	Advanced CEnumType
	Tv       CEnumType
	Now      CEnumType
	Face     CEnumType
}{
	None:     None,
	Kol:      Kol,
	Fat:      Fats,
	Advanced: Advanced,
	Tv:       Tv,
	Now:      Now,
	Face:     Face,
}

var EquityTypeDesEnum = map[CEnumType]string{
	EquityTypeEnum.None:     "未知",
	EquityTypeEnum.Kol:      "名师课堂卡",
	EquityTypeEnum.Fat:      "减脂训练营学员卡",
	EquityTypeEnum.Advanced: "入门训练营学员卡",
	EquityTypeEnum.Tv:       "TV大屏卡",
	EquityTypeEnum.Now:      "冥想专区卡",
	EquityTypeEnum.Face:     "面部专区卡",
}

func (e CEnumType) GetEquityTypeDesEnum() string {
	return EquityTypeDesEnum[e]
}

func (e CEnumType) ToInt32() int32 {
	return int32(e)
}

func ToCEnumType(e int32) CEnumType {
	return CEnumType(e)
}

var UserTypeEnum = struct {
	Unknow CEnumType // 未知
	Vistor CEnumType // 游客
	Normal CEnumType // 没有会员
	VIP    CEnumType // 会员
	YVIP   CEnumType // 年费会员
	SVIP   CEnumType // 高级会员
	YSVIP  CEnumType // 高级年费会员
	GVIP   CEnumType // 高级会员(新)
	YGVIP  CEnumType // 高级年费会员(新)
}{
	Unknow: -1,
	Vistor: 0,
	Normal: 1,
	VIP:    2,
	YVIP:   3,
	SVIP:   4,
	YSVIP:  5,
	GVIP:   6,
	YGVIP:  7,
}

// EffectiveUserMember 需要参与发放的有效的会员type
var EffectiveUserMember = []int32{
	UserTypeEnum.SVIP.ToInt32(),
	UserTypeEnum.YSVIP.ToInt32(),
}

var AllowAddEquityType = []int32{
	EquityTypeEnum.Kol.ToInt32(),
	EquityTypeEnum.Fat.ToInt32(),
	EquityTypeEnum.Advanced.ToInt32(),
	EquityTypeEnum.Tv.ToInt32(),
	EquityTypeEnum.Now.ToInt32(),
	EquityTypeEnum.Face.ToInt32(),
}

var AllowAddRecordType = []int32{
	RecordTypeEnum.Buy.ToInt32(),
	RecordTypeEnum.Give.ToInt32(),
	RecordTypeEnum.Back.ToInt32(),
}

var AllowOperationType = []int32{
	OperationEnum.Add.ToInt32(),
	OperationEnum.Sub.ToInt32(),
}

var AllowEquityDurationType = []int32{
	EquityDurationTypeEnum.Day.ToInt32(),
	EquityDurationTypeEnum.Month.ToInt32(),
	EquityDurationTypeEnum.Year.ToInt32(),
}

// AllowAddEquityDurType 加时长
var AllowAddEquityDurType = []int32{
	EquityTypeEnum.Kol.ToInt32(),
	EquityTypeEnum.Tv.ToInt32(),
	EquityTypeEnum.Now.ToInt32(),
	EquityTypeEnum.Face.ToInt32(),
}

// RecordTypeEnum 状态：1-购买，2-赠送，3-后台，4-系统转换
var RecordTypeEnum = struct {
	Buy    CEnumType
	Give   CEnumType
	Back   CEnumType
	System CEnumType
}{
	Buy:    1,
	Give:   2,
	Back:   3,
	System: 4,
}
var RecordTypeDesEnum = map[CEnumType]string{
	RecordTypeEnum.Buy:    "开通",
	RecordTypeEnum.Give:   "赠送",
	RecordTypeEnum.Back:   "系统发放",
	RecordTypeEnum.System: "会员权益升级开通",
}

func (e CEnumType) GetRecordTypeDesEnumDes() string {
	return RecordTypeDesEnum[e]
}

// EquityDurationTypeEnum 资源期限类型：1-天、2-月、3-年
var EquityDurationTypeEnum = struct {
	Day   CEnumType
	Month CEnumType
	Year  CEnumType
}{
	Day:   1,
	Month: 2,
	Year:  3,
}

var EquityDurationTypeDesEnum = map[CEnumType]string{
	EquityDurationTypeEnum.Day:   "天",
	EquityDurationTypeEnum.Month: "个月",
	EquityDurationTypeEnum.Year:  "年",
}

func (e CEnumType) GetDurationTypeDes() string {
	return EquityDurationTypeDesEnum[e]
}

type OperationType int

// OperationEnum 1-加2-减
var OperationEnum = struct {
	Add OperationType
	Sub OperationType
}{
	Add: 1,
	Sub: 2,
}

func (e OperationType) ToInt32() int32 {
	return int32(e)
}

func (e OperationType) ToInt64() int64 {
	return int64(e)
}
func (e OperationType) ToInt() int {
	return int(e)
}

type CommonStatus int

var CommonStatusEnum = struct {
	Normal  CommonStatus // 正常
	Invalid CommonStatus // 失效
	Close   CommonStatus // 关闭
}{
	Normal:  1,
	Invalid: 2,
	Close:   3,
}
