package library

// 计划资源类型 1普通计划 2名师课堂 3 训练营 4单独售卖课 5 选修课 6now冥想 7面部瑜伽
type SeriesType int32

var SeriesTypeEnum = struct {
	Program      SeriesType
	Kol          SeriesType
	O2           SeriesType
	ProductMain  SeriesType
	Elective     SeriesType
	NowMediation SeriesType
	Face         SeriesType
}{
	Program:      1,
	Kol:          2,
	O2:           3,
	ProductMain:  4,
	Elective:     5,
	NowMediation: 6,
	Face:         7,
}

// LastPracticeLogTypes 2020-07-29 leon 上次练习记录
var LastPracticeLogTypes = map[int32]int{
	PracticeFinish:           1,
	FinishSessionInProgram:   1,
	LeaderUserScheduleUnit:   1,
	PlayQuitSession:          1,
	PlayQuitSessionInProgram: 1,
	PlayQuitUserSchedule:     1,
}

// 课程类型 1 体式  2 冥想 3 now冥想, 130 直播课
const SessionContentTypeAction = 1
const SessionContentTypeMedia = 2
const SessionContentTypeNow = 3
const LiveSessionSourceType = 130

// 练习类型 1 课程 2 计划 3 名师课堂 4 训练营 5 选修课  6 智能课表 7 自定义课表, 8 直播课 9 面部课程 10 面部计划
type ResourceType int32

var ResourceTypeEnum = struct {
	UnKnown              ResourceType
	Session              ResourceType
	Program              ResourceType
	Kol                  ResourceType
	O2                   ResourceType
	Elective             ResourceType
	IntelligenceSchedule ResourceType
	UserSchedule         ResourceType
	LiveSession          ResourceType
	FaceSession          ResourceType
	FaceProgram          ResourceType
}{
	UnKnown:              0,
	Session:              1,
	Program:              2,
	Kol:                  3,
	O2:                   4,
	Elective:             5,
	IntelligenceSchedule: 6,
	UserSchedule:         7,
	LiveSession:          8,
	FaceSession:          9,
	FaceProgram:          10,
}

func (p ResourceType) ToInt32() int32 {
	return int32(p)
}

func (s SeriesType) ToInt32() int32 {
	return int32(s)
}

// SeriesTypeToPracticeType 计划资源对应的练习类型
var SeriesTypeToPracticeType = map[SeriesType]ResourceType{
	SeriesTypeEnum.Program:     ResourceTypeEnum.Program,
	SeriesTypeEnum.Kol:         ResourceTypeEnum.Kol,
	SeriesTypeEnum.O2:          ResourceTypeEnum.O2,
	SeriesTypeEnum.ProductMain: ResourceTypeEnum.Elective,
	SeriesTypeEnum.Elective:    ResourceTypeEnum.Elective,
	SeriesTypeEnum.Face:        ResourceTypeEnum.FaceProgram,
}

type PStatus int32

// CTypeEnum 1：正在进行,2：已完成,3:已放弃
var CTypeEnum = struct {
	Ing      PStatus
	Done     PStatus
	Renounce PStatus
}{
	Ing:      1,
	Done:     2,
	Renounce: 3,
}

func (p PStatus) ToInt32() int32 {
	return int32(p)
}

// DateTypeEnum 1 当天有课程 2 当天未练习 3 请假中
var DateTypeEnum = struct {
	HaveClass PStatus
	NOClass   PStatus
	HasLeave  PStatus
}{
	HaveClass: 1,
	NOClass:   2,
	HasLeave:  3,
}
