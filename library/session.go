package library

// 1:普通练习 2:KOL练习 3:O2课程练习 4 单独售卖课程练习，5 选修课 , 6面部瑜伽
const (
	SessionSeriesTypeGeneral = 1
	SessionSeriesTypeKol     = 2
	SessionSeriesTypeO2      = 3
	SessionSeriesTypeFace    = 6

	PlanSeriesTypeFace = 7
)

// SessionType 课程类型
type SessionType int

// SessionTypeEnum 枚举
var SessionTypeEnum = struct {
	TrainingCamp SessionType // 训练营
	OfflineRYT   SessionType // 教培
}{
	TrainingCamp: 1,
	OfflineRYT:   2,
}

// O2Status 数据状态
type O2Status int32

var O2DisplayEnum = struct {
	Yes O2Status // 1展示
	No  O2Status // 2不展示
}{
	Yes: 1,
	No:  2,
}
