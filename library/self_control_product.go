package library

// SelfControlProductType 自控产品类型
type SelfControlProductType int

// SelfControlProductTypeEnum 枚举
var SelfControlProductTypeEnum = struct {
	Fund            SelfControlProductType // 自控基金，在2018年10月后不存在该类型，汇入了通用挑战赛
	Challenge       SelfControlProductType // 自控挑战赛，后台界面为通用挑战赛
	NoviceChallenge SelfControlProductType // 新手挑战赛
}{
	Fund:            1,
	Challenge:       2,
	NoviceChallenge: 3,
}
