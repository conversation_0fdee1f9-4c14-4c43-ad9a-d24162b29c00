package library

// UserLoginSourceTypeThirdParty 这个是accountinfo_tp_weixin内的source_type字段
type UserLoginSourceTypeThirdParty int

// UserLoginSourceTypeThirdPartyEnum 取值
var UserLoginSourceTypeThirdPartyEnum = struct {
	App                         UserLoginSourceTypeThirdParty // APP第三方登陆来源
	H5                          UserLoginSourceTypeThirdParty // 每日瑜伽教你学
	MiniApp                     UserLoginSourceTypeThirdParty // 小程序(每日瑜伽打卡)
	MiniAppTraining             UserLoginSourceTypeThirdParty // 小程序(训练营)
	MiniAppShopChallenge        UserLoginSourceTypeThirdParty // 小程序(电商)
	MiniAppPartnerChallenge     UserLoginSourceTypeThirdParty // 小程序(结伴)
	MiniAppYogaOnlineCourse     UserLoginSourceTypeThirdParty // 小程序(瑜伽视频线上课)
	MiniAppYogaZeroPractice     UserLoginSourceTypeThirdParty // 小程序(「瑜伽0元练」微信小程序)
	MiniAppYogaClassOnlineClass UserLoginSourceTypeThirdParty // 小程序(瑜伽课堂在线班)
	MiniAppYogaGroup            UserLoginSourceTypeThirdParty // 小程序(瑜伽小组群)
	MiniAppYogaMeditationMusic  UserLoginSourceTypeThirdParty // 小程序(冥想音乐推荐)
	MiniAppYogaSleepAid         UserLoginSourceTypeThirdParty // 小程序(睡眠助眠解压音乐)
}{
	App:                         1,
	H5:                          2,
	MiniApp:                     3,
	MiniAppTraining:             4,
	MiniAppShopChallenge:        5,
	MiniAppPartnerChallenge:     6,
	MiniAppYogaOnlineCourse:     7,
	MiniAppYogaZeroPractice:     8,
	MiniAppYogaClassOnlineClass: 9,
	MiniAppYogaGroup:            10,
	MiniAppYogaMeditationMusic:  11,
	MiniAppYogaSleepAid:         12,
}
