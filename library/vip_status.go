package library

// 用户 VIP 状态属性,属性迁移 620 独立的用户分群
type UserVIPStatusAttr int

var UserVIPStatusAttrEnum = struct {
	UnKnow                     UserVIPStatusAttr
	VIPIsValid                 UserVIPStatusAttr
	VIPNotOpenWithInRegister15 UserVIPStatusAttr
	VIPNotOpenBeyondRegister15 UserVIPStatusAttr
	VIPOverdueWithIn15         UserVIPStatusAttr
	VIPOverdueBeyond15         UserVIPStatusAttr
}{
	UnKnow:                     0,  // 未知
	VIPIsValid:                 3,  // 会员有效期
	VIPNotOpenWithInRegister15: 15, // 注册15天内未开通会员
	VIPNotOpenBeyondRegister15: 16, // 注册15天已上未开通会员
	VIPOverdueWithIn15:         17, // 会员已过期15日内
	VIPOverdueBeyond15:         18, // 会员已过期15日已上
}
