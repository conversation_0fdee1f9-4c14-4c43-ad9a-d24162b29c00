package library

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/dead"
)

// UploadDeadLetterQueue 上报死信队列
func UploadDeadLetterQueue(data interface{}) {
	body, err := json.Marshal(data)
	if err != nil {
		logger.Errorf("序列化失败 %s", err)
		return
	}
	if _, err := usercenter.GetEngineMaster().Insert(&dead.Queue{
		Key:     DeadLetterQueueKeyEnum.AppAdsInfo,
		Content: string(body),
		Status:  DeadLetterQueueStatus.UnSolved,
	}); err != nil {
		logger.Errorf("写入死信队列失败 错误%s,内容:%s", err, string(body))
	}
}
