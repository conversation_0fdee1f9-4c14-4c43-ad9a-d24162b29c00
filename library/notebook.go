package library

var (
	NoteBookRankListSize                  = 200
	NoteBookBadgePracticeDayCount         = 5
	NoteBookDynamicRecordDayCount         = 5
	NoteBookPracticeFinishedRecordContent = "本月坚持记录，获得徽章一枚"
	NoteBookDynamicFinishedContent        = "本月坚持记录，获得徽章一枚"
	NoteBookPracticeFinishContent         = "本月竟做到全勤记录，yomi为你点赞"
	NoteBookPracticeRecordBackgroundImage = "http://qiniucdn.dailyyoga.com.cn/78ae43be6a5070fc827d6637fa1623cf"
)

type ContentType int32

var NoteBookContentTypeEnum = struct {
	Posts       ContentType
	Record      ContentType
	Birthday    ContentType
	Jointed     ContentType
	AllPractice ContentType
}{
	Posts:       1,
	Record:      2,
	Birthday:    3,
	Jointed:     4,
	AllPractice: 5,
}

type StatusType int32

var NoteBookContentStatusEnum = struct {
	Normal  StatusType
	Remove  StatusType
	Hide    StatusType
	Privacy StatusType
	Delete  StatusType
}{
	Normal:  1, // 同步
	Remove:  2, // 移除
	Hide:    3, // 隐藏
	Privacy: 4, // 设私
	Delete:  5, // 删除
}

var NoteBookContentStatusTypeEnum = struct {
	Normal  StatusType
	Remove  StatusType
	Delete  StatusType
	Hide    StatusType
	Privacy StatusType
}{
	Normal:  1,
	Remove:  2,
	Hide:    3,
	Privacy: 4,
	Delete:  5,
}

var NoteBookBadgeDynamicStatusEnum = struct {
	PracticeRecordAllFinished StatusType
	RecordFinished            StatusType
	PracticeFinished          StatusType
	UnFinished                StatusType
}{
	PracticeRecordAllFinished: 1, // 练习+记录本内容都完成
	RecordFinished:            2, // 仅记录本记录天数完成
	PracticeFinished:          3, // 仅练习天数完成
	UnFinished:                4, // 都未完成
}

type JoinStatus int32

var NoteBookJoinStatusEnum = struct {
	Jointed JoinStatus
	UnJoint JoinStatus
}{
	Jointed: 1,
	UnJoint: 2,
}

type PrivacyStatus int32

var NoteBookPrivacyStatuEnum = struct {
	Public  PrivacyStatus
	Private PrivacyStatus
}{
	Public:  1,
	Private: 2,
}

type TaskType int32

var NoteBookTaskTypeEnum = struct {
	NoteBookRankList TaskType
	Add2NoteBook     TaskType
	ModifyNoteBook   TaskType
}{
	NoteBookRankList: 1,
	Add2NoteBook:     2,
	ModifyNoteBook:   3,
}

type DayStatus int32

var NoteBookDayStatusEnum = struct {
	HasRecord DayStatus
	NoRecord  DayStatus
}{
	HasRecord: 1,
	NoRecord:  2,
}

var NoteBookRankListKey = "yoga_note_book_rank_list"
var NoteBookRankImageSizeMin = 3
var NoteBookRankImageSizeMax = 9
