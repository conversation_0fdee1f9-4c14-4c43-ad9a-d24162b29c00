package library

const IndexesTypeAuth = 1
const IndexesTypeMember = 2

const AuthenticationTypeOfficial = 1
const AuthenticationTypeCoach = 2
const AuthenticationTypeYogaFan = 3
const AuthenticationTypeOther = 4
const AuthenticationTypeInner = 5

type MemberLevel int

func (m MemberLevel) EffectOrderContrast(o MemberLevel) bool {
	return MemberLevelOrderIndex[m] > MemberLevelOrderIndex[o]
}

func (m MemberLevel) GetOrder() int {
	if order, ok := MemberLevelOrderIndex[m]; ok {
		return order
	}
	return 0
}

// MemberLevelOrderIndex 注意：1、该表内的值仅作为对比等级生效顺序，随时可能变化！
//      2、不要在任何地方存储，不要返回给客户端！
var MemberLevelOrderIndex = map[MemberLevel]int{
	MemberLevelVistor: 1, // 游客
	MemberLevelNormal: 2, // 没有会员
	MemberLevelVip:    3, // 会员
	MemberLevelYVip:   4, // 年费会员
	MemberLevelGVip:   5, // 高级会员(新)
	MemberLevelYGVip:  6, // 高级年费会员(新)
	MemberLevelSVip:   7, // 高级会员
	MemberLevelYSVip:  8, // 高级年费会员
}

const MemberLevelVistor MemberLevel = 0 // 游客
const MemberLevelNormal MemberLevel = 1 // 普通用户
const MemberLevelVip MemberLevel = 2    // 会员
const MemberLevelYVip MemberLevel = 3   // 年费会员
const MemberLevelSVip MemberLevel = 4   // 全能会员
const MemberLevelYSVip MemberLevel = 5  // 年费全能会员
const MemberLevelGVip MemberLevel = 6   // 全能会员 (7.0.0)
const MemberLevelYGVip MemberLevel = 7  // 年费全能会员 (7.0.0)

var CEAuthMum = struct {
	None           CEnumType // 普通用户
	Authentication CEnumType // 加V
	Talent         CEnumType // 达人
	AnnualMember   CEnumType // 年费会员
	Member         CEnumType // 会员
}{
	None:           0,
	Authentication: 80,
	Talent:         60,
	AnnualMember:   40,
	Member:         20,
}

func (e CEnumType) ToInt() int {
	return int(e)
}

// UserMemberDurationStatus 用户会员状态 与表 user_member_durtion 表的 status 字段对应
type UserMemberDurationStatus int

// UserMemberDurationStatusEnum 取值
var UserMemberDurationStatusEnum = struct {
	Normal  UserMemberDurationStatus
	Overdue UserMemberDurationStatus
}{
	Normal:  1, // 正常
	Overdue: 2, // 过期
}
