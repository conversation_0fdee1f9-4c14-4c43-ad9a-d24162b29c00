package library

const RedisKeyChallengeTaskComplete = "challenge_task_complete" // 任务完成数据缓存key前缀

// 挑战赛类型
type ChallengeType int32

func (c ChallengeType) ToInt32() int32 {
	return int32(c)
}

// ChallengeTypeEnum 挑战赛类型
var ChallengeTypeEnum = struct {
	Fund             ChallengeType
	Challenge        ChallengeType
	NoviceChallenge  ChallengeType
	ShopChallenge    ChallengeType
	MemberChallenge  ChallengeType
	WelfareChallenge ChallengeType
	MemberWelfare    ChallengeType
}{
	Fund:             1, // 自控基金
	Challenge:        2, // 自控挑战赛
	NoviceChallenge:  3, // 新手挑战赛
	ShopChallenge:    4, // 电商挑战赛
	MemberChallenge:  5, // 会员挑战赛
	WelfareChallenge: 6, // 新人福利赛
	MemberWelfare:    7, // 会员福利赛
}

// ChallengeTaskType 挑战赛任务类型
type ChallengeTaskType int32

func (t ChallengeTaskType) ToInt32() int32 {
	return int32(t)
}

// ChallengeTaskTypeEnum 挑战赛任务类型
var ChallengeTaskTypeEnum = struct {
	PlayTimeTask    ChallengeTaskType
	SignTask        ChallengeTaskType
	ShareTask       ChallengeTaskType
	FinishProgram   ChallengeTaskType
	FinishO2Session ChallengeTaskType
	NewTaskProcess  ChallengeTaskType
}{
	PlayTimeTask:    1, // 练习时长
	SignTask:        2, // 练习课程打卡
	ShareTask:       3, // 完成分享
	FinishProgram:   4, // 完成计划
	FinishO2Session: 5, // 完成训练营课程
	NewTaskProcess:  6, // 新任务类型(组合任务)
}

// ChallengeCompleteType 任务完成类型
type ChallengeCompleteType int32

func (c ChallengeCompleteType) ToInt32() int32 {
	return int32(c)
}

// ChallengeCompleteTypeEnum 任务完成类型枚举
var ChallengeCompleteTypeEnum = struct {
	Everyday ChallengeCompleteType
	Once     ChallengeCompleteType
}{
	Everyday: 1,
	Once:     2,
}

// ChallengeResourceType 资源类型
type ChallengeResourceType int32

var ChallengeResourceTypeEnum = struct {
	No        ResourceType
	Session   ResourceType
	Program   ResourceType
	Schedule  ResourceType
	O2Session ResourceType
}{
	No:        0, // 没有资源限制
	Session:   1, // 课程
	Program:   2, // 计划
	Schedule:  3, // 自定义课程表
	O2Session: 4, // 训练营
}

// ChallengeToUserStatus 用户自控状态
type ChallengeToUserStatus int32

func (e ChallengeToUserStatus) ToInt32() int32 {
	return int32(e)
}

// ChallengeToUserStatusEnum 用户自控状态
var ChallengeToUserStatusEnum = struct {
	WaitPayment ChallengeToUserStatus
	Underway    ChallengeToUserStatus
	Success     ChallengeToUserStatus
	Failed      ChallengeToUserStatus
}{
	WaitPayment: 1, // 待支付
	Underway:    2, // 进行中
	Success:     3, // 成功
	Failed:      4, // 失败
}

// ChallengePassType 挑战赛规则类型
type ChallengePassType int32

func (p ChallengePassType) ToInt32() int32 {
	return int32(p)
}

// ChallengePassTypeEnum 挑战赛规则类型
var ChallengePassTypeEnum = struct {
	Total ChallengePassType
	Keep  ChallengePassType
}{
	Total: 1, // 累计
	Keep:  2, // 连续
}

// ChallengeBoolLibrary 布尔变量
type ChallengeBoolLibrary int32

func (b ChallengeBoolLibrary) ToInt32() int32 {
	return int32(b)
}

// ChallengeBoolLibraryEnum 布尔变量
var ChallengeBoolLibraryEnum = struct {
	Yes ChallengeBoolLibrary
	No  ChallengeBoolLibrary
}{

	Yes: 1, // 是
	No:  0, // 否
}

// 2019年4月9日15:29:45 业务线:1-app 2-shop Water
const SelfControlProductBusinessLineApp = 1
const SelfControlProductBusinessLineShop = 2

// TimeType 时间类型 // 1天，2周，3月
type TimeType int32

var TimeTypeEnum = struct {
	Day   TimeType
	Week  TimeType
	Month TimeType
}{
	Day:   1,
	Week:  2,
	Month: 3,
}

// SubTaskTypeEnum 1目标，2计划 3时长
var SubTaskTypeEnum = struct {
	Tags        int32
	Program     int32
	Duration    int32
	KolProgram  int32
	FaceProgram int32
	FaceSession int32
}{
	Tags:        1,
	Program:     2,
	Duration:    3,
	KolProgram:  4,
	FaceProgram: 5,
	FaceSession: 6,
}
