package library

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

const DateFormatYear = "2006"
const DateFormatMonth = "200601"
const DateFormatDay = "20060102"
const InterfaceRetryNum = 1
const HTTPTimeOut = 3

const HTTPToH2oTimeOut = 10

const CacheExpireOneDay = 86400

var SessionPlayDoneStatus = struct {
	Default int32
	Done    int32
	Undone  int32
}{
	Default: 0,
	Done:    1,
	Undone:  2,
}

type DataStatus int

var DataStatusEnum = struct {
	Valid  DataStatus
	Delete DataStatus
}{
	Valid:  1,
	Delete: 2,
}

// DeleteStatusEnum 状态
var DeleteStatusEnum = struct {
	Delete, Valid int
}{
	Delete: 1, // 删除
	Valid:  2, // 有效(正常)
}

func (e DataStatus) ToInt64() int64 {
	return int64(e)
}

func (e DataStatus) ToInt32() int32 {
	return int32(e)
}

const M3U8Head = `#EXTM3U
#EXT-X-VERSION:3
#EXT-X-MEDIA-SEQUENCE:0
#EXT-X-ALLOW-CACHE:YES
#EXT-X-TARGETDURATION:25
`
const M3U8Foot = `#EXT-X-ENDLIST`

// ExcludeTypes 2020-04-07 water 取消掉11为了计划完成次数独立计算
var ExcludeTypes = map[int32]int{
	PlayQuitSession:          1,
	PlayQuitSessionInProgram: 1,
	PlayQuitUserSchedule:     1,
}
var TimeZoneBeijing = "Asia/Shanghai"

func GetKolShareAddress() string {
	cfgEnv := config.Get().GetEnv()
	switch cfgEnv {
	case env.Test, env.Dev:
		return "http://127.0.0.1:8102/h2oapi"
	case env.Mirror:
		return "http://api.dailyyoga.com/h2oapiv2"
	case env.Product:
		return "http://api.dailyyoga.com/h2oapi"
	}
	return ""
}

func GetSendPointAddress() string {
	cfgEnv := config.Get().GetEnv()
	switch cfgEnv {
	case env.Test, env.Dev:
		return "https://test.dailyyoga.com.cn/front_end_all/h2/YoCoin/#/?from_page=YTask"
	case env.Product, env.Mirror:
		return "https://o2o.dailyyoga.com.cn/front_end_all/h2/YoCoin/#/?from_page=YTask"
	}
	return ""
}

const SendPointByApi = 1 // nolint
const SendPointByBackground = 2

func WithBranch(n int32) int32 {
	if n < 0 {
		return -n
	}
	return n
}

func RemoveRepeatedElement(ids []int32) []int32 {
	result := make([]int32, 0)
	mpElement := make(map[int32]int)

	for _, item := range ids {
		mpElement[item] = 1
	}

	for key := range mpElement {
		result = append(result, key)
	}

	return result
}

// 最近练习的数量
const LastPracticeItemNum = 5

// 标准体重基数/KG
const StandBodyWeight = 60

// SLinkTypeToSearchType 转换linkType到搜索类型
func SLinkTypeToSearchType(c int32) int32 {
	searchType := int32(0)
	switch c {
	case ObjTypeEnum.Program:
		searchType = 1
	case ObjTypeEnum.Session:
		searchType = 2
	case ObjTypeEnum.Kol:
		searchType = 3
	case ObjTypeEnum.O2:
		searchType = 4
	case ObjTypeEnum.Ryt:
		searchType = 5
	case ObjTypeEnum.Live:
		searchType = 6
	default:
		searchType = 99
	}
	return searchType
}

// ObjTypeEnum linkType类型
// 2-课程 3-计划 21-训练营 30-名师课堂 49-线下培训 55-选修课 129-直播
// 不需要再练习目标出现 83-每日一听列表 116-正念冥想计划 115-正念冥想课程 4-帖子
var ObjTypeEnum = struct {
	Program                 int32
	Session                 int32
	Action                  int32
	O2                      int32
	Kol                     int32
	Ryt                     int32
	Elective                int32
	Live                    int32
	Homepage                int32
	SourceTypeNowMedProgram int32
	SourceTypeNowMdeSession int32
	SourceTypePost          int32
}{
	Program:                 3,
	Session:                 2,
	Action:                  0,
	O2:                      21,
	Kol:                     30,
	Ryt:                     49,
	Elective:                55,
	Live:                    129,
	Homepage:                31,
	SourceTypeNowMedProgram: 115,
	SourceTypeNowMdeSession: 116,
	SourceTypePost:          4,
}

const (
	ScreenTypeUnknown = iota
	ScreenTypePhone
	ScreenTypePad
	ScreenTypeTv
)

// TV 智能课表
const (
	TNotPracticed = iota
	TNotDone
	TDone
)

const (
	Order4IosIapQueueKey = "delay:queue:Order4IosIap"
	QueueDelaySeconds    = 30
)

const (
	Yes = 1
	No  = 2
)

const (
	UserNewInstallTimeKey = "DY:User:NewInstallTime"
)

const (
	AdChanneDef = iota
	AdChannelJuLiang
)

var ChannelMap = map[int]string{
	AdChannelJuLiang: "【DY_巨量】",
}

type MiniAppUserLabel struct {
	UID       int64 `json:"uid"`
	Age       int32 `json:"age"`
	BMI       int32 `json:"bmi"`
	IsMiniAPP int   `json:"is_mini_app"` // 是不是小程序数据来源
}

type ProjectType int

var ProjectTypeEnum = struct {
	All      ProjectType
	YoGa     ProjectType
	DanceFit ProjectType
	Fitness  ProjectType
	Stretch  ProjectType
	Children ProjectType
}{
	All:      0,
	YoGa:     1,
	DanceFit: 2,
	Fitness:  3,
	Stretch:  4,
	Children: 5,
}

var ProjectTypeEnumDesc = map[ProjectType]string{
	ProjectTypeEnum.All:      "未知",
	ProjectTypeEnum.YoGa:     "每日瑜伽",
	ProjectTypeEnum.DanceFit: "热汗舞蹈",
	ProjectTypeEnum.Fitness:  "硬汗健身",
	ProjectTypeEnum.Stretch:  "元气拉伸",
	ProjectTypeEnum.Children: "小树苗运动",
}

var ProjectTypeList = []ProjectType{ProjectTypeEnum.YoGa, ProjectTypeEnum.DanceFit,
	ProjectTypeEnum.Fitness, ProjectTypeEnum.Stretch, ProjectTypeEnum.Children}

const (
	PayTypeWechat = iota + 1
	PayTypeAlipay
	PayTypeApple
	Other
	DFPayTypeTikTok = 4
	DYPayTypeTikTok = 4
	FTPayTypeTikTok = 4
)

const (
	ConstOfferTypeNo            = 1
	ConstOfferTypeFirstBuy      = 2
	ConstOfferTypeTrial         = 3
	ConstOfferTypeTrialFirstBuy = 4
)

var OfferTypeDesc = map[int]string{
	ConstOfferTypeNo:            "无",
	ConstOfferTypeFirstBuy:      "首购",
	ConstOfferTypeTrial:         "试用",
	ConstOfferTypeTrialFirstBuy: "试用+首购",
}

// 会员产品类型
const (
	ProductType1Month = iota + 1
	ProductType3Month
	ProcuctType12Month
	ProductType6Month
	ProductType7Day
	ProductPermanentlyVip
)

var ProductTypeDesc = map[int]string{
	ProductType1Month:     "月度会员",
	ProductType3Month:     "季度会员",
	ProcuctType12Month:    "年度会员",
	ProductType6Month:     "半年会员",
	ProductType7Day:       "7天会员",
	ProductPermanentlyVip: "永久卡",
}

const TikTokMchID = "73994896449556503130"
const WXMchIDMain = "1319327901"
const WXMchIDOther = "1232282702"

func IntInArray(t int, arr []int) bool {
	for _, v := range arr {
		if v == t {
			return true
		}
	}
	return false
}

const (
	DfProductType1Month = iota + 1
	DfProductType3Month
	DfProcuctType12Month
	DfProductType6Month
	DfProductPermanentlyVip
)

var DfProductTypeDesc = map[int]string{
	DfProductType1Month:     "月卡",
	DfProductType3Month:     "季卡",
	DfProcuctType12Month:    "年卡",
	DfProductType6Month:     "半年卡",
	DfProductPermanentlyVip: "永久卡",
}
