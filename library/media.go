package library

type CDNType int

const SessionHLSBucket = "sessionhls"
const SessionBucket = "sessionpackage"
const SessionHLSDomain = "http://qiniuhls.dailyyoga.com.cn/"

var CDNEnum = struct {
	None   CDNType
	Aipai  CDNType
	Qiniu  CDNType
	UCloud CDNType
	YPY    CDNType // 又拍云
	BSY    CDNType // 白山云
}{
	None:   0,
	<PERSON><PERSON><PERSON>:  1,
	<PERSON><PERSON>:  2,
	<PERSON><PERSON><PERSON>: 3,
	YPY:    4,
	BSY:    5,
}

var CDNDomain = map[CDNType]string{
	CDNEnum.Aipai:  "hc40.aipai.com",
	CDNEnum.Qiniu:  "sessionpackage.dailyyoga.com.cn",
	CDNEnum.UCloud: "ucloudvideo.dailyyoga.com.cn",
	CDNEnum.YPY:    "ypyvideo.dailyyoga.com.cn",
	CDNEnum.BSY:    "bsycdn.dailyyoga.com.cn",
}
