package library

import (
	"strings"
	"time"
)

// OrderIDPrefix 订单号前缀
type OrderIDPrefix struct {
	str string
	num string
}

// OrderIDPrefixType 订单号的类型
type OrderIDPrefixType int

// OrderIDPrefixTypeEnum 订单号前缀类型
var OrderIDPrefixTypeEnum = struct {
	Str OrderIDPrefixType
	Num OrderIDPrefixType
}{
	Str: 1, // 字母
	Num: 2, // 数字
}

func (o OrderIDPrefix) String(t OrderIDPrefixType) string {
	if t == OrderIDPrefixTypeEnum.Str {
		return o.str
	}
	return o.num
}

// IsMatch 订单号和类型是否匹配
func (o OrderIDPrefix) IsMatch(billID string) bool {
	return strings.HasPrefix(billID, o.String(OrderIDPrefixTypeEnum.Str)) ||
		strings.HasPrefix(billID, o.String(OrderIDPrefixTypeEnum.Num))
}

// DurationType 周期类型
type DurationType int

// CalDuration 计算时间
// 2018-08-10 14:08:44 庞晓楠 创建
func (d DurationType) CalDuration(value int) time.Duration {
	switch d {
	case DurationTypeEnum.Day:
		return time.Hour * 24 * time.Duration(value)
	case DurationTypeEnum.Month:
		return time.Hour * 24 * 30 * time.Duration(value)
	case DurationTypeEnum.Year:
		return time.Hour * 24 * 365 * time.Duration(value)
	case DurationTypeEnum.Week:
		return time.Hour * 24 * 7 * time.Duration(value)
	case DurationTypeEnum.Second:
		return time.Second * time.Duration(value)
	}
	return 0
}

// DurationTypeEnum 枚举
var DurationTypeEnum = struct {
	Day    DurationType // 天
	Month  DurationType // 月
	Year   DurationType // 年
	Week   DurationType // 周
	Second DurationType // 秒
}{
	Day:    1, // 天
	Month:  2, // 月
	Year:   3, // 年
	Week:   4, // 周
	Second: 5, // 秒
}

// WebOrderType 会员订单类型
type WebOrderType int

// WebOrderTypeEnum 会员订单类型枚举
// 参考 yoga Library_Order_WebOrder::WEB_ORDER_TYPE_XXX
var WebOrderTypeEnum = struct {
	VIPPoint  WebOrderType // 瑜币会员订单
	VIP       WebOrderType // 普通会员
	VIPGroup  WebOrderType // 普通会员组合
	UpToSVIP  WebOrderType // 普通会员升级高级会员
	UpToYSVIP WebOrderType // 升级高级年费
	SVIP      WebOrderType // 高级会员
	SVIPGroup WebOrderType // 高级会员组合
	Group     WebOrderType // 无会员组合
	GVIP      WebOrderType // 新高级会员
	GVIPGroup WebOrderType // 新高级会员组合
}{
	VIPPoint:  0,  // 瑜币会员订单
	VIP:       10, // 普通会员
	VIPGroup:  11, // 普通会员组合
	UpToSVIP:  12, // 普通会员升级高级会员
	UpToYSVIP: 13, // 升级高级年费
	SVIP:      20, // 高级会员
	SVIPGroup: 21, // 高级会员组合
	Group:     30, // 无会员组合
	GVIP:      40, // 新高级会员
	GVIPGroup: 41, // 新高级会员组合
}

// WebProductSoureType 组合商品的组合类型
type WebProductSoureType int

func (w *WebProductSoureType) String() string {
	switch *w {
	case WebProductSoureTypeEnum.VIP:
		return "会员"
	case WebProductSoureTypeEnum.Eshop:
		return "自营商品"
	case WebProductSoureTypeEnum.O2:
		return "训练营"
	case WebProductSoureTypeEnum.Program:
		return "单计划售卖"
	}
	return "未知"
}

// WebProductSoureTypeEnum 组合商品的组合类型
var WebProductSoureTypeEnum = struct {
	VIP     WebProductSoureType // 会员
	Eshop   WebProductSoureType // 自营商品
	O2      WebProductSoureType // 训练营
	Program WebProductSoureType // 单计划售卖
}{
	VIP:     1,
	Eshop:   2,
	O2:      3,
	Program: 4,
}

// WebOrderPayStatus 订单支付状态
type WebOrderPayStatus int

// WebOrderPayStatusEnum 订单支付状态
var WebOrderPayStatusEnum = struct {
	No     WebOrderPayStatus // 待支付
	Yes    WebOrderPayStatus // 已支付
	Cancel WebOrderPayStatus // 已取消
}{
	No:     0,
	Yes:    1,
	Cancel: 2,
}

// PaymentCode 支付方式
type PaymentCode int

// PaymentCodeEnum 枚举 参考 yoga Library_Order_WebOrder::PAYMENT_XXX
var PaymentCodeEnum = struct {
	AndroidYogaWallet PaymentCode // Android Y币钱包
	IOSYogaWallet     PaymentCode // iOS Y币钱包

	YogaWalletCodes []PaymentCode // Y币钱包枚举列表
}{
	AndroidYogaWallet: 14,
	IOSYogaWallet:     24,

	YogaWalletCodes: []PaymentCode{
		14,
		24,
	},
}
