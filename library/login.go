package library

// UserLoginType 用户登陆方式
type UserLoginType int

// UserLoginTypeEnum 取值
var UserLoginTypeEnum = struct {
	Mobile        UserLoginType // 手机号
	Email         UserLoginType // 电子邮箱
	QQ            UserLoginType // qq
	Sina          UserLoginType // 新浪微博
	WeiXin        UserLoginType // 微信
	Mama          UserLoginType // 妈妈网
	Code          UserLoginType // 手机号+验证码
	Huawei        UserLoginType // 华为账户登录 @author:Sachin.song @date:2018-12-19
	Ios           UserLoginType // ios第三方登录
	OneKey        UserLoginType // 极光一键登陆 2020/9/30 3:48 下午 water
	Xiaomi        UserLoginType // 小米一键登陆 2020/10/26 3:54 下午 water
	MobileSystem  UserLoginType // 第三方手机号登录
	MiniAppMobile UserLoginType // 微信小程序授权手机号登陆
	SensorsData   UserLoginType // 神策的DISTINCT_ID登录
	H5            UserLoginType // h5登录
	HuaweiOnekey  UserLoginType // 华为手机号一键登录
}{
	Mobile:        1,
	Email:         2,
	QQ:            3,
	Sina:          4,
	WeiXin:        5,
	Mama:          6,
	Code:          7,
	Huawei:        8,
	Ios:           9,
	OneKey:        10,
	Xiaomi:        11,
	MobileSystem:  12,
	MiniAppMobile: 13,
	SensorsData:   14,
	H5:            15,
	HuaweiOnekey:  18,
}

const (
	Default       = iota // 默认
	BindStatusYes        // 绑定
	BindStatusNo         // 解绑
)

//
// CheckLoginType
//  @Description: 判断列表中是否有待查询的登录方式
//  @param loginType 待查询的登录方式
//  @param userLoginTypeArr 遍历的登录方式列表
//  @return bool
//
func CheckLoginType(loginType UserLoginType, userLoginTypeArr []UserLoginType) bool {
	flags := false
	for i := range userLoginTypeArr {
		if loginType == userLoginTypeArr[i] {
			flags = true
		}
	}
	return flags
}

// 9.34 需求12
// - 每日新进入用户的 5%（每20位用户进入1个）导入固定分群
// - 分群用户量达到5w人即停止用户加入
const (
	RedisKeyNewUserCounter = "yoga:user:register:count" // 新注册用户统计数
	ImportGroupLimitCount  = 1000000                    // 分群用户量达到5w人即停止用户加入，即注册总人数需要到达100w [1000000]
	ImportGroupEveryCount  = 20                         // 每进入用户的5%（每20位用户进入1个）[20]
	ImportGroupID          = 1215                       // 指定分群ID [1215]
)
