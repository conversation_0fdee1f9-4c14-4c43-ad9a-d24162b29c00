package library

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

const (
	FeedBackLinkType    = 71
	FeedBackLinkObjID   = 136
	ShareBadgeLinkObjID = 160

	IntroduceLinkType  = 100
	IntroduceLinkObjID = "https://o2o.dailyyoga.com.cn/front_end_all/h2/common/union_activity/index.html?activity_id=360&version=7.13.0.0&app=0" // nolint
)

var ShowStatusEnum = struct {
	Show DataStatus // 显示
	Hide DataStatus // 隐藏
}{
	Show: 1,
	Hide: 2,
}

// 徽章系列状态
type BadgeCategoryStatus int32

var BadgeCategoryStatusEnum = struct {
	Lock     BadgeCategoryStatus // 未解锁
	Doing    BadgeCategoryStatus // 解锁正在生成中
	UnLock   BadgeCategoryStatus // 解锁
	Finished BadgeCategoryStatus // 全部完成
}{
	Lock:     1,
	Doing:    2,
	UnLock:   3,
	Finished: 4,
}

// 徽章状态
type BadgeStatus int32

var BadgeStatusEnum = struct {
	NotObtain BadgeStatus // 未获得
	Doing     BadgeStatus // 进行中
	Obtain    BadgeStatus // 已获得
	OutOfDate BadgeStatus // 已绝版
}{
	NotObtain: 1,
	Doing:     2,
	Obtain:    3,
	OutOfDate: 4,
}

// 徽章系列任务类型
type BadgeCategoryType int32

// BadgeCategoryTypeEnum 徽章系列任务类型
var BadgeCategoryTypeEnum = struct {
	Acc   BadgeCategoryType // 累计
	Cycle BadgeCategoryType // 周期
	Once  BadgeCategoryType // 一次性
}{
	Acc:   1,
	Cycle: 2,
	Once:  3,
}

// 徽章系列任务触发行为
type BadgeCategoryProgressAction int32

// BadgeCategoryProgressActionEnum 徽章系列任务触发类型
var BadgeCategoryProgressActionEnum = struct {
	Unknown       BadgeCategoryProgressAction // 未知
	Practice      BadgeCategoryProgressAction // 练习完成
	LogIn         BadgeCategoryProgressAction // 登录
	ObtainPoints  BadgeCategoryProgressAction // 获得瑜币
	ConsumePoints BadgeCategoryProgressAction // 消耗瑜币
	UserLevel     BadgeCategoryProgressAction
}{
	Unknown:       0,
	Practice:      1,
	LogIn:         2,
	ObtainPoints:  3,
	ConsumePoints: 4,
	UserLevel:     5,
}

// 徽章系列任务模式
type BadgeCategoryProgressModeType int32

// BadgeCategoryProgressModeEnum 徽章系列任务统计模式
var BadgeCategoryProgressModeEnum = struct {
	Unknown  BadgeCategoryProgressModeType // 位置
	Acc      BadgeCategoryProgressModeType // 累计
	Continue BadgeCategoryProgressModeType // 连续
}{
	Unknown:  0,
	Acc:      1,
	Continue: 2,
}

// 徽章系列任务单位
type BadgeCategoryProgressUnitType int32

// BadgeCategoryProgressUnitEnum 徽章系列任务统计单位
var BadgeCategoryProgressUnitEnum = struct {
	Unknown BadgeCategoryProgressUnitType // 未知
	Day     BadgeCategoryProgressUnitType // 天
	Minute  BadgeCategoryProgressUnitType // 分钟
	Week    BadgeCategoryProgressUnitType // 周
}{
	Unknown: 0,
	Day:     1,
	Minute:  2,
	Week:    3,
}

// 徽章系列触发规则
type BadgeCategorySourceType int32

var BadgeCategorySourceTypeEnum = struct {
	Birthday           BadgeCategorySourceType // 生日
	NoteBook           BadgeCategorySourceType // 记录本
	Challenge          BadgeCategorySourceType // 挑战赛
	PracticeMeditation BadgeCategorySourceType // 练习冥想
	BirthdayBless      BadgeCategorySourceType // 送生日祝福
	BenefitGift        BadgeCategorySourceType // 发放大礼包
}{
	Birthday:           1,
	NoteBook:           2,
	Challenge:          3,
	PracticeMeditation: 4,
	BirthdayBless:      5,
	BenefitGift:        6,
}

// 徽章系列是否需要解锁
type BadgeCategoryNeedUnlockType int32

var BadgeCategoryNeedUnlockEnum = struct {
	NeedUnlock    BadgeCategoryNeedUnlockType // 需要解锁
	NotNeedUnlock BadgeCategoryNeedUnlockType // 不需要解锁
}{
	NeedUnlock:    1,
	NotNeedUnlock: 2,
}

// 徽章是否使用徽章系列分享配置
type BadgeUseCategoryShareConfigType int32

var BadgeUseCategoryShareConfigEnum = struct {
	Use    BadgeUseCategoryShareConfigType // 徽章使用徽章系列的分享配置
	NotUse BadgeUseCategoryShareConfigType // 徽章使用自定义按钮文案
}{
	Use:    1,
	NotUse: 2,
}

// 徽章是否使用徽章系列按钮配置
type BadgeUseCategoryButtonConfigType int32

var BadgeUseCategoryButtonConfigEnum = struct {
	Use    BadgeUseCategoryButtonConfigType // 徽章使用徽章系列的按钮配置
	NotUse BadgeUseCategoryButtonConfigType // 徽章使用自定义按钮文案
}{
	Use:    1,
	NotUse: 2,
}

type BadgeTaskType int32

var BadgeTaskTypeEnum = struct {
	Unknown            BadgeTaskType
	Practice           BadgeTaskType // 练习
	LogIn              BadgeTaskType // 登录
	Birthday           BadgeTaskType // 生日
	NoteBook           BadgeTaskType // 记录本
	ObtainPoints       BadgeTaskType // 获取瑜币
	ConsumePoints      BadgeTaskType // 消耗瑜币
	UserLevel          BadgeTaskType // 用户等级
	Challenge          BadgeTaskType // 挑战赛
	PracticeMeditation BadgeTaskType // 练习冥想
	BirthdayBless      BadgeTaskType // 生日祝福
	BenefitGift        BadgeTaskType // 发放大礼包
}{
	Unknown:            0,
	Practice:           1,
	LogIn:              2,
	Birthday:           3,
	NoteBook:           4,
	ObtainPoints:       5,
	ConsumePoints:      6,
	UserLevel:          7,
	Challenge:          8,
	PracticeMeditation: 9,
	BirthdayBless:      10,
	BenefitGift:        11,
}

type BirthdayStatusType int32

var BirthdayStatusEnum = struct {
	None         BirthdayStatusType
	Today        BirthdayStatusType
	CurrentMonth BirthdayStatusType
}{
	None:         0, // 不是当月生日
	Today:        1, // 当天生日
	CurrentMonth: 2, // 当月生日
}

type BadgeReadStatusType int32

var BadgeReadStatusEnum = struct {
	No  BadgeReadStatusType
	Yes BadgeReadStatusType
}{
	No:  1,
	Yes: 2,
}

const BrdgeCategoryObtainPointsIDQa = 44
const BrdgeCategoryObtainPointsIDOnline = 8
const BrdgeCategoryConsumePointsIDQa = 45
const BrdgeCategoryConsumePointsIDOnline = 9

func GetObtainPointsBadgeCategoryID() int64 {
	cfgEnv := config.Get().GetEnv()
	if cfgEnv == env.Test || cfgEnv == env.Dev {
		return BrdgeCategoryObtainPointsIDQa
	}
	return BrdgeCategoryObtainPointsIDOnline
}

func GetConsumePointsBadgeCategoryID() int64 {
	cfgEnv := config.Get().GetEnv()
	if cfgEnv == env.Test || cfgEnv == env.Dev {
		return BrdgeCategoryConsumePointsIDQa
	}
	return BrdgeCategoryConsumePointsIDOnline
}
