package library

const (
	SignIn                         = iota + 1 // 签到
	PracticeFinish                            // 练习完课程
	ShareCourse                               // 分享课程
	SharePlan                                 // 分享计划
	ShareCourseResult                         // 分享课程成果
	SharePlanResult                           // 分享计划成果
	Posts                                     // 发布帖子
	PostsComment                              // 评论
	CommentReply                              // 回复
	InviteFriends                             // 邀请好友
	FinishPlan                                // 完成计划
	BuyVIP                                    // 开通会员
	PostsBest                                 // 精华帖
	DownloadCourse                            // 下载课程
	StartPlan                                 // 开始计划
	CompleteInfo                              // 完善资料
	BindPhone                                 // 绑定手机号
	ReadPosts                                 // 浏览帖子
	LikePosts                                 // 赞帖子
	MobileRegister                            // 手机号注册
	EmailRegister                             // 邮箱注册
	ThirdFirstLogin                           // 第三方首次登录
	UserLogin                                 // 用户登录
	DownloadMusic                             // 下载音乐专辑
	NewUser                                   // 新增用户
	PostsIllegal                              // 发帖违规
	JoinActivity                              // 参加活动得分
	PostsEssence                              // 加精
	PostsTop                                  // 置顶
	PostsRecommend                            // 推荐
	BuyPoints                                 // 购买瑜币
	Prize                                     // 抽奖记录
	SharePosts                                // 分享帖子
	EnterprisePoints                          // 企业课送瑜币
	CouponPoints                              // 优惠码送瑜币
	SigninWithMood                            // 签到写心情
	ShareAchievement                          // 分享成就
	InviteFriendSuccess                       // 邀请好友成功
	JoinOrGiveUpSession                       // 参加或取消课程
	FinishSessionInProgram                    // 完成计划中的课程
	GiveUpProgram                             // 放弃计划
	RepeatProgram                             // 继续计划
	PartnerStart                              // 开始结伴扣抵押瑜币
	PartnerDismiss                            // 小队解散返还瑜币
	PartnerFinish                             // 完成结伴返还瑜币
	OfficialOperation                         // 官方操作修改瑜币
	PartnerCompensation                       // 队长退出，补偿队员瑜币
	OfficialPartnerFinish                     // 完成官方结伴奖励瑜币
	PartnerRecruitFail                        // 招募失败返还瑜币
	ShareYogaO2Course                         // 分享训练营课程
	AwardSigninGift                           // 记录礼包兑换（不只是签到，所有礼包均复用）
	PurgeOverduePoints                        // 清理瑜币
	ActivityRegister                          // 用户注册记录（活动，跟瑜币无关）
	LeaderKickOutMember                       // 踢出队伍返还瑜币
	LeaderUserScheduleUnit                    // 自定义课程表记录表
	ChallengePledge                           // 挑战赛抵押瑜币
	ChallengeComplete                         // 挑战赛返还瑜币
	ChallengeAward                            // 挑战赛平分瑜币
	BuyYogaO2Session                          // 购买瑜乐大学课程送瑜币
	ChallengeAwardSetPoints                   // 挑战赛指定瑜币
	BuyO2SessionWithPoint                     // 购买瑜乐大学课程时使用瑜币兑换
	UserFinishPartner                         // 日常瑜币任务：用户完成结伴赠送瑜币
	RecruitPartnerSuccess                     // 成功招募结伴成员，首次赠送瑜币
	YoCurrencyUserConversion                  // YO商城瑜币兑换
	FinishO2Session                           // 完成O2课程计划
	ReportPostReplySuccess                    // 举报帖子回复成功,赠送瑜币
	GrayUpgradeAward                          // 使用灰度版本加分
	PracticeSession                           // 播放课程
	PracticeSessionOfProgram                  // 播放计划中的课程
	UserDelEssenceBBS                         // 用户主动删除精华帖
	FromBackground                            // 后台操作
	FromBackgroundShieldingRecover            // 后台拉黑恢复操作
	PracticeFinishFirstTime                   // 首次练习完成课程
	ShareResultFirstTime                      // 首次分享练习结果
	PartnerFinishShareAward                   // 完成结伴平分失败者瑜币
	BuyPracticeWithPoint                      // 购买瑜乐大学课程时使用瑜币兑换
	BindWechat                                // 绑定微信 user_grow_log_77
	PlayQuitSession                           // 退出课程
	PlayQuitSessionInProgram                  // 退出计划中课程
	PlayQuitUserSchedule                      // 退出练习进度
	PracticeDaily                             // 每天练习记录
	ShareResult                               // 每天分享成功记录
	BrowseKol                                 // 浏览名师课堂
	BrowseMemberProgram                       // 浏览会员计划
	BrowseMemberCenter                        // 浏览会员中心
	BrowseSelfScale                           // 浏览个人-体脂秤页面
	JoinSelfControlFund                       // 加入挑战赛
	BrowseShop                                // 浏览商城
	JoinPartner                               // 加入结伴
	ListenOnceDay                             // 每日收听每日一听
	BrowseCamp                                // 浏览训练营
	BrowseCommunity                           // 浏览社区
	UnUse93                                   // 未使用
	UnUse94                                   // 未使用
	UnUse95                                   // 未使用
	UnUse96                                   // 未使用
	UnUse97                                   // 未使用
	UnUse98                                   // 未使用
	UnUse99                                   // 未使用
	ExchangeProduct                           // 兑吧操作
	TimeLimitTask                             // 限时任务
	BrowseProgram                             // 浏览计划
	BrowseSession                             // 浏览课程
	BrowseKOLEx                               // 浏览名师课堂
	BrowseO2Session                           // 浏览训练营
	BrowseRYT                                 // 浏览线下教陪
	LiveEvaluate                              // 直播课程评价行为记录
	LiveBroadcast                             // 直播习练历程上报
	LivePlayback                              // 直播(回放)习练历程上报
	LivePlaybackComplete                      // 直播(回放)完成 习练历程上报(占位 无表)
)

var LiveUsrActionTable = []int32{LiveBroadcast, LivePlayback, LivePlaybackComplete}

func IsLiveUsrActionTable(t int32) bool {
	for _, v := range LiveUsrActionTable {
		if v == t {
			return true
		}
	}
	return false
}
