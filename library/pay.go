package library

const (
	PaymentAndroidWx               = 11
	PaymentAndroidAlipay           = 12
	PaymentAndroidAlipayStages     = 16  // 花呗分期支付类型Water2019年4月8日10:35:59
	PaymentAndroidWxContract       = 17  // 微信签约代扣Water2019-05-3109:06Water
	PaymentIosIap                  = 20  // ios 苹果支付
	PaymentIosWx                   = 21  // ios微信支付
	PaymentIosAlipay               = 22  // ios支付宝
	PaymentYB                      = 24  // Y币
	PaymentIosAlipayStages         = 26  // 花呗分期支付类型Water2019年4月8日10:36:08
	PaymentIosWxContract           = 27  // 微信签约代扣Water2019-05-3109:06Water
	PaymentWebAlipay               = 32  // 官网支付宝
	PaymentAndroidWebWeixinJsapi   = 51  // 安卓微信公众号内支付
	PaymentIosWebWeixinJsapi       = 61  // ios微信公众号内支付
	PaymentAndroidWebWeixinH5      = 81  // 安卓微信h5支付
	PaymentIosWebWeixinH5          = 91  // ios微信h5支付
	PaymentAndroidWebAlipayH5      = 82  // 安卓支付宝h5支付
	PaymentIosWebAlipayH5          = 92  // ios支付宝h5支付
	PaymentAndroidWebWeixinMiniapp = 111 // 安卓训练营小程序支付
	PaymentIosWebWeixinMiniapp     = 121 // ios微信训练营小程序支付
	PaymentAndroidAlipayContract   = 144 // 支付宝自动订阅
	PaymentWeixinGzhContract       = 145 // 微信公众号自动订阅
	PaymentH5AlipayContract        = 146 // 支付宝h5自动订阅
	PaymentIosAlipayContract       = 147 // 支付宝ios自动订阅
	PaymentTiktokAndroid           = 151 // 安卓抖音支付
	PaymentTiktokIOS               = 152 // ios抖音支付
	PaymentTiktokAndroidContract   = 153 // 安卓抖音支付自动订阅
	PaymentTiktokIOSContract       = 154 // ios抖音支付自动订阅
	PaymentHMSContract             = 161 // 鸿蒙支付

	PaymentWeixinQrcode             = 41  // 微信扫码支付
	PaymentAlipayQrcode             = 42  // 支付宝扫码支付
	PaymentTvQrcode                 = 44  // TV扫码
	PaymentThirdPlatformPay         = 100 // 对接第三方平台支付sdk
	PaymentAndroidXIAOMI            = 132 // 小米支付 安卓
	PaymentAndroidXiaomiContract    = 133 // 小米自动扣费 安卓
	PaymentAndroidHuawei            = 15  // 华为支付
	PaymentAndroidHuaweiContract    = 18  // 华为签约代扣-android
	PaymentIosHuaweiContract        = 28  // 华为签约代扣-ios
	PaymentAndroidNewHuawei         = 134 // 华为钱包支付 非自动续订
	PaymentAndroidNewHuaweiContract = 135 // 华为钱包支付  自动续订
)

var PaymentTypeToRefundSource = map[int]int{
	PaymentAndroidWx:               PayTypeWechat,
	PaymentAndroidAlipay:           PayTypeAlipay,
	PaymentAndroidAlipayStages:     PayTypeAlipay,
	PaymentAndroidWxContract:       PayTypeWechat,
	PaymentIosWx:                   PayTypeWechat,
	PaymentIosAlipay:               PayTypeAlipay,
	PaymentIosAlipayStages:         PayTypeAlipay,
	PaymentIosWxContract:           PayTypeWechat,
	PaymentWebAlipay:               PayTypeAlipay,
	PaymentAndroidWebWeixinJsapi:   PayTypeWechat,
	PaymentIosWebWeixinJsapi:       PayTypeWechat,
	PaymentAndroidWebWeixinH5:      PayTypeWechat,
	PaymentIosWebWeixinH5:          PayTypeWechat,
	PaymentAndroidWebAlipayH5:      PayTypeAlipay,
	PaymentIosWebAlipayH5:          PayTypeAlipay,
	PaymentAndroidWebWeixinMiniapp: PayTypeWechat,
	PaymentIosWebWeixinMiniapp:     PayTypeWechat,
	PaymentAndroidAlipayContract:   PayTypeAlipay,
	PaymentWeixinGzhContract:       PayTypeWechat,
	PaymentH5AlipayContract:        PayTypeAlipay,
	PaymentIosAlipayContract:       PayTypeAlipay,
	PaymentWeixinQrcode:            PayTypeWechat,
	PaymentAlipayQrcode:            PayTypeAlipay,
	PaymentTiktokAndroid:           DYPayTypeTikTok,
	PaymentTiktokIOS:               DYPayTypeTikTok,
	PaymentTiktokIOSContract:       DYPayTypeTikTok,
	PaymentTiktokAndroidContract:   DYPayTypeTikTok,
	PaymentTvQrcode:                Other,
	PaymentThirdPlatformPay:        Other,
}

const (
	ConstPaymentOrderTypeVip                 = 1
	ConstPaymentOrderTypePreferentialPackage = 16
)

const (
	ConstWebProductDurationTypeDay   = 1
	ConstWebProductDurationTypeMonth = 2
	ConstWebProductDurationTypeYear  = 3
)

const (
	ConstStepProductTypeMonth    = 1
	ConstStepProductTypeQuarter  = 2
	ConstStepProductTypeYear     = 3
	ConstStepProductTypeHalfYear = 4
)

var ProductTypeYoGaDesc = map[int]string{
	ConstStepProductTypeMonth:    "月卡",
	ConstStepProductTypeQuarter:  "季卡",
	ConstStepProductTypeYear:     "年卡",
	ConstStepProductTypeHalfYear: "半年卡",
}

const (
	ConstMonthDays   = 30
	ConstMonthZero   = 0
	ConstMonthOne    = 1
	ConstMonthTwo    = 2
	ConstMonthThree  = 3
	ConstMonthFour   = 4
	ConstMonthFive   = 5
	ConstMonthSix    = 6
	ConstMonthSeven  = 7
	ConstMonthEight  = 8
	ConstMonthNine   = 9
	ConstMonthTen    = 10
	ConstMonthEleven = 11
	ConstMonthTwelve = 12
)

const (
	OrderIndexTypeWebOrder         = 1  // 会员订单
	OrderIndexTypeYogaO2Order      = 2  // 训练营
	OrderIndexTypeH2Order          = 3  // 挑战赛
	OrderTypeWebProductMain        = 4  // 会员组合套餐
	OrderTypeSelfControlChallenge  = 5  // 挑战赛
	OrderIndexTypePracticeOrder    = 6  // 课程购买
	OrderIndexTypeYogaCurrency     = 7  // Y币购买
	OrderTypePracticeKol           = 8  // 名师课堂
	OrderTypePracticeElective      = 9  // 选修课
	OrderTypeOrderMainCombinedSale = 11 // 打包购买
	OrderTypePreferentialPackage   = 16 // 特惠套餐
	OrderTypeTvEquityProduct       = 20 // TV 大屏卡
	OrderIndexTypeLiveCard         = 23 // 直播
)

const (
	WebOrderTypeVipPoint            = 0  // 瑜币会员订单
	WebOrderTypeVipCommon           = 10 // 普通会员
	WebOrderTypeVipAdvanced         = 20 // 高级会员
	WebOrderTypeVipCommonToAdvanced = 12 // 普通会员升级高级会员
	WebOrderTypeVipToYsvip          = 13 // 其他低级会员升级 高级年费会员 2018-01-04 15:34:13
	WebOrderTypeGroupCommon         = 11 // 普通会员组合
	WebOrderTypeGroupAdvanced       = 21 // 高级会员组合
	WebOrderTypeGroup               = 30 // 无会员组合
	WebOrderTypeGvipAdvanced        = 40 // 新高级会员
	WebOrderTypeGvipGroupAdvanced   = 41 // 新高级会员组合
)
