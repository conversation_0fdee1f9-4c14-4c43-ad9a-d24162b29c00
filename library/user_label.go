package library

const (
	UserTrainTagList               = "train_tag_config"
	UserLabel                      = "user_label_config"
	UserLabelCacheExpireTime       = 60 * 60
	UserSetting              int64 = 10001000442 // 用户配置信息

	DifficultyFilterTagID                = 1
	DifficultyLabelBrowseThreshold       = 3
	DifficultyLabelPracticeThreshold     = 1
	BodyPositionFilterTagID              = 36
	BodyPositionLabelSearchThreshold     = 5
	BodyPositionLabelBrowseThreshold     = 3
	BodyPositionLabelPracticeThreshold   = 1
	ClassificationFilterTagID            = 62
	ClassificationLabelSearchThreshold   = 5
	ClassificationLabelBrowseThreshold   = 3
	ClassificationLabelPracticeThreshold = 1

	PracticeTimeRangeLabelPracticeThreshold = 3
)

// OB属性 1:新用户 2:老用户 3:增值业务OB 4:通用OB
const (
	UnKnown = iota
	NewlyUser
	OldUser
	Appreciation
	Common
)

type SourceType int32

var SourceTypeEum = struct {
	Search   SourceType
	Practice SourceType
	Browse   SourceType
}{
	Search:   1,
	Practice: 2,
	Browse:   3,
}

type PracticeTimeRangeType int32

var PracticeTimeRangeTypeEnum = struct {
	Unknown PracticeTimeRangeType
	Morning PracticeTimeRangeType // 06:00:00-09:00:00
	Noon    PracticeTimeRangeType // 11:00:00-14:00:00
	Evening PracticeTimeRangeType // 21:00:00-23:59:59
	Night   PracticeTimeRangeType // 00:00:00-05:59:59
}{
	Unknown: 0,
	Morning: 1,
	Noon:    2,
	Evening: 3,
	Night:   4,
}

type ObjType int32

func (o ObjType) Int32() int32 {
	return int32(o)
}

// GetDifficultyThreshold 获取难度等级阈值
func GetDifficultyThreshold(sourceType SourceType) int32 {
	if sourceType == SourceTypeEum.Practice {
		return DifficultyLabelPracticeThreshold
	}

	return DifficultyLabelBrowseThreshold
}

// GetBodyPositionThreshold 获取练习部位阈值
func GetBodyPositionThreshold(sourceType SourceType) int32 {
	if sourceType == SourceTypeEum.Practice {
		return BodyPositionLabelPracticeThreshold
	}

	return BodyPositionLabelBrowseThreshold
}

// GetClassificationThreshold 获取流派阈值
func GetClassificationThreshold(sourceType SourceType) int32 {
	if sourceType == SourceTypeEum.Practice {
		return ClassificationLabelPracticeThreshold
	}

	return ClassificationLabelBrowseThreshold
}

var UserEquityTypeEnum = struct {
	Member ObjType
	Kol    ObjType
	Now    ObjType
	Live   ObjType
	Face   ObjType
}{
	Member: 1,
	Kol:    2,
	Now:    3,
	Live:   4,
	Face:   5,
}

var SubDurTypeEnum = struct {
	Month    CEnumType
	Quarter  CEnumType
	Year     CEnumType
	HalfYear CEnumType
}{
	Month:    1,
	Quarter:  2,
	Year:     3,
	HalfYear: 6,
}

var DurTypeEnum = struct {
	One    CEnumType
	Three  CEnumType
	Six    CEnumType
	Twelve CEnumType
}{
	One:    1,
	Three:  3,
	Six:    6,
	Twelve: 12,
}
