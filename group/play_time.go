package group

import (
	"encoding/json"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	newsessionplay "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/sessionplay"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/intelligence"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/practice"
)

type playTimePayload struct {
	UID                 int64 `json:"uid"`
	SourceDevice        int32 `json:"source_device"`
	ActionID            int64 `json:"action_id"`
	SessionID           int64 `json:"session_id"`
	ProgramID           int64 `json:"program_id"`
	PlayTime            int64 `json:"play_time"`
	SessionIndex        int64 `json:"session_index"`
	PracticeCurrentTime int64 `json:"practice_current_time"`
	AtThatTime          int64 `json:"at_that_time"`
	Calorie             int32 `json:"calorie"`
	IsDone              int32 `json:"is_done"`
}

type PlayTimeProcessor struct{}

func (p *PlayTimeProcessor) Run(_, payload []byte) error {
	data := playTimePayload{}

	if err := json.Unmarshal(payload, &data); err != nil {
		return err
	}

	// 安卓7.12.0.5因为上报playTime是带上了只能课程表id，导致习练历程数据问题，因此引入判断
	intelligenceUser, _ := intelligence.TbIntelligenceScheduleUser.GetIntelligenceUser(data.UID, data.ProgramID)

	if intelligenceUser != nil {
		data.ProgramID = 0
	}

	// 练习数据新逻辑处理
	beanNew := &newsessionplay.Detail{
		ReportTime:   data.PracticeCurrentTime,
		SessionID:    data.SessionID,
		PlayTime:     data.PlayTime,
		ActionID:     strconv.Itoa(int(data.ActionID)),
		ProgramID:    data.ProgramID,
		UID:          data.UID,
		Calorie:      data.Calorie,
		DoneStatus:   data.IsDone,
		CreateTime:   data.AtThatTime,
		SourceDevice: data.SourceDevice,
	}

	// 实时上报合法时间为正负1天，，不合法时间只记录不统计
	todayTime := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 0, 0, 0, 0, time.Local)
	if beanNew.ReportTime < todayTime.Unix()-86400 || beanNew.ReportTime > time.Now().Unix() {
		se := &newsessionplay.Expire{
			ReportTime:   beanNew.ReportTime,
			SessionID:    beanNew.SessionID,
			PlayTime:     beanNew.PlayTime,
			ActionID:     beanNew.ActionID,
			ProgramID:    beanNew.ProgramID,
			UID:          beanNew.UID,
			Calorie:      beanNew.Calorie,
			DoneStatus:   beanNew.DoneStatus,
			CreateTime:   beanNew.CreateTime,
			SourceDevice: beanNew.SourceDevice,
		}
		if err := se.Save(); err != nil {
			logger.Error(err)
		}
		return nil
	}
	// Prince 2020-08-25 使用协程规避消息堆积
	go practice.SessionPlay.Stat(beanNew)
	return nil
}
