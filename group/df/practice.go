package df

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/server/srv-task/service/df/practice"
)

var practiceType = struct {
	Default string
}{
	Default: "df_practice",
}

type practicePayload struct {
	Type   string      `json:"type"`
	Params interface{} `json:"params"`
}

type PracticeProcessor struct{}

func (p *PracticeProcessor) Run(_, payload []byte) error {
	data := practicePayload{}
	if err := json.Unmarshal(payload, &data); err != nil {
		return err
	}
	var err error
	params, err := json.Marshal(data.Params)
	if err != nil {
		return err
	}
	switch data.Type {
	case practiceType.Default:
		// 练习数据新逻辑
		_ = practice.Log.StatPracticeLog(params)
	default:
		return nil
	}
	return nil
}
