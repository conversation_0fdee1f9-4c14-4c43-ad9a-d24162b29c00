package df

import (
	"context"
	"encoding/json"

	"gitlab.dailyyoga.com.cn/protogen/pay-bridge-go/paybridge"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/df/order"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/df/product"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	srvo "gitlab.dailyyoga.com.cn/server/srv-task/service/order"
)

type PaymentAmountCheckParam struct {
	Amount  int64  `json:"amount"`
	OrderID string `json:"order_id"`
	Scene   int    `json:"scene"`
}

type RespCheckSign struct {
	Version string `json:"version"`
	Msg     string `json:"msg"`
	Type    string `json:"type"`
}

// 处理Ft 订单
// nolint
func HandleDFOrder(req *PaymentAmountCheckParam) {
	logger.Info("HandleFtOrder", "开始处理订单", req.OrderID)
	orderInfo := order.TbWebOrder.GetItemByOrderID(req.OrderID)
	if orderInfo == nil || orderInfo.PayType == library.PayTypeApple {
		return
	}
	if req.Scene != library.Yes && req.Amount == 0 {
		return
	}
	amount := float64(req.Amount) / 100.0
	scene := "下单告警"
	signPayCallback := false
	if req.Scene == library.Yes {
		scene = "巡检告警"
		if orderInfo.PayType == library.FTPayTypeTikTok {
			completeItem := order.TbCompleteNofify.GetItemByOrderID(orderInfo.OrderID)
			if completeItem != nil {
				cks := RespCheckSign{}
				if err := json.Unmarshal([]byte(completeItem.Notify), &cks); err != nil {
					logger.Error("Tiktok json 解析错误", err)
					return
				}
				if cks.Type == "sign_pay_callback" {
					signPayCallback = true
				}
			}
		}
		payBridge := grpc.GetPayBridgeClient()
		rsp, err := payBridge.QueryOrderInfo(context.Background(), &paybridge.OrderInfoReq{
			AppID:             GetMchIDByOrder(orderInfo),
			MchID:             GetMchIDByOrder(orderInfo),
			PayType:           int32(orderInfo.PayType),
			OutTradeNo:        orderInfo.OrderID,
			IsSignPayCallback: signPayCallback,
		})
		logger.Info("PaymentAmountCheck", "查询订单信息", orderInfo.OrderID, orderInfo.PayType, rsp, err)
		if rsp == nil || err != nil {
			logger.Info("PaymentAmountCheck", "查询订单信息失败", req.OrderID, err)
			return
		}
		if rsp.ErrorCode != 0 || rsp.Data == nil {
			logger.Info("PaymentAmountCheck", "查询订单信息失败", req.OrderID, rsp.ErrorCode, rsp.Msg)
			return
		}
		amount = float64(rsp.Data.TotalAmount) / 100.0
	}
	logger.Info("PaymentAmountCheck", "订单金额", orderInfo.OrderID, amount)
	productItem := product.TbWebProduct.GetItemByID(orderInfo.ProductID)
	if productItem == nil {
		logger.Info("PaymentAmountCheck", "查询商品信息失败", req.OrderID)
		return
	}
	if productItem.OfferType == library.ConstOfferTypeNo {
		LowPriceAlarm(orderInfo, productItem, amount, 0)
	}
	if amount == orderInfo.OrderAmount && productItem.Price == amount {
		return
	}
	productType := GetStepConfigByProduct(productItem)
	if productItem.IsSubscribe != library.Yes || orderInfo.PayType != library.PayTypeAlipay {
		logger.Info("PaymentAmountCheck", "告警-1", orderInfo.OrderID)
		// 告警
		srvo.PaymentAmountCheck(&srvo.PaymentAmountCheckRequest{
			ProjectName:   library.ProjectTypeEnumDesc[library.ProjectTypeEnum.DanceFit],
			UID:           orderInfo.UID,
			ProductID:     orderInfo.ProductID,
			ProductType:   library.DfProductTypeDesc[int(productType)],
			ProductAmount: productItem.Price,
			OrderAmount:   amount,
			OrderID:       orderInfo.OrderID,
			Scene:         scene,
		})
		return
	}
	alipayContractCharge := order.TbAlipayCharge.GetSuccessOrderByOrderID(orderInfo.OrderID)
	stepAmount := 0.0
	if alipayContractCharge != nil {
		stepAmount = alipayContractCharge.StepAmount
	}
	logger.Infof("PaymentAmountCheck 订单ID: %s, 订单金额: %f, 产品金额: %f, 优惠金额: %f, 账单金额: %f",
		orderInfo.OrderID, orderInfo.OrderAmount, productItem.Price, stepAmount, amount)
	if amount == orderInfo.OrderAmount && productItem.Price-stepAmount == amount {
		return
	}
	if productItem.OfferType == library.ConstOfferTypeNo {
		// 告警
		logger.Info("PaymentAmountCheck", "告警-2", orderInfo.OrderID)
		srvo.PaymentAmountCheck(&srvo.PaymentAmountCheckRequest{
			ProjectName:   library.ProjectTypeEnumDesc[library.ProjectTypeEnum.DanceFit],
			UID:           orderInfo.UID,
			ProductID:     orderInfo.ProductID,
			ProductType:   library.DfProductTypeDesc[int(productType)],
			ProductAmount: productItem.Price - stepAmount,
			OrderAmount:   amount,
			OrderID:       orderInfo.OrderID,
			Scene:         scene,
		})
		return
	}
	if orderInfo.IsRenew != library.Yes {
		price := CalculateOffer(productItem)
		if amount == orderInfo.OrderAmount && price == amount {
			return
		}
	}
	if alipayContractCharge == nil {
		logger.Info("PaymentAmountCheck", "告警-3", orderInfo.OrderID)
		srvo.PaymentAmountCheck(&srvo.PaymentAmountCheckRequest{
			ProjectName:   library.ProjectTypeEnumDesc[library.ProjectTypeEnum.DanceFit],
			UID:           orderInfo.UID,
			ProductID:     orderInfo.ProductID,
			ProductType:   library.DfProductTypeDesc[int(productType)],
			ProductAmount: productItem.Price - stepAmount,
			OrderAmount:   amount,
			OrderID:       orderInfo.OrderID,
			Scene:         scene,
		})
		return
	}
	contract := order.TbAlipayContract.GetItemByContractCode(alipayContractCharge.ContractCode)
	// 首购优惠
	FormatOffer(contract, orderInfo.IsRenew == library.Yes, productItem, alipayContractCharge)
	logger.Info("PaymentAmountCheck", "FormatOffer=1", productItem.Price,
		amount == orderInfo.OrderAmount, productItem.Price-stepAmount == amount)
	if amount == orderInfo.OrderAmount && productItem.Price-stepAmount == amount {
		return
	}
	// 告警
	logger.Info("PaymentAmountCheck", "告警-4", orderInfo.OrderID)
	srvo.PaymentAmountCheck(&srvo.PaymentAmountCheckRequest{
		ProjectName:   library.ProjectTypeEnumDesc[library.ProjectTypeEnum.DanceFit],
		UID:           orderInfo.UID,
		ProductID:     orderInfo.ProductID,
		ProductType:   library.DfProductTypeDesc[int(productType)],
		ProductAmount: productItem.Price - stepAmount,
		OrderAmount:   amount,
		OrderID:       orderInfo.OrderID,
		Scene:         scene,
	})
}

func CalculateOffer(productItem *product.WebProduct) float64 {
	switch productItem.OfferType {
	case library.ConstOfferTypeFirstBuy:
		// 针对首购价格大于原价，特殊处理
		if productItem.OfferFirstBuyPrice > productItem.OriginPrice {
			return productItem.OfferFirstBuyPrice
		}
		return productItem.OfferFirstBuyPrice
	case library.ConstOfferTypeTrial, library.ConstOfferTypeTrialFirstBuy:
		return productItem.OfferTrialPrice
	}
	return productItem.Price
}

func FormatOffer(contract *order.AlipayContract, isRenew bool, productItem *product.WebProduct,
	alipayContractCharge *order.AlipayContractCharge) {
	if contract == nil || contract.OfferType <= library.ConstOfferTypeNo {
		return
	}
	switch contract.OfferType {
	case library.ConstOfferTypeFirstBuy:
		list := order.TbAlipayCharge.GetValidChargeByCode(contract.ContractCode, alipayContractCharge.ID)
		if len(list) < int(contract.OfferFirstBuyCycle) {
			productItem.Price = contract.OfferFirstBuyPrice
		}
		logger.Info("PaymentAmountCheck", "FormatOffer", contract.OrderID, alipayContractCharge.ID,
			len(list), contract.OfferFirstBuyCycle, productItem.Price)
	case library.ConstOfferTypeTrial:
		if !isRenew {
			productItem.Price = contract.OfferTrialPrice
		}
	case library.ConstOfferTypeTrialFirstBuy:
		if !isRenew {
			productItem.Price = contract.OfferTrialPrice
		} else {
			list := order.TbAlipayCharge.GetValidChargeByCode(contract.ContractCode, alipayContractCharge.ID)
			if len(list) < int(contract.OfferFirstBuyCycle) {
				productItem.Price = contract.OfferFirstBuyPrice
			}
		}
	}
}

type AlipayCompleteRes struct {
	AppID string `json:"app_id"`
}
type WechatCompleteRes struct {
	MchID string `json:"mch_id"`
}

func GetMchIDByOrder(orderItem *order.WebOrder) string {
	if orderItem.MchID != "" {
		return orderItem.MchID
	}
	completeOrder := order.TbCompleteNofify.GetItemByOrderID(orderItem.OrderID)
	if completeOrder == nil {
		return ""
	}
	switch completeOrder.PayType {
	case library.PayTypeAlipay:
		alires := &AlipayCompleteRes{}
		err := json.Unmarshal([]byte(completeOrder.Notify), alires)
		if err == nil {
			return alires.AppID
		}
	case library.PayTypeWechat:
		wechatres := &WechatCompleteRes{}
		err := json.Unmarshal([]byte(completeOrder.Notify), wechatres)
		if err == nil {
			return wechatres.MchID
		}
	case library.FTPayTypeTikTok:
		tiktokOrder := order.TbWebOrder.GetItemByOrderID(orderItem.OrderID)
		if tiktokOrder != nil {
			return tiktokOrder.MchID
		}
	}
	return ""
}

// 低价告警
// nolint
func LowPriceAlarm(orderInfo *order.WebOrder, productItem *product.WebProduct, amount, stepAmount float64) {
	productType := GetStepConfigByProduct(productItem)
	switch productType {
	case library.DfProductType1Month:
		if amount >= 9 {
			return
		}
	case library.DfProductType3Month:
		if amount >= 58 {
			return
		}
	case library.DfProcuctType12Month:
		if amount >= 68 {
			return
		}
	case library.DfProductType6Month:
		if amount >= 58 {
			return
		}
	default:
		return
	}
	logger.Info("PaymentAmountCheck", "低价告警", orderInfo.OrderID)
	srvo.PaymentAmountCheck(&srvo.PaymentAmountCheckRequest{
		ProjectName:   library.ProjectTypeEnumDesc[library.ProjectTypeEnum.DanceFit],
		UID:           orderInfo.UID,
		ProductID:     orderInfo.ProductID,
		ProductType:   library.DfProductTypeDesc[int(productType)],
		ProductAmount: productItem.Price - stepAmount,
		OrderAmount:   amount,
		OrderID:       orderInfo.OrderID,
		Scene:         "低价告警",
	})
}

type DurationType int

// DurationTypeEnum 会员产品的会员时间类型
var DurationTypeEnum = struct {
	Day   DurationType
	Month DurationType
	Year  DurationType
}{
	Day:   1,
	Month: 2,
	Year:  3,
}

type StepPTypeInt int

var StepPTypeEnum = struct {
	Month    StepPTypeInt
	Quarter  StepPTypeInt
	Year     StepPTypeInt
	HalfYear StepPTypeInt
}{
	Month:    1,
	Quarter:  2,
	Year:     3,
	HalfYear: 4,
}

var DurationTypeToDays = map[DurationType]int{
	DurationTypeEnum.Day:   1,
	DurationTypeEnum.Month: 30,
	DurationTypeEnum.Year:  365,
}

const (
	MonthThree  = 3
	MonthSix    = 6
	MonthTwelve = 12
)

func GetStepConfigByProduct(item *product.WebProduct) StepPTypeInt {
	var stepProductType StepPTypeInt
	switch item.DurationType {
	case int(DurationTypeEnum.Day):
		if item.DurationValue < DurationTypeToDays[DurationTypeEnum.Month] {
			return 0
		}
		stepProductType = StepPTypeEnum.Month
	case int(DurationTypeEnum.Month):
		stepProductType = StepPTypeEnum.Month
		if item.DurationValue == MonthThree {
			stepProductType = StepPTypeEnum.Quarter
		}
		if item.DurationValue == MonthSix {
			stepProductType = StepPTypeEnum.HalfYear
		}
		if item.DurationValue == MonthTwelve {
			stepProductType = StepPTypeEnum.Year
		}
	case int(DurationTypeEnum.Year):
		stepProductType = StepPTypeEnum.Year
	default:
		return 0
	}
	return stepProductType
}
