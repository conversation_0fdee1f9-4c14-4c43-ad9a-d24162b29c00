package group

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/server/srv-task/service/order"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/payment"
)

var paymentType = struct {
	Complete      int
	Order4IosIap  int
	PaymentReport int
	ProtocolQuery int
}{
	Complete:      1, // 异步支付完成
	Order4IosIap:  2, // iOS 支付成功后回调
	PaymentReport: 3, // 付款处理
	ProtocolQuery: 4, // 签约查询
}

type paymentPayload struct {
	Type   int         `json:"type"`
	Params interface{} `json:"params"`
}

type PaymentProcessor struct{}

func (p *PaymentProcessor) Run(_, payload []byte) error {
	data := paymentPayload{}
	if err := json.Unmarshal(payload, &data); err != nil {
		return err
	}
	var err error
	params, err := json.Marshal(data.Params)
	if err != nil {
		return err
	}
	switch data.Type {
	case paymentType.Complete:
		var payload payment.CompleteOrderPayload
		if err := json.Unmarshal(params, &payload); err != nil {
			return err
		}
		payment.ServicePayment.CompleteOrder(&payload)
	case paymentType.Order4IosIap:
		var payload order.CreateOrder4IosIapPayload
		if err := json.Unmarshal(params, &payload); err != nil {
			return err
		}
		order.ServiceIosOrder.CreateOrder4IosIap(&payload)
	case paymentType.PaymentReport:
		var payload payment.ReportPaymentPayload
		if err := json.Unmarshal(params, &payload); err != nil {
			return err
		}
		payment.ServiceReport.ReportPayment(&payload)
	case paymentType.ProtocolQuery:
		var payload order.PQuery
		if err := json.Unmarshal(params, &payload); err != nil {
			return err
		}
		return order.HandleProtocolQuery(&payload)
	default:
		return nil
	}
	return nil
}
