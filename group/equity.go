package group

import (
	"encoding/json"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/group/equity"
)

var equityPayloadType = struct {
	InitUserEquity string
	AddUserEquity  string
	TransferEquity string
}{
	InitUserEquity: "init_user_kol_equity", // 初始化用户特权
	AddUserEquity:  "add_user_equity",      // 添加特权卡
	TransferEquity: "transfer_user_equity", // 转移用户权益
}

type EquityPayload struct {
	Type   string      `json:"type"`
	Params interface{} `json:"params"`
}

type EquityProcessor struct{}

func (e *EquityProcessor) getFunc(dataType string) func(params []byte) error {
	var typeFunc = map[string]func(params []byte) error{
		// 初始化用户的名师课堂会员卡
		equityPayloadType.InitUserEquity: e.InitUserEquity,
		// 名师课堂会员卡 减脂训练营学员卡 入门训练营学员卡 TV大屏卡 Now正念冥想权益卡
		equityPayloadType.AddUserEquity: e.AddUserEquity,
		// 迁移用户权益
		equityPayloadType.TransferEquity: e.TransferUserEquity,
	}

	if i, ok := typeFunc[dataType]; ok {
		return i
	}
	return nil
}

// InitUserEquity 初始化用户权益
func (e *EquityProcessor) InitUserEquity(params []byte) error {
	var payload equity.SetInitUserKolEquityPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return equity.SetInitUserKolEquity(&payload)
}

// AddUserEquity 添加特权卡
func (e *EquityProcessor) AddUserEquity(params []byte) error {
	var payload equity.SetAddUserKolEquityPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return equity.SetAddUserEquity(&payload)
}

// TransferUserEquity 迁移权益
func (e *EquityProcessor) TransferUserEquity(params []byte) error {
	var payload equity.SetTransferUserEquityPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	jsonParams, _ := json.Marshal(payload)
	logger.Info("---TransferUserEquity-- payload:", string(jsonParams))
	if payload.OldUID != 0 {
		return equity.SetTransferUserEquitTikTok(&payload)
	}
	return equity.SetTransferUserEquity(&payload)
}

// Run 执行函数
func (e *EquityProcessor) Run(_, payload []byte) error {
	data := EquityPayload{}

	if err := json.Unmarshal(payload, &data); err != nil {
		return err
	}
	var err error
	params, err := json.Marshal(data.Params)
	if err != nil {
		return err
	}

	if f := e.getFunc(data.Type); f != nil {
		return f(params)
	}
	return errors.New("unknown async process type")
}
