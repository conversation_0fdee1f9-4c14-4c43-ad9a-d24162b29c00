package async

import (
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/group"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/user"
)

type UserLabelPayload struct {
	UID                 int64  `json:"uid"`
	ProgramID           int64  `json:"program_id"`
	SessionID           int64  `json:"session_id"`
	O2SessionID         int64  `json:"o2_session_id"`
	SessionIndex        int32  `json:"session_index"`
	UserActionTable     int32  `json:"user_action_table"`
	Keyword             string `json:"keyword"`
	LinkType            int32  `json:"link_type"`
	SourceType          int32  `json:"source_type"`
	PracticeCurrentTime int64  `json:"practice_current_time"`
}

// UpdateUserLabel 更新用户标签
func ProcessUserLabel(data *UserLabelPayload) error {
	if data.UID < 1 {
		return nil
	}
	userLabelData := &group.LastUserLabelData{
		UID:             data.UID,
		ProgramID:       data.ProgramID,
		SessionID:       data.SessionID,
		O2SessionID:     data.O2SessionID,
		SessionIndex:    data.SessionIndex,
		UserActionTable: data.UserActionTable,
		Keyword:         data.Keyword,
		LinkType:        data.LinkType,
		ReportTime:      data.PracticeCurrentTime,
		SourceType:      data.SourceType,
	}
	return user.ServiceUserLabel.ProcessUserLabel(userLabelData)
}
