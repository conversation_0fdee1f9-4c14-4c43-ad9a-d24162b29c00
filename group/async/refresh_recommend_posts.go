package async

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

// refreshRecommendPosts 刷新推荐帖子缓存
func RefreshRecommendPosts() error {
	// request 620api
	resp, err := requests.New620Client(config.Get().Php620Address).Get("Yogaparadise/Index/refreshRecommedPostsList")
	if err != nil {
		return err
	}
	if !resp.IsOK() {
		logger.Errorf("refresh recommendPostsList status: %d", resp.StatusCode)
	} else {
		logger.Debugf("refresh recommendPostsList success; status: %d", resp.StatusCode)
	}
	return nil
}
