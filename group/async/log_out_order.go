package async

import (
	"context"

	yoga_cs "gitlab.dailyyoga.com.cn/protogen/yoga-cs-go/yoga-cs"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
)

type LogOutOrderPayload struct {
	PayType    int64  `json:"pay_type"`
	OrderID    string `json:"order_id"`
	NotifyData string `json:"notify_data"`
}

func ParseLogOutOrder(payload *LogOutOrderPayload) error {
	_, err := grpc.GetYoGaCsClientClient().LogOutOrder(context.Background(), &yoga_cs.LogOutOrderReq{
		Project:    1, // 瑜伽
		PayType:    payload.PayType,
		OrderID:    payload.OrderID,
		NotifyData: payload.NotifyData,
	})
	if err != nil {
		return err
	}
	return nil
}
