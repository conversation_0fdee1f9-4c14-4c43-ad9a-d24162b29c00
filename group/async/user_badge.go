package async

import (
	"gitlab.dailyyoga.com.cn/server/srv-task/service/badge"
)

type UserBadgePayload struct {
	UID         int64   `json:"uid"`
	Type        []int32 `json:"type"`
	BadgeIDList []int64 `json:"badge_id_list"`
}

// ProcessUserBadge 生成老用户徽章
func ProcessUserBadge(data UserBadgePayload) error {
	if data.UID < 1 {
		return nil
	}

	return badge.ServiceUserOldBadge.ProcessUserBadge(data.UID, data.Type, data.BadgeIDList)
}
