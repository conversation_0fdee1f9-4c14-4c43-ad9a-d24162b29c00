package async

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/push"
)

// water 2020-10-20 清理用户推送本地消息
type ClearLocalDataPayload struct {
	UID     int64 `json:"uid"`
	MsgType int32 `json:"msg_type"`
}

type MessageType int

var MessageEnum = struct {
	YoMi          MessageType // 瑜小蜜通知
	Comment       MessageType // 评论
	NewFans       MessageType // 新增粉丝
	Like          MessageType // 赞帖子
	Reply         MessageType // 回复帖子
	System        MessageType // 系统通知
	PartnerApply  MessageType // 加入结伴申请通知
	ApplyFeedback MessageType // 加入结伴申请通知反馈
}{
	YoMi:          1,
	Comment:       2,
	NewFans:       3,
	Like:          4,
	Reply:         5,
	System:        6,
	PartnerApply:  7,
	ApplyFeedback: 8,
}

func (e MessageType) ToInt32() int32 {
	return int32(e)
}

type clearLocal struct {
	UID            int64
	MsgType        int32
	ArrCondMsgType []int32
}

// /620/notice/clearLocalData 处理清理工作
func ClearLocalDataMain(d *ClearLocalDataPayload) error {
	if d.MsgType < 1 {
		return fmt.Errorf("清空本地数据 MsgType 类型错误:%d--%d", d.UID, d.MsgType)
	}
	t := &push.LastDetail{
		UID: d.UID,
	}
	c := &clearLocal{
		UID:     d.UID,
		MsgType: d.MsgType,
	}
	var err error
	switch d.MsgType {
	case MessageEnum.YoMi.ToInt32(),
		MessageEnum.NewFans.ToInt32(),
		MessageEnum.Like.ToInt32():
		c.ArrCondMsgType = []int32{d.MsgType}
	case MessageEnum.Comment.ToInt32():
		c.ArrCondMsgType = []int32{d.MsgType, MessageEnum.Reply.ToInt32()}
	default:
		return err
	}
	if err = t.DelForUIDMessage(c.ArrCondMsgType); err != nil {
		return fmt.Errorf("清空本地数据 last_notice_push_detail 出错")
	}
	go c.clearPushDetail()
	return err
}

func (c *clearLocal) getMonthFromRange(t1, t2 int64) [][]int64 {
	data := make([][]int64, 0)
	getMonthStart := func(t int64) int64 {
		d := time.Unix(t, 0)
		d = d.AddDate(0, 0, -d.Day()+1)
		startMonth := time.Date(d.Year(), d.Month(), d.Day(), 0, 0, 0, 0, time.Local)
		return startMonth.Unix()
	}
	getMonthEnd := func(t int64) int64 {
		monthTime := time.Unix(getMonthStart(t), 0).AddDate(0, 1, -1)
		monthEnd := time.Date(monthTime.Year(), monthTime.Month(), monthTime.Day(), 23, 59, 59, 0, time.Local)
		return monthEnd.Unix()
	}

	start := getMonthStart(t1)
	end := getMonthEnd(t2)
	if start > end {
		return data
	}
	var i int
	for {
		if start > end {
			break
		}
		temp := make([]int64, 0)
		tt := time.Unix(start, 0)
		if i == 0 {
			temp = append(temp, start)
			startTime := time.Unix(start, 0).AddDate(0, 1, -1)
			monthEnd := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 23, 59, 59, 0, time.Local)
			temp = append(temp, monthEnd.Unix())
		} else if time.Unix(start, 0).Format("200601") == time.Unix(end, 0).Format("200601") {
			break
		} else {
			startT := time.Date(tt.Year(), tt.Month(), tt.Day(), 0, 0, 0, 0, time.Local)
			temp = append(temp, startT.Unix())
			startTime := startT.AddDate(0, 1, -1)
			monthEnd := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 23, 59, 59, 0, time.Local)
			temp = append(temp, monthEnd.Unix())
		}
		start += time.Unix(start, 0).AddDate(0, 1, 0).Unix() - start
		data = append(data, temp)
	}
	return data
}

// 处理用户历史数据
func (c *clearLocal) clearPushDetail() {
	now := time.Now()
	startTime := now.AddDate(0, -3, 0).Unix()
	timeNow := now.Unix()
	arrMonthIndex := c.getMonthFromRange(startTime, timeNow)
	for _, v := range arrMonthIndex {
		p := &push.Detail{
			CreateTime: v[0],
			UID:        c.UID,
		}
		if c.MsgType == MessageEnum.YoMi.ToInt32() {
			yoMi := &push.Detail{
				CreateTime: v[0],
				FromUID:    c.UID,
			}
			if err := yoMi.DelForUIDMessage(c.ArrCondMsgType, v[0], v[1]); err != nil {
				logger.Errorf("清理yomi_notice_push_detail异常, err: %s", err)
			}
		}
		if err := p.DelForUIDMessage(c.ArrCondMsgType, v[0], v[1]); err != nil {
			logger.Errorf("清理notice_push_detail异常, err: %s", err)
		}
	}
}
