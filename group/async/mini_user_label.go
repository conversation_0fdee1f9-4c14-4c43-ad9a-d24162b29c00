package async

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/user"
)

type MiniUserLabelPayload struct {
	UID int64 `json:"uid"`
	Age int32 `json:"age"`
	BMI int32 `json:"bmi"`
}

// ProcessMiniUserLabel 更新用户标签
func ProcessMiniUserLabel(data *MiniUserLabelPayload) error {
	if data.UID < 1 {
		return nil
	}
	param := &library.MiniAppUserLabel{
		UID:       data.UID,
		Age:       data.Age,
		BMI:       data.BMI,
		IsMiniAPP: library.Yes,
	}
	if err := user.ServiceUserLabel.CalcMiniAppUserLabel(param); err != nil {
		logger.Warn("更新小程序用户标签失败", err)
	}
	return nil
}
