package async

import (
	"context"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"

	"gitlab.dailyyoga.com.cn/protogen/srv-usercenter-go/proto"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type UserWalletPayload struct {
	UID        int64  `json:"uid"`
	Amount     int32  `json:"amount"`
	SourceType int32  `json:"source_type"`
	SourceID   int32  `json:"source_id"`
	RecordDesc string `json:"record_desc"`
	OrderID    string `json:"order_id"`
	OrderIndex int32  `json:"order_index"`
	Time       int32  `json:"time"`
	IsRollBack int32  `json:"is_roll_back"`
	WalletType int32  `json:"wallet_type"`
	UpdateType int32  `json:"update_type"`
}

func WalletEarnMoney(data UserWalletPayload) error {
	rpc := grpc.GetUserCenterClient()
	isRollBack := false

	if data.IsRollBack == 1 {
		isRollBack = true
	}

	rsp, err := rpc.WalletEarnMoney(context.Background(), &proto.WalletEarnMoneyRequest{
		UID:        data.UID,
		Amount:     data.Amount,
		SourceType: data.SourceType,
		SourceId:   data.SourceID,
		RecordDesc: data.RecordDesc,
		OrderId:    data.OrderID,
		OrderIndex: data.OrderIndex,
		Time:       data.Time,
		IsRollback: isRollBack,
		WalletType: data.WalletType,
	})

	if err != nil {
		logger.Error("用户钱包充值失败:", data.UID, data.Amount, data.WalletType, data.SourceType, rsp.Msg)
	}

	return err
}

func WalletExpenseMoney(data UserWalletPayload) error {
	rpc := grpc.GetUserCenterClient()

	rsp, err := rpc.WalletExpenseMoney(context.Background(), &proto.WalletExpenseMoneyRequest{
		UID:        data.UID,
		Amount:     data.Amount,
		SourceType: data.SourceType,
		SourceId:   data.SourceID,
		RecordDesc: data.RecordDesc,
		OrderId:    data.OrderID,
		OrderIndex: data.OrderIndex,
		Time:       data.Time,
		WalletType: data.WalletType,
	})

	if err != nil {
		logger.Error("用户钱包扣钱失败:", data.UID, data.Amount, data.WalletType, data.SourceType, rsp.Msg)
	}

	return err
}

// UserWallet 钱包充值
func UserWallet(data UserWalletPayload) error {
	if data.UID < 0 || data.Amount < 0 {
		return nil
	}

	if data.UpdateType == 2 {
		// 减钱
		for i := 0; i < library.InterfaceRetryNum; i++ {
			if err := WalletExpenseMoney(data); err != nil {
				continue
			}

			break
		}
	} else {
		// 加钱
		for i := 0; i < library.InterfaceRetryNum; i++ {
			if err := WalletEarnMoney(data); err != nil {
				continue
			}

			break
		}
	}

	return nil
}
