package async

import (
	"context"
	"errors"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/push"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type SaveUserInfoForPushPayload struct {
	UID      int64               `json:"uid"`
	DeviceID string              `json:"device_id"`
	Channel  library.ChannelType `json:"channel"`
	Type     int32               `json:"type"`
	Version  string              `json:"version"`
	Token    string              `json:"token"`
}

// deprecated
// 异步保存用户信息供push使用
func SaveUserInfoForPush(data *SaveUserInfoForPushPayload) error {
	if data.UID < 0 {
		return errors.New("invalid uid")
	}
	go data.saveUserInfo()
	return nil
}

// deprecated
func (data *SaveUserInfoForPushPayload) saveUserInfo() {
	ctx := context.Background()
	redis := cache.GetLockRedis()
	lockKey := "SaveUserInfoForPush:" + strconv.Itoa(int(data.UID))
	value := strconv.FormatInt(time.Now().Unix(), 10)
	if lock, err := redis.SetNX(ctx, lockKey, value, 5*time.Second).Result(); err != nil {
		logger.Error(err)
		return
	} else if !lock {
		return
	}
	defer func() {
		if err := redis.Del(ctx, lockKey).Err(); err != nil {
			logger.Error(err)
		}
	}()
	session := usercenter.GetEngineMaster().NewSession()
	var rollBackErr error
	defer session.Close()
	defer func() {
		if rollBackErr != nil {
			logger.Error(rollBackErr)
			if err := session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()

	rollBackErr = push.TbThirdToken.DeleteByDeviceIDAndChannel(data.DeviceID, data.Channel)
	if rollBackErr != nil {
		return
	}

	rollBackErr = push.TbThirdToken.DeleteByUID(data.UID)
	if rollBackErr != nil {
		return
	}

	result := &push.ThirdToken{
		UID:      data.UID,
		DeviceID: data.DeviceID,
		Status:   library.StatusNormal,
		Channel:  data.Channel,
		Version:  data.Version,
		Type:     data.Type,
		Token:    data.Token,
	}
	if rollBackErr = result.Save(); rollBackErr != nil {
		return
	}
	rollBackErr = session.Commit()
	if rollBackErr != nil {
		return
	}
}
