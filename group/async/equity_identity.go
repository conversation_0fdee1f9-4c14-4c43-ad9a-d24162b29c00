package async

import (
	"context"
	"encoding/json"
	"fmt"

	"strconv"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/crypto"
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/goroutine"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/redisv2"
	sc "gitlab.dailyyoga.com.cn/server/go-artifact/sensorsdata"

	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/equity"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/live"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/order"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/yogao2school"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type UpEquityIdentityPayload struct {
	UID      int64 `json:"uid"`
	HasVip   bool  `json:"has_vip"`
	HasKol   bool  `json:"has_kol"`
	HasLive  bool  `json:"has_live"`
	HasNow   bool  `json:"has_now"`
	HasEshop bool  `json:"has_eshop"`
	HasCamp  bool  `json:"has_camp"`
	NeedAll  bool  `json:"need_all"`
}

// UserAllVipLabelEvent 神策事件上报
type UserAllVipLabelEvent struct {
	IsYogaVIP  int `json:"is_yoga_vip"`
	IsMsktVIP  int `json:"is_mskt_vip"`
	IsLiveVIP  int `json:"is_live_vip"`
	IsMXVIP    int `json:"is_mx_vip"`
	IsBuyEshop int `json:"is_buy_eshop"`
	IsBuyCamp  int `json:"is_buy_camp"`
}

const (
	LabelNo = iota
	LabelYes
)

var keyM = "UploadUserAllVipLabelEvent:Lock:"
var uCacheK = "UploadUserAllVipLabelEvent:data:"

// UploadUserAllVipLabelEvent 后续做优化功能 按需查询
func UploadUserAllVipLabelEvent(data *UpEquityIdentityPayload) error {
	rd := cache.GetLockRedis()
	ctx := context.Background()
	userCacheK := fmt.Sprintf(uCacheK+"%d", data.UID)
	cData, _ := rd.Get(ctx, userCacheK).Result()
	userEvent := &UserAllVipLabelEvent{}
	userEvent.getAllFunc(data.UID)
	jsonStr, err := json.Marshal(userEvent)
	if err != nil {
		logger.Error("全渠道用户身份上报 异常请检查", err)
		return err
	}
	if err == nil && cData != "" {
		md5StrOrg := crypto.Byte2Md5([]byte(cData))
		var md5StrDB string
		if err == nil {
			md5StrDB = crypto.Byte2Md5(jsonStr)
		}
		if md5StrOrg == md5StrDB && !setScLock(ctx, rd, data.UID) {
			return err
		}
	}
	setScLock(ctx, rd, data.UID)
	if err := rd.Set(ctx, userCacheK, string(jsonStr), getTodayEndDuration()).Err(); err != nil {
		logger.Error("全渠道用户身份上报 设置缓存异常请检查", err)
	}
	logger.Infof("UploadUserAllVipLabelEvent sc %+v", userEvent)
	userEvent.Track(strconv.FormatInt(data.UID, 10))
	return nil
}

// setScLock 加锁
func setScLock(ctx context.Context, rd *redisv2.Client, uid int64) bool {
	userKeyM := fmt.Sprintf(keyM+"%d", uid)
	if lock, err := rd.SetNX(ctx, userKeyM, 1, getTodayEndDuration()).Result(); err != nil {
		logger.Error(err)
		return true
	} else if !lock {
		return false
	}
	return true
}

// getTodayEndDuration 获取当天的结束时间
func getTodayEndDuration() time.Duration {
	loc, _ := time.LoadLocation("Asia/Shanghai")
	now := time.Now()
	tomorrow := time.Date(now.Year(), now.Month(), now.Day()+1, 00, 00, 00, 0, loc)
	return tomorrow.Sub(now)
}

// 获取全部的标签
func (o *UserAllVipLabelEvent) getAllFunc(uid int64) {
	fns := []func(uid int64){
		o.getYogaVIP,
		o.getEquity,
		o.getLive,
		o.getCamp,
		o.getShop,
	}
	for _, fn := range fns {
		fn(uid)
	}
}

// 获取vip状态
func (o *UserAllVipLabelEvent) getYogaVIP(uid int64) {
	vipList := user.TbUserMemberDuration.GetByUID(uid, false)
	if len(vipList) > 0 {
		o.IsYogaVIP = LabelYes
	}
}

// 获取冥想和名师课堂状态
func (o *UserAllVipLabelEvent) getEquity(uid int64) {
	eList := equity.TbUserEquityDuration.GetList(uid)
	if len(eList) > 0 {
		now := time.Now().Unix()
		for _, v := range eList {
			if v.EndTime < now {
				v.Status = library.DataStatusEnum.Delete.ToInt32()
				if err := v.Update(); err != nil {
					logger.Warn("更新用户特权卡失败", err)
				}
				continue
			}
			switch v.EquityType {
			case library.Kol:
				o.IsMsktVIP = LabelYes
			case library.Now:
				o.IsMXVIP = LabelYes
			}
		}
	}
}

// 获取直播卡状态
func (o *UserAllVipLabelEvent) getLive(uid int64) {
	effectiveList := live.TbLiveUserDuration.GetUserEffectiveList(uid)
	if len(effectiveList) > 0 {
		o.IsLiveVIP = LabelYes
	}
}

// 获取训练购买状态
func (o *UserAllVipLabelEvent) getCamp(uid int64) {
	if yogao2school.TbSessionMember.HavePurchasedO2ByUID(uid) {
		o.IsBuyCamp = LabelYes
	}
}

// 获取电商购买状态
func (o *UserAllVipLabelEvent) getShop(uid int64) {
	purchaseTimes := order.TbThirdKdtSubOrder.GetOrderTotalCountByUIDAndShopType(int(uid),
		order.KdtShopTypeEnum.OR)
	if purchaseTimes > 0 {
		o.IsBuyEshop = LabelYes
	}
}

// EventName 神策数据上报
func (UserAllVipLabelEvent) EventName() string {
	if config.Get().Env != env.Product && config.Get().Env != env.Mirror {
		return "h2_user_all_vip_label"
	}
	return "user_all_vip_label"
}

func (UserAllVipLabelEvent) Prefix() string {
	return ""
}

func (o *UserAllVipLabelEvent) Track(uid string) {
	userAllVipLabelEvent := *o
	goroutine.GoSafely(func() {
		if strings.Trim(uid, " ") == "" {
			return
		}
		sc.Track(uid, userAllVipLabelEvent, true)
	})
}
