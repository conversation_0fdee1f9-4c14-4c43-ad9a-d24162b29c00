package async

import (
	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/partner"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/practice"
)

// setTeamPlayTimeRanking 设置班级排行榜
func SetTeamPlayTimeRanking(data practice.SetTeamPlayTimeRankingPayload) error {
	if data.TeamID < 1 {
		return errors.New("invalid params: team_id")
	}

	list := partner.TbO2TeamMember.GetTeamMember(data.TeamID, partner.O2TeamMemberRoleID.Member)
	if len(list) > 0 {
		practice.TeamRanking.RefreshTeamPlayTimeRank(&data, list)
	}
	return nil
}
