package async

import (
	"encoding/json"
	"net/http"
	"net/url"
	"strings"

	"github.com/pkg/errors"

	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"

	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/distribute"
)

type DistributeLinkConvertPayload struct {
	SkuCode string `json:"sku_code"`
	URL     string `json:"url"`
}

// DistributeLinkConvert 课程分销长链转短链
func DistributeLinkConvert(data DistributeLinkConvertPayload) error {
	if data.URL == "" {
		return nil
	}
	if data.SkuCode == "" {
		return errors.New("undefined sku_code")
	}
	conf := config.Get().ShortURLConfig
	values := make(url.Values)
	values.Add("url", data.URL)
	header := http.Header{}
	header.Set("Content-Type", "application/x-www-form-urlencoded")
	body, err := requests.HTTPRequest(conf.URL, "POST", header, strings.NewReader(values.Encode()))
	if err != nil {
		return err
	}
	type respStruct struct {
		Status    int32  `json:"status"`
		ErrorCode int32  `json:"error_code"`
		ErrorDesc string `json:"error_desc"`
		Result    string `json:"result"`
	}
	var respBody respStruct
	err = json.Unmarshal(body, &respBody)
	if err != nil {
		return err
	}
	if respBody.Status != 1 || respBody.Result == "" {
		return nil
	}
	bean := distribute.URL{
		SkuCode:   data.SkuCode,
		ShortURL:  "http://t.dailyyogac.com/" + respBody.Result,
		OriginURL: data.URL,
	}
	if err := bean.Save(); err != nil {
		return err
	}

	return nil
}
