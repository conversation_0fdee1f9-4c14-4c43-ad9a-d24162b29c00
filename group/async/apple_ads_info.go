package async

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/apple"
)

// AppleAdsInfoIad Iad 框架上报
type AppleAdsInfoIad struct {
	DistinctID string `json:"distinct_id"`
	Data       struct {
		IadPurchaseDate     time.Time `json:"iad-purchase-date"`
		IadKeyword          string    `json:"iad-keyword"`
		IadAdgroupID        string    `json:"iad-adgroup-id"`
		IadCreativesetID    string    `json:"iad-creativeset-id"`
		IadCreativesetName  string    `json:"iad-creativeset-name"`
		IadCampaignID       string    `json:"iad-campaign-id"`
		IadLineitemID       string    `json:"iad-lineitem-id"`
		IadOrgID            string    `json:"iad-org-id"`
		IadConversionDate   time.Time `json:"iad-conversion-date"`
		IadKeywordID        string    `json:"iad-keyword-id"`
		IadConversionType   string    `json:"iad-conversion-type"`
		IadCountryOrRegion  string    `json:"iad-country-or-region"`
		IadOrgName          string    `json:"iad-org-name"`
		IadCampaignName     string    `json:"iad-campaign-name"`
		IadClickDate        time.Time `json:"iad-click-date"`
		IadAttribution      string    `json:"iad-attribution"`
		IadAdgroupName      string    `json:"iad-adgroup-name"`
		IadKeywordMatchtype string    `json:"iad-keyword-matchtype"`
		IadLineitemName     string    `json:"iad-lineitem-name"`
	} `json:"data"`
	IsLogin bool `json:"is_login"`
}

// AppleAdsInfoAdService AdService 框架上报
type AppleAdsInfoAdService struct {
	DistinctID string `json:"distinct_id"`
	Data       struct {
		KeywordID       int    `json:"keywordId"`
		ConversionType  string `json:"conversionType"`
		CreativeSetID   int    `json:"creativeSetId"`
		OrgID           int    `json:"orgId"`
		CampaignID      int    `json:"campaignId"`
		AdGroupID       int    `json:"adGroupId"`
		CountryOrRegion string `json:"countryOrRegion"`
		Attribution     bool   `json:"attribution"`
		ClickDate       string `json:"clickDate"`
	} `json:"data"`
	IsLogin bool `json:"is_login"`
}

// ScAppleAdsInfoEvent 神策事件上报
type ScAppleAdsInfoEvent struct {
	IsFirstDownload        string `json:"asa_is_first_download"`
	Country                string `json:"asa_country"`
	ClickAdTime            string `json:"asa_click_ad_time"`
	MatchType              string `json:"asa_matchtype"`
	AdDownloadAppTime      string `json:"asa_click_ad_download_app_time"`
	AdFirstDownloadAppTime string `json:"asa_click_ad_first_download_app_time"`
}

func (ScAppleAdsInfoEvent) EventName() string {
	return "ios_asa_appstore_install"
}

func (ScAppleAdsInfoEvent) Prefix() string {
	return ""
}

const iosASA = "ios_asa"
const iosOrganic = "ios_organic"

// UploadScAppleAdsInfoIad 苹果归因数据上报神策,客户端 Iad 框架
func UploadScAppleAdsInfoIad(data *AppleAdsInfoIad) {
	token := apple.AppServerInstance.GetToken()
	var handleFalse = true
	defer func() {
		// 如果该方法处理失败了,上报死信队列,下次处理
		if handleFalse {
			logger.Warnf("方法UploadScAppleAdsInfoIad中触发队列上报，上报数据%v", data)
			library.UploadDeadLetterQueue(data)
		}
	}()
	if token == nil {
		logger.Warnf("UploadScAppleAdsInfoIad中token为空触发队列上报%v", token)
		return
	}
	acl, err := apple.AppServerInstance.GetACL(token)
	if err != nil {
		logger.Warnf("apple acl 权限接口请求失败 %s", err.Error())
		return
	}
	// 开始上报神策
	profile := make(map[string]interface{})
	profile["asa_utm_source"] = iosOrganic
	var orgName string
	// 如果拿到数据 iad-attribution字段为假(false)，新增用户属性asa_utm_source字段写死"ios_organic"，其他字段不上报
	// 反之其他数据都上报
	if data.Data.IadAttribution == "true" {
		for _, item := range acl.Data {
			if strconv.Itoa(item.OrgID) == data.Data.IadOrgID {
				orgName = item.OrgName
				break
			}
		}
		// 并没有匹配上
		if orgName == "" {
			return
		}
		profile["asa_utm_source"] = iosASA
		// iad-org-name拼iad-org-id字段（下划线拼接name_id），赋值给新增的用户属性asa_utm_medium；
		profile["asa_utm_medium"] = fmt.Sprintf("%s_%s", orgName, data.Data.IadOrgID)
		// iad-keyword拼iad-keyword-id字段（下划线拼接name_id），赋值给新增的用户属性 asa_utm_term；
		profile["asa_utm_term"] = fmt.Sprintf("%s_%s", data.Data.IadKeyword, data.Data.IadKeywordID)
		// iad-creativeset-name 拼iad-creativeset-id字段（下划线拼接name_id），赋值给新增的用户属性 asa_utm_content
		profile["asa_utm_content"] = fmt.Sprintf("%s_%s", data.Data.IadCreativesetName, data.Data.IadCreativesetID)
		// iad-campaign-name 拼iad-campaign-id字段（下划线拼接name_id），赋值给新增的用户属性 asa_utm_campaign；
		profile["asa_utm_campaign"] = fmt.Sprintf("%s_%s", data.Data.IadCampaignName, data.Data.IadCampaignID)
		// iad-adgroup-name 拼iad-adgroup-id字段（下划线拼接name_id），赋值给新增的用户属性 asa_utm_group
		profile["asa_utm_group"] = fmt.Sprintf("%s_%s", data.Data.IadAdgroupName, data.Data.IadAdgroupID)
		// 事件上报
		sensorsdata.Track(data.DistinctID, ScAppleAdsInfoEvent{
			IsFirstDownload:        data.Data.IadConversionType,
			Country:                data.Data.IadCountryOrRegion,
			ClickAdTime:            data.Data.IadClickDate.Format("2006-01-02 15:04:05"),
			MatchType:              data.Data.IadKeywordMatchtype,
			AdDownloadAppTime:      data.Data.IadConversionDate.Format("2006-01-02 15:04:05"),
			AdFirstDownloadAppTime: data.Data.IadPurchaseDate.Format("2006-01-02 15:04:05"),
		}, data.IsLogin)
	}
	// 上报神策用户属性
	sensorsdata.ProfileSet(data.DistinctID, profile, data.IsLogin)
	handleFalse = false
	profileByte, _ := json.Marshal(profile)
	reqByte, _ := json.Marshal(data)
	logger.Info("UploadScAppleAdsInfoAdService", string(reqByte), string(profileByte))
}

// UploadScAppleAdsInfoAdService 苹果归因数据上报神策,客户端 AdService 框架
func UploadScAppleAdsInfoAdService(data *AppleAdsInfoAdService) {
	var handleFalse = true
	defer func() {
		// 如果该方法处理失败了,上报死信队列,下次处理
		if handleFalse {
			logger.Warnf("方法UploadScAppleAdsInfoAdService中触发队列上报，上报数据%v", data)
			library.UploadDeadLetterQueue(data)
		}
	}()

	// 并没有匹配上 water 2021.05.19 根据逻辑阅读 这块逻辑写的有问题 会造成死循环 故注释掉
	// if orgName == "" {
	//	return
	// }

	// 开始上报神策
	profile := make(map[string]interface{})
	profile["asa_utm_source"] = iosOrganic
	// 如果拿到数据 iad-attribution字段为假(false)，新增用户属性asa_utm_source字段写死"ios_organic"，其他字段不上报
	// 反之其他数据都上报
	if data.Data.Attribution {
		orgName := getOrgName(data.Data.OrgID)
		if orgName == "" {
			logger.Warnf("方法UploadScAppleAdsInfoAdService中没有匹配到orgName触发队列上报 %v", data.Data)
			return
		}
		profile["asa_utm_source"] = iosASA
		// iad-org-name拼iad-org-id字段（下划线拼接name_id），赋值给新增的用户属性asa_utm_medium；
		profile["asa_utm_medium"] = fmt.Sprintf("%s_%d", orgName, data.Data.OrgID)
		//  iad-keyword拼iad-keyword-id字段（下划线拼接name_id），赋值给新增的用户属性 asa_utm_term
		keywordText, matchType := getKeywordAndMatchType(data)
		profile["asa_utm_term"] = fmt.Sprintf("%s_%d", keywordText, data.Data.KeywordID)
		// iad-creativeset-name 拼iad-creativeset-id字段（下划线拼接name_id），赋值给新增的用户属性 asa_utm_content
		profile["asa_utm_content"] = fmt.Sprintf("%s_%s", "", "")

		if data.Data.CreativeSetID != 0 {
			creativeSets, err := apple.AppServerInstance.GetCreativeSetsByID(data.Data.CreativeSetID)
			if err != nil {
				logger.Errorf(err.Error())
				return
			}
			var creativeSetName string
			for _, item := range creativeSets.Data {
				if data.Data.CreativeSetID == item.ID {
					creativeSetName = item.Name
					break
				}
			}
			profile["asa_utm_content"] = fmt.Sprintf("%s_%d", creativeSetName, data.Data.CreativeSetID)
		}
		// iad-campaign-name 拼iad-campaign-id字段（下划线拼接name_id），赋值给新增的用户属性 asa_utm_campaign；
		campaign, err := apple.AppServerInstance.GetCampaignByID(data.Data.CampaignID)
		if err != nil {
			logger.Errorf(err.Error())
			return
		}
		profile["asa_utm_campaign"] = fmt.Sprintf("%s_%d", campaign.Data.Name, data.Data.CampaignID)
		// iad-adgroup-name 拼 iad-adgroup-id 字段（下划线拼接name_id），赋值给新增的用户属性 asa_utm_group
		adGroup, err := apple.AppServerInstance.GetAdGroupByCampaignIDAdgroupID(data.Data.CampaignID, data.Data.AdGroupID)
		if err != nil {
			logger.Errorf(err.Error())
			return
		}
		profile["asa_utm_group"] = fmt.Sprintf("%s_%d", adGroup.Data.Name, data.Data.AdGroupID)

		clickAdTime := time.Unix(0, 0)
		if data.Data.ClickDate != "" {
			// 苹果官方的响应文档里是这个格式,并不属于标准的 RFC3339 ,但是从 iad 框架中的时间是标准的 RFC3339 ,因此还有待验证
			if clickAdTime, err = time.ParseInLocation("2006-01-02T15:04Z", data.Data.ClickDate, time.Local); err != nil {
				logger.Errorf("apple ads 归因,时间转换错误 %s 原值:%s", err.Error(), data.Data.ClickDate)
				return
			}
		}

		// 事件上报
		sensorsdata.Track(data.DistinctID, ScAppleAdsInfoEvent{
			IsFirstDownload: data.Data.ConversionType,
			Country:         data.Data.CountryOrRegion,
			ClickAdTime:     clickAdTime.Format("2006-01-02 15:04:05"),
			MatchType:       matchType,
			// adService 框架回比 iad 少报两个字段,发现神策不能上报空,默认上报 0 的时间戳事件
			AdDownloadAppTime:      time.Unix(0, 0).Format("2006-01-02 15:04:05"),
			AdFirstDownloadAppTime: time.Unix(0, 0).Format("2006-01-02 15:04:05"),
		}, data.IsLogin)
	}

	// 上报神策用户属性
	sensorsdata.ProfileSet(data.DistinctID, profile, data.IsLogin)
	handleFalse = false
	profileByte, _ := json.Marshal(profile)
	reqByte, _ := json.Marshal(data)
	logger.Info("UploadScAppleAdsInfoAdService", string(reqByte), string(profileByte))
}

func getOrgName(orgID int) string {
	token := apple.AppServerInstance.GetToken()
	if token == nil {
		logger.Warnf("方法UploadScAppleAdsInfoAdService中没有拿到token，导致orgName为空触发队列上报 %v", token)
		return ""
	}
	acl, err := apple.AppServerInstance.GetACL(token)
	if err != nil {
		logger.Warnf("方法UploadScAppleAdsInfoAdService中调用GetACL出错，导致orgName为空触发队列上报 %v", err.Error())
		return ""
	}
	logger.Warnf("触发队列上报,得到的最终数据%v", acl.Data)
	org := ""
	for _, item := range acl.Data {
		if item.OrgID == orgID {
			org = item.OrgName
			break
		}
	}
	return org
}

func getKeywordAndMatchType(data *AppleAdsInfoAdService) (keywordText, matchType string) {
	// 查找关键词
	keywordsByCampaign, err := apple.AppServerInstance.GetKeywordsByCampaignID(data.Data.CampaignID)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}
	if keywordsByCampaign == nil {
		return
	}

	var isFind bool
	for _, keyword := range keywordsByCampaign.Data {
		if keyword.ID == data.Data.KeywordID {
			keywordText = keyword.Text
			matchType = keyword.MatchType
			isFind = true
			break
		}
	}

	// 由于苹果貌似会延迟,如果还没有找到,就去 adGroup 里再查一次
	if !isFind {
		keywords, err := apple.AppServerInstance.GetKeywordsByCampaignIDAdgroupID(data.Data.CampaignID,
			data.Data.AdGroupID)
		if err != nil {
			logger.Errorf(err.Error())
			return
		}
		for _, keyword := range keywords.Data {
			if keyword.ID == data.Data.KeywordID {
				keywordText = keyword.Text
				matchType = keyword.MatchType
				break
			}
		}
	}
	return keywordText, matchType
}
