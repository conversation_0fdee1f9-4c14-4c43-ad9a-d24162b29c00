package async

import (
	"sort"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/third"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type BindDistinctIDPayload struct {
	UID        int64  `json:"uid"`
	DistinctID string `json:"distinct_id"`
}

// HandleDistinctID 主要处理9.14的脏数据 之前不做绑定只做记录 9.15参与绑定登录后 数据需要进行处理
func HandleDistinctID(d *BindDistinctIDPayload) error {
	if d.UID == 0 || d.DistinctID == "" {
		return nil
	}
	bDataList := third.TbATpSensorsData.GetByOpenIDS(d.DistinctID)
	if len(bDataList) > 0 {
		sort.Slice(bDataList, func(i, j int) bool {
			return bDataList[i].UpdateTime > bDataList[j].UpdateTime
		})
		bindList := make([]*third.AccountinfoTpSensorData, 0)
		for _, v := range bDataList {
			if v.BindStatus == library.DataStatusEnum.Valid.ToInt32() {
				bindList = append(bindList, v)
			}
		}
		if len(bindList) > 1 {
			for i := 0; i < len(bindList); i++ {
				if bindList[i].UID != d.UID {
					bindList[i].BindStatus = library.DataStatusEnum.Delete.ToInt32()
					if err := bindList[i].Update(); err != nil {
						logger.Warn("BindDistinctIDPayload update Err", err)
						bindList = append(bindList[:i], bindList[i+1:]...)
						i--
					}
				}
			}
			for k, v := range bindList {
				if k == 0 {
					if err := v.Update(); err != nil {
						logger.Warn("BindDistinctIDPayload update Err", err)
					}
					continue
				}
				v.BindStatus = library.DataStatusEnum.Delete.ToInt32()
				if err := v.Update(); err != nil {
					logger.Warn("BindDistinctIDPayload update Err", err)
				}
			}
		}
	}
	return nil
}
