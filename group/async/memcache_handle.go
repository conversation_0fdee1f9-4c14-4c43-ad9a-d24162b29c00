package async

import (
	"fmt"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
)

type MemcacheDataHandlePayload struct {
	Key    string `json:"key"`
	Value  string `json:"value"`
	Expire int    `json:"expire_time"`
}

// 新版存储数据
func MemcachedDataHandle(data MemcacheDataHandlePayload) error {
	if data.Expire < 1 {
		return errors.New(fmt.Sprintf("key 过期时间必须设置，未设置过期时间，data:%+v", data))
	}
	err := cache.GetMemcached().SetEx(data.Key, data.Value, data.Expire)
	if err != nil {
		return errors.New(fmt.Sprintf("Memcached 存储失败, data:%+v, err: %s", data, err.Error()))
	}
	return nil
}
