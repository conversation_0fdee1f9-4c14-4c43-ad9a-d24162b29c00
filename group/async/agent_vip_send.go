package async

import (
	"encoding/json"
	"net/url"
	"strconv"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

type AgentVipSendPayload struct {
	UID int64 `json:"uid"`
}

func AgentVipSend(data AgentVipSendPayload) error {
	var ygRes yogaResponse
	values := make(url.Values)
	values.Add("uid", strconv.FormatInt(data.UID, 10))
	resp, err := requests.NewYogaClient(config.Get().PhpYogaAddress).PostForm("usercenter/user/agentVipSend", values)
	if err != nil {
		return err
	}
	if !resp.IsOK() || resp.Body == "" {
		logger.Error("发放代理商会员充值失败！uid:", data.UID)
		return nil
	}
	err = json.Unmarshal(resp.GetBodyByte(), &ygRes)
	if err != nil {
		logger.Error("发放代理商会员返回数据不能解析！", resp.Body)
		return nil
	}

	if ygRes.Errno != 0 {
		logger.Error("发放代理商会员充值失败", data.UID, ygRes)
	}
	return nil
}
