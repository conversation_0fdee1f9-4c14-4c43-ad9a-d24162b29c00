package async

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"

	"gitlab.dailyyoga.com.cn/server/srv-task/library"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/yogao2school"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"
)

type VoucherBatchPushPayload struct {
	TaskKey  string `json:"task_key"`
	GroupKey string `json:"group_key"`
}

type voucherTaskInfo struct {
	VoucherID     int64   `json:"voucher_id"`
	UID           int64   `json:"uid"`
	UseStartTime  int64   `json:"use_start_time"`
	UseEndTime    int64   `json:"use_end_time"`
	VoucherName   string  `json:"voucher_name"`
	VoucherDesc   string  `json:"voucher_decription"`
	Discount      float64 `json:"discount"`
	Total         float64 `json:"total"`
	UseType       int32   `json:"use_type"`
	ExpiryDay     int32   `json:"expiry_day"`
	StartTime     int64   `json:"start_time"`
	EndTime       int64   `json:"end_time"`
	VoucherStatus int32   `json:"voucher_status"`
	ResourceType  int32   `json:"resource_type"`
	ResourceValue int64   `json:"resource_value"`
	ProductType   int32   `json:"product_type"`
	SendType      int32   `json:"send_type"`
	LinkType      int32   `json:"linkType"`
	PushContent   string  `json:"push_content"`
	PushTitle     string  `json:"push_title"`
}

func VoucherBatchPush(data VoucherBatchPushPayload) error {
	if data.TaskKey == "" {
		return errors.New("undefined task_key")
	}
	if data.GroupKey == "" {
		return errors.New("undefined group_key")
	}

	rd := cache.GetYogaRedis()
	ctx := context.Background()

	taskInfoList := make([]*voucherTaskInfo, 0)
	taskListStr, err := rd.Get(ctx, data.TaskKey).Result()
	if err != nil {
		return err
	}
	if err := json.Unmarshal([]byte(taskListStr), &taskInfoList); err != nil {
		return err
	}

	for {
		// 获取用户列表
		uidList, err := getVoucherUserList(data.GroupKey)
		if err != nil {
			return nil
		}

		// 保存优惠券信息
		pushInfo := saveVoucher(taskInfoList, uidList)
		// 如果推送消息为空  不推送
		if pushInfo.title == "" || pushInfo.content == "" {
			continue
		}
		// 发起推送
		pushData := service.PushData{
			UserID:      strings.Join(uidList, ","),
			LinkType:    pushInfo.linkType,
			UserType:    pushInfo.sendType,
			Title:       pushInfo.title,
			Content:     pushInfo.content,
			MessageType: 6,
			ChannelType: 1,
			TargetTitle: "点击查看",
			SignID:      library.SignIDTypeEnum.SendAppVoucher,
		}

		if err := service.Push.Send(&pushData); err != nil {
			logger.Error(err)
		}
	}
}

type voucherPushInfo struct {
	sendType int32
	linkType int32
	content  string
	title    string
}

// getVoucherUserList 获取优惠券用户列表
func getVoucherUserList(groupKey string) ([]string, error) {
	rd := cache.GetYogaRedis()
	ctx := context.Background()

	uidList := make([]string, 0)

	length, err := rd.LLen(ctx, groupKey).Result()
	if err != nil {
		return nil, err
	}
	if length == 0 {
		return nil, errors.New("group length: zero")
	}
	if length > 100 {
		length = 100
	}

	for i := 0; i < int(length); i++ {
		uid, err := rd.RPop(ctx, groupKey).Result()
		if err != nil {
			return nil, err
		}
		uidList = append(uidList, uid)
	}

	if len(uidList) < 1 {
		return nil, errors.New("uid empty")
	}
	return uidList, nil
}

// saveVoucher 保存用户优惠券
func saveVoucher(taskInfoList []*voucherTaskInfo, uidList []string) voucherPushInfo {
	res := voucherPushInfo{}

	for _, item := range taskInfoList {
		res.sendType = item.SendType
		res.linkType = item.LinkType
		res.content = item.PushContent
		res.title = item.PushTitle

		var incrNum int32
		for k := range uidList {
			uid, _ := strconv.Atoi(uidList[k])
			bean := yogao2school.VoucherToUser{
				VoucherID:     item.VoucherID,
				UseStartTime:  item.UseStartTime,
				UseEndTime:    item.UseEndTime,
				VoucherName:   item.VoucherName,
				VoucherDesc:   item.VoucherDesc,
				Discount:      item.Discount,
				Total:         item.Total,
				UseType:       item.UseType,
				ExpiryDay:     item.ExpiryDay,
				StartTime:     item.StartTime,
				EndTime:       item.EndTime,
				VoucherStatus: item.VoucherStatus,
				ResourceType:  item.ResourceType,
				ResourceValue: item.ResourceValue,
				ProductType:   item.ProductType,
				UID:           int64(uid),
			}
			if err := bean.Save(); err != nil {
				logger.Errorf("voucher send failed! data: %+v, uid: %d", item, uid)
			}
			incrNum++
		}
		record := yogao2school.VoucherSendRecord{
			ID: item.ResourceValue,
		}
		if err := record.IncrAlreadySent(incrNum); err != nil {
			logger.Error(err)
		}
	}
	return res
}
