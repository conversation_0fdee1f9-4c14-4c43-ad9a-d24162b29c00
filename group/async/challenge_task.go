package async

import (
	"time"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/challenge"
)

type ChallengeTaskPayload struct {
	UID        int64 `json:"uid"`
	TaskType   int32 `json:"task_type"`
	ReportTime int64 `json:"report_time"`
}

func ProcessChallengeTask(data ChallengeTaskPayload) error {
	if data.UID < 0 {
		return errors.New("illegal event")
	}

	taskInfo := challenge.TaskInfo{
		UID:        data.UID,
		TaskType:   library.ChallengeTaskType(data.TaskType),
		Date:       time.Unix(data.ReportTime, 0).Format("2006-01-02"),
		ReportTime: data.ReportTime,
	}

	challenge.ServiceChallenge.ProcessTask(taskInfo)
	return nil
}
