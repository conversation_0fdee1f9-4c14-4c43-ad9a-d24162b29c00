package async

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type UserIndexesPayload struct {
	IndexType int `json:"index_type"`
	Value     int `json:"value"`
	UID       int `json:"uid"`
}

// SetUserIndexes 设置索引数据
func SetUserIndexes(data *UserIndexesPayload) error {
	switch data.IndexType {
	case library.IndexesTypeAuth:
		data.setAuth()
	case library.IndexesTypeMember:
		data.setMember()
	default:
		logger.Error("添加索引类型错误")
	}
	return nil
}

// setAuth 设置达人
func (s *UserIndexesPayload) setAuth() {
	authType := library.CEAuthMum.None
	switch s.Value {
	case library.AuthenticationTypeOfficial, library.AuthenticationTypeCoach, library.AuthenticationTypeOther:
		authType = library.CEAuthMum.Authentication
	case library.AuthenticationTypeYogaFan:
		authType = library.CEAuthMum.Talent
	}
	_, err := user.TbAccountInfoIndexOp.FindOrCreate(s.UID, authType.ToInt())
	if err != nil {
		logger.Info("添加搜索索引出错，indexesMember", err.Error())
	}
}

// setMember 设置会员
func (s *UserIndexesPayload) setMember() {
	authType := library.CEAuthMum.None
	switch library.MemberLevel(s.Value) {
	case library.MemberLevelVip, library.MemberLevelSVip, library.MemberLevelGVip:
		authType = library.CEAuthMum.Member
	case library.MemberLevelYVip, library.MemberLevelYSVip, library.MemberLevelYGVip:
		authType = library.CEAuthMum.AnnualMember
	case library.MemberLevelNormal, library.MemberLevelVistor:
	default:
		authType = library.CEAuthMum.None
	}
	_, err := user.TbAccountInfoIndexOp.FindOrCreate(s.UID, authType.ToInt())
	if err != nil {
		logger.Info("添加搜索索引出错，indexesMember", err.Error())
	}
}
