package async

import (
	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/stat/st"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type StReportPayload struct {
	Type         library.StReportType `json:"type"`
	UID          int64                `json:"uid"`
	ProgramID    int64                `json:"program_id"`
	SessionID    int64                `json:"session_id"`
	ActionID     int64                `json:"action_id"`
	SessionIndex int32                `json:"session_index"`
	DeviceType   int32                `json:"device_type"`
	Version      string               `json:"version"`
	PlayTime     int64                `json:"play_time"`
	Calories     int32                `json:"calories"`
}

// stReport 行为上报
func StReport(data *StReportPayload) error {
	if data.Type == "" {
		return errors.New("unknown st_report type")
	}
	bean := st.Table{
		UID:          data.UID,
		ProgramID:    data.ProgramID,
		SessionID:    data.SessionID,
		ActionID:     data.ActionID,
		SessionIndex: data.SessionIndex,
		PlayTime:     data.PlayTime,
		Calories:     data.Calories,
		DeviceType:   data.DeviceType,
		Version:      data.Version,
		StTypeName:   data.Type,
	}
	if err := bean.Save(); err != nil {
		logger.Error(err)
	}
	return nil
}
