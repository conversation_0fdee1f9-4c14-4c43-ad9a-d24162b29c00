package async

import (
	"context"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/order"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"
)

type TXSportsChargePayload struct {
	URL           string `json:"url"`
	AppName       string `json:"app_name"`
	Appid         string `json:"appid"`
	Bid           int    `json:"bid"`
	OrderID       string `json:"order_id"`
	Password      string `json:"password"`
	ProductType   string `json:"product_type"`
	QQAccessToken string `json:"qq_access_token"`
	Sign          string `json:"sign"`
	TS            int64  `json:"ts"`
	UserID        string `json:"user_id"`
	UserType      int    `json:"user_type"`
}

type TXSportResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

func TXSportsCharge(data *TXSportsChargePayload) error {
	if data.URL == "" {
		return nil
	}

	values := make(url.Values)
	values.Add("appName", data.AppName)
	values.Add("appid", data.Appid)
	values.Add("bid", strconv.Itoa(data.Bid))
	values.Add("orderId", data.OrderID)
	values.Add("password", data.Password)
	values.Add("productType", data.ProductType)
	values.Add("qq_access_token", data.QQAccessToken)
	values.Add("sign", data.Sign)
	values.Add("ts", strconv.FormatInt(data.TS, 10))
	values.Add("userId", data.UserID)
	values.Add("userType", strconv.Itoa(data.UserType))

	txOrder, err := order.TbWebOrderTencentSportsUniteVip.GetByOrderID(data.OrderID)
	if err != nil || txOrder == nil {
		logger.Error("用户订单查找出错！order_id:", data.OrderID, err)
		return nil
	}
	res := retryHTTP(data.URL, values, 3)
	if res.Code == 0 {
		txOrder.Status = 1
		logger.Info("通知充值成功！")
		return txOrder.Update()
	}
	txOrder.PayResult = res.Msg
	txOrder.Status = 2
	return txOrder.Update()
}

func retryHTTP(addr string, values url.Values, retryNum int) *TXSportResponse {
	var response *http.Response
	var err error
	var txRes TXSportResponse

	txRes.Code = 2 // 初始化为 2 不成功

	ctx := context.Background()

	for retring := 1; retring <= retryNum; retring++ {
		response, err = service.HTTP.PostForm(ctx, addr, values)
		if err != nil {
			logger.Error("请求直充接口报错！正在重试！")
			continue
		}
		body, err := ioutil.ReadAll(response.Body)
		if err != nil {
			logger.Error("返回参数错误！")
			continue
		}
		if err := response.Body.Close(); err != nil {
			logger.Error(err)
			continue
		}
		err = json.Unmarshal(body, &txRes)
		if err != nil {
			logger.Error("返回数据不能解析！")
		}

		if txRes.Code == 0 { // 如果返回充值成功则不在发送
			logger.Info(txRes)
		} else {
			logger.Warn("直充返回错误信息", txRes)
		}
		break
	}

	return &txRes
}
