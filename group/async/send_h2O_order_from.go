package async

import (
	"context"
	"errors"
	"net/url"
	"strconv"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"
)

type SendH2OrderPayload struct {
	UID           string `json:"uid"`
	OrderNo       string `json:"order_no"`
	ProductID     string `json:"product_id"`
	PurchaseType  string `json:"purchase_type"`
	OrderSource   string `json:"order_source"`
	OrderSourceID string `json:"order_source_id"`
	Price         string `json:"price"`
	Channel       string `json:"channel"`
	Version       string `json:"version"`
	ProductType   string `json:"product_type"`
	PurchaseTime  string `json:"purchase_time"`
	URL           string `json:"url"`
}

// h2o 订单分成
func SendH2OrderHandle(data *SendH2OrderPayload) error {
	logger.Warnf("SendH2OrderHandle 数据:%+v", data)
	uid, err := strconv.Atoi(data.UID)
	if err != nil {
		return errors.New("h2o 订单分成 转换uid 失败")
	}
	if uid < 0 {
		return errors.New("illegal event")
	}

	go func(data *SendH2OrderPayload) {
		sendH2Order(data)
	}(data)

	return nil
}

// 发送数据到h2o
func sendH2Order(data *SendH2OrderPayload) {
	postURL := library.GetKolShareAddress() + data.URL
	params := url.Values{}
	params.Set("uid", data.UID)
	params.Set("order_no", data.OrderNo)
	params.Set("product_id", data.ProductID)
	params.Set("purchase_type", data.PurchaseType)
	params.Set("order_source", data.OrderSource)
	params.Set("order_source_id", data.OrderSourceID)
	params.Set("price", data.Price)
	params.Set("channel", data.Channel)
	params.Set("version", data.Version)
	params.Set("product_type", data.PurchaseType)
	params.Set("purchase_time", data.PurchaseTime)

	resp, err := service.HTTP.PostForm(context.Background(), postURL, params)
	if err != nil {
		logger.Warnf("请求h2o发送分成数据 请求参数：%+v,报错信息：%s", params, err.Error())
		return
	}
	defer func() {
		_ = resp.Body.Close()
	}()
	if resp.StatusCode != 200 {
		logger.Errorf("h2o URL SendH2Order 请求状态 %d", resp.StatusCode)
	}
}
