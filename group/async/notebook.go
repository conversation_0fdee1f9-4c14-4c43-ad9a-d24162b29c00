package async

import (
	"context"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	dbnote "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/note"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/notebook"
)

type NoteBookPayload struct {
	UID      int64  `json:"uid"`
	TaskType int32  `json:"task_type"`
	ObjID    string `json:"obj_id"`
	ObjType  int32  `json:"obj_type"`
	Status   int    `json:"status"`
}

type ImageList struct {
	ImageList []string `json:"image_list"`
}

// syncNoteBookRankList 同步记录本信息
func syncNoteBookRankList(userNoteBookCollectList []dbnote.Collect) {
	ctx := context.Background()
	for _, item := range userNoteBookCollectList {
		if item.JoinStatus != int32(library.NoteBookJoinStatusEnum.Jointed) {
			continue
		}

		strUID := strconv.Itoa(int(item.UID))
		z := &redis.Z{
			Score:  float64(item.TotalRecordDay),
			Member: strUID,
		}
		if err := cache.GetYogaRedis().ZAdd(ctx, library.NoteBookRankListKey, z).Err(); err != nil {
			break
		}
	}
}

// processNoteBookRankList 同步达人记录本信息
func processNoteBookRankList() {
	for tableIndex := 0; tableIndex < 32; tableIndex++ {
		userNoteBookCollectList, err := dbnote.TbCollect.GetTopList(int64(tableIndex), 100)
		if err != nil {
			break
		}

		syncNoteBookRankList(userNoteBookCollectList)
	}
}

// sync2NoteBook 同步资源到记录本及排行榜
func sync2NoteBook(data NoteBookPayload) {
	noteBookInfo, err := dbnote.TbList.GetItem(data.UID, data.ObjID, data.ObjType)
	if err != nil || noteBookInfo != nil {
		return
	}

	noteBookCollectInfo, err := dbnote.TbCollect.GetItem(data.UID)
	if err != nil {
		return
	}

	// 获取资源信息
	objInfo := notebook.ServiceNoteBook.GetObjInfo(data.ObjID, data.ObjType)
	if objInfo == nil {
		logger.Warnf("无法获取记录本资源")
		return
	}

	// 插入到记录本
	list := &dbnote.List{
		UID:        data.UID,
		Type:       data.ObjType,
		ObjID:      data.ObjID,
		Content:    "{}",
		Status:     data.Status,
		CreateTime: objInfo.CreateTime,
		UpdateTime: time.Now().Unix(),
	}
	if err := list.Save(); err != nil {
		logger.Warnf("保存记录本内容失败 err: %s", err)
	}

	// 更新用户记录本信息
	if noteBookCollectInfo != nil {
		notebook.ServiceNoteBook.UpdateUserNoteBook(noteBookCollectInfo,
			data.ObjID, data.ObjType, data.Status, objInfo.CreateTime, true)
	} else {
		notebook.ServiceNoteBook.CreateUserNoteBook(data.UID, data.ObjID, data.ObjType, data.Status)
	}
}

// updateNoteBook 同步修改资源状态后修改记录本及排行榜
func updateNoteBook(data NoteBookPayload) {
	noteBookInfo, err := dbnote.TbList.GetItem(data.UID, data.ObjID, data.ObjType)
	if err != nil {
		return
	}

	noteBookCollectInfo, err := dbnote.TbCollect.GetItem(data.UID)
	if err != nil || noteBookCollectInfo == nil {
		return
	}

	// 获取资源信息
	objInfo := notebook.ServiceNoteBook.GetObjInfo(data.ObjID, data.ObjType)
	if objInfo == nil {
		logger.Warnf("无法获取记录本资源")
		return
	}

	needUpdateNoteBookDay, err := notebook.ServiceNoteBook.UpdateNoteBookInfo(noteBookInfo, data.UID, data.ObjType,
		data.ObjID, data.Status, objInfo.CreateTime)
	if err != nil {
		return
	}

	switch library.StatusType(data.Status) {
	case library.NoteBookContentStatusEnum.Normal,
		library.NoteBookContentStatusEnum.Privacy:
		notebook.ServiceNoteBook.UpdateUserNoteBook(noteBookCollectInfo, data.ObjID, data.ObjType, data.Status,
			objInfo.CreateTime, needUpdateNoteBookDay)
	case library.NoteBookContentStatusEnum.Remove,
		library.NoteBookContentStatusEnum.Hide,
		library.NoteBookContentStatusEnum.Delete:
		needUpdateNoteBookDay = true
		notebook.ServiceNoteBook.DeleteUserNoteBook(noteBookCollectInfo, data.ObjID, data.ObjType,
			data.Status, needUpdateNoteBookDay)
	default:
	}
}

// ProcessNoteBook 处理记录本异步任务
func ProcessNoteBook(data NoteBookPayload) error {
	switch library.TaskType(data.TaskType) {
	case library.NoteBookTaskTypeEnum.NoteBookRankList:
		processNoteBookRankList()
	case library.NoteBookTaskTypeEnum.Add2NoteBook:
		sync2NoteBook(data)
	case library.NoteBookTaskTypeEnum.ModifyNoteBook:
		updateNoteBook(data)
	default:
		return nil
	}
	return nil
}
