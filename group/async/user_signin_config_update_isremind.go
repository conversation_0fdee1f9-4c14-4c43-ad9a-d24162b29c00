package async

import (
	"fmt"

	"github.com/prometheus/common/log"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
)

// pxc 2021-6-21 将更新签到提醒状态迁移到go服务
type UserSignInConfigUpdateIsRemind struct {
	UID      int64 `json:"uid"`
	ISRemind int32 `json:"is_remind"`
}

// pxc 接口优化：/620/user/Signin/updateIsRemind
func UpdateIsRemind(d *UserSignInConfigUpdateIsRemind) error {
	if d.UID < 0 {
		return fmt.Errorf("设置更新签到提醒状态失败，参数不合法:%d--%d", d.UID, d.ISRemind)
	}
	SignInConfigInfo, err := db.TbUserSignInConfig.GetInfoByID(d.UID)
	if err != nil {
		// 添加
		logger.Errorf("更新签到提醒状态的用户id不存在 %d", d.UID)
		return err
	}
	if SignInConfigInfo == nil {
		signInConfigInfo := &db.SignInConfig{
			UID:      d.UID,
			IsRemind: d.ISRemind,
			Status:   1,
		}
		if err := signInConfigInfo.Save(); err != nil {
			log.Errorf("保存的信息失败, err: %s", err.Error())
			return err
		}
	} else {
		SignInConfigInfo.IsRemind = d.ISRemind
		if err := SignInConfigInfo.Update(); err != nil {
			log.Errorf("更新的信息失败, err: %s", err.Error())
			return err
		}
	}
	return nil
}
