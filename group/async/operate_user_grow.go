package async

import (
	"errors"
	"net/url"
	"strconv"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

type OperateUserGrowPayload struct {
	UID     string `json:"uid"`
	ModelID int64  `json:"model_id"`
	ObjID   int64  `json:"obj_id"`
	Version string `json:"version"`
	Channel string `json:"channel"`
}

// 操作成长值
func OperateUserGrow(data *OperateUserGrowPayload) error {
	uid, err := strconv.Atoi(data.UID)
	if err != nil {
		return errors.New("注册登陆异步事件 转换uid")
	}
	if uid < 0 {
		return errors.New("illegal event")
	}

	if err := data.operateGrow(); err != nil {
		logger.Error(err)
	}
	return nil
}

func (u *OperateUserGrowPayload) operateGrow() error {
	uri := "/user/user/operateUserGrow"
	params := url.Values{}
	params.Set("uid", u.UID)
	params.Set("model_id", strconv.FormatInt(u.ModelID, 10))
	params.Set("obj_id", strconv.FormatInt(u.ObjID, 10))
	params.Set("version", u.Version)
	params.Set("channel", u.Channel)
	resp, err := requests.New620Client(config.Get().Php620Address).PostForm(uri, params)
	if err != nil {
		return err
	}
	if !resp.IsOK() {
		logger.Errorf("send operateGrow error. status: %d, params: %s", resp.StatusCode, params.Encode())
	}
	return nil
}
