package async

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/srv-task/databases/stat/duiba"
)

type DuibaBannerClickReportPayload struct {
	AdsID      int64 `json:"ads_id"`
	DeviceType int32 `json:"device_type"`
}

// duibaBannerClickReport 兑吧banner点击上报
func DuibaBannerClickReport(data DuibaBannerClickReportPayload) error {
	if data.AdsID < 1 || data.DeviceType < 1 {
		return nil
	}

	date := time.Now().Format("20060102")
	month := time.Now().Format("200601")

	// st_daily
	if err := duibaStDaily(data, date); err != nil {
		return err
	}
	// st_monthly
	if err := duibaStMonthly(data, month); err != nil {
		return err
	}
	return nil
}

func duibaStDaily(data DuibaBannerClickReportPayload, date string) error {
	daily, err := duiba.TbBannerClickStat.GetRowByAdsAndDevice(duiba.StatDaily, date, data.AdsID, data.DeviceType)
	if err != nil {
		return err
	}
	if item, ok := daily.(*duiba.BannerClickDaily); ok {
		return duiba.TbBannerClickStat.IncrClickCnt(duiba.StatDaily, item.ID)
	}
	return duiba.TbBannerClickStat.Save(&duiba.BannerClickDaily{
		AdsID: data.AdsID, Date: date, ClickCnt: 1, Device: data.DeviceType,
	})
}

func duibaStMonthly(data DuibaBannerClickReportPayload, month string) error {
	monthly, err := duiba.TbBannerClickStat.GetRowByAdsAndDevice(duiba.StatMonthly, month, data.AdsID, data.DeviceType)
	if err != nil {
		return err
	}
	if item, ok := monthly.(*duiba.BannerClickMonthly); ok {
		return duiba.TbBannerClickStat.IncrClickCnt(duiba.StatMonthly, item.ID)
	}
	return duiba.TbBannerClickStat.Save(&duiba.BannerClickMonthly{
		AdsID: data.AdsID, Month: month, ClickCnt: 1, Device: data.DeviceType,
	})
}
