package async

import (
	"context"
	"fmt"
	"time"

	pb "gitlab.dailyyoga.com.cn/protogen/srv-usercenter-go/practice"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/statrds/practice"
	dbc "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/course"
	dbi "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/intelligence"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
	lib "gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type PracticeCalendarPayload struct {
	UID      int64 `json:"uid"`
	InitType int32 `json:"init_type"`
}

// InitPracticeCalendar 初始化数据
func InitPracticeCalendar(p *PracticeCalendarPayload) error {
	go func() {
		rd := cache.GetLockRedis()
		// @text 线上tv端习练历程重复统计，因为没锁住导致
		ctx := context.Background()
		cacheKey := fmt.Sprintf("locking:init:Calendar:%d", p.UID)
		exp := 20 * time.Second
		if lock, err := rd.SetNX(ctx, cacheKey, p.UID, 0).Result(); err != nil {
			logger.Error(err)
			return
		} else if !lock {
			return
		}
		rd.Expire(ctx, cacheKey, exp)
		defer func() {
			if err := rd.Del(ctx, cacheKey).Err(); err != nil {
				logger.Error(err)
			}
		}()
		calendar := db.TbCalendarP.GetHasData(p.UID)
		// 2是为了解决 加入首训推荐资源时 资源同步问题
		if p.InitType == 2 {
			if s := db.TbCalendarP.GetUseHasSession(p.UID, 2); s == nil {
				p.initProgram()
			}
			return
		}
		if calendar != nil {
			p.translationCalendar()
			return
		}
		p.initProgram()
		p.initIntelligenceSchedule()
	}()
	return nil
}

// initProgram 初始化计划
func (p *PracticeCalendarPayload) initProgram() {
	if data := dbc.TbProgramSchedule.GetUserLastProgram(p.UID, lib.SeriesTypeEnum.Program.ToInt32()); data != nil {
		rpc := grpc.GetUserCenterClient()
		if _, err := rpc.UpdatePracticeCalendar(context.Background(), &pb.UpdatePracticeCalendarRequest{
			UID:          p.UID,
			ProgramID:    data.ProgramID,
			ActionType:   3,
			ResourceType: 2,
		}); err != nil {
			logger.Errorf("练习日历 initProgram 初始化计划 报错")
		}
	}
}

// initIntelligenceSchedule 初始化智能课表
func (p *PracticeCalendarPayload) initIntelligenceSchedule() {
	if data := dbi.TbIntelligenceScheduleUser.GetIntelligenceScheduleUserIngDetail(p.UID); data != nil {
		rpc := grpc.GetUserCenterClient()
		if _, err := rpc.UpdatePracticeCalendar(context.Background(), &pb.UpdatePracticeCalendarRequest{
			UID:          p.UID,
			ProgramID:    data.ID,
			ActionType:   3,
			ResourceType: 6,
		}); err != nil {
			logger.Errorf("练习日历 initIntelligenceSchedule 初始化智能课表 报错")
		}
	}
}

// translationCalendar 平移日历的数据
func (p *PracticeCalendarPayload) translationCalendar() {
	rpc := grpc.GetUserCenterClient()
	if _, err := rpc.TranslationCalendar(context.Background(), &pb.TranslationCalendarRequest{
		UID: p.UID,
	}); err != nil {
		logger.Errorf("练习日历 translationCalendar 初始化智能课表 报错")
	}
}
