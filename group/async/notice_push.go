package async

import (
	"errors"
	"strconv"
	"strings"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/push"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"
)

type NoticePushPayload struct {
	PushID   int64 `json:"push_id"`
	PushData struct {
		Content     string               `json:"content"`
		Title       string               `json:"title"`
		LinkType    int32                `json:"link_type"`
		UserType    int32                `json:"user_type"`
		Platform    int32                `json:"platform"`
		Link        string               `json:"link"`
		ObjID       int64                `json:"obj_id"`
		Logo        string               `json:"logo"`
		Image       string               `json:"image"`
		ImagePad    string               `json:"image_pad"`
		MessageType int32                `json:"message_type"`
		ChannelType int32                `json:"channel_type"`
		Tags        []string             `json:"tags"`
		SignID      int64                `json:"sign_id"`
		ItemType    library.ItemTypeEnum `json:"item_type"`
	} `json:"push_data"`
}

func SystemNoticePush(data *NoticePushPayload) error {
	if data.PushID < 0 {
		return errors.New("invalid pushId")
	}
	go data.NoticePush()
	return nil
}

// noticePush 通知推送
func (data *NoticePushPayload) NoticePush() {
	// jpush 只能携带500个发送
	size := 500
	offset := 0
	for {
		var uidLists []string
		uids := push.TbSystemPushRecord.GetByPushID(data.PushID, size, offset)
		if uids == nil || len(uids) < 1 {
			break
		}
		for _, val := range uids {
			if val.UID != 0 {
				uidLists = append(uidLists, strconv.FormatInt(val.UID, 10))
			}
		}
		if len(uidLists) == 0 {
			break
		}
		logger.Info("待发推送uidList", uidLists)
		if err := push.UpdateSendStatus(data.PushID, uidLists, int32(library.SendStatusEnum.OnGoingSend)); err != nil {
			logger.Error(err)
		}

		pushData := service.PushData{
			UserID:  strings.Join(uidLists, ","),
			Content: data.PushData.Content, Title: data.PushData.Title,
			LinkType: data.PushData.LinkType, UserType: data.PushData.UserType,
			Platform: data.PushData.Platform, Link: data.PushData.Link,
			ObjID: data.PushData.ObjID, Logo: data.PushData.Logo,
			Image: data.PushData.Image, ImagePad: data.PushData.ImagePad,
			MessageType: data.PushData.MessageType, ChannelType: data.PushData.ChannelType,
			Tags: data.PushData.Tags, SignID: library.SignIDType(data.PushData.SignID),
			ItemType: data.PushData.ItemType,
		}

		_ = service.Push.Send(&pushData)
		if err := push.UpdateSendStatus(data.PushID, uidLists, int32(library.SendStatusEnum.AlreadySend)); err != nil {
			logger.Error(err)
		}
		offset += size
		if len(uids) < size {
			break
		}
	}
}
