package async

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/qiniu/go-sdk/v7/auth/qbox"
	"github.com/qiniu/go-sdk/v7/storage"
	"gitlab.dailyyoga.com.cn/server/go-artifact/crypto"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/course"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"
)

type SlicingMakeSessionPayload struct {
	SessionID int64 `json:"sessionId"`
}

// SlicingMakeSession 切片组合成课程
func SlicingMakeSession(data SlicingMakeSessionPayload) error {
	if data.SessionID == 0 {
		return errors.New("SlicingMakeSession unknown session_id or origin_url")
	}
	slicing := slicingSession{}
	m3u8List := slicing.getSlicingM3U8List(data.SessionID)
	if len(m3u8List) == 0 {
		return errors.New("SlicingMakeSession not find m3u8 list")
	}
	m3u8Content := make([]string, 0)
	for _, url := range m3u8List {
		slicingURL, err := slicing.httpGetM3U8FileContent(url, 0)
		if err != nil {
			logger.Error(err)
			return errors.New("SlicingMakeSession httpGetM3U8FileContent empty")
		}
		m3u8Content = append(m3u8Content, slicingURL...)
	}
	if len(m3u8Content) == 0 {
		return errors.New("SlicingMakeSession append m3u8Content empty")
	}
	var err error
	nM3u8 := slicing.productionM3U8(m3u8Content)
	upNM3u8Url, err := slicing.uploadM3U8File(nM3u8)
	if err != nil {
		logger.Error(err)
		return errors.New("SlicingMakeSession uploadFile error")
	}
	duration, err := slicing.getM3U8Duration(upNM3u8Url)
	if err != nil {
		logger.Error(err)
		return errors.New("SlicingMakeSession getM3U8Duration error")
	}

	sessionCalorieRatio, err := slicing.getSessionCalorieRatio(data.SessionID)
	if sessionCalorieRatio == 0 || err != nil {
		logger.Error("课程的强度系数出错 sessionId:" + strconv.FormatInt(data.SessionID, 10))
		return errors.New("SlicingMakeSession sessionCalorieRatio error")
	}
	// 卡路里系数
	formatDuration := math.Ceil(duration / 60)
	calorieRatio := int32(sessionCalorieRatio * 1000)
	calorie := int32(duration * sessionCalorieRatio * 60)
	err = slicing.updateSessionInfo(data.SessionID, calorie, calorieRatio, int32(formatDuration), upNM3u8Url)
	if err != nil {
		logger.Error(err)
		logger.Error("更新课程信息出错 sessionId:" + strconv.FormatInt(data.SessionID, 10))
		return errors.New("SlicingMakeSession updateSessionInfo error")
	}
	return nil
}

type slicingSession struct {
}

// 2019-11-09 water 取出视频切片
func (m *slicingSession) getSlicingM3U8List(sessionID int64) []string {
	m3u8List := make([]string, 0)
	tables := course.TbSessionExtendM3u8.GetList(sessionID, library.DataStatusEnum.Valid.ToInt64())
	if tables == nil {
		return m3u8List
	}
	for _, table := range tables {
		m3u8List = append(m3u8List, table.M3U8URL)
	}
	return m3u8List
}

// 2019-11-11 water 远程获取m3u8文件
func (m *slicingSession) httpGetM3U8FileContent(url string, times int) ([]string, error) {
	urlList := make([]string, 0)
	rep, err := service.HTTP.Get(context.Background(), url)
	if err != nil {
		logger.Error("获取七牛M3U8文件出错" + url + "请检查")
		return nil, err
	}
	defer func() {
		_ = rep.Body.Close()
	}()
	// ioutil.ReadAll来读取即使只有1byte也会申请512byte,轻则导致内存浪费严重，重则导致内存泄漏影响业务
	// 2020-2-10 water 处理http状态返回不是200的时候
	if rep.StatusCode != 200 {
		if times < 3 {
			logger.Error("减脂塑形业务七牛m3u8获取异常" + url + "重试第" + strconv.Itoa(times) + "次")
			return m.httpGetM3U8FileContent(url, times+1)
		}
		return nil, errors.New("减脂塑形业务七牛m3u8获取异常" + url + "重试3次失败请检查")
	}
	body, err := ioutil.ReadAll(rep.Body)
	if err != nil {
		logger.Error("获取七牛M3U8文件ioutil.ReadAll" + url + "请检查")
		return nil, err
	}
	src := string(body)
	sReader := strings.NewReader(src)
	bReader := bufio.NewScanner(sReader)
	bReader.Split(bufio.ScanLines)
	for bReader.Scan() {
		line := bReader.Text()
		if !strings.HasPrefix(line, "#EXT-X-") && !strings.HasPrefix(line, "#EXTM3U") {
			tsFileName := line
			urlList = append(urlList, tsFileName)
		}
	}
	return urlList, nil
}

// 2019-11-11 09:00 生产切片集合
func (m *slicingSession) productionM3U8(m3u8Content []string) string {
	res := library.M3U8Head
	for _, m3u8 := range m3u8Content {
		res += m3u8 + "\n"
	}
	res += library.M3U8Foot
	return res
}

// 2019-11-11 10:00:00 上传七牛m3u8文件
func (m *slicingSession) uploadM3U8File(m3u8 string) (string, error) {
	putPolicy := storage.PutPolicy{
		Scope: library.SessionHLSBucket,
	}
	cfg := config.Get()
	mac := qbox.NewMac(cfg.Qiniu.AccessKey, cfg.Qiniu.SecretKey)
	upToken := putPolicy.UploadToken(mac)
	qCfg := storage.Config{}
	// 空间对应的机房
	qCfg.Zone = &storage.ZoneHuadong
	// 是否使用https域名
	qCfg.UseHTTPS = false
	// 上传是否使用CDN上传加速
	qCfg.UseCdnDomains = false
	formUploader := storage.NewFormUploader(&qCfg)
	ret := storage.PutRet{}
	putExtra := storage.PutExtra{}
	data := []byte(m3u8)
	dataLen := int64(len(data))
	key := m.getSaveKey()
	err := formUploader.Put(context.Background(), &ret, upToken, key, bytes.NewReader(data), dataLen, &putExtra)
	if err != nil {
		logger.Error("M3u8集合出错")
		return "", err
	}
	m3u8Url := library.SessionHLSDomain + key
	return m3u8Url, nil
}

func (m *slicingSession) getSaveKey() string {
	nano := time.Now().UnixNano()
	return fmt.Sprintf("%s.m3u8", crypto.Byte2Md5([]byte(strconv.FormatInt(nano, 10))))
}

type M3U8info struct {
	Format FormatInfo `json:"format"`
}
type FormatInfo struct {
	Duration string `json:"duration"`
}

// 获取新生成的m3u8的时长
func (m *slicingSession) getM3U8Duration(m3u8Url string) (float64, error) {
	acInfoURL := m3u8Url + "?avinfo"
	resp, err := service.HTTP.Get(context.Background(), acInfoURL)
	if err != nil {
		return 0, err
	}
	if resp.StatusCode != 200 {
		return 0, errors.New("SlicingMakeSession getM3U8Duration HttpStatusCode error")
	}

	body, err := ioutil.ReadAll(resp.Body)
	defer func() {
		err := resp.Body.Close()
		if err != nil {
			logger.Error(err)
		}
	}()
	if err != nil {
		return 0, err
	}
	var respBody M3U8info
	err = json.Unmarshal(body, &respBody)
	if err != nil {
		return 0, err
	}
	duration, err := strconv.ParseFloat(respBody.Format.Duration, 64)
	if err != nil {
		return 0, err
	}
	return duration, nil
}

// 课程获取强度系数
func (m *slicingSession) getSessionCalorieRatio(sessionID int64) (float64, error) {
	table := course.TbSession.GetSessionByID(sessionID)
	if table != nil {
		strengthRatio, err := strconv.ParseFloat(table.StrengthRatio, 64)
		if err != nil {
			return 0, err
		}
		return strengthRatio, nil
	}
	return 0, nil
}

func (m *slicingSession) updateSessionInfo(sessionID int64, calorie, calorieRatio, duration int32,
	cnStreamMedia string) error {
	var rollbackErr, err error
	type ext struct {
		SessionPeriod string `json:"session_period"`
	}
	var e ext
	e.SessionPeriod = strconv.FormatInt(int64(duration), 10)
	extension, err := json.Marshal(e)
	if err != nil {
		logger.Error(err)
		return err
	}
	session := db.GetEngineMaster().NewSession()
	defer session.Close()
	defer func() {
		if rollbackErr != nil {
			logger.Error(err)
			_ = session.Rollback()
		}
		if err != nil {
			logger.Error(err)
		}
	}()

	rollbackErr = (&course.Session{
		SessionID:     sessionID,
		Calorie:       calorie,
		CalorieRatio:  calorieRatio,
		Duration:      duration,
		CnStreamMedia: cnStreamMedia,
		Extension:     string(extension),
	}).UpdateByTransaction(session)
	if rollbackErr != nil {
		return rollbackErr
	}
	err = session.Commit()
	if err != nil {
		return err
	}
	return nil
}
