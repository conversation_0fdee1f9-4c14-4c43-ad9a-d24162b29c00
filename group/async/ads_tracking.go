package async

import (
	"context"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"
)

type AdsTrackingPayload struct {
	URLs []string `json:"urls"`
}

// AdsTracking 广告第三方链接tracking
func AdsTracking(data AdsTrackingPayload) {
	for _, item := range data.URLs {
		res, err := service.HTTP.Get(context.Background(), item)
		if err != nil {
			logger.Warnf("ads urls tracking:%s, error: %s", item, err.Error())
			continue
		}
		if res.StatusCode != 200 {
			logger.Warnf("Ads url tracking:%s, status: %d", item, res.StatusCode)
		}
		if err := res.Body.Close(); err != nil {
			logger.Warn(err)
		}
	}
}
