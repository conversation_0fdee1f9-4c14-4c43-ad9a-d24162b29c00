package async

import (
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/qiniu/go-sdk/v7/auth/qbox"
	"github.com/qiniu/go-sdk/v7/storage"
	"gitlab.dailyyoga.com.cn/server/go-artifact/crypto"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/course"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

// MediaConvertHLSPayload 课程链接转流媒体传入参数
type MediaConvertHLSPayload struct {
	URL string `json:"url"`
}

// MediaConvertHLS 课程链接转流媒体
func MediaConvertHLS(data MediaConvertHLSPayload) error {
	if data.URL == "" {
		return errors.New("unknown session_id or origin_url")
	}
	convert := mediaConvert{}
	cdn := convert.getMediaURLCDN(data.URL)
	// 如果不是七牛云或者爱拍的地址，则不处理，因为不知道七牛云是否有源文件
	if cdn != library.CDNEnum.Qiniu && cdn != library.CDNEnum.Aipai {
		logger.Errorf("传入的地址不能找到对应七牛云存储链接，请核对，%s", data.URL)
		return nil
	}

	key, err := convert.getURLKey(data.URL)
	if err != nil {
		return err
	}
	if key == "" {
		return nil
	}
	info := course.TbVideoMap.GetRowByKey(key, cdn == library.CDNEnum.Qiniu, cdn == library.CDNEnum.Aipai)
	if info == nil {
		return nil
	}
	qiniuURL, _ := url.QueryUnescape(info.QiniuURL)

	saveKey, err := convert.convert2HLS(qiniuURL)
	if err != nil {
		return err
	}
	convert.save(data.URL, key, saveKey)
	return nil
}

type mediaConvert struct{}

func (m *mediaConvert) getMediaURLCDN(mediaURL string) library.CDNType {
	for cdn, domain := range library.CDNDomain {
		if strings.Contains(mediaURL, domain) {
			return cdn
		}
	}
	return library.CDNEnum.None
}

func (m *mediaConvert) getURLKey(mediaURL string) (string, error) {
	if mediaURL == "" {
		return "", nil
	}
	md5Str := crypto.Byte2Md5([]byte(mediaURL))
	str := md5Str[0:9]
	i, err := strconv.ParseInt(str, 16, 0)
	if err != nil {
		return "", err
	}
	return strconv.FormatInt(i, 10), nil
}

func (m *mediaConvert) convert2HLS(mediaURL string) (string, error) {
	key := m.getMediaKey(mediaURL)
	cfg := config.Get()
	mac := qbox.NewMac(cfg.Qiniu.AccessKey, cfg.Qiniu.SecretKey)

	operationManager := storage.NewOperationManager(mac, &storage.Config{UseHTTPS: false})
	saveKey := m.getSaveKey()

	fopM3U8 := "avthumb/m3u8/noDomain/1/segtime/10/vcodec/libx264/r/25/vb/500k/s/1280x720"
	fops := fopM3U8 + "|saveas/" + storage.EncodedEntry(library.SessionHLSBucket, saveKey)

	pipeline := "session"

	_, err := operationManager.Pfop(library.SessionBucket, key, fops, pipeline, "", true)
	if err != nil {
		return "", err
	}

	return saveKey, nil
}

func (m *mediaConvert) getMediaKey(str string) string {
	qiniuDomain := library.CDNDomain[library.CDNEnum.Qiniu]
	matchStr := ""
	switch {
	case strings.Contains(str, "http://"):
		matchStr = "http://" + qiniuDomain + "/"
	case strings.Contains(str, "https://"):
		matchStr = "https://" + qiniuDomain + "/"
	default:
		return ""
	}
	return strings.Replace(str, matchStr, "", -1)
}

func (m *mediaConvert) getSaveKey() string {
	nano := time.Now().UnixNano()
	key := crypto.Byte2Md5([]byte(strconv.FormatInt(nano, 10)))
	return fmt.Sprintf("%s.m3u8", key)
}

func (m *mediaConvert) save(sourceURL, key, saveKey string) {
	bean := course.SessionHLSMap{
		SourceURL: sourceURL,
		SourceKey: key,
		HLS:       library.SessionHLSDomain + saveKey,
	}

	if err := bean.Save(); err != nil {
		logger.Error(err)
	}
}
