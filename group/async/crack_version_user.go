package async

import (
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/client"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/third"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type CrackVersionUserPayload struct {
	UID         int64  `json:"uid"`
	DeviceID    string `json:"device_id"`
	AnonymousID string `json:"anonymous_id"`
	CrackScene  int    `json:"crack_scene"` // 黑产场景 1登录 2退登
}

const (
	CrackSceneLogin = iota + 1
	CrackSceneLogout
)

func ParseCrackVersionUser(data *CrackVersionUserPayload) error {
	switch data.CrackScene {
	case CrackSceneLogin:
		parseCrackVersionUserLogin(data)
	case CrackSceneLogout:
		parseCrackVersionUserLogout(data)
	default:
		logger.Error("未知的场景", data)
	}
	return nil
}

func parseCrackVersionUserLogin(data *CrackVersionUserPayload) {
	if data.AnonymousID == "" {
		return
	}
	isCrack := false
	sensorsBindList := third.TbATpSensorsData.GetByOpenIDS(data.AnonymousID)
	uidList := make([]int64, 0)
	for _, v := range sensorsBindList {
		uidList = append(uidList, v.UID)
	}
	uidList = append(uidList, data.UID)
	deviceIDList := make([]string, 0)
	uidChannels := client.TbAdUIDChannel.GetByUIDList(uidList)
	for _, v := range uidChannels {
		if v.DeviceID != "" {
			deviceIDList = append(deviceIDList, v.DeviceID)
		}
	}
	crackUIDUsers := user.TbCrackVersionUser.GetByUIDList(uidList)
	crackDeviceUsers := user.TbCrackVersionUser.GetByDeviceIDList(deviceIDList)
	if len(crackUIDUsers) > 0 || len(crackDeviceUsers) > 0 {
		isCrack = true
	}
	if isCrack {
		crackUser := user.TbCrackVersionUser.GetItemByUserInfo(data.UID, data.DeviceID)
		if crackUser != nil {
			crackUser.Status = library.Yes
			if err := crackUser.Update(); err != nil {
				logger.Error(err)
			}
			return
		}
		crackUser = &user.CrackVersionUser{
			UID:      data.UID,
			DeviceID: data.DeviceID,
			Status:   library.Yes,
			Message:  "关联设备存在黑产名单",
		}
		if err := crackUser.Save(); err != nil {
			logger.Error(err)
		}
	}
}

func parseCrackVersionUserLogout(data *CrackVersionUserPayload) {
	// 检查数据是否存在
	if user.TbCrackVersionUser.GetItemByUserInfo(data.UID, data.DeviceID) != nil {
		return
	}
	crackItem := user.TbCrackVersionUser.GetItem(data.UID)
	if crackItem != nil {
		crackItem.Status = library.Yes
		if crackItem.DeviceID == "" {
			crackItem.DeviceID = data.DeviceID
			if err := crackItem.Update(); err != nil {
				logger.Error("黑产用户更新deviceID失败，", err.Error())
			}
		} else if crackItem.DeviceID != data.DeviceID {
			crackItemNew := &user.CrackVersionUser{
				UID:      data.UID,
				DeviceID: data.DeviceID,
				Status:   library.Yes,
				Message:  "关联设备存在黑产名单",
			}
			if err := crackItemNew.Save(); err != nil {
				logger.Error(err)
			}
		}
	} else if data.DeviceID != "" {
		crackItem = user.TbCrackVersionUser.GetItemByDeviceID(data.DeviceID)
		if crackItem != nil {
			if crackItem.UID == 0 {
				crackItem.UID = data.UID
				crackItem.Status = library.Yes
				if err := crackItem.Update(); err != nil {
					logger.Error("黑产用户更新uid失败，", err.Error())
				}
			} else if crackItem.UID != data.UID {
				crackItemNew := &user.CrackVersionUser{
					UID:      data.UID,
					DeviceID: data.DeviceID,
					Status:   library.Yes,
					Message:  "关联设备存在黑产名单",
				}
				if err := crackItemNew.Save(); err != nil {
					logger.Error(err)
				}
			} else {
				crackItem.Status = library.Yes
				if err := crackItem.Update(); err != nil {
					logger.Error("黑产用户更新uid失败，", err.Error())
				}
			}
		}
	}
}
