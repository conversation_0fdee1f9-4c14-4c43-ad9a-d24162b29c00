package async

import (
	"net/url"
	"strconv"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

type CompleteOrderPayload struct {
	LogID int64 `json:"log_id"`
}

func CompleteOrder(data CompleteOrderPayload) error {
	logger.Info("异步调用支付成功回调:", data)
	go requestYogaCompleteOrder(data)
	return nil
}

func requestYogaCompleteOrder(data CompleteOrderPayload) {
	values := make(url.Values)
	values.Add("complete_log_id", strconv.FormatInt(data.LogID, 10))
	values.Add("complete_call_type", "2")
	resp, err := requests.NewYogaClient(config.Get().PhpYogaAddress).PostForm("order/completeorder/completeorder", values)
	if err != nil {
		logger.Error("异步调用支付成功回调报错", err)
		return
	}
	if !resp.Is<PERSON>() || resp.Body == "" {
		logger.Error("异步调用支付成功回调失败", resp, data)
		return
	}
}
