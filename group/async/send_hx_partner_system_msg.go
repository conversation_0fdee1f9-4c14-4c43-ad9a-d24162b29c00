package async

import (
	"bytes"
	"encoding/json"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/server/go-artifact/crypto"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/partner"
)

// 缓存前缀
var memCachePreFix string

// 环信token调用结构体
type getTokenRequest struct {
	GrantType    string `json:"grant_type"`
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
}

// 环信token返回值结构体
type getTokenResponse struct {
	AccessToken string `json:"access_token"`
	ExpireIn    int64  `json:"expire_in"`
}

// 环信获取指定用户返回结构体
type getDetailResponse struct {
	HxError string `json:"error"`
}

// 环信注册用户调用结构体
type registerRequest struct {
	Username int64  `json:"username"`
	Password string `json:"password"`
}

// 环信注册用户返回结构体
type registerResponse struct {
	HxError string `json:"error"`
}

// 环信注册用户返回结构体
type addGroupResponse struct {
	HxError string `json:"error"`
}

// 环信系统消息推送调用结构体
type sendSystemMsg struct {
	TargetType string  `json:"target_type"`
	Target     []int64 `json:"target"`
	From       string  `json:"from"`
	HxType     string  `json:"type"`
	Ext        sendExt `json:"ext"`
	Msg        sendMsg `json:"msg"`
}

// 环信系统消息ext结构体
type sendExt struct {
	TeamID          int64            `json:"team_id"`
	TeamName        string           `json:"team_name"`
	TeamImage       string           `json:"team_image"`
	IsSystemMessage int64            `json:"is_system_message"`
	EmApnsExt       sendExtPushTitle `json:"em_apns_ext"`
}

type sendExtPushTitle struct {
	EmPushTitle string `json:"em_push_title"`
}

type sendMsg struct {
	SendHxType string `json:"send_hx_type"`
	Msg        string `json:"msg"`
}

// 环信系统消息推送返回结构体
type sendSystemMsgResponse struct {
	HxError string `json:"error"`
}

// setTeamPlayTimeRanking 设置班级排行榜
func SendHXPartnerSystemMsg(data *partner.SendHXPartnerSystemMsgPayload) error {
	// 群组id为无效id，则报错
	if data.GroupID < 1 {
		logger.Error("环信系统消息推送，GroupID错误：%d", data.GroupID)
		return nil
	}

	// 用户id为无效id，则报错
	if data.UID < 1 {
		logger.Error("环信系统消息推送, UID错误: %d", data.UID)
		return nil
	}

	// qa和线上分开配置
	cfg := config.Get()
	memCachePreFix = cfg.YogaMemcached.Prefix

	// 获取环信token
	// 消费逻辑：若是token获取失败，不论哪步骤出错，都不做消费
	accessToken, err := getToken(memCachePreFix)
	if err != nil {
		logger.Error(err)
		return err
	}

	// 查询指定用户详情
	// 消费逻辑：若是查询用户有报错则不进行消费，用户信息没查询到和用户信息查询到两种情况已经排除
	hasUser, err := getUserDetail(data.UID, accessToken)
	if err != nil {
		logger.Error(err)
		return err
	}

	// 这里不可以修改，一定是&&的关系，表示：用户信息没有查询到，且没有报错，就是资源不存在
	if !hasUser && err == nil {
		// 未注册的有那个户进行注册
		// 消费逻辑：若是注册出错则不进行消费，用户环信未注册，和用户已经注册过两个情况已经排除
		registerResult, err := registerHxUsers(data.UID, accessToken)
		if err != nil || !registerResult {
			logger.Error(err)
			return err
		}
	}

	// 将用户加入到group群组
	// 消费逻辑：若是加入出错则不进行消费，加入成功和已经在群组两种情况已经排除
	joined, err := addGroup(data, accessToken)
	if err != nil {
		logger.Warn(err)
		return nil
	}

	if joined && err == nil {
		// 对该群组发送系统消息
		// 消费逻辑：只有发送成功则进行消费，其他报错均不消费
		_, err := sendSystemMsgToPartner(data, accessToken)
		if err != nil {
			logger.Error(err)
			return err
		}
	}

	return nil
}

// 向当前群组发送系统消息
func sendSystemMsgToPartner(data *partner.SendHXPartnerSystemMsgPayload, accessToken string) (bool, error) {
	c := newHxClient(accessToken)

	sendExtPushTitle := sendExtPushTitle{
		EmPushTitle: partner.HxSendRequest.HxExtPushTitle,
	}
	ext := sendExt{
		TeamID:          data.TeamID,
		TeamName:        data.TeamName,
		TeamImage:       data.TeamImage,
		IsSystemMessage: data.IsSystemMessage,
		EmApnsExt:       sendExtPushTitle,
	}
	msg := sendMsg{
		SendHxType: partner.HxSendRequest.HxType,
		Msg:        data.Content,
	}
	sendSystemMsg := sendSystemMsg{
		TargetType: partner.HxSendRequest.HxSendTargetType,
		Target:     []int64{data.GroupID},
		From:       partner.HxSendRequest.HxFrom,
		HxType:     partner.HxSendRequest.HxType,
		Ext:        ext,
		Msg:        msg,
	}
	jsonRequest, _ := json.Marshal(sendSystemMsg)
	response, err := c.Post("messages", bytes.NewReader(jsonRequest))
	if err != nil {
		logger.Error(err)
		return false, err
	}

	var sendSystemMsgResponse sendSystemMsgResponse
	if err := json.Unmarshal(response.GetBodyByte(), &sendSystemMsgResponse); err != nil {
		return false, err
	}

	// 若是error未被赋值，则系统消息推送成功
	// 返回值200，表示消息发送成功
	if response.IsOK() && sendSystemMsgResponse.HxError == "" {
		return true, nil
	}

	// 若是有error,则报错即可
	if sendSystemMsgResponse.HxError != "" {
		err = errors.New(sendSystemMsgResponse.HxError)
		return false, err
	}
	errString := fmt.Sprintf("环信系统消息推送出错，错误码是：%d", response.StatusCode)
	err = errors.New(errString)
	return false, err
}

// 将用户加入环信群组
// 一次给群添加一个成员，不同重复添加同一个成员。如果用户已经是群成员，将添加失败，并返回错误。
func addGroup(data *partner.SendHXPartnerSystemMsgPayload, accessToken string) (bool, error) {
	c := newHxClient(accessToken)
	url := fmt.Sprintf("chatgroups/%d/users/%d", data.GroupID, data.UID)
	response, err := c.Post(url, nil)
	if err != nil {
		logger.Error(err)
		return false, err
	}
	var addGroupResponse addGroupResponse
	if err := json.Unmarshal(response.GetBodyByte(), &addGroupResponse); err != nil {
		return false, err
	}

	// 有error 则将错误error返回
	if addGroupResponse.HxError != "" && addGroupResponse.HxError != partner.HXResponse.ForbiddenOp {
		err = errors.New(addGroupResponse.HxError)
		return false, err
	}

	// 若是error未被赋值，则加入群组成功,无需添加
	if response.IsOK() && addGroupResponse.HxError == "" {
		return true, nil
	}

	// 文档没有写，但是发现用户已经在群组，再加入群组会返回403，error是：forbidden_op
	if addGroupResponse.HxError == partner.HXResponse.ForbiddenOp {
		return true, nil
	}

	errString := fmt.Sprintf("环信加入群组失败,状态码：%d", response.StatusCode)
	err = errors.New(errString)
	return false, err
}

// 注册环信用户
func registerHxUsers(uid int64, accessToken string) (bool, error) {
	hxUserInfo, err := user.TbAccountEasemobd.GetUserHXInfo(uid, partner.HxAccountType.HxType)
	if err != nil {
		return false, err
	}

	// 进行环信注册
	if hxUserInfo == nil {
		c := newHxClient(accessToken)
		password := getPassWd(uid)
		registerRequest := registerRequest{
			Username: uid,
			Password: password,
		}
		jsonRequest, _ := json.Marshal(registerRequest)
		response, err := c.Post("users", bytes.NewReader(jsonRequest))
		if err != nil {
			return false, err
		}

		var registerResponse registerResponse
		if err := json.Unmarshal(response.GetBodyByte(), &registerResponse); err != nil {
			return false, err
		}

		// php是用返回值判断是否有error进行判断的
		// 但是实际看到环信官网文档是：
		//      返回值200，表示获取用户信息成
		//      返回值400，表示该用户不存在  error：duplicate_unique_property_exists
		// error没有初始化赋值，说明注册成功
		if registerResponse.HxError != "" && registerResponse.HxError != partner.HXResponse.UniquePropertyExists {
			// 环信文档：如果返回结果是429、503或者其他5xx，有可能代表该接口被限流了，请稍微暂停一下并重试
			// PHP未做此重试机制，暂时先打日志，若是真有日志，则加入重试机制
			err = errors.New(registerResponse.HxError)
			return false, err
		}

		// accountinfo_easemob中无数据，所以进行数据写入，环信用户已经注册和环信用户才注册的用户都做写入
		if registerResponse.HxError == partner.HXResponse.UniquePropertyExists || registerResponse.HxError == "" {
			_, err = user.TbAccountEasemobd.SetUserHXInfo(uid, password, partner.HxAccountType.HxType)
			if err != nil {
				return false, err
			}
			return true, nil
		}
	}

	return true, nil
}

// 查询指定用户的详细信息
func getUserDetail(uid int64, accessToken string) (bool, error) {
	url := fmt.Sprintf("users/%d", uid)
	response, err := newHxClient(accessToken).Get(url)
	if err != nil {
		logger.Error(err)
		return false, err
	}

	var getDetailResponse getDetailResponse
	if err := json.Unmarshal(response.GetBodyByte(), &getDetailResponse); err != nil {
		logger.Error("获取环信token，返回数据无法解析")
		return false, err
	}

	// php是用返回值判断是否有error进行判断的
	// 但是实际看到环信官网文档是：
	//      返回值200，表示获取用户信息成功
	//      返回值404，表示该用户不存在  error：service_resource_not_found
	// error没有初始化赋值，说明查到用户信息
	if getDetailResponse.HxError == "" {
		return true, nil
	}
	// 查询返回service_resource_not_found，说明没有查询到用户信息
	if getDetailResponse.HxError == partner.HXResponse.ResourceNotFound {
		return false, nil
	}

	errString := fmt.Sprintf("环信查询用户信息出错,状态码是：%d", response.StatusCode)
	err = errors.New(errString)
	return false, err
}

// 获取环信的token (获取token存入缓存)
// php逻辑：token缓存一天，但是文档标明：expires_in：token 有效时间，以秒为单位，在有效期内不需要重复获取,所以缓存时间可以改为接口返回的时间
func getToken(memCachePreFix string) (string, error) {
	date := time.Now().Format("20060102")
	hxTokenKey := getTokenKey(date, memCachePreFix)
	value, _ := cache.GetMemcached().Get(hxTokenKey)

	// token的值先写死
	if len(value) > 20 {
		return value, nil
	}

	// 调用环信接口换取token
	hxConfig := config.Get().HxConfig
	getTokenRequest := getTokenRequest{
		GrantType:    hxConfig.GrantType,
		ClientID:     hxConfig.ClientID,
		ClientSecret: hxConfig.ClientSecret,
	}
	jsonRequest, _ := json.Marshal(getTokenRequest)
	c := newHxClient("")
	response, err := c.Post("/token", bytes.NewReader(jsonRequest))
	if err != nil {
		logger.Error(err)
		return "", err
	}

	// token获取做200的判断，token只有取到值，则往下执行，否则不做消费
	if response.IsOK() {
		var getTokenResponse getTokenResponse

		if err := json.Unmarshal(response.GetBodyByte(), &getTokenResponse); err != nil {
			return "", err
		}

		if getTokenResponse.AccessToken != "" {
			// mirror再测试缓存的写入，此时只打日志
			// 跟php保存一致，token缓存一天，暂时不用token返回的有效期，key是带日期的方便查找
			expireIn := int64(86400)
			err = cache.GetMemcached().SetEx(hxTokenKey, getTokenResponse.AccessToken, int(expireIn))
			if err != nil {
				return "", err
			}
			return getTokenResponse.AccessToken, nil
		}
	}

	errString := fmt.Sprintf("环信查询用户信息出错,状态码是：%d", response.StatusCode)
	err = errors.New(errString)
	return "", err
}

// 获取环信token对应缓存key
func getTokenKey(date, memcachedPreFix string) string {
	cacheKeyStr := fmt.Sprintf("get_easemob_vip6_token" + date)
	cacheKey := crypto.Byte2Md5([]byte(cacheKeyStr))
	return fmt.Sprintf(memcachedPreFix+"%s", cacheKey)
}

//  根据uid生成password
func getPassWd(uid int64) string {
	uidStr := fmt.Sprintf("%d", uid)
	return crypto.Byte2Md5([]byte(uidStr))
}

// 封装环信调用
func newHxClient(accessToken string) *requests.Client {
	c := &requests.Client{}
	c.UseHTTPS()
	c.SetBaseDomain(config.Get().HxConfig.HxURL)
	c.SetHeader("Content-Type", "application/json")

	if accessToken != "" {
		c.SetHeader("Authorization", "Bearer "+accessToken)
	}
	return c
}
