package async

import (
	"context"
	"errors"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"

	pb "gitlab.dailyyoga.com.cn/protogen/yoga-cs-go/yoga-cs"
)

type SensorsEventPayload struct {
	UID                int64  `json:"uid"`
	OrderID            string `json:"order_id"`
	RefundOrderID      string `json:"refund_order_id"`
	EventType          string `json:"event_type"`
	IsChallengeRefund  bool   `json:"is_challenge_refund"`
	IsChannelCall      bool   `json:"is_channel_callback_event"`
	AppID              string `json:"app_id"`
	RetrieveType       int32  `json:"retrieve_type"`
	RetryDays          int32  `json:"retry_days"`
	RetryTimes         int32  `json:"retry_times"`
	RetryCharge        bool   `json:"retry_charge"`
	RetryTimesOfTheDay int32  `json:"retry_times_of_the_day"`
	DealUpdateTimes    int32  `json:"deal_update_times"`
}

func EventSubmitDyOrder(data *SensorsEventPayload) error {
	if data.OrderID == "" {
		logger.Error("EventSubmitDyOrder", *data)
		return nil
	}
	rpc := grpc.GetYoGaCsClientClient()
	resp, err := rpc.DYEventOrderReport(context.Background(), &pb.DYEventOrderReportReq{
		UID:                data.UID,
		OrderID:            data.OrderID,
		EventType:          data.EventType,
		RefundOrderID:      data.RefundOrderID,
		IsChallengeRefund:  data.IsChallengeRefund,
		RetryCharge:        data.RetryCharge,
		IsChannelCall:      data.IsChannelCall,
		AppID:              data.AppID,
		RetrieveType:       data.RetrieveType,
		RetryTimes:         data.RetryTimes,
		RetryDays:          data.RetryDays,
		RetryTimesOfTheDay: data.RetryTimesOfTheDay,
		DealUpdateTimes:    data.DealUpdateTimes,
	})
	if err != nil {
		return err
	}
	if resp.GetResultCode() != 1001 {
		return errors.New("数据处理失败")
	}
	return nil
}
