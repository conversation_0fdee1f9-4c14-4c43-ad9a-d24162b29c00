package async

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/pkg/errors"

	apb "gitlab.dailyyoga.com.cn/protogen/srv-usercenter-go/app"
	cpb "gitlab.dailyyoga.com.cn/protogen/srv-usercenter-go/common"
	pb "gitlab.dailyyoga.com.cn/protogen/srv-usercenter-go/huawei"
	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/goroutine"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	sc "gitlab.dailyyoga.com.cn/server/go-artifact/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/third"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

const userWechatInfoCacheKey = "user:wechat:info:cachekey:"

type LoginRegister struct {
	UID        string `json:"uid"`
	SourceType int    `json:"source_type"`
	Gender     int    `json:"gender"`
	LoginType  int    `json:"loginType"`
	NickName   string `json:"nickName"`
	UserName   string `json:"username"`
	Unionid    string `json:"unionid"`
	Channels   string `json:"channels"`
	Version    string `json:"version"`
	Thumbnail  string `json:"Thumbnail"`
	DYei       string `json:"dyei"`
	DistinctID string `json:"distinct_id"`
	OsType     int32  `json:"os_type"`
	DeviceID   string `json:"device_id"`
	Alias      int    `json:"alias"`
}

// RegisterImportGroupEvent 神策事件上报:注冊用戶5%導入固定分群
type RegisterImportGroupEvent struct {
	AbGroup string `json:"ab_group"`
}

// water 2020-05-26 注册登陆异步事件
func ProcessLoginRegister(data *LoginRegister) error {
	uid, err := strconv.Atoi(data.UID)
	if err != nil {
		return errors.New("注册登陆异步事件 转换uid")
	}
	if uid < 0 {
		return errors.New("illegal event")
	}
	register := data
	goroutine.GoSafely(func() {
		if err := register.asyncUserInvite(); err != nil {
			logger.Error(err)
		}
		if err := register.asyncSendBenefitGift(); err != nil {
			logger.Error(err)
		}
		if err := register.asyncCompleteMobileTask(); err != nil {
			logger.Error(err)
		}
	})
	// ocpd 数据处理
	goroutine.GoSafely(func() {
		if err := register.asyncOCPDRegister(); err != nil {
			logger.Warn(err)
		}
	})
	goroutine.GoSafely(func() {
		if err := register.uploadDeviceIDAnonymous(); err != nil {
			logger.Warn(err)
		}
	})
	// 9.34 需求12  每日新进入用户的 5%（每20位用户进入1个）导入固定分群
	goroutine.GoSafely(func() {
		if err := register.importGroup(); err != nil {
			logger.Warn(err)
		}
	})
	return nil
}

// 导入固定分群
func (u *LoginRegister) importGroup() error {
	if u.LoginType != int(library.UserLoginTypeEnum.SensorsData) {
		return nil
	}
	rd := cache.GetYogaRedis()
	counter := rd.Incr(context.Background(), library.RedisKeyNewUserCounter).Val() - 1
	logger.Debugf("LoginRegisterCounter:%d", counter)
	// 分群用户量达到上限即停止用户加入
	if counter >= library.ImportGroupLimitCount {
		return nil
	}
	// 每日新进入用户的x%(每n位用户进入1个)
	if counter%library.ImportGroupEveryCount == 0 {
		groupID := int64(library.ImportGroupID)
		uid, err := strconv.Atoi(u.UID)
		if err != nil {
			logger.Error("[registerImportGroup]ParseInt u.UID fail:", err)
			return err
		}
		user.TbUserGroupExt.Insert(int64(uid), groupID)
		user.TbUserGroup.IncrTotalCount(groupID)
		// 神策事件上報
		userEvent := &RegisterImportGroupEvent{
			AbGroup: strconv.Itoa(library.ImportGroupID),
		}
		userEvent.Track(strconv.Itoa(uid))
	}
	return nil
}

// EventName 神策数据上报
func (RegisterImportGroupEvent) EventName() string {
	if config.Get().Env != env.Product && config.Get().Env != env.Mirror {
		return "h2_abt_all_vip"
	}
	return "abt_all_vip"
}

func (RegisterImportGroupEvent) Prefix() string {
	return ""
}

func (r *RegisterImportGroupEvent) Track(uid string) {
	event := *r
	goroutine.GoSafely(func() {
		if strings.Trim(uid, " ") == "" {
			return
		}
		sc.Track(uid, event, true)
	})
}

//  上传设备的 device_id 和 anonymous_id 绑定
func (u *LoginRegister) uploadDeviceIDAnonymous() error {
	channel, err := strconv.ParseInt(u.Channels, 10, 64)
	if err != nil {
		logger.Error("asyncOCPD channel format fail", err)
	}
	device := &cpb.AppDevice{
		Channel: int32(channel),
		OSType:  u.OsType,
	}
	resp, err := grpc.GetUserCenterClient().UploadDeviceIDAnonymous(context.Background(),
		&apb.UploadDeviceIDAnonymousRequest{
			Device:      device,
			DeviceID:    u.DeviceID,
			AnonymousID: u.DistinctID,
		})
	if err != nil {
		logger.Error("请求失败", err, resp)
		return err
	}
	return nil
}

const (
	AppRegister   = 7 // 注册应用
	channelHuaWei = "100020"
	channelOppo   = "100011" // OPP渠道
	channelVivo   = "100017" // Vivo渠道
)

// 华为OCPD数据回传
func (u *LoginRegister) asyncOCPDRegister() error {
	ctx := context.Background()
	if u.DYei == "" || u.Channels == "" {
		return nil
	}
	rd := cache.GetYogaRedis()
	switch u.Channels {
	case channelHuaWei:
		cacheKey := fmt.Sprintf("hw:ocpd:%v", u.DYei)
		cacheValue, err := rd.Get(ctx, cacheKey).Result()
		if err == redis.Nil {
			logger.Debugf("缓存不存在: %s", cacheKey)
			return nil
		} else if err != nil {
			logger.Error(err)
			return nil
		}
		if cacheValue == "" {
			return nil
		}
	case channelOppo, channelVivo:
		if u.DistinctID == "" {
			return nil
		}
	default:
		return nil
	}
	uid, err := strconv.ParseInt(u.UID, 10, 64)
	if err != nil {
		logger.Error("asyncOCPD UID format fail", err)
		return nil
	}

	channel, err := strconv.ParseInt(u.Channels, 10, 64)
	if err != nil {
		logger.Error("asyncOCPD channel format fail", err)
		return nil
	}
	rsp, err := grpc.GetUserCenterClient().HuaweiPush(ctx, &pb.PushRequest{
		CustomerId: u.DYei,
		ActionType: AppRegister,
		UID:        uid,
		DistinctID: u.DistinctID,
		Channel:    channel,
	})
	if err != nil {
		logger.Warnf("opcd 回传失败:%+v 返回值：%+v", u, rsp)
		return err
	}
	uCacheKey := fmt.Sprintf("hw:ocpd:uid:%v", u.UID)
	hr, _ := time.ParseDuration("360h") // 15天
	contentJSON, err := json.Marshal(u)
	if err != nil {
		logger.Warnf("opcd json 回传失败:%+v 返回值：%+v", u, rsp)
		return err
	}
	if err := rd.SetEX(ctx, uCacheKey, string(contentJSON), hr); err != nil {
		logger.Warn("回传设置 uid 对应 dyei 出错", err.Err())
	}
	return nil
}

// 被邀请用户注册后
func (u *LoginRegister) asyncUserInvite() error {
	uri := "/user/user/asyncUserInvite"
	params := url.Values{}
	params.Set("uid", u.UID)
	params.Set("login_type", strconv.Itoa(u.LoginType))
	params.Set("unionid", u.Unionid)
	params.Set("nickName", u.NickName)
	params.Set("version", u.Version)
	params.Set("channels", u.Channels)
	params.Set("username", u.UserName)
	resp, err := requests.New620Client(config.Get().Php620Address).PostForm(uri, params)
	if err != nil {
		return err
	}
	if !resp.IsOK() {
		logger.Errorf("send asyncUserInvite error. status: %d, params: %s", resp.StatusCode, params.Encode())
	}
	return nil
}

// 大礼包发放
func (u *LoginRegister) asyncSendBenefitGift() error {
	flag := library.CheckLoginType(library.UserLoginType(u.LoginType), []library.UserLoginType{
		library.UserLoginTypeEnum.Code,
		library.UserLoginTypeEnum.OneKey,
		library.UserLoginTypeEnum.MiniAppMobile,
		library.UserLoginTypeEnum.HuaweiOnekey,
	})
	if !flag {
		if u.LoginType == int(library.UserLoginTypeEnum.WeiXin) {
			// 往新表内添加数据
			if err := u.recordWechatInfo(); err != nil {
				logger.Error("recordWechatInfo---err", err)
				return err
			}
			// 记录用户的微信账号
			if err := u.recordWechatExtInfo(); err != nil {
				logger.Error("recordWechatExtInfo---err", err)
				return err
			}
		} else if u.LoginType != int(library.UserLoginTypeEnum.SensorsData) {
			// 用户第三方登陆注册时，记录或更改用户第三方账号状态
			if err := u.recordInfo(); err != nil {
				logger.Error("recordInfo---err", err)
				return err
			}
		}
		// 调用yoga发放礼包
		if err := u.asyncUseGiftByTypeTask(); err != nil {
			logger.Error("asyncUseGiftByTypeTask---err", err)
			return err
		}
	}

	return nil
}

// 手机号注册发放奖励
func (u *LoginRegister) asyncCompleteMobileTask() error {
	uri := "/user/user/completeMobileTask"
	params := url.Values{}
	params.Set("uid", u.UID)
	params.Set("login_type", strconv.Itoa(u.LoginType))
	params.Set("username", u.UserName)
	params.Set("version", u.Version)
	params.Set("channels", u.Channels)
	resp, err := requests.New620Client(config.Get().Php620Address).PostForm(uri, params)
	if err != nil {
		return err
	}
	if !resp.IsOK() {
		logger.Errorf("send asyncUserInvite error. status: %d, params: %s", resp.StatusCode, params.Encode())
	}
	return nil
}

//
// asyncUseGiftByTypeTask
//  @Description: 调用yoga发放礼包
//  @receiver u
//  @return error
//
func (u *LoginRegister) asyncUseGiftByTypeTask() error {
	uri := "/coupon/gift/usegiftbytype"
	params := url.Values{}
	params.Set("uid", u.UID)
	params.Set("page_type", strconv.Itoa(int(library.GiftPageTypeEnum.Register)))
	resp, err := requests.NewYogaClient(config.Get().PhpYogaAddress).PostForm(uri, params)
	if err != nil {
		return err
	}
	if !resp.IsOK() {
		logger.Errorf("send asyncUserInvite error. status: %d, params: %s", resp.StatusCode, params.Encode())
	}
	return nil
}

//
// recordWechatInfo
//  @Description: 往新表内添加数据
//  @receiver u
//  @return error
//
func (u *LoginRegister) recordWechatInfo() error {
	var err error
	if u.Unionid == "" {
		logger.Error("recordWechatInfo 没有传递 unionid：", u)
		return err
	}
	// 通过 unionid 查询
	wechatInfo := third.TbAccountInfoTpWechatMain.GetMainInfoByUnionID(u.Unionid)
	uid, err := strconv.ParseInt(u.UID, 10, 64)
	data := &third.AccountInfoTpWechatMain{
		UID:          uid,
		UnionID:      u.Unionid,
		BindStatus:   library.BindStatusNo,
		Nickname:     u.NickName,
		HeadImageURL: u.Thumbnail,
	}

	rd := cache.GetYogaRedis()
	ctx := context.Background()

	if wechatInfo == nil || wechatInfo.ID == 0 {
		// 如果数据不存在
		if err := data.Save(); err != nil {
			return err
		}
		if err := data.UpdateUIDByUnionID(u.Unionid); err != nil {
			return err
		}
		rd.Del(ctx, userWechatInfoCacheKey+u.UID)
	} else if wechatInfo.BindStatus == library.BindStatusNo {
		// 已解绑
		if err := data.Update(); err != nil {
			return err
		}
		if err := data.UpdateUIDByUnionID(u.Unionid); err != nil {
			return err
		}
		rd.Del(ctx, userWechatInfoCacheKey+u.UID)
	} else if wechatInfo.BindStatus == library.Default &&
		strconv.FormatInt(wechatInfo.UID, 10) != "" &&
		strconv.FormatInt(wechatInfo.UID, 10) != u.UID {
		logger.Errorf("recordWechatInfo 帐号不统一 ：数据库记录帐号：%+v 传参过来的帐号：%+v", wechatInfo, u)
	}
	return err
}

//
// recordWechatExtInfo
//  @Description: 记录用户的微信账号
//  @receiver u
//  @return error
//
func (u *LoginRegister) recordWechatExtInfo() error {
	var err error
	wechatInfoExtend := third.TbAccountInfoTpWechatMainExtend.GetMainInfoExtend(&third.GetMainInfoExtendRequest{
		UnionID:    u.Unionid,
		OpenID:     u.UserName,
		SourceType: u.SourceType,
	})
	if wechatInfoExtend == nil || wechatInfoExtend.ID == 0 {
		// 如果数据不存在
		data := third.AccountInfoTpWechatMainExtend{
			UnionID:    u.Unionid,
			OpenID:     u.UserName,
			SourceType: u.SourceType,
			Alias:      u.Alias,
		}
		if err := data.Save(); err != nil {
			return err
		}
		// 跟赵越确认，绑定app的微信就增加微信成长值，即：source_type是1
		if u.SourceType == int(library.UserLoginSourceTypeThirdPartyEnum.App) {
			o := &OperateUserGrowPayload{
				UID:     u.UID,
				ModelID: library.BindWechat,
				Version: u.Version,
				Channel: u.Channels,
			}
			if err := o.operateGrow(); err != nil {
				return err
			}
		}
	}

	return err
}

//
// recordInfo
//  @Description: 用户第三方登陆注册时，记录或更改用户第三方账号状态
//  @receiver u
//  @return error
//
func (u *LoginRegister) recordInfo() error {
	var err error

	if flag := library.CheckLoginType(library.UserLoginType(u.LoginType), []library.UserLoginType{
		library.UserLoginTypeEnum.Mobile,
		library.UserLoginTypeEnum.Email,
	}); flag {
		return err
	}

	accountInfoTp := third.Tp.GetAccountInfoTpByTableName(
		u.UserName,
		u.LoginType,
		library.BindStatusYes,
	)
	if accountInfoTp == nil {
		// 如果数据不存在
		uid, _ := strconv.ParseInt(u.UID, 10, 64)
		a := third.AccountInfoTp{
			UID:        uid,
			OpenID:     u.UserName,
			BindStatus: library.BindStatusYes,
			Nickname:   u.NickName,
			UnionID:    u.Unionid,
			SourceType: u.SourceType,
		}
		if err := third.Tp.SaveAccountInfoTp(a.FormatAccountInfoTp(u.LoginType)); err != nil {
			return err
		}
	} else if library.CheckLoginType(library.UserLoginType(u.LoginType), []library.UserLoginType{
		library.UserLoginTypeEnum.QQ,
		library.UserLoginTypeEnum.WeiXin,
		library.UserLoginTypeEnum.Huawei,
	}) {
		a := third.AccountInfoTp{
			UnionID:    u.Unionid,
			SourceType: u.SourceType,
		}
		id, idErr := strconv.ParseInt(accountInfoTp["id"], 10, 64)
		if id == 0 || idErr != nil {
			logger.Warnf("recordInfo 无法获取到id：%+v", accountInfoTp)
			return nil
		}
		formattedInfoToUpdate := a.FormatAccountInfoTp(u.LoginType)
		valFormattedInfoToUpdate := reflect.ValueOf(formattedInfoToUpdate)
		if !valFormattedInfoToUpdate.IsValid() {
			logger.Errorf("recordInfo (Update): Formatted account info for update "+
				"is nil (FormatAccountInfoTp returned nil). User: %+v, LoginType: %d. Skipping update.", u, u.LoginType)
			return errors.New("无法格式化账户信息以进行更新，可能是不支持的登录类型")
		}
		if err := third.Tp.UpdateAccountInfoTp(formattedInfoToUpdate, id); err != nil {
			return err
		}
	}

	return err
}
