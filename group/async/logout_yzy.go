package async

import (
	"encoding/json"
	"net/http"
	"net/url"
	"strings"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
)

const (
	// 有赞云登出接口
	AppYouznYunLogoutURL = "https://uic.youzan.com/sso/open/logout"
)

// LogoutYZYPayload 有赞登出
type LogoutYZYPayload struct {
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	OpenUserID   string `json:"open_user_id"`
}

// YZYResponse 有赞登出的响应
type YZYResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"msg"`
	Data    interface{} `json:"data"`
}

// LogoutYZY 登出有赞
func LogoutYZY(data *LogoutYZYPayload) {
	values := make(url.Values)
	values.Add("client_id", data.ClientID)
	values.Add("client_secret", data.ClientSecret)
	values.Add("open_user_id", data.OpenUserID)
	header := http.Header{}
	header.Set("Content-Type", "application/x-www-form-urlencoded")
	body, err := requests.HTTPRequest(AppYouznYunLogoutURL, "POST", header, strings.NewReader(values.Encode()))
	if err != nil {
		logger.Errorf("登出有赞云失败,错误:%s 且请求参数为:%s", err, values.Encode())
		return
	}
	y := new(YZYResponse)
	if err = json.Unmarshal(body, y); err != nil {
		logger.Errorf("登出有赞云失败,错误:%s 响应结果内容为:%s", err, string(body))
		return
	}
	if y.Code != 0 {
		logger.Errorf("登出有赞云失败,响应结果内容为:%+v", y)
	}
}
