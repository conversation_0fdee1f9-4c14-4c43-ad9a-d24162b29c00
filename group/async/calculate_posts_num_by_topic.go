package async

import (
	"context"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/yogaparadise"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/topic"
)

func CalculatePostsNumByTopic(data topic.PostsMsgPayload) error {
	// @text  topicId为无效id，则报错
	if data.TopicID < 1 {
		logger.Error("统计话题中帖子个数，TopicID出错：%d", data.TopicID)
		return nil
	}

	// @text 统计话题下的帖子个数（只查询公开，状态正常，未被禁止的帖子个数）
	total, err := db.TbTopicToPosts.GetPostsNumByTopic(data.TopicID)
	ctx := context.Background()

	if err == nil {
		// @text 更新话题帖子个数缓存
		rd := cache.GetYogaRedis()
		var postsTopicPostCount int64 = 10011000281
		cacheKey := strconv.FormatInt(postsTopicPostCount, 10) + ":" + strconv.FormatInt(data.TopicID, 10)
		exp := 24 * time.Hour
		err := rd.SetEX(ctx, cacheKey, strconv.FormatInt(total, 10), exp)
		if err.Err() != nil {
			logger.Error("统计话题中帖子个数，更新缓存出错，话题id：%d", data.TopicID)
		}
	}

	return nil
}
