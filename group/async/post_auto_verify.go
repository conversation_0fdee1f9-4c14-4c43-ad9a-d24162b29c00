package async

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	proto "gitlab.dailyyoga.com.cn/protogen/moderation-go"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	db "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/posts"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"
)

type PostAutoVerifyPayload struct {
	UID      int64 `json:"uid"`
	Mobile   int64 `json:"mobile"`
	PostID   int64 `json:"post_id"`
	Type     int64 `json:"type"`
	ReportID int64 `json:"report_id"`
}

const VerifyPassText = "pass"

// PostAutoVerify 帖子审核
func PostAutoVerify(data *PostAutoVerifyPayload) error {
	if data.PostID < 1 {
		logger.Errorf("审核帖子提交数据出错 %+v", data)
	}
	item := db.TbPosts.GetItem(data.PostID)
	if item == nil {
		logger.Errorf("审核帖子的ID未查找到数据 %d", data.PostID)
		return errors.New("审核帖子的ID未查找到数据")
	}
	// 20221202 用户意见反馈 只在自己帖子里面看到
	// nolint
	if strings.Index(item.Content, "#用户意见反馈#") >= 0 {
		item.IsForbidden = 1
		if err := item.Update(); err != nil {
			logger.Error(err)
		}
		return nil
	}
	rpc := grpc.GetModerationClient()
	content := fmt.Sprintf("%s%s", item.Title, item.Content)
	var moderation string
	if content != "" {
		contentJSON, err := json.Marshal([]string{content})
		if err != nil {
			logger.Warnf("生成审核文本出错:%s", err.Error())
		}
		tRsp, err := rpc.TextModeration(context.Background(), &proto.TModerationRequest{Content: string(contentJSON)})
		if err != nil {
			logger.Error("调用文本审核服务出错:%s", err.Error())
		}
		if tRsp != nil {
			if !tRsp.Verify {
				for _, v := range tRsp.List {
					if v.Suggestion != VerifyPassText {
						moderation += fmt.Sprintf("敏感词：[%s]\n", v.Content)
					}
				}
			}
		}
	}

	if item.Images != "" {
		image := strings.Split(item.Images, ",")
		imageJSON, err := json.Marshal(image)
		if err != nil {
			logger.Warnf("生成审核图片出错:%s", err.Error())
		}

		iRsp, err := rpc.ImageModeration(context.Background(), &proto.IModerationRequest{Content: string(imageJSON)})
		if err != nil {
			logger.Error("调用审核图片服务出错:%s", err.Error())
		}
		if iRsp != nil {
			if !iRsp.Verify && len(iRsp.List) > 0 {
				for _, v := range iRsp.List {
					if v.Suggestion != VerifyPassText {
						moderation += fmt.Sprintf("\n敏感图:[图片连接：%s 规则：%s]\n", v.Content, v.Message)
					}
				}
			}
		}
	}
	// IsForbidden 审核状态：0：审核通过(别人可以看到)1：审核通过(只在我的帖子内看到) 2-待审核（审核中）进入机审状态 3-机审核不通过
	// 4-人工审核不通过(post表内isForbidden)
	if data.Type == 1 {
		data.postVerify(moderation, item)
	} else if data.Type == 2 {
		data.postReport(moderation, item)
	}
	return nil
}

// postVerify 发帖审核帖子
func (u *PostAutoVerifyPayload) postVerify(moderation string, item *db.Posts) {
	if moderation != "" {
		bean := &db.PostAutoVerify{
			UID:        u.UID,
			ObjID:      u.PostID,
			Mobile:     fmt.Sprintf("%d", u.Mobile),
			Moderation: moderation,
			Status:     3,
			VerifyTime: time.Now().Unix(),
		}
		if err := bean.Save(); err != nil {
			logger.Error(err)
		}
		item.IsForbidden = 3
		if err := item.Update(); err != nil {
			logger.Error(err)
		}
		Content := fmt.Sprintf("您好，系统检测您%s  发布的帖子含敏感内容，需人工审核。"+
			"您可前往个人中心-我的帖子 查看进度", time.Now().Format("2006-01-02 15:04"))
		pushData := service.PushData{
			UserID:      fmt.Sprintf("%d", u.UID),
			LinkType:    0,
			UserType:    0,
			Title:       "社区帖子审核通知",
			Content:     Content,
			MessageType: 6,
			ChannelType: 1,
			TargetTitle: "点击查看",
		}
		if err := service.Push.Send(&pushData); err != nil {
			logger.Error(err)
		}
		return
	}
	if err := u.operateAutoVerify(1); err != nil {
		logger.Error("审核通过调用620出错:%s", err.Error())
	}
}

// 0：不属实(人审)1：不属实(机审核)2-待审核（审核中） 3-属实(机审) 4-属实 (post表内isForbidden)
func (u *PostAutoVerifyPayload) postReport(moderation string, post *db.Posts) {
	item := db.TbPostAutoReport.GetItemByID(u.ReportID)
	if item == nil {
		return
	}
	if moderation != "" {
		item.Status = 3
		if err := item.Update(); err != nil {
			logger.Error(err)
		}
		post.IsForbidden = 1
		if err := post.Update(); err != nil {
			logger.Error(err)
		}
		if err := u.operateAutoVerify(3); err != nil {
			logger.Error("审核通过调用620出错:%s", err.Error())
		}
		return
	}
	item.Status = 1
	if err := item.Update(); err != nil {
		logger.Error(err)
	}
}

// 审核通过后调用的逻辑
func (u *PostAutoVerifyPayload) operateAutoVerify(status int64) error {
	uri := "/yogacircle/relieveForbidden"
	params := url.Values{}
	params.Set("post_id", strconv.FormatInt(u.PostID, 10))
	params.Set("status", strconv.FormatInt(status, 10))
	resp, err := requests.New620Client(config.Get().Php620Address).PostForm(uri, params)
	if err != nil {
		return err
	}
	if !resp.IsOK() {
		logger.Errorf("send operateAutoVerify error. status: %d, params: %s", resp.StatusCode, params.Encode())
	}
	return nil
}
