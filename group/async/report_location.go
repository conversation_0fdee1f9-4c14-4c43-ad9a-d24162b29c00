package async

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/server/go-artifact/crypto"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/top"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/location"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"
)

type AreaInfo struct {
	Aid   string `json:"aid"`
	Aname string `json:"aname"`
	ID    string `json:"id"`
}

type CityInfo struct {
	AreaList []AreaInfo `json:"areaList"`
	Cid      string     `json:"cid"`
	Cname    string     `json:"cname"`
	ID       string     `json:"id"`
}

type ProvinceInfo struct {
	CityList []CityInfo `json:"citylist"`
	ID       string     `json:"id"`
	PID      string     `json:"pid"`
	Pname    string     `json:"pname"`
}

type ReportLocationPayload struct {
	UID      int64  `json:"uid"`
	Lat      string `json:"lat"`
	Lng      string `json:"lng"`
	IP       string `json:"ip"`
	DeviceID string `json:"device_id"`
	IDFA     string `json:"idfa"`
}

type BaiduIPLocalDetail struct {
	Province     string `json:"province"`
	City         string `json:"city"`
	District     string `json:"district"`
	Street       string `json:"street"`
	StreetNumber string `json:"street_number"`
}

type BaiduIPLocalContent struct {
	AddressDetail BaiduIPLocalDetail `json:"address_detail"`
	Address       string             `json:"address"`
}

type BaiduIPLocationResult struct {
	Address string              `json:"address"`
	Content BaiduIPLocalContent `json:"content"`
	Status  int                 `json:"status"`
}

type LocationPos struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type BaiduAddressComponent struct {
	Country         string `json:"country"`
	CountryCode     int    `json:"country_code"`
	CountryCodeIso  string `json:"country_code_iso"`
	CountryCodeIso2 string `json:"country_code_iso_2"`
	Province        string `json:"province"`
	City            string `json:"city"`
	District        string `json:"district"`
	Town            string `json:"town"`
	Street          string `json:"street"`
	StreetNumber    string `json:"street_number"`
}

type BaiduLocationResult struct {
	Location           LocationPos           `json:"location"`
	FormattedAddress   string                `json:"formatted_address"`
	Business           string                `json:"business"`
	AddressComponent   BaiduAddressComponent `json:"addresscomponent"`
	SematicDescription string                `json:"sematic_description"`
	CityCode           int32                 `json:"cityCode"`
}

type BaiduLocationResponse struct {
	Status int                 `json:"status"`
	Result BaiduLocationResult `json:"result"`
}

type AMAPStreetNumber struct {
	Number string `json:"number"`
	Street string `json:"street"`
}

type AMapAddressComponent struct {
	Country      string           `json:"country"`
	Province     string           `json:"province"`
	City         string           `json:"city"`
	District     string           `json:"district"`
	Township     string           `json:"township"`
	StreetNumber AMAPStreetNumber `json:"streetNumber"`
}

type AMapRegeoCode struct {
	AddressComponent AMapAddressComponent `json:"addressComponent"`
	FormattedAddress string               `json:"formatted_address"`
}

type AMapLocationResponse struct {
	Status    string        `json:"status"`
	Info      string        `json:"info"`
	InfoCode  string        `json:"infocode"`
	RegeoCode AMapRegeoCode `json:"regeocode"`
}

type LocationInfo struct {
	Country      string `json:"country"`
	CountryCode  string `json:"country_code"`
	Province     string `json:"province"`
	City         string `json:"city"`
	District     string `json:"district"`
	Township     string `json:"township"`
	Street       string `json:"street"`
	StreetNumber string `json:"street_number"`
	Address      string `json:"address"`
	Type         int32  `json:"type"`
}

type DeviceLocationInfo struct {
	Province int64 `json:"province"`
	City     int64 `json:"city"`
}

// getUserLocationByIp 根据ip地址获取位置信息
func getUserLocationByIP(uid int64, ip string) *location.UserGeographyPosition {
	ipLocation, err := location.TbUserGeoggraphyPosition.GetItemByIP(uid, ip)
	if err != nil {
		return nil
	}

	return ipLocation
}

// getLocationFromBaidu 根据经纬度通过百度api获取位置信息
func getLocationFromBaidu(lat, lng string) (*LocationInfo, error) {
	logger.Info("lat:", lat, "lng:", lng)
	return nil, nil
}

// getLocationByIP 根据ip地址获取位置信息
func getLocationByIP(uid int64, ip string) *LocationInfo {
	locationInfo := &LocationInfo{
		Type: int32(library.LocationTypeEnum.IP),
	}

	// 先根据uid+ip获取用户位置（发部分情况下用网络或位置不变，减少请求接口次数，防止超过30w/天配额）
	userGeographyPosition := getUserLocationByIP(uid, ip)
	if userGeographyPosition != nil {
		locationInfo.CountryCode = userGeographyPosition.CountryCode
		locationInfo.Province = userGeographyPosition.Province
		locationInfo.City = userGeographyPosition.City
		locationInfo.District = userGeographyPosition.District
		locationInfo.Street = userGeographyPosition.Street
		return locationInfo
	}
	return nil
}

// getLocation 获取用户位置信息
func getLocation(uid int64, lat, lng, ip string) *LocationInfo {
	zero := "0.0"
	if (lat != "0" && lng != "0") && (lat != zero && lng != zero) && (lat != "" || lng != "") {
		if locationInfo, err := getLocationFromBaidu(lat, lng); err == nil {
			return locationInfo
		}
	}

	if locationInfo := getLocationByIP(uid, ip); locationInfo != nil {
		return locationInfo
	}
	return nil
}

// getLocationCacheKey 获取某个城市位置缓存key
func getLocationCacheKey(province, city string, needReal int32) string {
	cacheKeyStr := fmt.Sprintf("new_region-%s-%s-%d", province, city, needReal)
	cacheKey := crypto.Byte2Md5([]byte(cacheKeyStr))

	return fmt.Sprintf("product_pre_memcache%s", cacheKey)
}

// 获取城市代码缓存key
func getRegionCacheKey() string {
	regionCacheKeyStr := "new_all_region_list"
	regionCacheKey := crypto.Byte2Md5([]byte(regionCacheKeyStr))

	return fmt.Sprintf("product_pre_memcache%s", regionCacheKey)
}

// getDeviceLocationCacheKey 设备对应位置信息缓存key
func getDeviceLocationCacheKey(deviceID string) string {
	deviceLocationCacheKeyStr := fmt.Sprintf("user:area:by:deviceId:%s", deviceID)
	return deviceLocationCacheKeyStr
}

// getLocationCode 获取城市对应的编码
func getLocationCode(province, city string) *DeviceLocationInfo {
	regionCacheKey := getRegionCacheKey()
	var provinceList []ProvinceInfo
	deviceLocationInfo := &DeviceLocationInfo{}

	value, err := cache.GetMemcached().Get(regionCacheKey)
	logger.Infof("location: %s err: %s", regionCacheKey, err)
	if err == nil || value != "" {
		if err = json.Unmarshal([]byte(value), &provinceList); err != nil {
			logger.Warnf("获取城市代码缓存失败")
		}

		for _, item := range provinceList {
			if strings.Contains(item.Pname, province) {
				deviceLocationInfo.Province, _ = strconv.ParseInt(item.PID, 10, 64)
				if len(item.CityList) == 0 {
					deviceLocationInfo.City = 0
					break
				}

				for _, cityInfo := range item.CityList {
					if strings.Contains(cityInfo.Cname, city) {
						deviceLocationInfo.City, _ = strconv.ParseInt(cityInfo.Cid, 10, 64)
						break
					}
				}

				break
			}
		}
	} else {
		// 在620项目reportGeoInfo会更新城市代码json文件
		logger.Warnf("无法获取城市位置代码")
	}

	return deviceLocationInfo
}

// isUserChangeLocation 判断用户最近位置是否发生变化
func isUserChangeLocation(userLocationInfo *location.UserGeographyPosition, locationInfo *LocationInfo) bool {
	if userLocationInfo != nil &&
		userLocationInfo.Country == locationInfo.Country &&
		userLocationInfo.Province == locationInfo.Province &&
		userLocationInfo.City == locationInfo.City &&
		userLocationInfo.District == locationInfo.District {
		return false
	}

	return true
}

// updateAddressCode 更新设备地理位置和用户微信
func updateAddressCode(province, city, deviceID string, needReal int32) {
	deviceLocationInfo := &DeviceLocationInfo{
		Province: 2,
		City:     52,
	}
	locationCacheKey := getLocationCacheKey(province, city, needReal)
	deviceLocationKey := getDeviceLocationCacheKey(deviceID)

	value, err := cache.GetMemcached().Get(locationCacheKey)
	if err != nil || value == "" {
		if needReal == 1 {
			deviceLocationInfo.Province = 0
			deviceLocationInfo.City = 0
		}

		if province != "" || city != "" {
			deviceLocationInfo = getLocationCode(province, city)
		}

		deviceLocationStr, err := json.Marshal(deviceLocationInfo)
		if err == nil {
			_ = cache.GetMemcached().SetEx(locationCacheKey, string(deviceLocationStr), 86400)
		} else {
			logger.Warnf("位置格式化失败 err: %s", err)
		}
	} else if err := json.Unmarshal([]byte(value), &deviceLocationInfo); err != nil {
		logger.Warnf("设备位置解析失败 err: %s", err)
	}

	deviceLocationStr, err := json.Marshal(deviceLocationInfo)
	if err == nil && deviceLocationInfo.Province > 0 && deviceLocationInfo.City > 0 {
		exp := 3 * 24 * time.Hour
		if err := cache.GetYogaRedis().
			SetEX(context.Background(), deviceLocationKey, string(deviceLocationStr), exp).Err(); err != nil {
			logger.Error(err)
		}
	}
}

// updateUserLocation 更新用户位置信息
func updateUserLocation(uid int64, ip, idfa string, locationInfo *LocationInfo) {
	accountInfoExtra, err := user.TbAccountInfoExtra.GetItem(uid)
	if err != nil {
		return
	}

	address := fmt.Sprintf("%s%s%s", locationInfo.District, locationInfo.Street, locationInfo.StreetNumber)
	if accountInfoExtra != nil {
		accountInfoExtra.LastIP = ip
		accountInfoExtra.Province = locationInfo.Province
		accountInfoExtra.City = locationInfo.City
		accountInfoExtra.Address = address

		if idfa != "" {
			accountInfoExtra.Idfa = idfa
		}
		_ = accountInfoExtra.Update()
	} else {
		accountInfoExtra := &user.AccountInfoExtra{
			UID:      uid,
			LastIP:   ip,
			Province: locationInfo.Province,
			City:     locationInfo.City,
			Address:  address,
		}
		if idfa != "" {
			accountInfoExtra.Idfa = idfa
		}

		_ = accountInfoExtra.Save()
	}
}

func ProcessLocation(data *ReportLocationPayload) error {
	go func(data *ReportLocationPayload) {
		handleLocation(data)
	}(data)
	return nil
}

// 增加异步
func handleLocation(data *ReportLocationPayload) {
	// 查询用户位置信息
	locationInfo := getLocation(data.UID, data.Lat, data.Lng, data.IP)
	if locationInfo == nil {
		return
	}

	// 获取用户已有的位置上报
	userLastLocation, err := location.TbUserGeoggraphyPosition.GetLastItemByType(data.UID, locationInfo.Type)
	if err != nil {
		return
	}

	// 判断用户位置是否变更
	hasChangeLocation := isUserChangeLocation(userLastLocation, locationInfo)
	if hasChangeLocation {
		userLocation := &location.UserGeographyPosition{
			UID:         data.UID,
			IP:          data.IP,
			Lat:         data.Lat,
			Lon:         data.Lng,
			CountryCode: locationInfo.CountryCode,
			Country:     locationInfo.Country,
			Province:    locationInfo.Province,
			City:        locationInfo.City,
			District:    locationInfo.District,
			Township:    locationInfo.Township,
			Street:      locationInfo.Street,
			Address:     locationInfo.Address,
			Type:        locationInfo.Type,
		}
		if err := userLocation.Save(); err != nil {
			logger.Warnf("插入用户微信信息失败 err: %s", err)
		}
	}

	// 更新设备位置信息
	updateAddressCode(locationInfo.Province, locationInfo.City, data.DeviceID, 1)
	// 更新用户的位置信息
	updateUserLocation(data.UID, data.IP, data.IDFA, locationInfo)
	// 友钱api
	if data.IDFA != "" {
		updateCooperationInfo(data.IDFA)
	}
}

func updateCooperationInfo(idfa string) {
	// 根据 idfa 获取任务是否存在
	cooperation := top.TbCooperation.GetCooperation(idfa)
	if cooperation == nil || cooperation.CallbackURL == "" {
		return
	}
	// 请求第三方接口
	values := make(url.Values)
	values.Add("idfa", idfa)
	response, err := service.HTTP.PostForm(context.Background(), cooperation.CallbackURL, values)
	defer func() {
		_ = response.Body.Close()
	}()
	if err != nil {
		logger.Error("get CooperationInfo request", err)
		return
	}
	if response.StatusCode == 200 {
		cooperation.Idfa = idfa
		cooperation.Status = 1
		cooperation.UpdateTimes++
		if err := cooperation.Update(); err != nil {
			logger.Warnf("更新 cooperation 失败 id: %d, err: %s", cooperation.ID, err)
		}
	}
}
