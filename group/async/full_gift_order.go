package async

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/order"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type FullGiftOrderPayload struct {
	UID         int64  `json:"uid"`
	OrderID     string `json:"order_id"`
	Type        int64  `json:"type"`
	ProductName string `json:"product_name"`
	ProductID   string `json:"product_id"`
	TrueMoney   int64  `json:"true_money"` // 单位分
}

type orderData struct {
	UID             int64
	OrderID         string
	OrderType       int64
	ProductName     string
	ProductID       string
	Status          int64
	CreateOrderTime int64
	PayOrderTime    int64
	CreateTime      int64
	UpdateTime      int64
	TrueMoney       int64
}

// type 1 会员（若是pp的订单号【特惠套餐】需要拆分渠道号，只录入app的渠道号）
// 2 训练营  4 组合商品（需要根据配置再进行拆单） 8 kol相关   6 电商  12 ryt
func FullGiftOrder(data FullGiftOrderPayload) error {
	// @text 根据订单id查询主表支付状态，uid下单时间以及支付时间。
	orderInfo := order.TbOrderMain.GetByOrderID(data.OrderID)
	if orderInfo == nil {
		logger.Errorf("订单拆单同步，订单id未找到对应订单：%s", data.OrderID)
		return nil
	}

	orderData := &orderData{
		data.UID,
		data.OrderID,
		data.Type,
		data.ProductName,
		data.ProductID,
		1,
		orderInfo.CreateTime,
		orderInfo.UpdateTime,
		orderInfo.CreateTime,
		orderInfo.CreateTime,
		data.TrueMoney,
	}

	// @text 若是组合商品，需要按照配置进行拆单
	if data.Type == library.OrderTypeWebProduct {
		return data.splitOrder(orderData)
	}
	InsertOrder(orderData)
	return nil
}

// 2020-07-20 water 拆除逻辑
func (d *FullGiftOrderPayload) splitOrder(orderData *orderData) error {
	webOrder, err := order.TbWebOrder.GetByOrderID(d.OrderID)
	if err != nil {
		logger.Error("会员订单查询, 订单id未找到对应订单: %d", d.OrderID)
		return nil
	}

	combinationProduct := order.TbWebProductMainToProduct.GetByWebProductMainID(webOrder.ProductID)
	if len(combinationProduct) == 0 {
		logger.Error("组合商品订单查询, 订单id未找到对应订单: %d", d.OrderID)
		return nil
	}

	// @text 生成三条子订单数据(经测试发现：凡事组合商品，必有会员，否则神策上报和拆单均会存在问题，配置人员需要注意！！！)
	for _, item := range combinationProduct {
		orderData.ProductID = fmt.Sprintf("%d", item.WebProductSourceID)

		switch item.WebProductSourceType {
		case library.WebProductVip:
			orderData.OrderType = library.OrderTypeVip
			orderData.ProductName = "组合商品拆单(会员)--" + fmt.Sprintf("%d", item.WebProductSourceID)
			orderData.TrueMoney = int64(item.WebProductDividedPrice * 100)
			// 会员
			InsertOrder(orderData)
		case library.WebProductMall:
			// 电商（不包含有赞）
			orderData.OrderType = library.OrderTypeMall
			orderData.ProductName = "组合商品拆单(电商)--" + fmt.Sprintf("%d", item.WebProductSourceID)
			orderData.TrueMoney = int64(item.WebProductDividedPrice * 100)
			InsertOrder(orderData)
		case library.WebProductO2:
			// o2训练营
			orderData.OrderType = library.OrderTypeCamp
			orderData.ProductName = "组合商品拆单(训练营)--" + fmt.Sprintf("%d", item.WebProductSourceID)
			orderData.TrueMoney = int64(item.WebProductDividedPrice * 100)
			InsertOrder(orderData)
		case library.WebProductChoose:
		}
	}
	return nil
}

func InsertOrder(orderData *orderData) {
	fo := &order.FullGiftBaseOrder{
		UID:             orderData.UID,
		OrderID:         orderData.OrderID,
		OrderType:       orderData.OrderType,
		ProductName:     orderData.ProductName,
		ProductID:       orderData.ProductID,
		Status:          orderData.Status,
		CreateOrderTime: orderData.CreateOrderTime,
		PayOrderTime:    orderData.PayOrderTime,
		CreateTime:      orderData.CreateTime,
		UpdateTime:      orderData.UpdateTime,
		TrueMoney:       orderData.TrueMoney,
	}
	if err := fo.InsertNew(); err != nil {
		logger.Error(err)
	}
}
