package async

import (
	"encoding/json"
	"errors"
	"net/url"
	"strconv"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
)

type UseGiftPayload struct {
	UID    int64 `json:"uid"`
	GiftID int64 `json:"gift_id"`
}

type yogaResponse struct {
	Errno   int           `json:"errno"`
	ErrMsg  string        `json:"errmsg"`
	Data    []interface{} `json:"data"`
	CtxBack []interface{} `json:"_ctx_back"`
}

func SendUseGift(data UseGiftPayload) error {
	var ygRes yogaResponse
	values := make(url.Values)
	values.Add("uid", strconv.FormatInt(data.UID, 10))
	values.Add("gift_id", strconv.FormatInt(data.GiftID, 10))

	resp, err := requests.NewYogaClient(config.Get().PhpYogaAddress).PostForm("coupon/gift/usegift", values)
	if err != nil {
		return err
	}
	if !resp.IsOK() || resp.Body == "" {
		return errors.New("分发大礼包返回数据出错")
	}
	err = json.Unmarshal(resp.GetBodyByte(), &ygRes)
	if err != nil {
		logger.Error("返回数据不能解析！", resp.Body)
		return nil
	}

	if ygRes.Errno != 0 {
		logger.Error("分发大礼包接口信息返回失败", ygRes)
	}
	return nil
}
