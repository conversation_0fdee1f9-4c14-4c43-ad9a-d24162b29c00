package yaga

import (
	"context"
	"encoding/json"
	"strconv"

	"gitlab.dailyyoga.com.cn/protogen/pay-bridge-go/paybridge"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/contract"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/equity"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/order"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	srvo "gitlab.dailyyoga.com.cn/server/srv-task/service/order"
)

type PaymentAmountCheckParam struct {
	Amount  int64  `json:"amount"`
	OrderID string `json:"order_id"`
	Scene   int    `json:"scene"`
}

type RespCheckSign struct {
	Version string `json:"version"`
	Msg     string `json:"msg"`
	Type    string `json:"type"`
}

// nolint
func GetMchIDByOrderDY(orderItem *order.Main) (int, string) {
	payType, ok := library.PaymentTypeToRefundSource[int(orderItem.PaymentCode)]
	if !ok {
		payType = library.Other
	}
	if payType == library.Other {
		payType = checkScanCodePay(orderItem.OrderID)
	}
	switch payType {
	case library.PayTypeAlipay:
		item := order.TbOrderByMchid.GetMchidByOrderID(orderItem.OrderID)
		if item != nil {
			return payType, item.MchID
		}
		charge := order.TbAlipayCharge.GetItemByOrderID(orderItem.OrderID)
		if charge != nil {
			if contractItem := order.TbAlipayContract.GetValidChargeByCode(charge.ContractCode); contractItem != nil {
				return payType, contractItem.AppID
			}
		}
		return payType, "2016012701123319"
	case library.PayTypeWechat:
		machID := library.WXMchIDMain
		if library.IntInArray(int(orderItem.PaymentCode), []int{
			library.PaymentTvQrcode,
			library.PaymentIosWebWeixinJsapi,
			library.PaymentAndroidWebWeixinJsapi,
			library.PaymentAndroidWebWeixinH5,
		}) {
			machID = library.WXMchIDOther
		}
		return payType, machID
	case library.DYPayTypeTikTok:
		machID := library.TikTokMchID
		mch := order.TbOrderByMchid.GetMchidByOrderID(orderItem.OrderID)
		if mch != nil {
			machID = mch.MchID
		}
		return payType, machID
	default:
		return payType, ""
	}
}

func checkScanCodePay(orderID string) int {
	if ali := order.TbYogaO2AlipayCallback.GetByOrderID(orderID); ali != nil {
		return library.PayTypeAlipay
	}
	if wx := order.TbYogaO2WeixinpayCallback.GetByOrderID(orderID); wx != nil {
		return library.PayTypeWechat
	}
	compReqLogItem := order.TbCompReqLog.GetItem(orderID)
	if compReqLogItem == nil {
		return library.Other
	}
	compReq := struct {
		MchID string `json:"mch_id"`
		AppID string `json:"app_id"`
	}{}
	if err := json.Unmarshal([]byte(compReqLogItem.Request), &compReq); err != nil {
		logger.Error(err)
		return library.Other
	}
	if compReq.MchID != "" {
		return library.PayTypeWechat
	}
	if compReq.AppID != "" {
		return library.PayTypeAlipay
	}
	return library.Other
}

func GetDurationType(productID int64, paymentOrderType int, orderID string) int {
	res := getProductDuration(productID, paymentOrderType)
	productType := 0
	if res.DurationType == 0 || res.DurationValue == 0 {
		logger.Info("PaymentAmountCheck", "getDurationType", productID, paymentOrderType, orderID)
		return productType
	}
	logger.Info("PaymentAmountCheck", "getDurationType", res.DurationType, res.DurationValue, orderID)
	switch res.DurationType {
	case library.ConstWebProductDurationTypeDay:
		if res.DurationValue < library.ConstMonthDays {
			return library.ConstStepProductTypeMonth
		}
		productType = library.ConstStepProductTypeMonth
	case library.ConstWebProductDurationTypeMonth:
		productType = library.ConstStepProductTypeMonth
		if res.DurationValue == library.ConstMonthThree {
			productType = library.ConstStepProductTypeQuarter
		}
		if res.DurationValue == library.ConstMonthSix {
			productType = library.ConstStepProductTypeHalfYear
		}
		if res.DurationValue >= library.ConstMonthTwelve {
			productType = library.ConstStepProductTypeYear
		}
	case library.ConstWebProductDurationTypeYear:
		productType = library.ConstStepProductTypeYear
	default:
		return productType
	}
	return productType
}

func getProductDuration(productID int64, paymentOrderType int) ResEquityProductDur {
	res := ResEquityProductDur{}
	switch paymentOrderType {
	case library.ConstPaymentOrderTypeVip:
		productItem := order.TbWebProduct.GetByID(productID)
		if productItem != nil {
			res.DurationType = productItem.DurationType
			res.DurationValue = productItem.DurationValue
		}
	case library.ConstPaymentOrderTypePreferentialPackage:
		res = getEquityProductDur(productID)
	default:
		return res
	}
	return res
}

type ResEquityProductDur struct {
	DurationType  int `json:"duration_type"`
	DurationValue int `json:"duration_value"`
}

func getEquityProductDur(productID int64) ResEquityProductDur {
	res := ResEquityProductDur{}
	packageDetail, err := equity.TbWebPreferentialPackageDetail.GetList(productID)
	if err != nil {
		return ResEquityProductDur{}
	}
	if len(packageDetail) == 0 {
		return res
	}
	product := packageDetail[0]
	if product.EquityType == 0 {
		entity := order.TbWebProduct.GetByID(int64(product.EquityData))
		if entity == nil {
			return res
		}
		res.DurationType = entity.DurationType
		res.DurationValue = entity.DurationValue
		return res
	}
	entity := equity.TbWekEquityProduct.GetItem(int64(product.EquityData))
	if entity == nil {
		return res
	}
	res.DurationType = int(entity.EquityDurationType)
	res.DurationValue = int(entity.EquityDurationValue)
	return res
}

type ProductItemTmp struct {
	OfferType          int
	Price              float64
	IsSubscribe        int
	ProductType        int
	ProductID          int64
	OriginPrice        float64
	OfferFirstBuyPrice float64
	OfferTrialPrice    float64
	FirstBuyPrice      float64
}

func GetProductItem(orderInfo *order.Main) *ProductItemTmp {
	switch orderInfo.OrderType {
	case library.OrderIndexTypeWebOrder, library.OrderTypeWebProductMain:
		item, err := order.TbWebOrder.GetByOrderID(orderInfo.OrderID)
		if err != nil || item == nil {
			logger.Error("getWebOrder", err)
			return nil
		}
		switch int(item.OrderType) {
		case library.WebOrderTypeVipCommon, library.WebOrderTypeVipAdvanced, library.WebOrderTypeGvipAdvanced:
			productItem := order.TbWebProduct.GetByID(item.ProductID)
			if productItem == nil {
				logger.Error("getWebOrder", "productItem is nil", item.ProductID)
				return nil
			}
			price, err := strconv.ParseFloat(productItem.Price, 64)
			if err != nil {
				logger.Error("getWebOrder", err)
				return nil
			}
			purchaseType := library.No
			if productItem.PurchaseType == library.No {
				purchaseType = library.Yes
			}
			if productItem.OfferType == 0 {
				productItem.OfferType = library.ConstOfferTypeNo
			}
			return &ProductItemTmp{
				OfferType:          int(productItem.OfferType),
				Price:              price,
				IsSubscribe:        purchaseType,
				ProductID:          item.ProductID,
				OriginPrice:        price,
				OfferFirstBuyPrice: productItem.OfferFirstBuyPrice,
				OfferTrialPrice:    productItem.OfferTrialPrice,
				FirstBuyPrice:      float64(productItem.FirstBuyPrice),
			}
		case library.WebOrderTypeGroupCommon, library.WebOrderTypeGroupAdvanced, library.WebOrderTypeGroup:
			productItem := order.TbWebProductMain.GetItem(item.ProductID)
			if productItem == nil {
				logger.Error("getWebOrder", "productItem is nil", item.ProductID)
				return nil
			}
			price, err := strconv.ParseFloat(productItem.Price, 64)
			if err != nil {
				logger.Error("getWebOrder", err)
				return nil
			}
			return &ProductItemTmp{
				OfferType:   library.ConstOfferTypeNo,
				Price:       price,
				IsSubscribe: library.No,
				ProductID:   item.ProductID,
				OriginPrice: price,
			}
		}
	case library.OrderTypePreferentialPackage:
		packageItem := equity.TbWebPreferentialPackage.GetItem(orderInfo.OrderIndexID)
		if packageItem == nil {
			return nil
		}
		purchaseType := library.No
		if packageItem.PurchaseType == library.No {
			purchaseType = library.Yes
		}
		if packageItem.OfferType == 0 {
			packageItem.OfferType = library.ConstOfferTypeNo
		}
		return &ProductItemTmp{
			OfferType:          int(packageItem.OfferType),
			Price:              float64(packageItem.Price) / 100,
			IsSubscribe:        purchaseType,
			ProductID:          orderInfo.OrderIndexID,
			OriginPrice:        float64(packageItem.Price) / 100,
			OfferFirstBuyPrice: packageItem.OfferFirstBuyPrice,
			OfferTrialPrice:    packageItem.OfferTrialPrice,
			FirstBuyPrice:      float64(packageItem.FirstPrice) / 100,
		}
	}
	return nil
}

// nolint
func HandleDYOrder(req *PaymentAmountCheckParam) {
	logger.Info("HandleDYOrder", "开始处理订单", req.OrderID)
	orderInfo := order.TbOrderMain.GetByOrderID(req.OrderID)
	if orderInfo == nil {
		return
	}
	if orderInfo.OrderType != library.ConstPaymentOrderTypeVip &&
		orderInfo.OrderType != library.ConstPaymentOrderTypePreferentialPackage {
		return
	}
	payType, mchID := GetMchIDByOrderDY(orderInfo)
	if mchID == "" {
		return
	}
	if payType == library.PayTypeApple || payType == library.Other {
		return
	}
	if req.Scene != library.Yes && req.Amount == 0 {
		return
	}
	amount := float64(req.Amount) / 100.0
	scene := "下单告警"
	signPayCallback := false
	if req.Scene == library.Yes && orderInfo.OrderAmountTotal != 0 {
		scene = "巡检告警"
		if payType == library.DYPayTypeTikTok {
			if orderInfo.OrderType == library.ConstPaymentOrderTypeVip {
				productItem := order.TbTiktokCharge.GetItemByOrderID(orderInfo.OrderID)
				if productItem != nil {
					signPayCallback = true
				}
			}
			if orderInfo.OrderType == library.ConstPaymentOrderTypePreferentialPackage {
				productItem := order.TbPreferentialTiktokCharge.GetByOrderID(orderInfo.OrderID)
				if productItem != nil {
					signPayCallback = true
				}
			}
		}
		payBridge := grpc.GetPayBridgeClient()
		rsp, err := payBridge.QueryOrderInfo(context.Background(), &paybridge.OrderInfoReq{
			AppID:             mchID,
			MchID:             mchID,
			PayType:           int32(payType),
			OutTradeNo:        orderInfo.OrderID,
			IsSignPayCallback: signPayCallback,
		})
		logger.Info("PaymentAmountCheck", "查询订单信息", orderInfo.OrderID, payType, rsp, err)
		if rsp == nil || err != nil {
			logger.Info("PaymentAmountCheck", "查询订单信息失败", req.OrderID, err)
			return
		}
		if rsp.ErrorCode != 0 || rsp.Data == nil {
			logger.Info("PaymentAmountCheck", "查询订单信息失败", req.OrderID, rsp.ErrorCode, rsp.Msg)
			return
		}
		amount = float64(rsp.Data.TotalAmount) / 100.0
	}
	logger.Info("PaymentAmountCheck", "订单金额", orderInfo.OrderID, amount)
	productItem := GetProductItem(orderInfo)
	if productItem == nil {
		logger.Info("PaymentAmountCheck", "查询商品信息失败", req.OrderID)
		return
	}
	if productItem.FirstBuyPrice > 0 && productItem.FirstBuyPrice == orderInfo.OrderAmountTotal {
		logger.Info("PaymentAmountCheck", "FirstBuyPrice", productItem.FirstBuyPrice, orderInfo.OrderID)
		productItem.Price = productItem.FirstBuyPrice
	}
	durationType := GetDurationType(productItem.ProductID, orderInfo.OrderType, orderInfo.OrderID)
	productItem.ProductType = durationType
	logger.Info("PaymentAmountCheck", "OfferType", productItem.OfferType, req.OrderID, productItem.ProductID)
	stepAmount := 0.0
	alipayContractCharge := contract.TbWebOrderAlipayContractCharge.GetSuccessOrderByOrderID(orderInfo.OrderID)
	if alipayContractCharge != nil {
		stepAmount = alipayContractCharge.StepAmount
	}
	alipayContractChargePreferential := contract.TbPreferentialOrderAlipayContractCharge.GetSuccessOrderByOrderID(orderInfo.OrderID)
	if alipayContractChargePreferential != nil && alipayContractChargePreferential.StepAmount != 0 {
		stepAmount = alipayContractChargePreferential.StepAmount
	}
	if productItem.OfferType == library.ConstOfferTypeNo {
		if stepAmount > 0 {
			LowPriceAlarmStepAmount(orderInfo, productItem, amount, stepAmount)
		} else {
			LowPriceAlarm(orderInfo, productItem, amount, 0)
		}
	}
	mainTotal := order.TbYogaO2Total.GetMainTotalByOrderID(orderInfo.OrderID)
	if mainTotal != nil {
		voucherAmount, err := strconv.ParseFloat(mainTotal.Value, 64)
		if err != nil {
			logger.Error("PaymentAmountCheck", "ParseFloat", err)
		}
		if voucherAmount < 0 {
			voucherAmount = voucherAmount * (-1)
		}
		if productItem.Price == amount+voucherAmount {
			productItem.Price = amount
			logger.Info("PaymentAmountCheck", "voucherAmount", voucherAmount, amount, orderInfo.OrderID)
		}
		logger.Info("PaymentAmountCheck", "voucherAmount", voucherAmount, amount, orderInfo.OrderID)
	}
	if amount == orderInfo.OrderAmountTotal && productItem.Price == amount {
		return
	}
	if productItem.IsSubscribe != library.Yes || payType != library.PayTypeAlipay {
		logger.Info("PaymentAmountCheck", "告警-1", orderInfo.OrderID)
		// 告警
		srvo.PaymentAmountCheck(&srvo.PaymentAmountCheckRequest{
			ProjectName:   library.ProjectTypeEnumDesc[library.ProjectTypeEnum.YoGa],
			UID:           orderInfo.UID,
			ProductID:     productItem.ProductID,
			ProductType:   library.ProductTypeYoGaDesc[durationType],
			ProductAmount: productItem.Price,
			OrderAmount:   amount,
			OrderID:       orderInfo.OrderID,
			Scene:         scene,
		})
		return
	}
	if orderInfo.OrderType == library.ConstPaymentOrderTypeVip {
		HandleDYOrderVip(orderInfo, productItem, amount, scene)
	}
	if orderInfo.OrderType == library.ConstPaymentOrderTypePreferentialPackage {
		HandleDYOrderPreferentialPackage(orderInfo, productItem, amount, scene)
	}
}

// nolint
func HandleDYOrderVip(orderInfo *order.Main, productItem *ProductItemTmp, amount float64, scene string) {
	alipayContractCharge := contract.TbWebOrderAlipayContractCharge.GetSuccessOrderByOrderID(orderInfo.OrderID)
	stepAmount := 0.0
	if alipayContractCharge != nil {
		stepAmount = alipayContractCharge.StepAmount
	}
	logger.Infof("PaymentAmountCheck 订单ID: %s, 订单金额: %f, 产品金额: %f, 优惠金额: %f, 账单金额: %f",
		orderInfo.OrderID, orderInfo.OrderAmountTotal, productItem.Price, stepAmount, amount)
	if amount == orderInfo.OrderAmountTotal && productItem.Price-stepAmount == amount {
		return
	}
	if productItem.OfferType == library.ConstOfferTypeNo || productItem.OfferType == 0 {
		// 告警
		logger.Info("PaymentAmountCheck", "告警-2", orderInfo.OrderID)
		srvo.PaymentAmountCheck(&srvo.PaymentAmountCheckRequest{
			ProjectName:   library.ProjectTypeEnumDesc[library.ProjectTypeEnum.YoGa],
			UID:           orderInfo.UID,
			ProductID:     productItem.ProductID,
			ProductType:   library.ProductTypeDesc[productItem.ProductType],
			ProductAmount: productItem.Price - stepAmount,
			OrderAmount:   amount,
			OrderID:       orderInfo.OrderID,
			Scene:         scene,
		})
		return
	}
	contracnNum := 0
	if alipayContractCharge != nil {
		contractList := contract.TbWebOrderAlipayContractCharge.GetSuccessOrderByContractCode(alipayContractCharge.ContractCode)
		contracnNum = len(contractList)
	}
	if alipayContractCharge != nil && contracnNum < 2 {
		price := CalculateOffer(productItem)
		if amount == orderInfo.OrderAmountTotal && price == amount {
			return
		}
	}
	contractItemByOrderID := contract.TbWebOrderAlipayContract.GetItemByOrderID(orderInfo.OrderID)
	logger.Info("PaymentAmountCheck", "contractItemByOrderID", contractItemByOrderID, orderInfo.OrderID)
	if alipayContractCharge == nil && contractItemByOrderID != nil {
		logger.Info("PaymentAmountCheck", "contractItemByOrderID====", contractItemByOrderID)
		alipayContractCharge = &contract.WebOrderAlipayContractCharge{
			ContractCode: contractItemByOrderID.ContractCode,
		}
	}
	if alipayContractCharge == nil {
		logger.Info("PaymentAmountCheck", "告警-3", orderInfo.OrderID)
		srvo.PaymentAmountCheck(&srvo.PaymentAmountCheckRequest{
			ProjectName:   library.ProjectTypeEnumDesc[library.ProjectTypeEnum.YoGa],
			UID:           orderInfo.UID,
			ProductID:     productItem.ProductID,
			ProductType:   library.ProductTypeDesc[productItem.ProductType],
			ProductAmount: productItem.Price - stepAmount,
			OrderAmount:   amount,
			OrderID:       orderInfo.OrderID,
			Scene:         scene,
		})
		return
	}
	contractItem := contract.TbWebOrderAlipayContract.GetItemByContractCode(alipayContractCharge.ContractCode)
	// 首购优惠
	FormatOffer(contractItem, contracnNum > 1, productItem, alipayContractCharge)
	logger.Info("PaymentAmountCheck", "FormatOffer=1", productItem.Price,
		amount == orderInfo.OrderAmountTotal, productItem.Price-stepAmount == amount)
	if amount == orderInfo.OrderAmountTotal && productItem.Price-stepAmount == amount {
		return
	}
	// 告警
	logger.Info("PaymentAmountCheck", "告警-4", orderInfo.OrderID)
	srvo.PaymentAmountCheck(&srvo.PaymentAmountCheckRequest{
		ProjectName:   library.ProjectTypeEnumDesc[library.ProjectTypeEnum.YoGa],
		UID:           orderInfo.UID,
		ProductID:     productItem.ProductID,
		ProductType:   library.ProductTypeDesc[productItem.ProductType],
		ProductAmount: productItem.Price - stepAmount,
		OrderAmount:   amount,
		OrderID:       orderInfo.OrderID,
		Scene:         scene,
	})
}

// nolint
func HandleDYOrderPreferentialPackage(orderInfo *order.Main, productItem *ProductItemTmp, amount float64, scene string) {
	alipayContractCharge := contract.TbPreferentialOrderAlipayContractCharge.GetSuccessOrderByOrderID(orderInfo.OrderID)
	stepAmount := 0.0
	if alipayContractCharge != nil {
		stepAmount = alipayContractCharge.StepAmount
	}
	logger.Infof("PaymentAmountCheck 订单ID: %s, 订单金额: %f, 产品金额: %f, 优惠金额: %f, 账单金额: %f",
		orderInfo.OrderID, orderInfo.OrderAmountTotal, productItem.Price, stepAmount, amount)
	if amount == orderInfo.OrderAmountTotal && productItem.Price-stepAmount == amount {
		return
	}
	if productItem.OfferType == library.ConstOfferTypeNo || productItem.OfferType == 0 {
		// 告警
		logger.Info("PaymentAmountCheck", "告警-2", orderInfo.OrderID)
		srvo.PaymentAmountCheck(&srvo.PaymentAmountCheckRequest{
			ProjectName:   library.ProjectTypeEnumDesc[library.ProjectTypeEnum.YoGa],
			UID:           orderInfo.UID,
			ProductID:     productItem.ProductID,
			ProductType:   library.ProductTypeDesc[productItem.ProductType],
			ProductAmount: productItem.Price - stepAmount,
			OrderAmount:   amount,
			OrderID:       orderInfo.OrderID,
			Scene:         scene,
		})
		return
	}
	contracnNum := 0
	if alipayContractCharge != nil {
		contractList := contract.TbPreferentialOrderAlipayContractCharge.GetSuccessOrderByContractCode(alipayContractCharge.ContractCode)
		contracnNum = len(contractList)
	}
	if alipayContractCharge != nil && contracnNum < 2 {
		price := CalculateOffer(productItem)
		logger.Info("PaymentAmountCheck", "CalculateOffer", price, amount, orderInfo.OrderAmountTotal, orderInfo.OrderID)
		if amount == orderInfo.OrderAmountTotal && price == amount {
			return
		}
	}
	contractItemByOrderID := contract.TbWebOrderAlipayContract.GetItemByOrderID(orderInfo.OrderID)
	if alipayContractCharge == nil && contractItemByOrderID != nil {
		alipayContractCharge = &contract.PreferentialAlipayContractCharge{
			ContractCode: contractItemByOrderID.ContractCode,
		}
	}
	if alipayContractCharge == nil {
		logger.Info("PaymentAmountCheck", "告警-3", orderInfo.OrderID)
		srvo.PaymentAmountCheck(&srvo.PaymentAmountCheckRequest{
			ProjectName:   library.ProjectTypeEnumDesc[library.ProjectTypeEnum.YoGa],
			UID:           orderInfo.UID,
			ProductID:     productItem.ProductID,
			ProductType:   library.ProductTypeDesc[productItem.ProductType],
			ProductAmount: productItem.Price - stepAmount,
			OrderAmount:   amount,
			OrderID:       orderInfo.OrderID,
			Scene:         scene,
		})
		return
	}
	contractItem := contract.TbWebOrderAlipayContract.GetItemByContractCode(alipayContractCharge.ContractCode)
	// 首购优惠
	FormatOfferPreferentialPackage(contractItem, contracnNum > 1, productItem, alipayContractCharge)
	logger.Info("PaymentAmountCheck", "FormatOffer=1", productItem.Price,
		amount == orderInfo.OrderAmountTotal, productItem.Price-stepAmount == amount)
	if amount == orderInfo.OrderAmountTotal && productItem.Price-stepAmount == amount {
		return
	}
	// 告警
	logger.Info("PaymentAmountCheck", "告警-4", orderInfo.OrderID)
	srvo.PaymentAmountCheck(&srvo.PaymentAmountCheckRequest{
		ProjectName:   library.ProjectTypeEnumDesc[library.ProjectTypeEnum.YoGa],
		UID:           orderInfo.UID,
		ProductID:     productItem.ProductID,
		ProductType:   library.ProductTypeDesc[productItem.ProductType],
		ProductAmount: productItem.Price - stepAmount,
		OrderAmount:   amount,
		OrderID:       orderInfo.OrderID,
		Scene:         scene,
	})
}

func CalculateOffer(productItem *ProductItemTmp) float64 {
	switch productItem.OfferType {
	case library.ConstOfferTypeFirstBuy:
		// 针对首购价格大于原价，特殊处理
		if productItem.OfferFirstBuyPrice > productItem.OriginPrice {
			return productItem.OfferFirstBuyPrice
		}
		return productItem.OfferFirstBuyPrice
	case library.ConstOfferTypeTrial, library.ConstOfferTypeTrialFirstBuy:
		return productItem.OfferTrialPrice
	}
	return productItem.Price
}

// nolint
func FormatOffer(contractItme *contract.WebOrderAlipayContract, isRenew bool, productItem *ProductItemTmp,
	alipayContractCharge *contract.WebOrderAlipayContractCharge) {
	if contractItme == nil || contractItme.OfferType <= library.ConstOfferTypeNo {
		return
	}
	switch contractItme.OfferType {
	case library.ConstOfferTypeFirstBuy:
		list := contract.TbWebOrderAlipayContractCharge.GetValidChargeByCode(contractItme.ContractCode, int64(alipayContractCharge.ID))
		if len(list) < contractItme.OfferFirstBuyCycle {
			productItem.Price = contractItme.OfferFirstBuyPrice
		}
		logger.Info("PaymentAmountCheck", "FormatOffer", contractItme.OrderID, alipayContractCharge.ID,
			len(list), contractItme.OfferFirstBuyCycle, productItem.Price)
	case library.ConstOfferTypeTrial:
		if !isRenew {
			productItem.Price = contractItme.OfferTrialPrice
		}
	case library.ConstOfferTypeTrialFirstBuy:
		if !isRenew {
			productItem.Price = contractItme.OfferTrialPrice
		} else {
			list := contract.TbWebOrderAlipayContractCharge.GetValidChargeByCode(contractItme.ContractCode, int64(alipayContractCharge.ID))
			if len(list) < contractItme.OfferFirstBuyCycle {
				productItem.Price = contractItme.OfferFirstBuyPrice
			}
		}
	}
}

// nolint
func FormatOfferPreferentialPackage(contractItme *contract.WebOrderAlipayContract, isRenew bool, productItem *ProductItemTmp,
	alipayContractCharge *contract.PreferentialAlipayContractCharge) {
	if contractItme == nil || contractItme.OfferType <= library.ConstOfferTypeNo {
		return
	}
	switch contractItme.OfferType {
	case library.ConstOfferTypeFirstBuy:
		list := contract.TbPreferentialOrderAlipayContractCharge.GetValidChargeByCode(contractItme.ContractCode, int64(alipayContractCharge.ID))
		if len(list) < contractItme.OfferFirstBuyCycle {
			productItem.Price = contractItme.OfferFirstBuyPrice
		}
		logger.Info("PaymentAmountCheck", "FormatOffer", contractItme.OrderID, alipayContractCharge.ID,
			len(list), contractItme.OfferFirstBuyCycle, productItem.Price)
	case library.ConstOfferTypeTrial:
		if !isRenew {
			productItem.Price = contractItme.OfferTrialPrice
		}
	case library.ConstOfferTypeTrialFirstBuy:
		if !isRenew {
			productItem.Price = contractItme.OfferTrialPrice
		} else {
			list := contract.TbPreferentialOrderAlipayContractCharge.GetValidChargeByCode(contractItme.ContractCode, int64(alipayContractCharge.ID))
			if len(list) < contractItme.OfferFirstBuyCycle {
				productItem.Price = contractItme.OfferFirstBuyPrice
			}
		}
	}
}

// 低价告警
// nolint
func LowPriceAlarm(orderInfo *order.Main, productItem *ProductItemTmp, amount, stepAmount float64) {
	logger.Info("PaymentAmountCheck", "LowPriceAlarm", productItem.ProductType, amount, stepAmount, orderInfo.OrderID)
	switch productItem.ProductType {
	case library.ConstStepProductTypeMonth:
		if amount >= 19 {
			return
		}
	case library.ConstStepProductTypeQuarter:
		if amount >= 78 {
			return
		}
	case library.ConstStepProductTypeYear:
		if amount >= 88 {
			return
		}
	case library.ConstStepProductTypeHalfYear:
		if amount >= 88 {
			return
		}
	default:
		return
	}
	logger.Info("PaymentAmountCheck", "低价告警", orderInfo.OrderID)
	srvo.PaymentAmountCheck(&srvo.PaymentAmountCheckRequest{
		ProjectName:   library.ProjectTypeEnumDesc[library.ProjectTypeEnum.YoGa],
		UID:           orderInfo.UID,
		ProductID:     productItem.ProductID,
		ProductType:   library.ProductTypeYoGaDesc[productItem.ProductType],
		ProductAmount: productItem.Price - stepAmount,
		OrderAmount:   amount,
		OrderID:       orderInfo.OrderID,
		Scene:         "低价告警",
	})
}

func LowPriceAlarmStepAmount(orderInfo *order.Main, productItem *ProductItemTmp, amount, stepAmount float64) {
	logger.Info("PaymentAmountCheck", "LowPriceAlarm", productItem.ProductType, amount, stepAmount, orderInfo.OrderID)
	switch productItem.ProductType {
	case library.ConstStepProductTypeMonth:
		if amount >= 19 {
			return
		}
	case library.ConstStepProductTypeQuarter:
		if amount >= 60 {
			return
		}
	case library.ConstStepProductTypeYear:
		if amount >= 88 {
			return
		}
	case library.ConstStepProductTypeHalfYear:
		if amount >= 88 {
			return
		}
	default:
		return
	}
	logger.Info("PaymentAmountCheck", "低价告警", orderInfo.OrderID)
	srvo.PaymentAmountCheck(&srvo.PaymentAmountCheckRequest{
		ProjectName:   library.ProjectTypeEnumDesc[library.ProjectTypeEnum.YoGa],
		UID:           orderInfo.UID,
		ProductID:     productItem.ProductID,
		ProductType:   library.ProductTypeYoGaDesc[productItem.ProductType],
		ProductAmount: productItem.Price - stepAmount,
		OrderAmount:   amount,
		OrderID:       orderInfo.OrderID,
		Scene:         "低价告警",
	})
}
