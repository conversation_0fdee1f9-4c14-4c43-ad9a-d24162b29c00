package yq

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/server/srv-task/service/yq/practice"
)

var practiceType = struct {
	Default string
	Feel    string
}{
	Default: "yq_practice",
	Feel:    "yq_practice_feel",
}

type practicePayload struct {
	Type   string      `json:"type"`
	Params interface{} `json:"params"`
}

type PracticeProcessor struct{}

func (p *PracticeProcessor) Run(_, payload []byte) error {
	data := practicePayload{}
	if err := json.Unmarshal(payload, &data); err != nil {
		return err
	}
	var err error
	params, err := json.<PERSON>(data.Params)
	if err != nil {
		return err
	}
	switch data.Type {
	case practiceType.Default:
		// 练习数据新逻辑
		_ = practice.Log.StatPracticeLog(params)
	case practiceType.Feel:
		// 练习数据新逻辑
		_ = practice.Log.StatPracticeFeel(params)
	default:
		return nil
	}
	return nil
}
