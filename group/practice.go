package group

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/server/srv-task/service/crack"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/practice"
)

var practiceType = struct {
	Clearing      int
	Logging       int
	Day           int
	Feel          int
	Login         int
	Crack         int
	StartPractice int
}{
	Clearing:      1,
	Logging:       2,
	Day:           3,
	Feel:          4,
	Login:         5,
	Crack:         6,
	StartPractice: 7,
}

type practicePayload struct {
	Type   int         `json:"type"`
	Params interface{} `json:"params"`
}

type PracticeProcessor struct{}

func (p *PracticeProcessor) Run(_, payload []byte) error {
	data := practicePayload{}
	if err := json.Unmarshal(payload, &data); err != nil {
		return err
	}
	var err error
	params, err := json.Marshal(data.Params)
	if err != nil {
		return err
	}
	switch data.Type {
	case practiceType.Clearing:
		_ = practice.Log.CleanUserPracticeLog(params)
	case practiceType.Logging:
		// 练习数据新逻辑
		_ = practice.Log.StatPracticeLog(params)
	case practiceType.Day:
		// 更新用户练习天数
		var payload practice.DayPayload
		if err := json.Unmarshal(params, &payload); err != nil {
			return err
		}
		practice.Log.StatPracticeDay(&payload)
	case practiceType.Feel:
		// 练习感受
		var bean practice.FeelPracticePayload
		if err := json.Unmarshal(params, &bean); err != nil {
			return err
		}
		practice.Log.StatPracticeFeel(&bean)
	case practiceType.Login:
		// 首次异步计算用户登录历史数据完成后通知计算登录类徽章
		var loginPayload practice.LoginPayload
		if err := json.Unmarshal(params, &loginPayload); err != nil {
			return err
		}
		practice.Log.StatUserLogin(&loginPayload)
	case practiceType.Crack:
		// 破解版
		if err := crack.MCrack(params); err != nil {
			return err
		}
	case practiceType.StartPractice:
		// ActionLog 播放课程 播放计划中的课程
		var StartPracticePayload practice.StartPracticePayload
		if err := json.Unmarshal(params, &StartPracticePayload); err != nil {
			return err
		}
		practice.StPractice.StartPractice(&StartPracticePayload)
	default:
		return nil
	}
	return nil
}
