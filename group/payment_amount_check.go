package group

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/server/go-artifact/env"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	dforder "gitlab.dailyyoga.com.cn/server/srv-task/databases/df/order"
	ftorder "gitlab.dailyyoga.com.cn/server/srv-task/databases/ft/order"
	ygaorder "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/order"
	"gitlab.dailyyoga.com.cn/server/srv-task/group/df"
	"gitlab.dailyyoga.com.cn/server/srv-task/group/ft"
	"gitlab.dailyyoga.com.cn/server/srv-task/group/yaga"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
)

type PaymentAmountCheck struct{}

type PaymentAmountCheckParam struct {
	Amount  int64  `json:"amount"`
	OrderID string `json:"order_id"`
	Scene   int    `json:"scene"`
}

type PaymentAmountCheckPayload struct {
	Type   string                   `json:"type"`
	Params *PaymentAmountCheckParam `json:"params"`
}

func (p *PaymentAmountCheck) Run(_, payload []byte) error {
	logger.Info("PaymentAmountCheck", string(payload))
	var payloadData PaymentAmountCheckPayload
	if err := json.Unmarshal(payload, &payloadData); err != nil {
		logger.Error("PaymentAmountCheck", "解析参数失败", string(payload), err)
		return err
	}
	if payloadData.Params == nil {
		logger.Info("PaymentAmountCheck", "参数为空")
		return nil
	}
	param := payloadData.Params
	projectType := GetProjectTypeEnum(param.OrderID)
	logger.Info("PaymentAmountCheck", "订单项目", param.OrderID, projectType)
	switch projectType {
	case library.ProjectTypeEnum.YoGa:
		yaga.HandleDYOrder(&yaga.PaymentAmountCheckParam{
			Amount:  param.Amount,
			OrderID: param.OrderID,
			Scene:   param.Scene,
		})
	case library.ProjectTypeEnum.DanceFit:
		cfgEnv := config.Get().Env
		if cfgEnv != env.Product {
			return nil
		}
		df.HandleDFOrder(&df.PaymentAmountCheckParam{
			Amount:  param.Amount,
			OrderID: param.OrderID,
			Scene:   param.Scene,
		})
	case library.ProjectTypeEnum.Fitness:
		cfgEnv := config.Get().Env
		if cfgEnv != env.Product {
			return nil
		}
		ft.HandleFtOrder(&ft.PaymentAmountCheckParam{
			Amount:  param.Amount,
			OrderID: param.OrderID,
			Scene:   param.Scene,
		})
	default:
		logger.Info("PaymentAmountCheck", "未查询到订单", param.OrderID)
		return nil
	}
	return nil
}

func GetProjectTypeEnum(orderID string) library.ProjectType {
	projectType := YogaOrder(orderID)
	if projectType == library.ProjectTypeEnum.All {
		projectType = DfOrder(orderID)
	}
	if projectType == library.ProjectTypeEnum.All {
		projectType = FTOrder(orderID)
	}
	return projectType
}

func YogaOrder(orderID string) library.ProjectType {
	order := ygaorder.TbOrderMain.GetByOrderID(orderID)
	if order == nil {
		return library.ProjectTypeEnum.All
	}
	return library.ProjectTypeEnum.YoGa
}

func DfOrder(orderID string) library.ProjectType {
	order := dforder.TbWebOrder.GetItemByOrderID(orderID)
	if order == nil {
		return library.ProjectTypeEnum.All
	}
	return library.ProjectTypeEnum.DanceFit
}

func FTOrder(orderID string) library.ProjectType {
	order := ftorder.TbWebOrder.GetItemByOrderID(orderID)
	if order == nil {
		return library.ProjectTypeEnum.All
	}
	return library.ProjectTypeEnum.Fitness
}

func HandlYoGaOrder(req *PaymentAmountCheckParam) {

}

func HandleDFOrder(req *PaymentAmountCheckParam) {

}
