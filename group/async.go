package group

import (
	"encoding/json"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/group/async"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/order"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/partner"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/practice"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/topic"
)

var asyncPayloadType = struct {
	SetTeamPlayTimeRanking         string
	AdsTracking                    string
	RefreshRecommendPosts          string
	DuibaBannerClickReport         string
	StReport                       string
	NoticePush                     string
	DistributeLinkConvert          string
	VoucherBatchPush               string
	MediaConvertHLS                string
	UserWallet                     string
	MemcachedDataHandle            string
	TXSportsCharge                 string
	AsyncSendGift                  string
	SlicingMakeSession             string
	ChallengeTask                  string
	AsyncSendScrmCard              string
	ReportLocation                 string
	UserBadge                      string
	SendHXPartnerSystemMsg         string
	CreateOrder4IosIap             string
	NoteBook                       string
	AdsReport                      string
	LoginRegister                  string
	AgentVipSend                   string
	ManualSendPoint                string
	FullGiftOrder                  string
	SendYouzanCoupon               string
	UpdateUserLabel                string
	UpdateMiniUserLabel            string
	CalculatePostsNum              string
	SetUserIndexes                 string
	ClearLocalData                 string
	AsyncCompleteOrder             string
	AverageSendPoint               string
	OperateUserGrow                string
	SaveUserInfoForPush            string
	LogoutYZY                      string
	SendH2OrderFrom                string
	PostAutoVerify                 string
	UserSignInConfigUpdateIsRemind string
	InitPracticeCalendar           string
	AppleAdsInfo                   string
	BindDistinctID                 string
	EquityAllIdentity              string
	DfPractice                     string
	SensorsEventDyOrder            string
	CrackVersionUser               string
	LogOutOrder                    string
}{
	SetTeamPlayTimeRanking:         "set_team_play_time_ranking", // 设置训练营班级排行榜
	AdsTracking:                    "ads_tracking",               // 第三方广告链接 tracking
	RefreshRecommendPosts:          "refresh_recommend_posts",    // 刷新推荐贴缓存
	DuibaBannerClickReport:         "duiba_banner_click_report",  // 兑吧 banner 点击上报
	StReport:                       "st_report",                  // 用户行为上报，只有ios在上报，后台也没有使用这个数据
	NoticePush:                     "notice_push",                // 消息推送
	DistributeLinkConvert:          "distribute_link_convert",    // 分销渠道长连接转短链
	VoucherBatchPush:               "voucher_batch_push",         // 批量发送优惠券
	MediaConvertHLS:                "media_convert_hls",          // 课程连接转m3u8
	UserWallet:                     "user_wallet",                // 用户钱包操作
	MemcachedDataHandle:            "memcached_data_handle",
	TXSportsCharge:                 "tx_sports_charge",               // 腾讯体育联合会员直充任务
	AsyncSendGift:                  "use_gift",                       // 异步分发大礼包
	SlicingMakeSession:             "slicing_make_session",           // 切片组成课程
	ChallengeTask:                  "challenge_task",                 // 挑战赛任务处理
	AsyncSendScrmCard:              "send_scrm_card",                 // 异步发放有赞会员卡
	ReportLocation:                 "report_location",                // 用户位置信息
	UserBadge:                      "create_user_badge",              // 用户历史徽章
	SendHXPartnerSystemMsg:         "send_hx_partner_system_msg",     // 发送环信结伴系统消息
	CreateOrder4IosIap:             "creat_order4_ios_iap_action",    // iOS IAP 支付成功回调
	NoteBook:                       "notebook",                       // 记录本异步任务
	AdsReport:                      "ads_report",                     // 广告上报统计
	LoginRegister:                  "login_register",                 // 登陆注册异步处理
	AgentVipSend:                   "agent_vip_send",                 // 代理商会员发放,
	ManualSendPoint:                "manual_send_point",              // 发送瑜币汇总以及瑜币徽章相关
	FullGiftOrder:                  "full_gift_order",                // 同步满赠活动订单拆单基础数据表
	SendYouzanCoupon:               "send_youzan_coupon",             // 发放有赞优惠券
	UpdateUserLabel:                "update_user_label",              // 更新用户分群标签
	UpdateMiniUserLabel:            "update_mini_user_label",         // 更新小程序用户分群标签
	CalculatePostsNum:              "calculate_posts_count_by_topic", // 计算话题参与帖子个数
	SetUserIndexes:                 "set_user_indexes",
	ClearLocalData:                 "clear_local_data",                   // 清理本地消息
	AsyncCompleteOrder:             "complete_order",                     // 异步调用完成订单回调接口
	AverageSendPoint:               "average_send_point",                 // 发放平分瑜币
	OperateUserGrow:                "operate_user_grow",                  // 处理并发添加成长
	SaveUserInfoForPush:            "save_user_info_for_push",            // 异步保存用户信息供push使用
	LogoutYZY:                      "logout_yzy",                         // 退出有赞云
	SendH2OrderFrom:                "send_h2O_order_from",                // 订单分成 提供给h2o
	PostAutoVerify:                 "post_auto_verify",                   // 审核帖子状态
	UserSignInConfigUpdateIsRemind: "user_signin_config_update_isremind", // 更新签到提醒状态
	InitPracticeCalendar:           "init_practice_calendar",             // 初始化练习日历
	AppleAdsInfo:                   "apple_ads_info",                     // ios 广告异步处理
	EquityAllIdentity:              "equity_all_identity",                // 用户当下权益身份标签 全渠道埋点
	SensorsEventDyOrder:            "sensors_event_dy_order",             // 神策事件上报
	CrackVersionUser:               "crack_version_user",                 // 黑产用户逻辑
	LogOutOrder:                    "log_out_order",                      // 记录外部订单关联
}

type asyncPayload struct {
	Type   string      `json:"type"`
	Params interface{} `json:"params"`
}

type AsyncProcessor struct{}

func (a *AsyncProcessor) getFunc(dataType string) func(params []byte) error {
	var typeFunc = map[string]func(params []byte) error{
		asyncPayloadType.SetTeamPlayTimeRanking:         a.setTeamPlayTimeRanking,
		asyncPayloadType.AdsTracking:                    a.adsTracking,
		asyncPayloadType.RefreshRecommendPosts:          a.refreshRecommendPosts,
		asyncPayloadType.DuibaBannerClickReport:         a.duibaBannerClickReport,
		asyncPayloadType.StReport:                       a.stReport,
		asyncPayloadType.NoticePush:                     a.noticePush,
		asyncPayloadType.DistributeLinkConvert:          a.distributeLinkConvert,
		asyncPayloadType.VoucherBatchPush:               a.voucherBatchPush,
		asyncPayloadType.MediaConvertHLS:                a.mediaConvertHLS,
		asyncPayloadType.UserWallet:                     a.userWallet,
		asyncPayloadType.MemcachedDataHandle:            a.memcachedDataHandle,
		asyncPayloadType.TXSportsCharge:                 a.txSportsCharge,
		asyncPayloadType.AsyncSendGift:                  a.sendUseGift,
		asyncPayloadType.SlicingMakeSession:             a.slicingMakeSession,
		asyncPayloadType.ChallengeTask:                  a.challengeTask,
		asyncPayloadType.AsyncSendScrmCard:              a.SendScrmCard,
		asyncPayloadType.ReportLocation:                 a.reportLocation,
		asyncPayloadType.UserBadge:                      a.createUserOldBadge,
		asyncPayloadType.SendHXPartnerSystemMsg:         a.sendHXPartnerSystemMsg,
		asyncPayloadType.NoteBook:                       a.asyncNoteBook,
		asyncPayloadType.CreateOrder4IosIap:             a.createOrder4IosIap,
		asyncPayloadType.AdsReport:                      a.adsReport,
		asyncPayloadType.LoginRegister:                  a.loginRegister,
		asyncPayloadType.AgentVipSend:                   a.agentVipSend,
		asyncPayloadType.FullGiftOrder:                  a.fullGiftOrder,
		asyncPayloadType.SendYouzanCoupon:               a.SendYouzanCoupon,
		asyncPayloadType.ManualSendPoint:                a.manualSendPoint,
		asyncPayloadType.UpdateUserLabel:                a.asyncUserLabel,
		asyncPayloadType.UpdateMiniUserLabel:            a.asyncMiniUserLabel,
		asyncPayloadType.CalculatePostsNum:              a.asyncCalculatePostsNumByTopic,
		asyncPayloadType.SetUserIndexes:                 a.setUserIndexes,
		asyncPayloadType.ClearLocalData:                 a.clearLocalData,
		asyncPayloadType.AsyncCompleteOrder:             a.completeOrder,
		asyncPayloadType.AverageSendPoint:               a.averageSendPoint,
		asyncPayloadType.OperateUserGrow:                a.operateUserGrow,
		asyncPayloadType.SaveUserInfoForPush:            a.SaveUserInfoForPush,
		asyncPayloadType.LogoutYZY:                      a.logoutYZY,
		asyncPayloadType.SendH2OrderFrom:                a.SendH2OrderFrom,
		asyncPayloadType.PostAutoVerify:                 a.postAutoVerify,
		asyncPayloadType.UserSignInConfigUpdateIsRemind: a.userSignInConfigUpdateIsRemind,
		asyncPayloadType.InitPracticeCalendar:           a.InitPracticeCalendar,
		asyncPayloadType.AppleAdsInfo:                   a.appleAdsInfo,
		asyncPayloadType.EquityAllIdentity:              a.equityAllIdentity,
		asyncPayloadType.SensorsEventDyOrder:            a.EventDyOrder,
		asyncPayloadType.CrackVersionUser:               a.CrackVersionUser,
		asyncPayloadType.LogOutOrder:                    a.LogOutOrder,
	}

	if i, ok := typeFunc[dataType]; ok {
		return i
	}
	return nil
}

// setTeamPlayTimeRanking 设置班级排行榜
func (a *AsyncProcessor) setTeamPlayTimeRanking(params []byte) error {
	var payload practice.SetTeamPlayTimeRankingPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.SetTeamPlayTimeRanking(payload)
}

// adsTracking 第三方广告链接 tracking
func (a *AsyncProcessor) adsTracking(params []byte) error {
	var payload async.AdsTrackingPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	async.AdsTracking(payload)
	return nil
}

// refreshRecommendPosts 刷新推荐贴缓存
func (a *AsyncProcessor) refreshRecommendPosts(params []byte) error {
	logger.Debug(string(params))
	return async.RefreshRecommendPosts()
}

// duibaBannerClickReport 兑吧banner点击上报
func (a *AsyncProcessor) duibaBannerClickReport(params []byte) error {
	var payload async.DuibaBannerClickReportPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.DuibaBannerClickReport(payload)
}

// stReport 日志上报
func (a *AsyncProcessor) stReport(params []byte) error {
	var payload async.StReportPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.StReport(&payload)
}

// noticePush 消息推送
func (a *AsyncProcessor) noticePush(params []byte) error {
	var payload async.NoticePushPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.SystemNoticePush(&payload)
}

// distributeLinkConvert 分销渠道长链转短链
func (a *AsyncProcessor) distributeLinkConvert(params []byte) error {
	var payload async.DistributeLinkConvertPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.DistributeLinkConvert(payload)
}

// voucherBatchPush 批量发送优惠券
func (a *AsyncProcessor) voucherBatchPush(params []byte) error {
	var payload async.VoucherBatchPushPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.VoucherBatchPush(payload)
}

// mediaConvertHLS 课程链接转m3u8
func (a *AsyncProcessor) mediaConvertHLS(params []byte) error {
	var payload async.MediaConvertHLSPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.MediaConvertHLS(payload)
}

func (a *AsyncProcessor) userWallet(params []byte) error {
	var payload async.UserWalletPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.UserWallet(payload)
}

func (a *AsyncProcessor) memcachedDataHandle(params []byte) error {
	var payload async.MemcacheDataHandlePayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.MemcachedDataHandle(payload)
}

func (a *AsyncProcessor) txSportsCharge(params []byte) error {
	var payload async.TXSportsChargePayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.TXSportsCharge(&payload)
}

func (a *AsyncProcessor) sendUseGift(params []byte) error {
	var payload async.UseGiftPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.SendUseGift(payload)
}

func (a *AsyncProcessor) slicingMakeSession(params []byte) error {
	var payload async.SlicingMakeSessionPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.SlicingMakeSession(payload)
}

func (a *AsyncProcessor) challengeTask(params []byte) error {
	var payload async.ChallengeTaskPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.ProcessChallengeTask(payload)
}

// 发放有赞会员卡
func (a *AsyncProcessor) SendScrmCard(params []byte) error {
	var payload async.UseScrmPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.SendScrmCard(payload)
}

func (a *AsyncProcessor) reportLocation(params []byte) error {
	var payload async.ReportLocationPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}

	return async.ProcessLocation(&payload)
}

func (a *AsyncProcessor) createUserOldBadge(params []byte) error {
	var payload async.UserBadgePayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}

	return async.ProcessUserBadge(payload)
}

// sendPartnerHXMsg 发送环信结伴系统消息
func (a *AsyncProcessor) sendHXPartnerSystemMsg(params []byte) error {
	var payload partner.SendHXPartnerSystemMsgPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.SendHXPartnerSystemMsg(&payload)
}

// asyncCalculatePostsNumByTopic 计算话题下帖子个数
func (a *AsyncProcessor) asyncCalculatePostsNumByTopic(params []byte) error {
	var payload topic.PostsMsgPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.CalculatePostsNumByTopic(payload)
}

// iOS 支付成功后回调
func (a *AsyncProcessor) createOrder4IosIap(params []byte) error {
	var payload order.CreateOrder4IosIapPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	order.ServiceIosOrder.CreateOrder4IosIap(&payload)
	return nil
}

func (a *AsyncProcessor) asyncNoteBook(params []byte) error {
	var payload async.NoteBookPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}

	return async.ProcessNoteBook(payload)
}

// 广告展示统计
func (a *AsyncProcessor) adsReport(params []byte) error {
	var payload async.AdsReportPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.AdsReport(payload)
}

// 用户login事件
func (a *AsyncProcessor) loginRegister(params []byte) error {
	var payload async.LoginRegister
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.ProcessLoginRegister(&payload)
}

// 代理商会员充值异步任务
func (a *AsyncProcessor) agentVipSend(params []byte) error {
	var payload async.AgentVipSendPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.AgentVipSend(payload)
}

// 同步满赠需求订单拆单数据
func (a *AsyncProcessor) fullGiftOrder(params []byte) error {
	var payload async.FullGiftOrderPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.FullGiftOrder(payload)
}

// 发放有赞优惠券
func (a *AsyncProcessor) SendYouzanCoupon(params []byte) error {
	var payload async.SendYouzanCouponPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.SendYouzanCoupon(payload)
}

// 批量发送瑜币->删除
func (a *AsyncProcessor) manualSendPoint(params []byte) error {
	return nil
}

// asyncUserLabel 更新用户标签
func (a *AsyncProcessor) asyncUserLabel(params []byte) error {
	var payload async.UserLabelPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.ProcessUserLabel(&payload)
}

// asyncMiniUserLabel 更新小程序用户标签
func (a *AsyncProcessor) asyncMiniUserLabel(params []byte) error {
	var payload async.MiniUserLabelPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.ProcessMiniUserLabel(&payload)
}

// setUserIndexes 设置sphinx使用的用户索引的原数据表
func (a *AsyncProcessor) setUserIndexes(params []byte) error {
	var payload async.UserIndexesPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.SetUserIndexes(&payload)
}

// 清理本地消息
func (a *AsyncProcessor) clearLocalData(params []byte) error {
	var payload async.ClearLocalDataPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.ClearLocalDataMain(&payload)
}

// 异步调用完成订单回调
func (a *AsyncProcessor) completeOrder(params []byte) error {
	var payload async.CompleteOrderPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.CompleteOrder(payload)
}

// 发放平分瑜币->删除
func (a *AsyncProcessor) averageSendPoint(params []byte) error {
	return nil
}

// 添加成长(客户端并发请求 异步处理)
func (a *AsyncProcessor) operateUserGrow(params []byte) error {
	var payload async.OperateUserGrowPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.OperateUserGrow(&payload)
}

// logoutYZY 有赞登出接口,异步处理
func (a *AsyncProcessor) logoutYZY(params []byte) error {
	var payload async.LogoutYZYPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	go async.LogoutYZY(&payload)
	return nil
}

// 异步保存用户信息供push使用
func (a *AsyncProcessor) SaveUserInfoForPush(params []byte) error {
	var payload async.SaveUserInfoForPushPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return nil
}

// 分成数据 提交到h2o
func (a *AsyncProcessor) SendH2OrderFrom(params []byte) error {
	var payload async.SendH2OrderPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.SendH2OrderHandle(&payload)
}

// 发帖审核帖子
func (a *AsyncProcessor) postAutoVerify(params []byte) error {
	var payload async.PostAutoVerifyPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.PostAutoVerify(&payload)
}

// 更新用户签到提醒状态 /620/user/Signin/updateIsRemind
func (a *AsyncProcessor) userSignInConfigUpdateIsRemind(params []byte) error {
	var payload async.UserSignInConfigUpdateIsRemind
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.UpdateIsRemind(&payload)
}

// 初始化 练习日历
func (a *AsyncProcessor) InitPracticeCalendar(params []byte) error {
	var payload async.PracticeCalendarPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.InitPracticeCalendar(&payload)
}

// appleAdsInfo IOS 异步处理
func (a *AsyncProcessor) appleAdsInfo(params []byte) error {
	var iframe = new(struct {
		Iframe string `json:"iframe"`
	})
	if err := json.Unmarshal(params, iframe); err != nil {
		return err
	}
	switch iframe.Iframe {
	case "iad":
		var payload async.AppleAdsInfoIad
		if err := json.Unmarshal(params, &payload); err != nil {
			return err
		}
		go async.UploadScAppleAdsInfoIad(&payload)
	case "ad_service":
		var payload async.AppleAdsInfoAdService
		if err := json.Unmarshal(params, &payload); err != nil {
			return err
		}
		go async.UploadScAppleAdsInfoAdService(&payload)
	default:
		logger.Error("苹果归因数据上报,非法的 iframe " + iframe.Iframe)
	}
	return nil
}

// 用户当下权益身份标签
func (a *AsyncProcessor) equityAllIdentity(params []byte) error {
	var payload async.UpEquityIdentityPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.UploadUserAllVipLabelEvent(&payload)
}

// EventDyOrder 神策事件上报
func (a *AsyncProcessor) EventDyOrder(params []byte) error {
	var payload async.SensorsEventPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.EventSubmitDyOrder(&payload)
}

// LogOutOrder 黑产用户逻辑处理
func (a *AsyncProcessor) LogOutOrder(params []byte) error {
	var payload async.LogOutOrderPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.ParseLogOutOrder(&payload)
}

// CrackVersionUser 黑产用户逻辑处理
func (a *AsyncProcessor) CrackVersionUser(params []byte) error {
	var payload async.CrackVersionUserPayload
	if err := json.Unmarshal(params, &payload); err != nil {
		return err
	}
	return async.ParseCrackVersionUser(&payload)
}

// Run 执行函数
func (a *AsyncProcessor) Run(key, payload []byte) error {
	data := asyncPayload{}
	// 请注意, data.Params 会被反序列化为一个 map 类型
	if err := json.Unmarshal(payload, &data); err != nil {
		return err
	}
	var err error
	params, err := json.Marshal(data.Params)
	if err != nil {
		return err
	}
	logger.Info("async key:", data.Type, "data:", string(params))
	if f := a.getFunc(data.Type); f != nil {
		return f(params)
	}
	return errors.New("unknown async process type")
}
