package equity

import (
	"errors"
	"fmt"
	"net/url"
	"strconv"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	dbequity "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/equity"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/vip"
	"gitlab.dailyyoga.com.cn/server/srv-task/group/async"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	serquity "gitlab.dailyyoga.com.cn/server/srv-task/service/equity"
)

type SetAddUserKolEquityPayload struct {
	UID                 int64  `json:"uid"`
	EquityType          int32  `json:"equity_type"`
	Operation           int32  `json:"operation"`
	RecordType          int32  `json:"record_type"`
	EquityDurationType  int32  `json:"equity_duration_type"`
	EquityDurationValue int64  `json:"equity_duration_value"`
	SourceType          int32  `json:"source_type"`
	SourceID            int64  `json:"source_id"`
	Reason              string `json:"reason"`
	AdminName           string `json:"admin_name"`
	CreateTime          int64  `json:"create_time"`
}

// SetAddUserEquity 添加特权卡
func SetAddUserEquity(data *SetAddUserKolEquityPayload) error {
	inArray := func(t int32, arr []int32) bool {
		for _, v := range arr {
			if v == t {
				return true
			}
		}
		return false
	}
	if data.EquityType != library.AdminVip && !inArray(data.EquityType, library.AllowAddEquityType) {
		logger.Error("添加卡EquityType:类型错误:", data.EquityType, "uid:", data.UID, "info:", data)
		return nil
	}
	// 检查操作添加类型
	if !inArray(data.RecordType, library.AllowAddRecordType) ||
		!inArray(data.Operation, library.AllowOperationType) ||
		!inArray(data.EquityDurationType, library.AllowEquityDurationType) {
		logger.Error("RecordType:类型错误:", data.RecordType, "uid:", data.UID, "info:", data)
		return nil
	}

	if inArray(data.EquityType, library.AllowAddEquityDurType) {
		if err := addKolUserEquity(data); err != nil {
			logger.Error(err)
			return err
		}
		// 2022/12/2 10:19 water 异步上报 用户当下权益身份标签
		if err := async.UploadUserAllVipLabelEvent(&async.UpEquityIdentityPayload{
			UID:    data.UID,
			HasKol: true,
			HasNow: true,
		}); err != nil {
			logger.Warn(err)
		}
	} else if data.EquityType == library.AdminVip {
		if data.Operation == library.OperationEnum.Sub.ToInt32() {
			data.EquityDurationValue = -(data.EquityDurationValue)
		}
		if err := addMemberUserEquity(data); err != nil {
			logger.Error(err)
			return err
		}
		// 补充后台操作会员记录
		record := &vip.AdminVipRecord{
			AdminUID:         0,
			OperatorName:     data.AdminName,
			Uids:             strconv.Itoa(int(data.UID)),
			SourceType:       101,
			VipType:          6, // 即member_level
			VipDurationType:  data.EquityDurationType,
			VipDurationValue: data.EquityDurationValue,
			Remark:           data.Reason,
		}
		_ = record.Save()
	} else {
		err := addCampUserEquity(data)
		if err != nil {
			logger.Error(err)
			return err
		}
	}

	return nil
}

func addCampUserEquity(data *SetAddUserKolEquityPayload) error {
	init := serquity.GetAccountInfo(data.UID)
	if init.TbAccountInfo == nil || init.TbAccountInfo.AccountID <= 0 {
		logger.Error("SetInitUserKolEquity:用户uid不存在", data.UID)
		return nil
	}
	uid := init.TbAccountInfo.AccountID
	var rollBackErr error
	session := usercenter.GetEngineMaster().NewSession()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return errors.New("failed to open transaction")
	}
	defer session.Close()
	defer func() {
		if rollBackErr != nil {
			logger.Error(rollBackErr)
			if err := session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	userKolDuration := init.CalculationUserKolDuration(data.EquityDurationType, data.Operation,
		data.EquityDurationValue, 0, 0)
	userEquityDuration := &dbequity.UserEquityDuration{
		UID:        uid,
		EquityType: data.EquityType,
		StartTime:  userKolDuration.StartTime,
		EndTime:    userKolDuration.EndTime,
		Status:     library.DataStatusEnum.Valid.ToInt32(),
	}
	if rollBackErr = userEquityDuration.SaveByTransaction(session); rollBackErr != nil {
		return errors.New("保存UserEquityDuration信息失败")
	}
	userEquityRecord := &dbequity.UserEquityRecord{
		UID:                 uid,
		EquityType:          data.EquityType,
		StartTime:           userKolDuration.StartTime,
		EndTime:             userKolDuration.EndTime,
		RecordType:          data.RecordType,
		EquityDurationType:  data.EquityDurationType,
		OperationType:       data.Operation,
		EquityDurationValue: data.EquityDurationValue,
		ShowStatus:          library.DataStatusEnum.Valid.ToInt32(),
		SourceType:          data.SourceType,
		SourceID:            data.SourceID,
		Desc: fmt.Sprintf("%s%d%s%s", library.RecordTypeEnum.System.GetRecordTypeDesEnumDes(),
			data.EquityDurationValue, library.ToCEnumType(data.EquityDurationType).GetDurationTypeDes(),
			library.ToCEnumType(data.EquityType).GetEquityTypeDesEnum()),
		Reason:     data.Reason,
		AdminName:  data.AdminName,
		CreateTime: data.CreateTime,
	}

	if rollBackErr = userEquityRecord.SaveByTransaction(session); rollBackErr != nil {
		return errors.New("保存UserEquityRecord信息失败")
	}

	userEquityDurationHistory := &dbequity.UserEquityDurationHistory{
		UID:                  uid,
		UserEquityDurationID: userEquityDuration.ID,
		UserEquityRecordID:   userEquityRecord.ID,
		EquityType:           userEquityRecord.EquityType,
		StartTime:            0,
		EndTime:              0,
		ChangeStartTime:      userEquityRecord.StartTime,
		ChangeEndTime:        userEquityRecord.EndTime,
	}
	if rollBackErr = userEquityDurationHistory.SaveByTransaction(session); rollBackErr != nil {
		return errors.New("保存userEquityDurationHistory信息失败")
	}
	if rollBackErr = session.Commit(); rollBackErr != nil {
		return errors.New("事务提交失败")
	}

	return nil
}

func addKolUserEquity(data *SetAddUserKolEquityPayload) error {
	if err := SetInitUserKolEquity(&SetInitUserKolEquityPayload{UID: data.UID, EquityType: data.EquityType}); err != nil {
		logger.Error("初始化权益卡错误", data.UID, data.EquityType, err)
		return err
	}

	init := serquity.GetAccountInfo(data.UID)
	if init == nil || init.TbAccountInfo == nil {
		logger.Error("SetInitUserKolEquity:用户uid不存在", data.UID)
		return nil
	}
	uid := init.TbAccountInfo.AccountID

	var rollBackErr error
	session := usercenter.GetEngineMaster().NewSession()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return errors.New("failed to open transaction")
	}
	defer session.Close()
	defer func() {
		if rollBackErr != nil {
			logger.Error(rollBackErr)
			if err := session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()

	userEquityDuration := init.GetUserKolEquityInfo(data.EquityType)
	var originalStartTime, originalEndTime int64
	userKolDuration := init.CalculationUserKolDuration(data.EquityDurationType, data.Operation,
		data.EquityDurationValue, originalStartTime, originalEndTime)
	// 说明已经初始化
	if userEquityDuration != nil && userEquityDuration.ID > 0 {
		originalStartTime = userEquityDuration.StartTime
		originalEndTime = userEquityDuration.EndTime
		// 2021-01-13 处理数据异常
		if userEquityDuration.Status == library.DataStatusEnum.Delete.ToInt32() {
			originalEndTime = 0
		}
		userKolDuration = init.CalculationUserKolDuration(data.EquityDurationType, data.Operation,
			data.EquityDurationValue, originalStartTime, originalEndTime)

		status := library.DataStatusEnum.Delete.ToInt32()
		if userKolDuration.StartTime < userKolDuration.EndTime {
			status = library.DataStatusEnum.Valid.ToInt32()
			userEquityDuration.StartTime = userKolDuration.StartTime
			userEquityDuration.EndTime = userKolDuration.EndTime
		}
		userEquityDuration.Status = status
		if rollBackErr = userEquityDuration.UpdateByTransaction(session); rollBackErr != nil {
			return errors.New("保存UserEquityDuration信息失败")
		}
	} else {
		if data.Operation == library.OperationEnum.Sub.ToInt32() {
			logger.Error("操作错误用户还未有名师课堂无限卡:", data.RecordType, "uid:", uid, "info:", data)
			return nil
		}

		userEquityDuration = &dbequity.UserEquityDuration{
			UID: uid, EquityType: data.EquityType, Status: library.DataStatusEnum.Valid.ToInt32(),
			StartTime: userKolDuration.StartTime, EndTime: userKolDuration.EndTime,
		}
		if rollBackErr = userEquityDuration.SaveByTransaction(session); rollBackErr != nil {
			return errors.New("保存UserEquityDuration信息失败")
		}
	}

	userEquityRecord := &dbequity.UserEquityRecord{
		UID: uid, EquityType: data.EquityType,
		StartTime: userKolDuration.StartTime, EndTime: userKolDuration.EndTime,
		RecordType: data.RecordType, EquityDurationType: data.EquityDurationType, OperationType: data.Operation,
		EquityDurationValue: data.EquityDurationValue, ShowStatus: library.DataStatusEnum.Valid.ToInt32(),
		SourceType: data.SourceType, SourceID: data.SourceID,
		Desc: fmt.Sprintf("%s%d%s%s",
			library.ToCEnumType(data.RecordType).GetRecordTypeDesEnumDes(),
			data.EquityDurationValue, library.ToCEnumType(data.EquityDurationType).GetDurationTypeDes(),
			library.ToCEnumType(data.EquityType).GetEquityTypeDesEnum()),
		Reason: data.Reason, AdminName: data.AdminName, CreateTime: data.CreateTime,
	}
	if rollBackErr = userEquityRecord.SaveByTransaction(session); rollBackErr != nil {
		return errors.New("保存UserEquityRecord信息失败")
	}

	userEquityDurationHistory := &dbequity.UserEquityDurationHistory{
		UID: uid, EquityType: userEquityRecord.EquityType,
		UserEquityDurationID: userEquityDuration.ID, UserEquityRecordID: userEquityRecord.ID,
		StartTime: originalStartTime, EndTime: originalEndTime,
		ChangeStartTime: userEquityRecord.StartTime, ChangeEndTime: userEquityRecord.EndTime,
	}
	if rollBackErr = userEquityDurationHistory.SaveByTransaction(session); rollBackErr != nil {
		return errors.New("保存userEquityDurationHistory信息失败")
	}
	if rollBackErr = session.Commit(); rollBackErr != nil {
		return errors.New("事务提交失败")
	}
	return nil
}

func addMemberUserEquity(data *SetAddUserKolEquityPayload) error {
	uVal := make(url.Values)
	uVal.Add("uid", strconv.FormatInt(data.UID, 10))
	uVal.Add("vip_duration_type", strconv.Itoa(int(data.EquityDurationType))) // 会员计量方式 1-天 2 月 3年
	uVal.Add("vip_duration_value", strconv.FormatInt(data.EquityDurationValue, 10))
	uVal.Add("member_level", strconv.FormatInt(6, 10))
	uVal.Add("business_line", strconv.FormatInt(3, 10)) // 会员运营
	uVal.Add("category_item", strconv.FormatInt(2, 10)) // 活动发放
	uVal.Add("source_type", strconv.FormatInt(6, 10))   // 管理后台添加
	resp, err := requests.NewYogaClient(config.Get().PhpYogaAddress).PostForm("/usercenter/user/setvipflexible", uVal)
	logger.Infof("---addMemberUserEquity-resp uid:%d Response:%+v", data.UID, resp)
	if err != nil {
		logger.Errorf("调用yoga发放会员报错, err: %s", err)
		return err
	}
	if resp == nil || !resp.IsOK() || resp.Body == "" {
		logger.Error("调用yoga发放会员失败", resp)
		return fmt.Errorf("数据返回失败 %+v", resp)
	}
	return nil
}
