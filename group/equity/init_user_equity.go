package equity

import (
	"fmt"
	"math"
	"time"

	"github.com/pkg/errors"
	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	dbequity "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/equity"
	"gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service"
	serquity "gitlab.dailyyoga.com.cn/server/srv-task/service/equity"
)

type SetInitUserKolEquityPayload struct {
	UID        int64 `json:"uid"`
	EquityType int32 `json:"equity_type"`
}

func getKolEffectiveDuration(uid int64, equityType int32) (duration *serquity.CalculationEffectiveDuration,
	exist, isVipPause bool, err error) {
	init := serquity.GetAccountInfo(uid)
	if init.TbAccountInfo == nil || init.TbAccountInfo.AccountID <= 0 {
		err = errors.Errorf("setInitUserKOLEquity, uid %d not found", uid)
		return
	}

	userKOLEquity := init.GetUserKolEquityInfo(equityType)
	if userKOLEquity != nil && userKOLEquity.ID > 0 {
		exist = true
		return
	}
	duration = init.CalculationInitUserKolDuration()
	isVipPause = init.IsVIPPause()
	return
}

// SetInitUserKolEquity 2020-01-18 初始化用户的会员卡
func SetInitUserKolEquity(data *SetInitUserKolEquityPayload) error {
	if data.UID <= 0 || data.EquityType != library.Kol {
		return nil
	}

	var rollBackErr error
	session := usercenter.GetEngineMaster().NewSession()
	if err := session.Begin(); err != nil {
		return errors.Errorf("failed to open transaction, err: %s", err)
	}
	defer session.Close()
	defer func() {
		if rollBackErr != nil {
			logger.Error(rollBackErr)
			if err := session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()

	kolEffectiveDuration, exist, isVipPause, err := getKolEffectiveDuration(data.UID, data.EquityType)
	if err != nil {
		return err
	}
	if exist {
		return nil
	}
	if kolEffectiveDuration != nil && (kolEffectiveDuration.StartTime < 1 || kolEffectiveDuration.EndTime < 1) {
		userEquityDuration := &dbequity.UserEquityDuration{
			UID: data.UID, EquityType: data.EquityType, Status: library.DataStatusEnum.Delete.ToInt32(),
			StartTime: kolEffectiveDuration.StartTime, EndTime: kolEffectiveDuration.EndTime,
		}
		if rollBackErr = userEquityDuration.SaveByTransaction(session); rollBackErr != nil {
			return errors.New("初始化UserEquityDuration信息失败")
		}
		if rollBackErr = session.Commit(); rollBackErr != nil {
			return errors.New("事务提交失败")
		}
		return nil
	}
	if kolEffectiveDuration.StartTime > kolEffectiveDuration.EndTime {
		logger.Error("SetInitUserKolEquity 用户名师课堂无限卡时有误:",
			data.UID, "开始时间", kolEffectiveDuration.StartTime, "结束时间", kolEffectiveDuration.EndTime)
		return nil
	}
	// 会员暂停状态下直接给用户多赠送90名师课堂无限卡
	if isVipPause {
		kolEffectiveDuration.EndTime += 90 * 86400
	}

	status := library.DataStatusEnum.Valid.ToInt32()
	if kolEffectiveDuration.EndTime < time.Now().Unix() {
		status = library.DataStatusEnum.Delete.ToInt32()
	}
	userEquityDuration := &dbequity.UserEquityDuration{
		UID: data.UID, EquityType: data.EquityType, Status: status,
		StartTime: kolEffectiveDuration.StartTime, EndTime: kolEffectiveDuration.EndTime,
	}
	if rollBackErr = userEquityDuration.SaveByTransaction(session); rollBackErr != nil {
		return errors.New("保存UserEquityDuration信息失败")
	}
	first, _ := service.GetDayFirstAndLastUnixTimestamp(time.Now())
	if kolEffectiveDuration.EndTime < first {
		logger.Error("用户名师课堂无限卡时有误:", data.UID, "开始时间", first, "结束时间", kolEffectiveDuration.EndTime)
		return nil
	}
	equityDurationValue := math.Floor((float64(kolEffectiveDuration.EndTime) - float64(first)) / 86400)
	duration := int64(equityDurationValue)
	userEquityRecord := &dbequity.UserEquityRecord{
		UID: data.UID, EquityType: data.EquityType,
		StartTime: kolEffectiveDuration.StartTime, EndTime: kolEffectiveDuration.EndTime,
		RecordType:          library.RecordTypeEnum.System.ToInt32(),
		EquityDurationType:  library.EquityDurationTypeEnum.Day.ToInt32(),
		OperationType:       library.OperationEnum.Add.ToInt32(),
		EquityDurationValue: duration, ShowStatus: library.DataStatusEnum.Valid.ToInt32(),
		Desc: fmt.Sprintf("%s%d%s%s",
			library.RecordTypeEnum.System.GetRecordTypeDesEnumDes(),
			duration, library.EquityDurationTypeEnum.Day.GetDurationTypeDes(),
			library.EquityTypeEnum.Kol.GetEquityTypeDesEnum()),
	}
	if rollBackErr = userEquityRecord.SaveByTransaction(session); rollBackErr != nil {
		return errors.New("保存UserEquityRecord信息失败")
	}

	userEquityDurationHistory := &dbequity.UserEquityDurationHistory{
		UID: data.UID, EquityType: userEquityRecord.EquityType,
		UserEquityDurationID: userEquityDuration.ID, UserEquityRecordID: userEquityRecord.ID,
		ChangeStartTime: userEquityRecord.StartTime, ChangeEndTime: userEquityRecord.EndTime,
	}
	if rollBackErr = userEquityDurationHistory.SaveByTransaction(session); rollBackErr != nil {
		return errors.New("保存userEquityDurationHistory信息失败")
	}

	if err := session.Commit(); err != nil {
		return errors.New("事务提交失败")
	}
	return nil
}
