package equity

import (
	"context"
	"fmt"
	"net/url"
	"strconv"
	"sync"
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/go-artifact/logger"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"

	pb "gitlab.dailyyoga.com.cn/protogen/srv-usercenter-go/proto"
	"gitlab.dailyyoga.com.cn/server/srv-task/cache"
	"gitlab.dailyyoga.com.cn/server/srv-task/config"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter"
	cdb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/contract"
	edb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/equity"
	"gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/iap"
	odb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/order"
	tvdb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/tv"
	undb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/union"
	udb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user"
	tdb "gitlab.dailyyoga.com.cn/server/srv-task/databases/usercenter/user/third"
	"gitlab.dailyyoga.com.cn/server/srv-task/grpc"
	lib "gitlab.dailyyoga.com.cn/server/srv-task/library"
	"gitlab.dailyyoga.com.cn/server/srv-task/service/comm"
)

type SetTransferUserEquityPayload struct {
	UID        int64  `json:"uid"`
	DistinctID string `json:"distinct_id"`
	tOrder     []string
	logoutFlag bool
	OldUID     int64 `json:"old_uid"`
}

const setTransferLockKey = "set:transfer:user:equity:"

// SetTransferUserEquity 权益迁移
// nolint
func SetTransferUserEquity(data *SetTransferUserEquityPayload) error {
	rd := cache.GetLockRedis()
	v := strconv.FormatInt(time.Now().Unix(), 10)
	ctx := context.Background()
	cacheKey := fmt.Sprintf("%s:%d", setTransferLockKey, data.UID)
	if lock, err := rd.SetNX(ctx, cacheKey, v, 30*time.Second).Result(); err != nil {
		logger.Error(err)
		return nil
	} else if !lock {
		return nil
	}
	defer func() {
		if err := rd.Del(ctx, cacheKey).Err(); err != nil {
			logger.Error(err)
		}
	}()
	data.tOrder = make([]string, 0)
	logger.Infof("---SetTransferUserEquity-0-- uid:%d distinct_id:%s logoutFlag:%t", data.UID, data.DistinctID,
		data.logoutFlag)
	if dUData := tdb.TbATpSensorsData.GetByOpenIDBind(data.DistinctID); dUData != nil {
		// 只做一次迁移
		if d := edb.TbUserEquityTransfer.GetItemByDUID(dUData.UID); d != nil {
			logger.Infof("---SetTransferUserEquity--只做一次迁移- uid:%d dUData:%d", data.UID, dUData.UID)
			return nil
		}
		user := udb.TbAccountInfo.GetByUID(dUData.UID)
		if user == nil {
			logger.Infof("---SetTransferUserEquity--用户信息不存在- uid:%d dUData:%d", data.UID, dUData.UID)
			return nil
		}
		// 神策登录方式 14
		if user.LoginType != 14 || user.Mobile != "0" {
			return nil
		}

		// 判断用户是否绑定 绑定直接忽略
		if data.getUserTpList(dUData.UID) {
			logger.Infof("---SetTransferUserEquity--被忽略- uid:%d dUData:%d", data.UID, user.AccountID)
			return nil
		}
		// 神策登录时不做处理
		if data.UID == user.AccountID {
			return nil
		}
		logger.Infof("---SetTransferUserEquity--1- uid:%d dUData:%d", data.UID, user.AccountID)
		var wg sync.WaitGroup
		wg.Add(4)
		go func() {
			defer wg.Done()
			data.transferOrder(user.AccountID)
		}()
		go func() {
			defer wg.Done()
			data.transferUserEquity(user.AccountID)
		}()
		go func() {
			defer wg.Done()
			data.transferWallet(user.AccountID)
		}()
		go func() {
			defer wg.Done()
			data.transferTvIntelligence(user.AccountID)
		}()
		wg.Wait()
		if data.logoutFlag {
			logger.Infof("---SetTransferUserEquity logoutFlag uid:%d dUData:%d logoutFlag:%t", data.UID,
				user.AccountID, data.logoutFlag)
			user.DeviceID = ""
			user.Alternate3 = ""
			if err := user.SaveTransferUser(); err != nil {
				logger.Warn("SetTransferUserEquity 权益迁移 注销DistinctID失败", user.AccountID, err)
			}
			dUData.BindStatus = lib.DataStatusEnum.Delete.ToInt32()
			if err := dUData.Update(); err != nil {
				logger.Warn("SetTransferUserEquity 权益迁移 取消绑定失败", err)
			}

			tr := edb.UserEquityTransfer{
				DUID:       dUData.UID,
				UID:        data.UID,
				CreateTime: time.Now().Unix(),
			}
			if err := tr.Save(); err != nil {
				logger.Warn("SetTransferUserEquity 保存迁移用户失败", err)
			}
		}
	}

	return nil
}

func SetTransferUserEquitTikTok(data *SetTransferUserEquityPayload) error {
	rd := cache.GetLockRedis()
	v := strconv.FormatInt(time.Now().Unix(), 10)
	ctx := context.Background()
	cacheKey := fmt.Sprintf("%s:%d", setTransferLockKey, data.UID)
	if lock, err := rd.SetNX(ctx, cacheKey, v, 30*time.Second).Result(); err != nil {
		logger.Error(err)
		return nil
	} else if !lock {
		return nil
	}
	defer func() {
		if err := rd.Del(ctx, cacheKey).Err(); err != nil {
			logger.Error(err)
		}
	}()
	data.tOrder = make([]string, 0)
	dUData := tdb.AccountinfoTpSensorData{
		UID: data.OldUID,
	}
	// 只做一次迁移
	if d := edb.TbUserEquityTransfer.GetItemByDUID(dUData.UID); d != nil {
		logger.Infof("---SetTransferUserEquity--只做一次迁移- uid:%d dUData:%d", data.UID, dUData.UID)
		return nil
	}
	user := udb.TbAccountInfo.GetByUID(dUData.UID)
	if user == nil {
		logger.Infof("---SetTransferUserEquity--用户信息不存在- uid:%d dUData:%d", data.UID, dUData.UID)
		return nil
	}

	logger.Infof("---SetTransferUserEquity--1- uid:%d dUData:%d", data.UID, user.AccountID)
	var wg sync.WaitGroup
	wg.Add(4)
	go func() {
		defer wg.Done()
		data.transferOrder(user.AccountID)
	}()
	go func() {
		defer wg.Done()
		data.transferUserEquity(user.AccountID)
	}()
	go func() {
		defer wg.Done()
		data.transferWallet(user.AccountID)
	}()
	go func() {
		defer wg.Done()
		data.transferTvIntelligence(user.AccountID)
	}()
	wg.Wait()
	data.logoutFlag = true
	if data.logoutFlag {
		logger.Infof("---SetTransferUserEquity logoutFlag uid:%d dUData:%d logoutFlag:%t", data.UID,
			user.AccountID, data.logoutFlag)
		user.DeviceID = ""
		user.Alternate3 = ""
		if err := user.SaveTransferUser(); err != nil {
			logger.Warn("SetTransferUserEquity 权益迁移 注销DistinctID失败", user.AccountID, err)
		}
		dUData.BindStatus = lib.DataStatusEnum.Delete.ToInt32()
		if err := dUData.Update(); err != nil {
			logger.Warn("SetTransferUserEquity 权益迁移 取消绑定失败", err)
		}

		tr := edb.UserEquityTransfer{
			DUID:       dUData.UID,
			UID:        data.UID,
			CreateTime: time.Now().Unix(),
		}
		if err := tr.Save(); err != nil {
			logger.Warn("SetTransferUserEquity 保存迁移用户失败", err)
		}
	}
	return nil
}

// transferTvIntelligence TV 智能课表迁移
func (t *SetTransferUserEquityPayload) transferTvIntelligence(dUID int64) {
	uSchedule := tvdb.TbTvIntelligenceSUser.GetUserSchedule(t.UID, 2)
	if uSchedule != nil {
		logger.Warnf("老账号有账号不迁移 UID:%d table:%+v dUid:%d", t.UID, uSchedule, dUID)
		return
	}
	dSchedule := tvdb.TbTvIntelligenceSUser.GetUserSchedule(dUID, 2)
	if dSchedule == nil {
		logger.Warnf("D账号有账号没有课表 UID:%d  dUid:%d", t.UID, dUID)
		return
	}
	session := usercenter.GetEngineMaster().NewSession()
	defer session.Close()
	err := session.Begin()
	if err != nil {
		logger.Error("事务开启失败 ", err)
		return
	}
	logger.Warnf("D账号有课表迁移 UID:%d table:%+v dUid:%d", t.UID, dSchedule, dUID)
	dSchedule.UID = t.UID
	dSchedule.UpdateTime = time.Now().Unix()
	if err := dSchedule.UpdateByTransaction(session); err != nil {
		logger.Error("TV 智能课表更新失败 %s", err.Error())
		return
	}
	userSchedule := tvdb.TbTvIntelligenceSUserSession.GetListByScheduleID(dSchedule.ID)
	if userSchedule == nil {
		if err := session.Rollback(); err != nil {
			logger.Warn("迁移订单出错", err)
			return
		}
	}
	for _, v := range userSchedule {
		v.UID = t.UID
		if err := v.UpdateByTransaction(session); err != nil {
			logger.Warn("TV 迁移订单出错", err)
		}
	}
	if err := session.Commit(); err != nil {
		logger.Warn("迁移订单出错", err)
		return
	}
}

// TransferOrder 订单处理
// nolint
func (t *SetTransferUserEquityPayload) transferOrder(dUID int64) {
	session := usercenter.GetEngineMaster().NewSession()
	defer session.Close()
	err := session.Begin()
	if err != nil {
		logger.Error("事务开启失败 ", err)
		return
	}

	logger.Infof("---SetTransferUserEquity-transferOrder-- uid:%d distinct_id:%s dUid:%d", t.UID, t.DistinctID, dUID)
	wList := odb.TbOrderMain.GetOrderListAll(dUID)
	if len(wList) < 1 {
		if err = session.Commit(); err != nil {
			logger.Warn("迁移订单出错", err)
			return
		}
		return
	}
	logger.Infof("---SetTransferUserEquity-logoutFlag-- uid:%d distinct_id:%s dUid:%d", t.UID, t.DistinctID, dUID)
	t.logoutFlag = true
	if err = t.transferWebOrder(session, dUID); err != nil {
		if err = session.Rollback(); err != nil {
			logger.Warn("迁移订单出错", err)
			return
		}
	}
	if err = t.iosIapOrderIDfv(session, dUID); err != nil {
		if err = session.Rollback(); err != nil {
			logger.Warn("迁移订单出错", err)
			return
		}
	}
	if err = t.transferLiveOrder(session, dUID); err != nil {
		if err = session.Rollback(); err != nil {
			logger.Warn("迁移订单出错", err)
			return
		}
	}
	if err = t.transferPracticeOrder(session, dUID); err != nil {
		if err = session.Rollback(); err != nil {
			logger.Warn("迁移订单出错", err)
			return
		}
	}
	if err = t.transferEqKolOrder(session, dUID); err != nil {
		if err = session.Rollback(); err != nil {
			logger.Warn("迁移订单出错", err)
			return
		}
	}
	if err = t.transferEqNowOrder(session, dUID); err != nil {
		if err = session.Rollback(); err != nil {
			logger.Warn("迁移订单出错", err)
			return
		}
	}
	if err = t.transferEqTVOrder(session, dUID); err != nil {
		if err = session.Rollback(); err != nil {
			logger.Warn("迁移订单出错", err)
			return
		}
	}
	if err = t.transferCurrencyOrder(session, dUID); err != nil {
		if err = session.Rollback(); err != nil {
			logger.Warn("迁移订单出错", err)
			return
		}
	}
	if err = t.transferHMSIapOrder(session, dUID); err != nil {
		if err = session.Rollback(); err != nil {
			logger.Warn("迁移订单出错", err)
			return
		}
	}

	// 下面的两个方法是打底 新家迁移表时 在这两个方法之上加
	if err = t.transferOrderMain(session); err != nil {
		if err = session.Rollback(); err != nil {
			logger.Warn("迁移订单出错", err)
			return
		}
	}
	if err = t.transferIndexOrder(session); err != nil {
		if err = session.Rollback(); err != nil {
			logger.Warn("迁移订单出错", err)
			return
		}
	}

	// 迁移自动订阅数据
	if err = t.transferSubscribeUser(session, dUID); err != nil {
		if err = session.Rollback(); err != nil {
			logger.Error("迁移自动订阅订单出错", err.Error())
			return
		}
	}

	// 迁移签约
	if err = t.transferContract(session, dUID); err != nil {
		if err = session.Rollback(); err != nil {
			logger.Error("迁移签约订单出错", err.Error())
			return
		}
	}
	// 退款
	if err = t.transferRefundUser(session, dUID); err != nil {
		if err = session.Rollback(); err != nil {
			logger.Error("迁移自动订阅订单出错", err.Error())
			return
		}
	}

	if err = session.Commit(); err != nil {
		logger.Warn("迁移订单出错", err)
		return
	}
}

// transferRefundUser 退款
func (t *SetTransferUserEquityPayload) transferRefundUser(s *xorm.Session, dUID int64) error {
	logger.Infof("transferRefundUser-ok uid:%d distinct_id:%s dUid:%d",
		t.UID, t.DistinctID, dUID)
	refundList := odb.TbOrderRefund.GetOrderListAll(dUID)
	for _, v := range refundList {
		v.UID = t.UID
		if err := v.UpdateByTransaction(s); err != nil {
			return fmt.Errorf("更新TbOrderRefund失败 %s", err.Error())
		}
	}
	refundUserList := odb.TbOrderRefundUser.GetOrderListAll(dUID)
	for _, v := range refundUserList {
		v.UID = t.UID
		if err := v.UpdateByTransaction(s); err != nil {
			return fmt.Errorf("更新TbOrderRefundUser失败 %s", err.Error())
		}
	}
	return nil
}

// transferWallet 钱包迁移
func (t *SetTransferUserEquityPayload) transferWallet(dUID int64) {
	rpc := grpc.GetUserCenterClient()
	w, err := rpc.WalletBasicInfo(context.Background(), &pb.WalletBasicInfoRequest{
		UID:        dUID,
		WalletType: 2, // 2-瑜伽币钱包
	})
	if err != nil || w.GetResultCode() != 1001 {
		logger.Error("用户钱包获取失败:", dUID)
		return
	}
	if w.GetBalance() <= 0 {
		return
	}
	ctx := context.Background()
	logger.Infof("---SetTransferUserEquity-logoutFlag-- uid:%d distinct_id:%s dUid:%d", t.UID, t.DistinctID, dUID)
	t.logoutFlag = true
	rsp, err := rpc.WalletEarnMoney(ctx, &pb.WalletEarnMoneyRequest{
		UID:        t.UID,
		Amount:     w.GetBalance(),
		SourceType: 15,
		RecordDesc: "系统转移",
		WalletType: 2, // 2-瑜伽币钱包
	})
	if err != nil {
		logger.Error("用户钱包充值失败:", t.UID, rsp.Msg)
	}

	cRsp, cErr := rpc.WalletExpenseMoney(ctx, &pb.WalletExpenseMoneyRequest{
		UID:        dUID,
		Amount:     w.GetBalance(),
		SourceType: 15,
		RecordDesc: "系统转移",
		WalletType: 2, // 2-瑜伽币钱包
	})
	if cErr != nil {
		logger.Error("用户钱包扣减失败:", t.UID, cRsp.Msg)
	}
}

// iosIapOrderIDfv 迁移苹果回调log
func (t *SetTransferUserEquityPayload) iosIapOrderIDfv(s *xorm.Session, dUID int64) error {
	if wList := odb.TbIosIapOrderIDfv.GetOrderListAll(dUID); len(wList) > 0 {
		for _, v := range wList {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新苹果订单失败 %s", err.Error())
			}
		}
	}
	return nil
}

// transferWebOrder 迁移会员订单
func (t *SetTransferUserEquityPayload) transferWebOrder(s *xorm.Session, dUID int64) error {
	if wList := odb.TbWebOrder.GetOrderListAll(dUID); len(wList) > 0 {
		for _, v := range wList {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新会员订单失败 %s", err.Error())
			}
			t.tOrder = append(t.tOrder, v.OrderID)
			if table := undb.TbRecordMain.GetRecordByOrderID(v.OrderID); table != nil {
				table.UID = t.UID
				if err := table.UpdateByTransaction(s); err != nil {
					return fmt.Errorf("更新会员订单失败 %s", err.Error())
				}
				if uTable := undb.TbActivityRecord.GetRecordListByOrderID(v.OrderID); len(uTable) > 0 {
					for _, vc := range uTable {
						vc.UID = t.UID
						if err := vc.UpdateByTransaction(s); err != nil {
							return fmt.Errorf("更新会员订单失败 %s", err.Error())
						}
					}
				}
			}
		}
	}
	return nil
}

// transferLiveOrder 迁移直播订单
func (t *SetTransferUserEquityPayload) transferLiveOrder(s *xorm.Session, dUID int64) error {
	if wList := odb.TbLiveUserOrder.GetOrderListAll(dUID); len(wList) > 0 {
		for _, v := range wList {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新Live订单失败 %s", err.Error())
			}
			t.tOrder = append(t.tOrder, v.OrderID)
		}
	}
	return nil
}

// transferIndexOrder 迁移order_practice
func (t *SetTransferUserEquityPayload) transferPracticeOrder(s *xorm.Session, dUID int64) error {
	if wList := odb.TbOrderPractice.GetOrderListAll(dUID); len(wList) > 0 {
		for _, v := range wList {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新KOL订单失败 %s", err.Error())
			}
			t.tOrder = append(t.tOrder, v.OrderID)
		}
	}
	return nil
}

// transferIndexOrder 迁移order_equity_kol
func (t *SetTransferUserEquityPayload) transferEqKolOrder(s *xorm.Session, dUID int64) error {
	if wList := odb.TbOrderEquityKol.GetOrderListAll(dUID); len(wList) > 0 {
		for _, v := range wList {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新EQKOL订单失败 %s", err.Error())
			}
			t.tOrder = append(t.tOrder, v.OrderID)
		}
	}
	return nil
}

// transferIndexOrder 迁移order_equity_now
func (t *SetTransferUserEquityPayload) transferEqNowOrder(s *xorm.Session, dUID int64) error {
	if wList := odb.TbOrderEquityNow.GetOrderListAll(dUID); len(wList) > 0 {
		for _, v := range wList {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新EQNOW订单失败 %s", err.Error())
			}
			t.tOrder = append(t.tOrder, v.OrderID)
		}
	}
	return nil
}

// transferIndexOrder 迁移order_equity_TV
func (t *SetTransferUserEquityPayload) transferEqTVOrder(s *xorm.Session, dUID int64) error {
	if wList := odb.TbOrderEquityTV.GetOrderListAll(dUID); len(wList) > 0 {
		for _, v := range wList {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新EQTV订单失败 %s", err.Error())
			}
			t.tOrder = append(t.tOrder, v.OrderID)
		}
	}
	return nil
}

// transferIndexOrder 迁移yoga_currency
func (t *SetTransferUserEquityPayload) transferCurrencyOrder(s *xorm.Session, dUID int64) error {
	if wList := odb.TbOrderYogaCurrency.GetOrderListAll(dUID); len(wList) > 0 {
		for _, v := range wList {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新Currency订单失败 %s", err.Error())
			}
			t.tOrder = append(t.tOrder, v.OrderID)
		}
	}
	return nil
}

// transferHMSIapOrder 迁移鸿蒙
func (t *SetTransferUserEquityPayload) transferHMSIapOrder(session *xorm.Session, dUID int64) error {
	rList := iap.TbHmsIapOrder.GetListByUID(dUID)
	if len(rList) > 0 {
		for _, v := range rList {
			v.UID = t.UID
			if err := v.UpdateByTran(session); err != nil {
				return err
			}
		}
	}
	oList := iap.TbHmsIapRequest.GetListByUID(dUID)
	if len(oList) > 0 {
		for _, v := range oList {
			v.UID = t.UID
			if err := v.UpdateByTran(session); err != nil {
				return err
			}
		}
	}
	return nil
}

// transferOrderMain 迁移主订单
func (t *SetTransferUserEquityPayload) transferOrderMain(s *xorm.Session) error {
	if wList := odb.TbOrderMain.GetOrderListAllByOrder(t.tOrder); len(wList) > 0 {
		for _, v := range wList {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新主订单失败 %s", err.Error())
			}
		}
	}
	return nil
}

// transferIndexOrder 迁移orderIndex
func (t *SetTransferUserEquityPayload) transferIndexOrder(s *xorm.Session) error {
	if wList := odb.TbOrderIndex.GetOrderListAllByOrder(t.tOrder); len(wList) > 0 {
		for _, v := range wList {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新会员订单失败 %s", err.Error())
			}
		}
	}
	return nil
}

func (t *SetTransferUserEquityPayload) transferUserEquity(dUID int64) {
	// 这里选择time sleep50毫秒，线上问题，login转移权益时同时掉了getMeInfo，getMeInfo有修改user_member_duration的status操作
	// 会导致用户会员被加上然后又将状态置为2(过期）的情况，只要将两个步骤错开执行即可
	time.Sleep(50 * time.Millisecond)
	if err := t.transferEquityMember(dUID); err != nil {
		logger.Warn("迁移订单出错", err)
	}
	go t.transferEquity(dUID)
}

func (t *SetTransferUserEquityPayload) transferEquityMember(dUID int64) error {
	logger.Infof("---SetTransferUserEquity-transferEquityMember- uid:%d distinct_id:%s dUid:%d", t.UID,
		t.DistinctID, dUID)
	dur := udb.TbUserMemberDuration.GeUserLastedDuration(dUID)
	if dur == nil {
		return nil
	}
	if dur.EndTime < time.Now().Unix() {
		return nil
	}
	logger.Infof("---SetTransferUserEquity-transferEquityMember-OK uid:%d distinct_id:%s dUid:%d", t.UID,
		t.DistinctID, dUID)
	expireDay := comm.Days(time.Now().Unix(), dur.EndTime, true)
	logger.Infof("---SetTransferUserEquity-logoutFlag-- uid:%d distinct_id:%s dUid:%d", t.UID, t.DistinctID, dUID)
	t.logoutFlag = true
	uVal := make(url.Values)
	uVal.Add("uid", strconv.FormatInt(t.UID, 10))
	uVal.Add("vip_duration_type", "1") // 会员计量方式 1-天 2 月 3年
	uVal.Add("vip_duration_value", strconv.FormatInt(expireDay, 10))
	uVal.Add("member_level", strconv.FormatInt(6, 10))
	uVal.Add("business_line", strconv.FormatInt(3, 10)) // 会员运营
	uVal.Add("category_item", strconv.FormatInt(2, 10)) // 活动发放
	uVal.Add("source_type", strconv.FormatInt(6, 10))   // 管理后台添加
	resp, err := requests.NewYogaClient(config.Get().PhpYogaAddress).PostForm("/usercenter/user/setvipflexible", uVal)
	logger.Infof("---SetTransferUserEquity-transferEquityMember-resp uid:%d distinct_id:%s dUid:%d Response:%+v", t.UID,
		t.DistinctID, dUID, resp)
	if err != nil {
		logger.Errorf("调用yoga发放会员报错, err: %s", err)
		return err
	}
	if resp == nil || !resp.IsOK() || resp.Body == "" {
		logger.Error("调用yoga发放会员失败", resp)
		return fmt.Errorf("数据返回失败 %+v", resp)
	}

	dVal := make(url.Values)
	dVal.Add("uid", strconv.FormatInt(dUID, 10))
	dVal.Add("vip_duration_type", "1") // 会员计量方式 1-天 2 月 3年
	dVal.Add("vip_duration_value", strconv.FormatInt(-expireDay, 10))
	dVal.Add("member_level", strconv.FormatInt(int64(dur.MemberLevel), 10))
	dVal.Add("business_line", strconv.FormatInt(3, 10)) // 会员运营
	dVal.Add("category_item", strconv.FormatInt(2, 10)) // 活动发放
	dVal.Add("source_type", strconv.FormatInt(6, 10))   // 管理后台添加
	dResp, dErr := requests.NewYogaClient(config.Get().PhpYogaAddress).PostForm("/usercenter/user/setvipflexible", dVal)
	if dErr != nil {
		logger.Errorf("调用yoga发放会员报错, err: %s", err)
		return err
	}
	if dResp == nil || !dResp.IsOK() || dResp.Body == "" {
		logger.Error("调用yoga扣减会员失败", dResp)
		return fmt.Errorf("数据返回失败 %+v", resp)
	}

	return nil
}

// transferEquity
func (t *SetTransferUserEquityPayload) transferEquity(dUID int64) {
	logger.Infof("---SetTransferUserEquity-transferEquity- uid:%d distinct_id:%s dUid:%d", t.UID, t.DistinctID, dUID)
	durList := edb.TbUserEquityDuration.GetList(dUID)
	if len(durList) < 1 {
		return
	}
	logger.Infof("-SetTransferUserEquity-transferEquity-ok uid:%d distinct_id:%s dUid:%d", t.UID, t.DistinctID, dUID)
	for _, v := range durList {
		if v.EndTime < time.Now().Unix() {
			continue
		}
		logger.Infof("---SetTransferUserEquity-logoutFlag-- uid:%d distinct_id:%s dUid:%d", t.UID,
			t.DistinctID, dUID)
		t.logoutFlag = true
		expireDay := comm.Days(time.Now().Unix(), v.EndTime, true)
		e := &SetAddUserKolEquityPayload{
			UID:                 t.UID,
			EquityType:          v.EquityType,
			Operation:           lib.OperationEnum.Add.ToInt32(),
			RecordType:          lib.RecordTypeEnum.Back.ToInt32(),
			EquityDurationType:  lib.EquityDurationTypeEnum.Day.ToInt32(),
			EquityDurationValue: expireDay,
			AdminName:           "transfer_system",
			CreateTime:          time.Now().Unix(),
		}
		if err := SetAddUserEquity(e); err != nil {
			logger.Error("特权发放失败", e)
		}
		b := &SetAddUserKolEquityPayload{
			UID:                 dUID,
			EquityType:          v.EquityType,
			Operation:           lib.OperationEnum.Sub.ToInt32(),
			RecordType:          lib.RecordTypeEnum.Back.ToInt32(),
			EquityDurationType:  lib.EquityDurationTypeEnum.Day.ToInt32(),
			EquityDurationValue: expireDay,
			AdminName:           "transfer_system",
			CreateTime:          time.Now().Unix(),
		}
		if err := SetAddUserEquity(b); err != nil {
			logger.Error("特权扣减失败", b)
		}
	}
}

/**
// transferEquityLive 迁移直播权益
func (t *SetTransferUserEquityPayload) transferEquityLive(dUID int64) {
	if uLives := udb.TbLiveUserDuration.GetUserEffectiveList(dUID,
		lib.DataStatusEnum.Valid.ToInt32(), 1); len(uLives) > 0 {
		var quantity int32
		var exp int64
		for _, v := range uLives {
			if v.EndTime < time.Now().Unix() && v.EndTime != 0 {
				continue
			}
			logger.Infof("---SetTransferUserEquity-logoutFlag-- uid:%d distinct_id:%s dUid:%d", t.UID,
				t.DistinctID, dUID)
			t.logoutFlag = true
			quantity += v.Quantity
			if v.EndTime > 0 {
				expireDay := comm.Days(time.Now().Unix(), v.EndTime, true)
				exp += expireDay
				continue
			}
			exp += v.Exp
		}
		rpc := grpc.GetLiveCourseClient()
		w, err := rpc.OperationUserLiveEquity(context.Background(), &lpb.OperationUserLiveEquityRequest{
			UIDS:          fmt.Sprintf("%d", t.UID),
			ProductType:   1, // 课程包
			Operation:     1, // 加有效期
			Quantity:      quantity,
			RecordType:    3, // 后台操作
			DurationType:  1, // 天
			DurationValue: exp,
			SourceType:    3, // 后台权益
			AdminName:     "transfer_system",
		})
		if err != nil || w.GetResultCode() != 1001 {
			logger.Error("直播权益迁移失败:", t.UID)
			return
		}

		cw, cErr := rpc.CloseUserAllLiveEquity(context.Background(), &lpb.CloseUserAllLiveEquityRequest{
			UID:        dUID,
			SourceType: 3, // 后台权益
			AdminName:  "transfer_system",
		})
		if cErr != nil || cw.GetResultCode() != 1001 {
			logger.Error("直播权益关闭失败:", dUID)
			return
		}
	}
}
*/
// transferSubscribeUser 自动订阅
func (t *SetTransferUserEquityPayload) transferSubscribeUser(s *xorm.Session, dUID int64) error {
	logger.Infof("transferSubscribeUser-ok uid:%d distinct_id:%s dUid:%d",
		t.UID, t.DistinctID, dUID)
	webList := odb.TbWPSubU.GetAllWebSubscribeUser(dUID)
	for _, v := range webList {
		v.UID = t.UID
		logger.Infof("transferSubscribeUser-web-ok uid:%d distinct_id:%s dUid:%d",
			t.UID, t.DistinctID, dUID)
		if err := v.UpdateByTransaction(s); err != nil {
			return fmt.Errorf("更新WebSubscribeUser失败 %s", err.Error())
		}
	}
	pSubList := odb.TbPPSubU.GetAllPSubscribeUser(dUID)
	for _, v := range pSubList {
		v.UID = t.UID
		logger.Infof("-transferSubscribeUser-pp-ok uid:%d distinct_id:%s dUid:%d",
			t.UID, t.DistinctID, dUID)
		if err := v.UpdateByTransaction(s); err != nil {
			return fmt.Errorf("更新PreferentialProductSubscribeUser失败 %s", err.Error())
		}
	}
	lSubList := odb.TbLSubU.GetLiveSubscribeUser(dUID)
	for _, v := range lSubList {
		v.UID = t.UID
		logger.Infof("transferSubscribeUser-live-ok uid:%d distinct_id:%s dUid:%d",
			t.UID, t.DistinctID, dUID)
		if err := v.UpdateByTransaction(s); err != nil {
			return fmt.Errorf("更新LiveProductSubscribeUser订单失败 %s", err.Error())
		}
	}
	tList := odb.TbTiktokContract.GetListByUID(dUID)
	if len(tList) > 0 {
		for _, v := range tList {
			v.UID = t.UID
			if err := v.UpdateByTran(s); err != nil {
				return err
			}
		}
		cList := odb.TbTiktokCharge.GetListByUID(dUID)
		if len(cList) > 0 {
			for _, c := range cList {
				c.UID = t.UID
				if err := c.UpdateByTran(s); err != nil {
					return err
				}
			}
		}
		pList := odb.TbPreferentialTiktokCharge.GetListByUID(dUID)
		if len(pList) > 0 {
			for _, p := range pList {
				p.UID = t.UID
				if err := p.UpdateByTran(s); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

// transferContract 迁移签约
// nolint
func (t *SetTransferUserEquityPayload) transferContract(s *xorm.Session, dUID int64) error {
	if alis := cdb.TbWebOrderAlipayContract.GetOrderListAll(dUID); alis != nil {
		for _, v := range alis {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新TbWebOrderAlipayContract 订阅失败 %s", err.Error())
			}
		}
		if wChargeList := cdb.TbWebOrderAlipayContractCharge.GetChargeListAll(dUID); len(wChargeList) > 0 {
			for _, v := range wChargeList {
				v.UID = t.UID
				if err := v.UpdateByTransaction(s); err != nil {
					return fmt.Errorf("更新TbWebOrderAlipayContractCharge 订阅失败 %s", err.Error())
				}
			}
		}
		if pChargeList := cdb.TbPreferentialOrderAlipayContractCharge.GetChargeListAll(dUID); len(pChargeList) > 0 {
			for _, v := range pChargeList {
				v.UID = t.UID
				if err := v.UpdateByTransaction(s); err != nil {
					return fmt.Errorf("更新TbPreferentialOrderAlipayContractCharge 订阅失败 %s", err.Error())
				}
			}
		}
		if liveChargeList := cdb.TbLiveOrderAlipayContractCharge.GetChargeListAll(dUID); len(liveChargeList) > 0 {
			for _, v := range liveChargeList {
				v.UID = t.UID
				if err := v.UpdateByTransaction(s); err != nil {
					return fmt.Errorf("更新TbLiveOrderAlipayContractCharge 订阅失败 %s", err.Error())
				}
			}
		}
	}
	if alis := cdb.TbWebOrderHuaweiContract.GetOrderListAll(dUID); alis != nil {
		for _, v := range alis {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新TbWebOrderHuaweiContract 订阅失败 %s", err.Error())
			}
		}
	}
	if alis := cdb.TbWebOrderXiaomiContract.GetOrderListAll(dUID); alis != nil {
		for _, v := range alis {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新TbWebOrderXiaomiContract 订阅失败 %s", err.Error())
			}
		}
	}
	if alis := cdb.TbWebOrderNewHuaweiContract.GetOrderListAll(dUID); alis != nil {
		for _, v := range alis {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新TbWebOrderNewHuaweiContract 订阅失败 %s", err.Error())
			}
		}
		if wChargeList := cdb.TbWebOrderNHuaweiContractCharge.GetChargeListAll(dUID); len(wChargeList) > 0 {
			for _, v := range wChargeList {
				v.UID = t.UID
				if err := v.UpdateByTransaction(s); err != nil {
					return fmt.Errorf("更新TbWebOrderNHuaweiContractCharge 订阅失败 %s", err.Error())
				}
			}
		}
		if pChargeList := cdb.TbPreferentialOrderNHuaweiContractCharge.GetChargeListAll(dUID); len(pChargeList) > 0 {
			for _, v := range pChargeList {
				v.UID = t.UID
				if err := v.UpdateByTransaction(s); err != nil {
					return fmt.Errorf("更新TbPreferentialOrderNHuaweiContractCharge 订阅失败 %s", err.Error())
				}
			}
		}
		if liveChargeList := cdb.TbLiveOrderNHuaweiContractCharge.GetChargeListAll(dUID); len(liveChargeList) > 0 {
			for _, v := range liveChargeList {
				v.UID = t.UID
				if err := v.UpdateByTransaction(s); err != nil {
					return fmt.Errorf("更新TbLiveOrderNHuaweiContractCharge 订阅失败 %s", err.Error())
				}
			}
		}
	}
	if alis := odb.TbWebWxContract.GetOrderListAll(dUID); alis != nil {
		for _, v := range alis {
			v.UID = t.UID
			if err := v.UpdateByTransaction(s); err != nil {
				return fmt.Errorf("更新TbWebOrderNewHuaweiContract 订阅失败 %s", err.Error())
			}
		}
		if wChargeList := cdb.TbWebOrderWexinContractCharge.GetChargeListAll(dUID); len(wChargeList) > 0 {
			for _, v := range wChargeList {
				v.UID = t.UID
				if err := v.UpdateByTransaction(s); err != nil {
					return fmt.Errorf("更新TbWebOrderWexinContractCharge 订阅失败 %s", err.Error())
				}
			}
		}
		if liveChargeList := cdb.TbLiveOrderWechatContractCharge.GetChargeListAll(dUID); len(liveChargeList) > 0 {
			for _, v := range liveChargeList {
				v.UID = t.UID
				if err := v.UpdateByTransaction(s); err != nil {
					return fmt.Errorf("更新TbLiveOrderWechatContractCharge 订阅失败 %s", err.Error())
				}
			}
		}
	}
	return nil
}

func (t *SetTransferUserEquityPayload) getUserTpList(dUID int64) bool {
	if wx := tdb.TpWeixin.GetByOpenIDBind(dUID); wx != nil {
		return true
	}

	if qq := tdb.TpAQq.GetByOpenIDBind(dUID); qq != nil {
		return true
	}
	if sa := tdb.TpASinaweibo.GetByOpenIDBind(dUID); sa != nil {
		return true
	}
	if apple := tdb.TpAaTpIos.GetByOpenIDBind(dUID); apple != nil {
		return true
	}
	return false
}
